package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.utils.ExceptionUtil;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.dto.AppointmentApplyDto;
import com.redxun.fire.core.pojo.dto.MaintenancePlanDto;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.maintenance.MaintenancePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * 维保计划表_new前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-11-07
 */
@Slf4j
@Api(tags = "维保计划表_new")
@RestController
@RequestMapping("/maintenancePlan")
public class MaintenancePlanController {

    @Resource
    MaintenancePlanService service;

    @Resource
    IBaseDeviceTemporaryService baseDeviceTemporaryService;

    @PostMapping("select")
    public JsonPageResult selectMaintenancePlanPage(HttpServletRequest request, @RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.selectMaintenancePlanPage(request, queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }


    /**
     * app端获取維保信息
     */
    @PostMapping("selectMaintenancePlan")
    public JsonResult selectMaintenancePlan(HttpServletRequest request, @RequestBody @Validated MaintenancePlanDto maintenancePlanDto) {
        return new JsonResult().setSuccess(true).setData(service.selectMaintenancePlanTmp(request, maintenancePlanDto));
    }

    /**
     * app端获取維保信息
     */
    @PostMapping("selectMaintenancePlanOnPage")
    public JsonResult selectMaintenancePlanOnPage(HttpServletRequest request, @RequestBody QueryData queryData) {
        return new JsonResult().setSuccess(true).setData(service.selectMaintenancePlanOnPage(request, queryData));
    }

    /**
     * app端获取检查计划
     */
    @PostMapping("selectMaintenanceCheckPlan")
    public JsonResult selectMaintenanceCheckPlan(HttpServletRequest request, @RequestBody @Validated MaintenancePlanDto maintenancePlanDto) {
        return new JsonResult().setSuccess(true).setData(service.selectMaintenanceCheckPlan(request, maintenancePlanDto));
    }

    /**
     * app端获取检查计划
     */
    @PostMapping("selectMaintenanceCheckPlanOnPage")
    public JsonResult selectMaintenanceCheckPlanOnPage(HttpServletRequest request, @RequestBody QueryData queryData) {
        return new JsonResult().setSuccess(true).setData(service.selectMaintenanceCheckPlanOnPage(request, queryData));
    }


//    /**
//     * 导出excel
//     *
//     * @return
//     */
//    @MethodDefine(title = "导出EXCEL", path = "/export", method = HttpMethodConstants.POST,
//            params = {@ParamDefine(title = "查询数据", varName = "params")})
//    @PostMapping("/export")
//    public void exportMaintenancePlan(@RequestBody MaintenancePlanDto maintenancePlanDto) throws IOException {
//        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        HttpServletResponse response=attributes.getResponse();
//        List<MaintenancePlan> maintenancePlanList = service.getMaintenancePlan(maintenancePlanDto);
//        List<MaintenancePlanExcel> maintenancePlanExcelList =  new MaintenancePlanExcel().init(maintenancePlanList);
//        //导出操作
//        ExcelUtil.exportExcel(maintenancePlanExcelList, "维保计划", "维保计划", MaintenancePlanExcel.class, "维保计划", response);
//    }


    /**
     * 大瓶导出 数据
     */
    @GetMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response) throws IOException {
        this.service.exportMaintenancePlan(request, response);
    }


    /**
     * 根据维保合同获取月份数据
     */
    @PostMapping("getMaintenancePlanByMonth/{wbId}")
    public JsonResult getMaintenancePlanByMonth(@PathVariable String wbId) {
        return new JsonResult().setSuccess(true).setData(service.getMaintenancePlanByWbId(wbId));
    }

    /**
     * APP获取测试|联动计划列表
     */
    @PostMapping("getAppTestPlanList")
    public JsonResult getAppTestPlanList(HttpServletRequest request, @RequestBody @Validated AppointmentApplyDto appointmentApplyDto) {
        return new JsonResult().setSuccess(true).setData(service.getAppTestPlanList(request, appointmentApplyDto));
    }

    /**
     * APP获取测试|联动计划列表(分页)
     */
    @PostMapping("getAppTestPlanPage")
    public JsonResult getAppTestPlanPage(HttpServletRequest request, @RequestBody QueryData queryData) {

        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.getAppTestPlanPage(request, queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }


    /**
     * WEB 获取测试|联动计划列表
     */
    @PostMapping("getWebTestPlanPage")
    public JsonResult getWebTestPlanPage(HttpServletRequest request, @RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("");
        try {
            IPage page = service.getWebTestPlanPage(request, queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        jsonResult.setShow(false);
        return jsonResult;
    }

    /**
     * WEB 获取测试|联动已测试点位列表
     */
    @PostMapping("getWebTestPointPage")
    public JsonResult getWebTestPointPage(@RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.getWebTestPointPage(queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    /***
     * 时间调整
     */
    @PostMapping("planTimeAdjustment")
    public JsonResult planTimeAdjustment(HttpServletRequest request, @RequestBody @Validated(Update.class) List<MaintenancePlanDto> maintenancePlanDtoList) {
        return JsonPageResult.getSuccess("返回数据成功!").setData(service.planTimeAdjustment(request, maintenancePlanDtoList));
    }


    /**
     * 大屏维保计划列表
     */
    @PostMapping("maintenancePlanPage")
    public JsonPageResult maintenancePlanPage(HttpServletRequest request, @RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.maintenancePlanPage(request, queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    /**
     * 大屏维保计划统计
     */
    @PostMapping("maintenancePlanStatistics")
    public JsonResult maintenancePlanStatistics(HttpServletRequest request) {
        return JsonPageResult.getSuccess("返回数据成功!").setData(service.maintenancePlanStatistics(request));
    }

    /**
     * 大瓶导出 数据
     */
    @GetMapping("exportMaintenancePlan")
    public void exportMaintenancePlan(HttpServletRequest request, HttpServletResponse response) throws IOException {
        this.service.export(request, response);
    }


    /**
     * 联动测试计划详情导出 数据
     */
    @PostMapping("exportLinkTestPlan")
    @ApiOperation("导出测试项")
    @ResponseBody
    public void exportLinkTestPlan(@RequestBody @Validated MaintenancePlanSysDto maintenancePlanSysDto, HttpServletRequest request, HttpServletResponse response) throws Exception {
        this.service.exportLinkTestPlan(maintenancePlanSysDto, request, response);
    }


    /**
     * 维保计划进度
     *
     * @return
     */
    @GetMapping("planProgress")
    public JsonResult planProgress(String buildingId) throws ParseException {
        return JsonPageResult.getSuccess("返回数据成功!").setData(this.service.planProgress(buildingId));
    }


    /**
     * 刷新维保数据
     *
     * @return
     */
    @GetMapping(value = "/restPlan")
    public ResultMsg rest(String buildingId) {
        baseDeviceTemporaryService.restPlan(buildingId);
        return ResultMsg.getResultMsg("处理成功", 200);
    }


    /**
     * @return com.redxun.common.base.entity.JsonResult
     * @Description: 删除维保历史数据
     * <AUTHOR>
     * @date 2024/5/7 5:19 PM
     */
    @GetMapping("/clean")
    public JsonResult clean(String startTime) {
        JsonResult result = JsonResult.Success();
        service.cleanData(startTime);
        return result;
    }


    /**
     * @return com.redxun.common.base.entity.JsonResult
     * @Description: 同步维保历史数据到新表
     * <AUTHOR>
     * @date 2024/5/7 5:19 PM
     */
    @GetMapping("/transferData")
    public JsonResult transferData(String startTime, String endTime, Integer type, String planId) {
        if (type == null) {
            return JsonResult.Fail("缺少参数");
        }

        JsonResult result = JsonResult.Success();
        service.transferData(startTime, endTime, type, planId);
        return result;
    }


    /**
     * @return com.redxun.common.base.entity.JsonResult
     * @Description: 同步维保历史数据到新表-点位计划
     * <AUTHOR>
     * @date 2024/5/7 5:19 PM
     */
    @GetMapping("/transferAppointmentPointData")
    public JsonResult transferAppointmentPointData(String effectiveTime) {
        if (effectiveTime == null) {
            return JsonResult.Fail("缺少参数");
        }

        JsonResult result = JsonResult.Success();
        service.transferAppointmentPointData(effectiveTime);
        return result;
    }


}
