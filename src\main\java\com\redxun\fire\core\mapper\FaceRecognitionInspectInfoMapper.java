package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.dutymanage.FaceRecognitionInspectInfo;
import com.redxun.fire.core.pojo.vo.FaceRecognitionInspectInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【face_recognition_inspect_info(人脸识别查岗结果)】的数据库操作Mapper
* @createDate 2025-01-23 09:54:26
* @Entity generator.domain.FaceRecognitionInspectInfo
*/
public interface FaceRecognitionInspectInfoMapper extends BaseMapper<FaceRecognitionInspectInfo> {

    IPage<FaceRecognitionInspectInfoVo> queryPage(Page<FaceRecognitionInspectInfoVo> page, @Param("param") Map<String, String> params);
}




