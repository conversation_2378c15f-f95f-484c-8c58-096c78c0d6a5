package com.redxun.fire.core.controller;

import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.service.monitor.IBuildingConsoleService;
import com.redxun.fire.core.consts.Constant;
import com.redxun.fire.core.utils.ServletsUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @program jpaas
 * @description 单店监控台接口
 * @create 2020-09-27 10:47
 **/

@RestController
@RequestMapping("/building-console")
public class BuildingConsoleController {

    @Resource
    private IBuildingConsoleService buildingConsoleService;


    /**
     * 获取单个建筑物详情
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getBuilding", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getBuilding(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取单个建筑物详情
        Map allBuildingList = buildingConsoleService.getBuilding(param);
        return ResultMsg.getResultMsg("单个建筑物详情获取成功", allBuildingList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 获取当班人员
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getUser", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getUser(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取当班人员信息
        List<Map<String, String>> result = buildingConsoleService.getUser(param);
        return ResultMsg.getResultMsg("单个建筑物详情获取成功", result, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 获取火警信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFire", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getBuildingFire(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取单个建筑物火警信息
        Map allBuildingList = buildingConsoleService.getBuildingFire(param);
        return ResultMsg.getResultMsg("建筑物火警信息获取成功", allBuildingList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.6 获取预警信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWarning", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWarning(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map faultTypeList = buildingConsoleService.getWarning(param);
        return ResultMsg.getResultMsg("建筑物获取预警信息获取成功", faultTypeList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 获取单个建筑物故障信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFault", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getBuildingFault(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取单个建筑物故障信息
        Map allBuildingList = buildingConsoleService.getBuildingFault(param);
        return ResultMsg.getResultMsg("建筑物故障信息获取成功", allBuildingList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 获取误报统计
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFireTypeList", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFireTypeList(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取误报统计
        List<Map> fireTypeList = buildingConsoleService.getFireTypeList(param);
        return ResultMsg.getResultMsg("建筑物误报信息获取成功", fireTypeList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.11 获取故障统计折线图
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFaultTypeList", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFaultTypeList(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取故障统计折线图
        List<Map> fireTypeList = buildingConsoleService.getFaultTypeList(param);
        return ResultMsg.getResultMsg("建筑物故障统计信息获取成功", fireTypeList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.8 获取水压检测信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPreInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPreInfo(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Integer> preInfo = buildingConsoleService.getPreInfo(param);
        return ResultMsg.getResultMsg("建筑物水压信息获取成功", preInfo, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.9 获取水泵监测信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPump", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPump(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Integer> faultTypeList = buildingConsoleService.getPump(param);
        return ResultMsg.getResultMsg("建筑物水泵信息获取成功", faultTypeList, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }


    /**
     * 2.13 获取远传设备
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getIotTotle", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getIotTotal(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Object> iotTotle = buildingConsoleService.getIotStatus(param);
        return ResultMsg.getResultMsg("建筑物远传设备信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.14 获取远传主机
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getHostTotal", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getHostTotal(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Object> iotTotle = buildingConsoleService.getHostTotal(param);
        return ResultMsg.getResultMsg("建筑物远传主机获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.3 获取消防水压总数
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPreTotle", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPreTotle(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Integer> iotTotle = buildingConsoleService.getPreTotle(param);
        return ResultMsg.getResultMsg("建筑物水泵信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.3 获取消防水泵总数
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPumpTotle", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPumpTotle(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        Map<String, Integer> iotTotle = buildingConsoleService.getPumpTotle(param);
        return ResultMsg.getResultMsg("建筑物水泵信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.3 获取单店评分
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getScore", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getScore(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        List<Map> iotTotle = buildingConsoleService.getScore(param);
        return ResultMsg.getResultMsg("建筑物水泵信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 2.3 获取评分设置
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getScoreStatic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getScoreStatic(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        List<Map> iotTotle = buildingConsoleService.getScoreStatic(param);
        return ResultMsg.getResultMsg("建筑物水泵信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 处理异常信息列表
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getExceptionList", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getExceptionList(@RequestParam("pageIndex") Integer pageIndex, @RequestParam("pageRows") Integer pageRows, HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        return ResultMsg.getResultMsg("获取用户信息传输装置异常信息成功", buildingConsoleService.getExceptionList(pageIndex, pageRows, param), 200);
    }

    /**
     * 获取主机异常信息列表
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getLocalHostExceptionList", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getLocalHostExceptionList(@RequestParam("pageIndex") Integer pageIndex, @RequestParam("pageRows") Integer pageRows, HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        return ResultMsg.getResultMsg("获取主机异常信息列表成功", buildingConsoleService.getLocalHostExceptionList(pageIndex, pageRows, param), 200);
    }

    /**
     * 处理异常信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/updateException", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg updateException(HttpServletRequest request, @RequestBody Map<String, Object> map) {
        return buildingConsoleService.updateExpHandleInfo(request, map);

    }

    /**
     * 处理主机异常信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/updateLocalHostException", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg updateLocalHostException(HttpServletRequest request, @RequestBody Map<String, Object> map) {
        return buildingConsoleService.updateLocalHostException(request, map);

    }


    /**
     * 2.13 获取远传设备
     *
     * @return
     */
    @RequestMapping(value = "/getIotTotleAll", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getIotTotleAll() {
        Map<String, Object> iotTotle = buildingConsoleService.getIotStatusAll();
        return ResultMsg.getResultMsg("建筑物远传设备信息获取成功", iotTotle, Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

}
