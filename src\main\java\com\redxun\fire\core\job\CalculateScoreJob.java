package com.redxun.fire.core.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.BaseManagementScore;
import com.redxun.fire.core.entity.BaseScore;
import com.redxun.fire.core.entity.MaintenancePlan;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.BaseManagementScoreMapper;
import com.redxun.fire.core.mapper.BaseScoreMapper;
import com.redxun.fire.core.mapper.FaultFireStatisticsMapper;
import com.redxun.fire.core.mapper.FireInfoMapper;
import com.redxun.fire.core.mapper.WaterAbnormalMapper;
import com.redxun.fire.core.service.alarm.IFirePageWindowService;
import com.redxun.fire.core.service.maintenance.MaintenancePlanService;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.DateUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 * @author: 訾浩
 * @since: 2024/4/26 15:24
 * @description: 计算综合管理评分
 * @cron: 0 0 0 * * ?
 */
@Slf4j
@Service
public class CalculateScoreJob extends IJobHandler {

    @Resource
    private BaseScoreMapper baseScoreMapper;
    @Resource
    private BaseBuildingMapper baseBuildingMapper;
    @Resource
    private BaseDevicePointMapper baseDevicePointMapper;
    @Resource
    private FireInfoMapper fireInfoMapper;
    @Resource
    private BaseManagementScoreMapper baseManagementScoreMapper;
    @Resource
    private MaintenancePlanService maintenancePlanService;
    @Resource
    private IFirePageWindowService firePageWindowService;
    @Resource
    private FaultFireStatisticsMapper faultFireStatisticsMapper;
    @Resource
    private WaterAbnormalMapper waterAbnormalMapper;

    @Override
    @XxlJob("CalculateScoreJobHandler")
    public void execute() throws Exception {
        calculateScore();
    }

    @Async
    public void calculateScore() throws ParseException {
        //综合管理评分
        log.info("计算综合管理评分任务开始");
        List<BaseScore> manageScores = baseScoreMapper.selectList(new QueryWrapper<BaseScore>().eq("cal_type", "0"));
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(new QueryWrapper<>());
        for (BaseBuilding baseBuilding : baseBuildings) {
            String id = baseBuilding.getId();
            log.info("计算综合管理评分，建筑id:" + id);
            //各个评分展示
            boolean flag = true;
            //当天时间+广场ID查询是否有记录
            BaseManagementScore baseManagementScore = baseManagementScoreMapper.selectNewData(id);
            if(baseManagementScore == null){
                baseManagementScore = new BaseManagementScore();
                flag = false;
            }
            baseManagementScore.setBid(id);
            String calTime = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            baseManagementScore.setCalTime(calTime);
            Integer score = 0;
            Double dayScore = null;
            Map<String, Object> param = null;
            Date date = null;
            //查询
            Integer totalDev = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("building_id", id).eq("super_type", "0"));
            Integer totalPump = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("building_id", id).eq("super_type", "1"));
            Integer totalPre = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("building_id", id).eq("super_type", "2"));
            for (BaseScore baseScore : manageScores) {
                Double value = baseScore.getScoreValue();
                Double faultRate = 0.0;
                Integer num = 0;
                List<String> periotList = firePageWindowService.getPeriotDate(30);
                switch (baseScore.getScoreType()) {
                    case "日均故障率评分":
                        param = new HashMap<>();
                        faultRate = 0.0;
                        num = 0;
                        param.put("buildingId", id);
                        param.put("types", "1");
                        periotList = firePageWindowService.getPeriotDate(7);
                        for (String p : periotList) {
                            param.put("todayDate", p);
                            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
                            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                                if(faultFireStatistics.get(0) != null && faultFireStatistics.get(0).get("faultRate")!= null ) {
                                    faultRate += Double.valueOf(faultFireStatistics.get(0).get("faultRate").toString());
                                    num++;
                                }
                            }
                        }
                        log.info("计算综合管理评分，日均故障数据总数:" + num);
                        log.info("计算综合管理评分，日均故障率总数:" + faultRate);
                        if(num == 0){
                            dayScore = 0.;
                        }else{
                            dayScore = faultRate/num/100;
                        }
                        log.info("计算综合管理评分，日均故障率:" + dayScore);
                        if (dayScore >= 0.002) {
                            score += 0;
                            log.info("计算综合管理评分，日均故障率评分:" + score);
                            baseManagementScore.setFaultScore("0");
                        } else {
                            Integer tmp = new Double(value * (1 - 500 * dayScore)).intValue();
                            score += tmp;
                            log.info("计算综合管理评分，日均故障率评分:" + score);
                            baseManagementScore.setFaultScore(tmp.toString());
                        }
                        break;
                    case "日均反复误报率评分":
                        param = new HashMap<>();
                        faultRate = 0.0;
                        num = 0;
                        param.put("buildingId", id);
                        param.put("types", "2");
                        periotList = firePageWindowService.getPeriotDate(7);
                        for (String p : periotList) {
                            param.put("todayDate", p);
                            List<Map<String, Object>> faultFireStatistics = faultFireStatisticsMapper.selectStatisticsByDateList(param);
                            if (!CollectionUtils.isEmpty(faultFireStatistics)) {
                                if(faultFireStatistics.get(0) != null && faultFireStatistics.get(0).get("alarmRate")!= null ) {
                                    faultRate += Double.valueOf(faultFireStatistics.get(0).get("alarmRate").toString());
                                    num++;
                                }
                            }
                        }
                        if(num == 0){
                            dayScore = 0.;
                        }else{
                            dayScore = faultRate/num/100;
                        }
                        if (dayScore >= 0.002) {
                            score += 0;
                            log.info("计算综合管理评分，日均反复误报率评分:" + score);
                            baseManagementScore.setMisinfoScore("0");
                        } else {
                            Integer tmp = new Double(value * (1 - 500 * dayScore)).intValue();
                            score += tmp;
                            log.info("计算综合管理评分，日均反复误报率评分:" + score);
                            baseManagementScore.setMisinfoScore(tmp.toString());
                        }
                        break;
                    case "日均水压监测异常评分":
                        param = new HashMap<>();
                        param.put("buildingId", id);
                        param.put("type", 1);
                        //处理时长
                        Double processTime = waterAbnormalMapper.getProcessTime(param);

                        //获取报备设备点位对应时长信息
                        param.put("devType", 2);
                        Double reportedDeviceProcessTime = waterAbnormalMapper.getReportedDeviceProcessTime(param);
                        processTime = transformTime(processTime, reportedDeviceProcessTime);

                        processTime = processTime == null ? 0. : processTime;
                        if (totalPre == 0) {
                            dayScore = 0.;
                        }else{
                            dayScore = processTime / totalPre;
                        }
                        if(dayScore == 0.){
                            score += 10;
                            log.info("计算综合管理评分，日均水压监测故障率评分:" + score);
                            baseManagementScore.setPreScore("10");
                        }else if (dayScore >= 4) {
                            score += 0;
                            log.info("计算综合管理评分，日均水压监测故障率评分:" + score);
                            baseManagementScore.setPreScore("0");
                        } else {
                            Integer tmp = new Double(10 -5 * (10 * dayScore - 2) / 19).intValue();
                            tmp = tmp > 10 ? 10 : tmp;
                            tmp = tmp < 0 ? 0 : tmp;
                            score += tmp;
                            log.info("计算综合管理评分，日均水压监测故障率评分:" + score);
                            baseManagementScore.setPreScore(tmp.toString());
                        }
                        break;
                    case "日均水泵监测异常评分":
                        param = new HashMap<>();
                        param.put("buildingId", id);
                        param.put("type", 0);
                        //处理时长
                        processTime = waterAbnormalMapper.getProcessTime(param);
                        //获取报备设备点位对应时长信息
                        param.put("devType", 1);
                        Double reportedProcessTime = waterAbnormalMapper.getReportedDeviceProcessTime(param);
                        processTime = transformTime(processTime, reportedProcessTime);

                        processTime = processTime == null ? 0. : processTime;
                        if (totalPump == 0) {
                            dayScore = 0.;
                        }else{
                            dayScore = processTime / totalPump;
                        }
                        if(dayScore == 0.){
                            score += 10;
                            log.info("计算综合管理评分，日均水泵监测故障率评分:" + score);
                            baseManagementScore.setPumpScore("10");
                        }else if (dayScore >= 10) {
                            score += 0;
                            log.info("计算综合管理评分，日均水泵监测故障率评分:" + score);
                            baseManagementScore.setPumpScore("0");
                        } else {
                            Integer tmp = new Double(10 - 10 * (2 * dayScore - 1) / 19).intValue();
                            tmp = tmp > 10 ? 10 : tmp;
                            tmp = tmp < 0 ? 0 : tmp;
                            score += tmp;
                            log.info("计算综合管理评分，日均水泵监测故障率评分:" + score);
                            baseManagementScore.setPumpScore(tmp.toString());
                        }
                        break;
                    case "消防维保进度评分":
                        MaintenancePlan maintenancePlan = maintenancePlanService.planProgress(id);
                        if (maintenancePlan != null && maintenancePlan.getPercentageMon() != null) {
                            int currentDays = DateUtil.getMonthByChooseDay();
                            int monthLastDay = DateUtil.getMonthLastDay(new Date());
                            double v = Double.valueOf(maintenancePlan.getPercentageMon()) / (1. * currentDays / monthLastDay);
                            if (v >= 1) {
                                v = 1;
                            }
                            Integer tmp = new Double(value * v).intValue();
                            score += tmp;
                            baseManagementScore.setServiceScore(tmp.toString());
                        } else {
                            score += 0;
                            baseManagementScore.setServiceScore(String.format("%.0f", 0.));
                        }
                        log.info("计算综合管理评分，消防维保进度评分:" + score);
                        break;
                    case "应急处置评分":
                        param = new HashMap<>();
                        param.put("buildingId", id);
                        date = DateUtils.addDay(new Date(), -30);
                        param.put("startTime", DateUtil.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
                        param.put("endTime", DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                        Double dayScore1 = fireInfoMapper.selectEmergencyToScore(param);
                        log.info("当日得分情况{}",dayScore1);
                        if (dayScore1 != null) {
                            if (dayScore1 / 100 > 1) {
                                Integer tmp = new Double(value).intValue();
                                score += tmp;
                                baseManagementScore.setDisposeScore(tmp.toString());
                            } else {
                                Integer tmp = new Double(value * dayScore1 / 100).intValue();
                                score += tmp;
                                baseManagementScore.setDisposeScore(tmp.toString());
                            }
                        } else {
//                            score += new Double(value).intValue();
                            score += 0;
                            baseManagementScore.setDisposeScore(String.format("%.0f", 0.));
                        }
                        log.info("应急处置评分:" + score);
                        break;
                    default:
                        break;
                }
            }
            if(flag){
                baseManagementScoreMapper.updateById(baseManagementScore);
            }else{
                baseManagementScoreMapper.insert(baseManagementScore);
            }
            BaseBuilding baseBuilding1 = new BaseBuilding();
            baseBuilding1.setId(id);
            baseBuilding1.setManagementScore(score.toString());
            log.info("计算综合管理评分，更新综合管理评分建筑id:" + id + "分数:" + baseBuilding1.getManagementScore());
            try {
                baseBuildingMapper.updateById(baseBuilding1);
                log.info("计算综合管理评分，，更新综合管理评分成功建筑id:" + id + "分数:" + baseBuilding1.getManagementScore());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("计算综合管理评分，更新综合管理评分失败！" + id);
            }
        }
        log.info("计算综合管理评分任务结束");
    }

    /**
     * 根据异常实际时长和报备时长 获取计算分数所用时长
     * @param realTime 实际时长
     * @param reportTime 报备时长
     * @return
     */
    private static Double transformTime(Double realTime, Double reportTime){
        if(realTime == null){
            realTime = 0.;
        }
        if(reportTime == null){
            reportTime = 0.;
        }
        BigDecimal a1 = new BigDecimal(realTime);
        BigDecimal a2 = new BigDecimal(reportTime);
        Double v = a1.subtract(a2).doubleValue();
        // 异常时间为按小时精度 两次计算相差0.02默认相同时间,将不计算分数
        if(v <= 0.02){
            v = (double) 0;
        }
        return v;
    }
}
