package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.BaseLocalHostInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BaseLocalHostInfoMapper extends BaseMapper<BaseLocalHostInfo> {

    List<BaseLocalHostInfo> selectByBuildIds(@Param("param") Map param);

    List<BaseLocalHostInfo> selectByBuildId(@Param("buildId") String buildId);

    Integer initLocalHostInfo(@Param("buildId") String buildId);

    List<BaseLocalHostInfo> selectLocalHostInfoByBuildingId(@Param("buildId") String buildingId);
}
