package com.redxun.fire.core.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.redxun.api.feign.DeviceClient;
import com.redxun.api.model.dto.EquipmentDataInfoDto;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.pojo.qo.MerchantQO;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.building.IMidMerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 中台商户信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/fire/merchant")
@Slf4j
public class MidMerchantController {

    @Resource
    IMidMerchantService midMerchantService;

    @Resource
    private DeviceClient deviceClient;

    @Resource
    private IBaseBuildingService baseBuildingService;

    /**
     * 获取商户列表
     */
    @PostMapping("/getMerchantList")
    public JsonResult getMerchantList(@RequestBody @Validated MerchantQO qo) {
        List<MerchantQO> list = midMerchantService.getMerchantList(qo);
        return JsonResult.getSuccessResult(list);
    }

    /**
     * 商户信息导出
     */
    @GetMapping("/exportMerchantByBuildingId")
    public void exportMerchantByBuildingId(HttpServletResponse response, String buildingId) throws IOException {
        midMerchantService.exportMerchantByBuildingId(buildingId,response);
    }

    @GetMapping("/getMerchantCloseDevice/{buildId}/{merchantId}")
    public JsonResult getMerchantCloseDevice(@PathVariable("buildId") String buildId,
                                             @PathVariable("merchantId") String merchantId){
        Map<String,Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("classCodeList",new String[]{"GLDYPDX"});
        stringObjectMap.put("merchantId",merchantId);
        BaseBuilding baseBuilding = baseBuildingService.getById(buildId);
        if(ObjectUtil.isNull(baseBuilding) || StrUtil.isBlank(baseBuilding.getProjectId())){
            return JsonResult.Fail("该项目不存在或者项目ID为空");
        }
        stringObjectMap.put("projectId",baseBuilding.getProjectId());
        JsonResult<List<EquipmentDataInfoDto>> equipmentAndInfoPoint = deviceClient.getEquipmentAndInfoPoint(JSONObject.parseObject(JSONObject.toJSONString(stringObjectMap)));
        if(equipmentAndInfoPoint.isSuccess()){
            List<EquipmentDataInfoDto> infoPointData = equipmentAndInfoPoint.getData();
            if(!infoPointData.isEmpty()){
                List<Map<String, String>> mapList = infoPointData.stream().map(equipmentDataInfoDto -> {
                    Map<String, String> stringStringMap = new HashMap<>();
                    stringStringMap.put("wztPointId", equipmentDataInfoDto.getId());
                    stringStringMap.put("pointDesc", equipmentDataInfoDto.getLocalName());
                    return stringStringMap;
                }).collect(Collectors.toList());
                return JsonResult.getSuccessResult(mapList);
            }
            return JsonResult.Success();

        }
        log.error("获取低电压配电箱,{}",JSONObject.toJSONString(stringObjectMap));
        return JsonResult.Fail("系统异常,请求失败");

    }

}
