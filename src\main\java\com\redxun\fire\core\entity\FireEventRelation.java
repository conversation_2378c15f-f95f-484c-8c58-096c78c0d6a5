package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.redxun.fire.config.LongToJsonSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 火警事件关联表
 */
@Data
@Accessors(chain = true)
public class FireEventRelation implements Serializable {

    @JsonSerialize(using = LongToJsonSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String eventId;

    private String buildId;

    private String buildName;

    private String eventTypeStr;

    private String pointId;

    private String pointDesc;

    private String pointCode;

    private String pointType;

    private String pointTypeStr;

    private String zoneName;

    private String type;

    @TableField(exist = false)
    private String processResultSr;

    @TableField(exist = false)
    private String receiver;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 指定时区
    @TableField(exist = false)
    private Date updateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 指定时区
    @TableField(value = "CREATE_TIME_")
    private Date createTime;

    @TableField(exist = false)
    private Integer pointNum;

}
