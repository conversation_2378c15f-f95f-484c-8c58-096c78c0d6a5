<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.ConfigRelationMapper">
    <!--功能设置所有页面查询统一使用一个接口，根据code编码配置不同获取页面各配置信息-->
    <select id="queryConfigByCode" parameterType="String" resultType="com.redxun.fire.core.entity.ConfigRelation">
		select
        id,config_code,sort,config_str_val,config_tzr_id,config_name,config_int_day,config_int_hour,config_int_min,config_int_ss,config_double_data,config_date_start,config_date_end,min_decrease_per_time, consecutive_decrease_alarm_count,low_pressure_alarm_value
		from config_relation where
		config_code like
		<choose>
            <when test="configCode !=null and configCode =='dxtz_1'">
                CONCAT('',#{configCode},'\_%')
            </when>
		    <otherwise>
                CONCAT('',#{configCode},'%')
            </otherwise>
        </choose>
		order by config_code, sort
    </select>
    <!--功能设置所有页面保存统一使用一个接口，先执行删除操作再执行批量插入操作-->
    <delete id="delConfigByCode" parameterType="list">
        DELETE
        FROM
        config_relation
        WHERE
        config_code in
        <foreach collection="configList" item="config" separator="," open="(" close=")">
            #{config.configCode}
        </foreach>
    </delete>
    <!--功能设置-误报原因 新增操作 先获取最大该code最大排序-->
    <select id="getConfigMaxSort" parameterType="String" resultType="com.redxun.fire.core.entity.ConfigRelation">
		select max(sort)+1 sort  from config_relation where
		config_code  = #{configCode} group by config_code
    </select>
    <!--功能设置-误报原因上移下移操作-->
    <update id="updateConfigSort" parameterType="com.redxun.fire.core.entity.ConfigRelation">
        update config_relation
        set sort=#{sort}
        where  config_code  = #{configCode}
        and sort = #{oldSort}
    </update>
    <!--功能设置-误报原因编辑操作-->
    <update id="updateConfigInfo" parameterType="com.redxun.fire.core.entity.ConfigRelation">
        update config_relation
        set config_str_val =#{configStrVal}
        where  config_code  = #{configCode}
        and sort = #{sort}
    </update>
    <!--功能设置 -误报原因 -删除 逻辑 -->
    <delete id="delConfig" parameterType="com.redxun.fire.core.entity.ConfigRelation">
        DELETE
        FROM
        config_relation
        WHERE
        config_code = #{configCode}
        and  sort = #{sort}
    </delete>

    <select id="selectByConfigCode" resultType="map">
        select * from config_relation where config_code = #{value} order by sort
    </select>
    <select id="getTimeByCode" resultType="java.lang.Long">
        select (config_int_hour*60+config_int_min)*60 time	from config_relation where  config_code = #{configCode}
    </select>
    <select id="queryConfigByCodeNew" resultType="com.redxun.fire.core.pojo.vo.ConfigRelationDictVo">
        select
            id,
            config_str_val,
            config_int_min,
            config_int_hour,
            config_tzr_id
        from
            config_relation
        where
            config_code like CONCAT('',#{configCode},'%')
        order by
            config_code, sort
    </select>

</mapper>
