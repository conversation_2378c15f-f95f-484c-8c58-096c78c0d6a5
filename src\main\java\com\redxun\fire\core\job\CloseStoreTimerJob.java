package com.redxun.fire.core.job;

import com.redxun.fire.core.service.alarm.IStatCloseStoreRealService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CloseStoreTimerJob extends IJobHandler {

    @Resource
    IStatCloseStoreRealService statCloseStoreRealService;

    /**
     * 每日推送闭店监测广场未设置闭店时间消息提醒
     * @Scheduled(cron = "0 0 8 * * ?")
     */
    @Override
    @XxlJob("closeStoreTimerJob")
    public void execute() throws Exception{
        log.info("closeStoreDelayQueueCreate->闭店监测广场时间提醒 开始");
        statCloseStoreRealService.closeStoreTimeRemind();
        log.info("closeStoreDelayQueueCreate->闭店监测广场时间提醒 关闭");
    }
}
