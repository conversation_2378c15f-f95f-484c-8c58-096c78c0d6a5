package com.redxun.fire.core.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.FireEventRelation;
import com.redxun.fire.core.entity.FireRealEvent;
import com.redxun.fire.core.entity.SuspectedFire;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.service.alarm.IFireRealEventService;
import com.redxun.fire.core.service.other.ITimeTaskService;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SuspectedRealFireHandler extends IJobHandler {

    @Resource
    private SuspectedFireMapper suspectedFireMapper;

    @Resource
    private BaseBuildingMapper baseBuildingMapper;
    @Resource
    private StoreDbMapper storeDbMapper;
    @Resource
    private IFireRealEventService fireRealEventService;
    @Autowired
    private FireEventRelationMapper fireEventRelationMapper;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private ITimeTaskService iTimeTaskService;
    @XxlJob("SuspectedRealFire")
    @Override
    public void execute() throws Exception {
        //开始时间
        Date now = new Date();
        log.info("疑似真实火警任务开始-开始时间：" + DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss"));
        //查询建筑表
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(new LambdaQueryWrapper<BaseBuilding>()
                .eq(BaseBuilding::getProjectType,"1").like(BaseBuilding::getBuildingName,"万达广场"));
        //查询为调试状态的建筑
        List<String> buildIds = storeDbMapper.findDubegBuild();
        List<String> finalBuildIds = baseBuildings.stream().filter(baseBuilding -> !buildIds.contains(baseBuilding.getId())).map(baseBuilding -> baseBuilding.getId()).collect(Collectors.toList());

        //查询主逻辑
        Map<String, List<SuspectedFire>> suspectedFiresMap = new HashMap<>();
        List<SuspectedFire> suspectedFires = suspectedFireMapper.selectList(new QueryWrapper<SuspectedFire>().eq("main_logic", "2"));
        if (suspectedFires != null && suspectedFires.size() > 0) {
            suspectedFiresMap = suspectedFires.stream().collect(Collectors.groupingBy(e -> e.getModeName()));
            log.info("疑似真实火警-规则数量：" + suspectedFiresMap.keySet().size());
        }
        Date endTime = new Date();

        Date startTime = null;
        if(redisUtils.exists("yisizsfire")){
            startTime = DateUtils.parseDatetime((String) redisUtils.get("yisizsfire"));
        }else{
            startTime = DateUtils.addMinute(endTime,-10);
        }

        if(DateUtil.betweenSecond(DateUtil.formatTime(startTime),DateUtil.formatTime(endTime))>60*10){
            startTime = DateUtils.addMinute(endTime,-10);
        }

        //获取所有的fire关联事件
        LambdaQueryWrapper<FireEventRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(FireEventRelation::getCreateTime,startTime,endTime);
        queryWrapper.in(FireEventRelation::getBuildId,finalBuildIds);
        List<FireEventRelation> fireEventRelations = fireEventRelationMapper.selectList(queryWrapper);
        if(fireEventRelations.isEmpty()){
            log.info("疑似真实火警查询时间，{}，{}",DateUtils.formatDatetime(startTime),DateUtils.formatDatetime(endTime));
            return ;
        }
        List<FireRealEvent> fireRealEventList = new ArrayList<>();
        //按照buildingId分组遍历，获取广场疑似真实火警事件
        Map<String, List<FireEventRelation>> stringListMap = fireEventRelations.stream().collect(Collectors.groupingBy(fireEventRelation -> fireEventRelation.getBuildId()));
        Map<String, List<SuspectedFire>> finalSuspectedFiresMap = suspectedFiresMap;
        stringListMap.keySet().forEach(s -> {
            try {
                FireRealEvent fireRealEvent = suspectedRealFireTimeTask(finalSuspectedFiresMap, baseBuildingMapper.selectById(s),stringListMap.get(s));
                if(ObjectUtil.isNotNull(fireRealEvent) && !BeanUtil.isEmpty(fireRealEvent)){
                    fireRealEventList.add(fireRealEvent);
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });

        if(!fireRealEventList.isEmpty()){
            fireRealEventService.saveOrUpdateBatch(fireRealEventList);
        }
        redisUtils.set("yisizsfire",DateUtils.formatDatetime(endTime));
    }

    //查询子逻辑
    public FireRealEvent suspectedRealFireTimeTask(Map<String,List<SuspectedFire>> listMap, BaseBuilding baseBuilding,List<FireEventRelation> fireEventRelations) throws ParseException {
        Map<String, List<FireEventRelation>> fireEventRelationGroups = fireEventRelations.stream().
                collect(Collectors.groupingBy(FireEventRelation::getEventTypeStr));
        //获取当前的前15分钟时间，判断此时此刻是否有真实火警信息，有则更新，无则添加
        AtomicReference<FireRealEvent> fireRealEvent = new AtomicReference<>(getFireRealEventByTime(baseBuilding));
        if(ObjectUtil.isNull(fireRealEvent.get())){
            fireRealEvent.set(new FireRealEvent());
            suspectedFireLogicForInsert(listMap,baseBuilding, fireRealEvent.get(),fireEventRelationGroups);
        }else{
            suspectedFireLogicForUpdate(fireEventRelationGroups,baseBuilding,fireRealEvent.get(),fireEventRelations);
        }
        return fireRealEvent.get();
    }


    public FireRealEvent getFireRealEventByTime(BaseBuilding baseBuilding){
        LambdaQueryWrapper<FireRealEvent> queryWrapper = new LambdaQueryWrapper<>();
//        Date nowBefore = DateUtils.addMinute(new Date(), -15);
        queryWrapper.eq(FireRealEvent::getBuildId,baseBuilding.getId());
        queryWrapper.ge(FireRealEvent::getLastTime,new Date());
        return fireRealEventService.getBaseMapper().selectOne(queryWrapper);
    }

    public List<FireRealEvent> getFireRealEventByBuilding(BaseBuilding baseBuilding){
        LambdaQueryWrapper<FireRealEvent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FireRealEvent::getBuildId,baseBuilding.getId());
        return fireRealEventService.getBaseMapper().selectList(queryWrapper);
    }



    /**
     * 逻辑判断
     * @param baseBuilding
     */
    public void suspectedFireLogicForInsert(Map<String,List<SuspectedFire>> suspectedFires,
                                            BaseBuilding baseBuilding,FireRealEvent realEvent
    ,Map<String, List<FireEventRelation>> listMap) {
        List<FireRealEvent> fireRealEventByBuilding = getFireRealEventByBuilding(baseBuilding);
        StringBuilder stringBuilder = new StringBuilder();
        if(!fireRealEventByBuilding.isEmpty()){
            fireRealEventByBuilding.stream().forEach(fireRealEvent -> stringBuilder.append(fireRealEvent.getFireEventId()));
        }


        StringBuilder eventIds = new StringBuilder();
        List<FireEventRelation> fireRealEventsYs = new ArrayList<>();
        List<FireEventRelation> fireRealEventsRq = new ArrayList<>();
        List<FireEventRelation> fireRealEventsZs = new ArrayList<>();

        listMap.keySet().forEach(s -> {
            if("疑似火警".equals(s)){
                checkAlarmForYs(listMap.get(s),fireRealEventsYs,suspectedFires.get(s));
            }
            if("燃气火警".equals(s)){
                checkAlarmForRq(listMap.get(s),fireRealEventsRq,suspectedFires.get(s).get(0));
            }
            if("确认火警".equals(s)){
                fireRealEventsZs.addAll(listMap.get(s));
            }

        });
        if(fireRealEventsYs.size()+fireRealEventsRq.size()+fireRealEventsZs.size()>0){
            realEvent.setBuildId(baseBuilding.getId()).setBuildName(baseBuilding.getBuildingName()).setCreateTime(new Date())
                    .setYsFire(fireRealEventsYs.size()).setCfFire(fireRealEventsZs.size())
                    .setPointNum(fireRealEventsYs.size()+fireRealEventsRq.size()+fireRealEventsZs.size());
            if(!fireRealEventsYs.isEmpty()){
                fireRealEventsYs.stream().forEach(fireEventRelation -> eventIds.append(fireEventRelation.getId()).append(","));
            }
            if(!fireRealEventsRq.isEmpty()){
                fireRealEventsRq.stream().forEach(fireEventRelation -> eventIds.append(fireEventRelation.getId()).append(","));
            }
            if(!fireRealEventsZs.isEmpty()){
                fireRealEventsZs.stream().forEach(fireEventRelation -> eventIds.append(fireEventRelation.getId()).append(","));
            }
            realEvent.setFireEventId(eventIds.toString().substring(0,eventIds.length()-1));
            realEvent.setCreateTime(new Date());
            realEvent.setLastTime(DateUtils.addMinute(new Date(),15));
            realEvent.setUpdateTime(new Date());
        }
    }


    public void suspectedFireLogicForUpdate(Map<String, List<FireEventRelation>> listMap,
                                            BaseBuilding baseBuilding,FireRealEvent realEvent,
                                            List<FireEventRelation> fireEventRelations){
        List<FireRealEvent> fireRealEventByBuilding = getFireRealEventByBuilding(baseBuilding);
        StringBuilder stringBuilder = new StringBuilder();
        if(!fireRealEventByBuilding.isEmpty()){
            fireRealEventByBuilding.stream().forEach(fireRealEvent -> stringBuilder.append(fireRealEvent.getFireEventId()));
        }
        StringBuilder eventIds = new StringBuilder();
        listMap.keySet().forEach(s -> {
            if("疑似火警".equals(s)){
                realEvent.setYsFire(realEvent.getYsFire()+listMap.get(s).size());
                realEvent.setPointNum(realEvent.getPointNum()+listMap.get(s).size());
            }
            if("燃气火警".equals(s)){
                realEvent.setPointNum(realEvent.getPointNum()+listMap.get(s).size());
            }
            if("确认火警".equals(s)){
                realEvent.setYsFire(realEvent.getCfFire()+listMap.get(s).size());
                realEvent.setPointNum(realEvent.getPointNum()+listMap.get(s).size());
            }
        });
        fireEventRelations.stream().forEach(s ->{
            eventIds.append(",").append(s.getId());
        });
        realEvent.setFireEventId(realEvent.getFireEventId()+","+eventIds);
        realEvent.setUpdateTime(new Date());
    }

    //判断燃气火警，三分钟内2个燃气信号
    public void checkAlarmForRq(List<FireEventRelation> relations,List<FireEventRelation> eventIds,SuspectedFire suspectedFire){
        if(relations.size()>=suspectedFire.getModeGtVal()){
            eventIds.addAll(relations);
        }
    }


    //疑似火警判断
    public void checkAlarmForYs(List<FireEventRelation> relations,List<FireEventRelation> eventIds,List<SuspectedFire> suspectedFireList){
       Integer i = 0;
        for (SuspectedFire suspectedFire:suspectedFireList){
            //大于值
            Double modeGtVal = suspectedFire.getModeGtVal();
            AtomicInteger yg = new AtomicInteger();
            AtomicInteger wg = new AtomicInteger();
            AtomicInteger sl = new AtomicInteger();
            AtomicInteger yk = new AtomicInteger();
            AtomicInteger sb = new AtomicInteger();
            AtomicInteger xb = new AtomicInteger();
            AtomicInteger gwdl = new AtomicInteger();
            AtomicInteger hwds = new AtomicInteger();
            AtomicInteger cfzd = new AtomicInteger();
            AtomicInteger qbxh = new AtomicInteger();
            AtomicInteger rqzj = new AtomicInteger();
            relations.stream().forEach(fireEventRelation -> {
                if (StringUtils.equals(fireEventRelation.getPointType(), "YG") || StringUtils.equals(fireEventRelation.getPointType(), "DPQ")) {
                    //烟感
                    yg.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "WG")) {
                    //温感
                    wg.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "SL")) {
                    //水流
                    sl.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "YK")) {
                    //压开
                    yk.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "SB")) {
                    //手报
                    sb.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "XHSBJ")) {
                    //消报
                    xb.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "DYQQ")) {
                    //感温电缆
                    gwdl.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "HWDS")) {
                    //红外对射
                    hwds.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "CZM")) {
                    //厨房自动灭火
                    cfzd.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "SP") || StringUtils.equals(fireEventRelation.getPointType(), "PLB") || StringUtils.equals(fireEventRelation.getPointType(), "SPHJ")) {
                    //起泵信号
                    qbxh.getAndIncrement();
                } else if (StringUtils.equals(fireEventRelation.getPointType(), "RQZJ") || StringUtils.equals(fireEventRelation.getPointType(), "ROTT") || StringUtils.equals(fireEventRelation.getPointType(), "RQMJ")) {
                    //燃气报警
                    rqzj.getAndIncrement();
                }
            });
            BigDecimal result =
                    new BigDecimal(0).add(new BigDecimal(suspectedFire.getModeSmoke()).multiply(new BigDecimal(yg.get())))//+烟感
                            .add(new BigDecimal(suspectedFire.getModeSense()).multiply(new BigDecimal(wg.get())))//+温感
                            .add(new BigDecimal(suspectedFire.getModeWater()).multiply(new BigDecimal(sl.get())))//+水流
                            .add(new BigDecimal(suspectedFire.getModePress()).multiply(new BigDecimal(yk.get())))//+压开
                            .add(new BigDecimal(suspectedFire.getModeHand()).multiply(new BigDecimal(sb.get())))//+手报
                            .add(new BigDecimal(suspectedFire.getModeDispel()).multiply(new BigDecimal(xb.get())))//+消报
                            .add(new BigDecimal(suspectedFire.getModeCable()).multiply(new BigDecimal(gwdl.get())))//+感温电缆
                            .add(new BigDecimal(suspectedFire.getModeInfrared()).multiply(new BigDecimal(hwds.get())))//+红外对射
                            .add(new BigDecimal(suspectedFire.getModeAutomatic()).multiply(new BigDecimal(cfzd.get())))//+厨房自动灭火
                            .add(new BigDecimal(suspectedFire.getModePump()).multiply(new BigDecimal(qbxh.get()))
                                    .add(new BigDecimal(suspectedFire.getModeRqzj()).multiply(new BigDecimal(rqzj.get()))
                                    ));//+起泵信号
            if (result.compareTo(new BigDecimal(modeGtVal)) > -1) {
                //作用：插入接警中心
                eventIds.addAll(relations);
                i++;
            }
        }
        if(i<suspectedFireList.size()){
            eventIds.clear();
        }
    }
}
