package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.pojo.vo.SuspectedFireVo;
import com.redxun.fire.core.service.alarm.ISuspectedFireService;
import com.redxun.fire.core.utils.validated.Delete;
import com.redxun.fire.core.utils.validated.Insert;
import com.redxun.fire.core.utils.validated.Update;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 接警中心-功能设置-疑似火警-逻辑生成表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-28
 */
@RestController
@RequestMapping("/suspected-fire")
public class SuspectedFireController {

    @Resource
    private ISuspectedFireService iSuspectedFireService;

    /**
     * 疑似火警-逻辑生成列表--查询
     *
     * @return
     */
    @RequestMapping("/querySuspectedFireList")
    public JsonResult querySuspectedFireList(@RequestParam(value = "mainLogic",required = false,defaultValue = "1") String mainLogic) {
        JsonResult<List<SuspectedFireVo>> result = JsonResult.Success();
        List<SuspectedFireVo> list = iSuspectedFireService.querySuspectedFireList(mainLogic);
        result.setData(list);
        return result;
    }

    /**
     * 疑似火警-逻辑生成列表--维保模式更新
     *
     * @param suspectedFireVo
     * @return
     */
    @RequestMapping("/updateSuspectedFireById")
    public JsonResult updateSuspectedFireById(HttpServletRequest request, @RequestBody @Validated(Update.class) SuspectedFireVo suspectedFireVo) {
        JsonResult result = JsonResult.Success();
        Boolean flag = iSuspectedFireService.updateSuspectedFireById(request, suspectedFireVo);
        result.setData(flag);
        return result;
    }

    /**
     * 疑似火警-逻辑生成列表--删除
     *
     * @param suspectedFireVo
     * @return
     */
    @RequestMapping("/delSuspectedFireById")
    public JsonResult delSuspectedFireById(HttpServletRequest request, @RequestBody @Validated(Delete.class) SuspectedFireVo suspectedFireVo) {
        JsonResult result = JsonResult.Success();
        Boolean flag = iSuspectedFireService.delSuspectedFireById(request, suspectedFireVo);
        result.setData(flag);
        return result;
    }

    /**
     * 疑似火警-逻辑生成列表--生成逻辑
     *
     * @param suspectedFireVo
     * @return
     */
    @RequestMapping("/saveSuspectedFire")
    public JsonResult saveSuspectedFire(HttpServletRequest request, @RequestBody @Validated(Insert.class) SuspectedFireVo suspectedFireVo) {
        JsonResult<String> result = JsonResult.Success();
        String id = iSuspectedFireService.saveSuspectedFire(request, suspectedFireVo);
        //suspectedFireQuartzImpl.implementationRule(id);
        result.setData(id);
        return result;
    }

    /**
     * 疑似火警-逻辑生成列表--生成且逻辑
     *
     * @param suspectedFireVo
     * @return
     */
    @RequestMapping("/saveAndSuspectedFire")
    public JsonResult saveAndSuspectedFire(HttpServletRequest request, @RequestBody @Validated(Insert.class) SuspectedFireVo suspectedFireVo) {
        JsonResult<String> result = JsonResult.Success();
        String id = iSuspectedFireService.saveAndSuspectedFire(request, suspectedFireVo);
        result.setData(id);
        return result;
    }
}
