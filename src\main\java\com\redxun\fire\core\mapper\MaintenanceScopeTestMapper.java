package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.MaintenancePlan;
import com.redxun.fire.core.entity.MaintenanceScopeTest;
import com.redxun.fire.core.pojo.dto.AppointmentApplyDto;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.pojo.dto.ScopeTestDto;
import com.redxun.fire.core.pojo.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
  * 维保计划范围表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2020-11-08
 */
@Mapper
public interface MaintenanceScopeTestMapper extends BaseMapper<MaintenanceScopeTest> {

    List<ScopeTestVo> getScopeTestByFloor(@Param("planId") String planId, @Param("deviceTypeCode") String deviceTypeCode);

    List<ScopeTestVo> getScopeTestByLoop(@Param("planId") String planId, @Param("deviceTypeCode") String deviceTypeCode);

    List<ScopeTestVo> getScopeTestByZone(@Param("planId") String planId, @Param("deviceTypeCode") String deviceTypeCode);

    List<MaintenanceScopeTest> getScopeTestIdByDevice(@Param("param") AppointmentApplyDto query);

    IPage<DevicePointInfoVo> page(Page<MaintenancePlan> page, @Param("param") Map<String, String> params);

    List<DevicePointInfoVo> getDeviceStatistics(@Param("planId")String planId, @Param("fireproofSysId")String fireproofSysId);

    List<AppointmentApplyVo> getSysFireNameAndScope(@Param("appointmentApplyId") String appointmentApplyId);

    List<AppointmentApplyVo> getSysFireNameAndScopeList(@Param("appointmentApplyIdList") List<String> appointmentApplyIdList);

    List<MaintenancePlanSysVo> getSysFireNameAndScopeByEffectiveTime(@Param("param") MaintenancePlanSysDto maintenancePlanSysDto);

    List<DevicePointInfoVo> getDevicePointInfoByScopeTestId(@Param("scopeTestId") List<String> scopeTestId);

    Set<String> getScopeTestByConfig(@Param("param") ScopeTestDto scopeTestDto);

    int getUnqualifiedTestCt(@Param("buildingId") String buildingId,@Param("effectiveTime") String effectiveTime,@Param("scheduling") String scheduling);

    List<MaintenancePlanDetailsVo> selectScopeTest(@Param("scheduling")String scheduling, @Param("buildingId") String buildingId, @Param("effectiveTime") String effectiveTime, @Param("sysId")String sysId);

    List<DevicePointInfoVo> getPoIntNumByApplyId(@Param("appointmentApplyId") String appointmentApplyId);

    List<DevicePointInfoVo> getAppointmentPoint(@Param("appointmentApplyId") String appointmentApplyId);

    List<DevicePointInfoVo> getTestDeviceInfo(@Param("appointmentApplyId") String appointmentApplyId, @Param("fireproofSysName") String fireproofSysName);

    List<DevicePointInfoVo> selectAllStatistics(@Param("id") String id, @Param("param") MaintenancePlanSysDto maintenancePlanSysDto);

    List<ScopeTestVo> getScopeTestByDeviceType(@Param("planId") String planId, @Param("deviceTypeCode") String deviceTypeCode);

    List<DevicePointInfoVo> getWebDevicePointInfoByScopeTestId(@Param("scopeTestId") List<String> scopeTestIdList, @Param("batchCode")String batchCode);

    List<String> getWebDevicePointIdByScopeTestId(@Param("scopeTestId") List<String> scopeTestIdList, @Param("batchCode")String batchCode);

    String getMaintenanceConfigByScopeTestId(@Param("scopeTestId")  String scopeTestId);

    Integer getSumPointByScopeTestId(@Param("scopeTestIdList")  List<String> scopeTestIdList);

//    List<ScopeTestVo> getScopeTestByFireSysAndPlanId(@Param("fireproofSysId")String fireproofSysId, @Param("effectiveTime")String effectiveTime, @Param("buildingId")String buildingId);

//    List<DevicePointInfoVo> selectStatisticsByPlanId(String id);

    List<MaintenanceScopeTest> getScopeTestByPlanId(@Param("planId")String planId, @Param("sysId") String sysId);

    Set<String> getScopeTestByConfigLoop(@Param("param") ScopeTestDto scopeTestDto);

    List<ScopeTestRuleVo> getMaintenanceConfigListByScopeTestId(@Param("scopeTestIds") List<String> scopeTestIds, @Param("effectiveTime")String effectiveTime);

    List<ScopeTestVo> getScopeTestByFireSysAndPlanIdAndScheduling(@Param("fireproofSysId")String fireproofSysId, @Param("effectiveTime")String effectiveTime, @Param("buildingId")String buildingId, @Param("scheduling")String scheduling);

    List<DevicePointInfoVo> getWebDevicePointInfoByScopeTestIdAndScheduling(@Param("scopeTestId")List<String> scopeTestIdList, @Param("batchCode")String batchCode, @Param("scheduling") String scheduling, @Param("effectiveTime")String effectiveTime);

    List<MaintenancePlanSysVo> getSysFireNameAndScopeByEffectiveTime2(@Param("param") MaintenancePlanSysDto maintenancePlanSysDto);

    Integer sumFinishPointByPlanId(@Param("maintenancePlanId")String maintenancePlanId);

    ScopeTestVo getSysDetailByScopeId(@Param("scopeid")String scopeid);

    ScopeTestVo getScopeTestByPlanAndSysIdAndScopeName(@Param("deviceName") String deviceName, @Param("scopeTestName")String scopeTestName, @Param("planId")String planId );

    ScopeTestVo getScopeTestByFireSysGroupByDeviceName(@Param("buildingId")String buildingId, @Param("effectiveTime")String effectiveTime, @Param("deviceName") String deviceName, @Param("scopeTestName")String scopeTestName, @Param("fireproofSysId")String fireproofSysId);

    List<String> getFinishPointByBuildingIdAndEffTime(@Param("buildingId")String buildingId, @Param("effectiveTime")String effectiveTime);

    List<MaintenanceScopeTest> selectDeviceTypeScopeTestByParam(@Param(value = "param")Map<String,Object> param);

    List<QuarterlyScope> selectDeviceTypeScopeTestQuarterlyByParam(@Param(value = "param")Map<String,Object> param);

    List<ScopeTestVo> getScopeListByPlanId(@Param("id")String id);

    int updateByIdNew(MaintenanceScopeTest maintenanceScope);
}
