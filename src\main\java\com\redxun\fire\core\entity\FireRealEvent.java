package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.redxun.fire.config.LongToJsonSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class FireRealEvent implements Serializable {

    @JsonSerialize(using = LongToJsonSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String buildId;

    private String buildName;

    private String fireEventId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastTime;

    private Integer ysFire;

    private Integer cfFire;

    private Integer pointNum;

    private String handlerStatus;

    @TableField(exist = false)
    private String devTypes;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
