package com.redxun.fire.core.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.redxun.api.feign.BpmClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.pojo.dto.WdReportingCountDto;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.MessageUtil;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @author: tangz
 * @since: 2024/4/26 09:27
 * @description: 每日8、14点定时待办提醒业务
 * @cron: 0 0 8,14 * * ?
 */
@Slf4j
@Service
public class BpmRemindJob extends IJobHandler {

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private BpmClient bpmClient;

    static String  msg = "【智慧消防】截止今日HOUR点，您有COUNT条流程待办超过1个工作日，请尽快登录惠达云手机APP处理。（如已处理请忽略）";

    @Override
    @XxlJob("BpmAutoRemindJobHandler")
    public void execute() throws Exception {
        //已调试完成
        log.info("待办消息推送任务开始");
        Date date = DateUtils.addDay(new Date(), -1);
        JsonResult jsonResult = bpmClient.countTodoTaskByAppId("1762648905649561602", DateUtils.formatDatetime(date));
        if(!jsonResult.isSuccess()){
            log.error("每日8、14点定时待办提醒业务，查询中台接口报错[{}]", jsonResult);
            return;
        }
        List<WdReportingCountDto> list = JSONArray.parseArray(JSON.toJSONString(jsonResult.getData()), WdReportingCountDto.class);
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        for (WdReportingCountDto dto : list) {
            List<String> userIdList = new ArrayList<>();
            userIdList.add(dto.getUserId());
            messageUtil.sendMessage("2", "流程待办消息提醒", msg.replace("HOUR",hour + "").replace("COUNT", dto.getBpmNum().toString()), userIdList);
        }
        log.info("待办消息推送任务结束");
    }
}
