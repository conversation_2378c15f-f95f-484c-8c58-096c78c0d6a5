package com.redxun.fire.core.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.redxun.api.feign.SystemClient;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.danger.DangerStandardConfiguration;
import com.redxun.fire.core.feign.SecurityDutyClient;
import com.redxun.fire.core.service.danger.DangerStandardConfigurationService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/major/danger")
public class MajorDangerController extends IJobHandler {

    @Autowired
    private SecurityDutyClient securityDutyClient;
    @Autowired
    private SystemClient systemClient;

    @Autowired
    private DangerStandardConfigurationService dangerStandardConfigurationService;


    /**
     * 获取履职隐患三级分类字典
     * @return
     */
    @GetMapping("/taskTmpDict")
    public JsonResult taskTmpDict(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId","1");
        jsonObject.put("dictKeys", Arrays.asList("thirdYh"));
        return securityDutyClient.taskTmpDict(jsonObject);
    }

    /**
     * 获取设备类型和异常类型
     * @param dictKey
     * @return
     */
    @GetMapping("/getXfDictByKey")
    public JsonResult getDeviceType(@RequestParam String dictKey){
        return dangerStandardConfigurationService.getDeviceType(dictKey);
    }

    /**
     * 获取隐患级别  yh_risk_level
     * 获取考核标签  yh_punish_type
     * @return
     */
    @GetMapping("/getListByKey/{key}")
    public JsonResult getListByKey(@PathVariable("key") String key){
        return systemClient.getListByKey(key);
    }

    /**
     * 保存隐患标准
     * @param dangerStandardConfiguration
     * @return
     */
    @PostMapping("/saveDanagerStandard")
    public JsonResult addDangerStandardConfiguration(@RequestBody DangerStandardConfiguration dangerStandardConfiguration){
        return dangerStandardConfigurationService.add(dangerStandardConfiguration);

    }

    /**
     * 删除deleteDangerStandard
     * @param stringStringMap
     * @return
     */
    @PostMapping("/deleteDangerStandard")
    public JsonResult deleteDangerStandard(@RequestBody Map<String,String> stringStringMap){
        List<Long> longs = Arrays.stream(stringStringMap.get("ids").split(","))
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toList());

        LambdaUpdateWrapper<DangerStandardConfiguration> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DangerStandardConfiguration::getIsActive,"0");
        updateWrapper.in(DangerStandardConfiguration::getId,longs);
        dangerStandardConfigurationService.update(updateWrapper);
        return JsonResult.getSuccessResult("删除成功");
    }


    /**
     * 定时任务生成隐患
     * @throws Exception
     */
    @Override
    @XxlJob("createMajorDangerWater")
    @GetMapping("/createMajorDangerWater")
    public void execute() throws Exception {
        dangerStandardConfigurationService.createMajorDangerWater();
    }

    /**
     * 获取隐患性情
     * @param infoId
     * @return
     */
    @GetMapping("/getDangerInfo/{infoId}")
    public JsonResult getDangerInfo(@PathVariable("infoId") Long infoId){
       return dangerStandardConfigurationService.getInfo(infoId);
    }

    /**
     * 事件中心列表查询
     * @param queryData
     * @return
     */
    @PostMapping("/findDangerEvent")
    public JsonPage findDangerEvent(@RequestBody QueryData queryData){
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageNo() < 1) {
            queryData.setPageSize(10);
        }
        return dangerStandardConfigurationService.findDangerEvent(queryData);
    }


    @PostMapping("/updateDangerInfo/{infoId}")
    public JsonResult updateDangerInfo(@PathVariable("infoId") Long infoId,@RequestBody Map<String,String> stringStringMap){
        return dangerStandardConfigurationService.updateDangerInfo(infoId,stringStringMap);
    }

    /**
     * 隐患清单列表
     * @param queryData
     * @return
     */
    @PostMapping("/findDangerInfo")
    public JsonPage findDangerInfo(@RequestBody QueryData queryData){
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageSize() < 1) {
            queryData.setPageSize(10);
        }
        return dangerStandardConfigurationService.findDangerInfo(queryData);
    }

    @PostMapping("/findStandardConfiguration")
    public JsonPage findStandardConfiguration(@RequestBody QueryData queryData){
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageNo() < 1) {
            queryData.setPageSize(10);
        }
        return dangerStandardConfigurationService.findStandardConfiguration(queryData);
    }
    /**
     * 查询隐患状态
     * @param subInfoId
     * @return
     */
    @GetMapping("/checkDangerStatus/{subInfoId}")
    public JsonResult checkDangerStatus(@PathVariable Long subInfoId){
        return dangerStandardConfigurationService.checkDangerStatus(subInfoId);
    }

    /**
     * 变更隐患状态
     * @param subInfoId
     * @return
     */
    @PostMapping("/updateDangerStatus/{lzId}")
    public JsonResult updateDangerStatus(@PathVariable Long lzId){
        return dangerStandardConfigurationService.updateDangerStatus(lzId);
    }

    @PostMapping("/pushDangerInfo")
    public JsonResult pushDangerInfo(@RequestBody Map<String, String> map, HttpServletRequest request){
        return dangerStandardConfigurationService.pushDangerInfo(map,request);
    }

    @PostMapping("/deleteDangerInfo/{infoId}")
    public JsonResult deleteDangerInfo(@PathVariable Long infoId){
        return dangerStandardConfigurationService.deleteDangerInfo(infoId);
    }
}
