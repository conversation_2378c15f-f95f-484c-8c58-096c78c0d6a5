
package com.redxun.fire.core.kitchen.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.redxun.common.base.db.BaseDao;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.service.impl.SuperServiceImpl;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceBasic;
import com.redxun.fire.core.kitchen.entity.KitchenSignA;
import com.redxun.fire.core.kitchen.entity.KitchenSignB;
import com.redxun.fire.core.kitchen.fvo.AddBasePointReqFvo;
import com.redxun.fire.core.kitchen.fvo.KitchenSignFvo;
import com.redxun.fire.core.kitchen.fvo.KitchenSupplyFvo;
import com.redxun.fire.core.kitchen.mapper.KitchenSignAMapper;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.building.IMidMerchantService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.device.impl.BaseDevicePointServiceImpl;
import com.redxun.fire.core.utils.HexUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * [厨房监测设备A注册信息表]业务服务类
 */
@Slf4j
@Service
public class KitchenSignAServiceImpl extends SuperServiceImpl<KitchenSignAMapper, KitchenSignA> implements BaseService<KitchenSignA> {

    @Resource
    private KitchenSignAMapper kitchenSignAMapper;

    @Resource
    private KitchenSignBServiceImpl kitchenSignBService;

    @Resource
    KitchenEquipStatServiceImpl kitchenEquipStatService;

    @Resource
    KitchenDeviceBasicServiceImpl kitchenDeviceBasicService;


    @Resource
    IBaseBuildingService baseBuildingService;

    @Resource
    IBaseBuildingFloorService baseBuildingFloorService;

    @Resource
    BaseDevicePointServiceImpl baseDevicePointService;


    @Resource
    IMidMerchantService midMerchantService;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public BaseDao<KitchenSignA> getRepository() {
        return kitchenSignAMapper;
    }

    /**
     * 注册新增A版设备信息
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult addBasePointInfo(AddBasePointReqFvo param) throws Exception {
        final val origin = param.getOrigin();
        if ("1".equals(origin)) {
            String projectId = param.getProjectId();
            if (StrUtil.isNotBlank(projectId)) {
                final val one = baseBuildingService.getOne(new LambdaQueryWrapper<BaseBuilding>()
                        .eq(BaseBuilding::getProjectId, projectId));
                if (ObjectUtil.isNotEmpty(one)) {
                    param.setBuildingId(one.getId());
                    param.setBuildingName(one.getBuildingName());
                }
            }
            final val wztFloorId = param.getWztFloorId();
            if (StrUtil.isNotBlank(wztFloorId)) {
                final val one = baseBuildingFloorService.getOne(new LambdaQueryWrapper<BaseBuildingFloor>()
                        .eq(BaseBuildingFloor::getWztFloorId, wztFloorId));
                if (ObjectUtil.isNotEmpty(one)) {
                    param.setFloorId(one.getId());
                    param.setFloor(one.getFloor() + "");
                    param.setFloorName(one.getFloorName());
                }
            }
            final val wztMerchantId = param.getWztMerchantId();
            if (StrUtil.isNotBlank(wztMerchantId)) {
                final val one = midMerchantService.getById(wztMerchantId);
                if (ObjectUtil.isNotEmpty(one)) {
                    param.setMerchantCode(one.getMerchantCode());
                    param.setMerchantId(one.getId());
                    param.setMerchantName(one.getMerchantName());
                    param.setMerchantTypeCode(one.getTypeCode());
                    param.setMerchantTypeName(one.getTypeName());
                }
            }
        }

        LambdaQueryWrapper<KitchenSignA> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenSignA::getHostId, param.getHostId());
        queryWrapper.or(qw -> qw.eq(KitchenSignA::getMerchantId, param.getMerchantId()));
//        queryWrapper.eq(KitchenSignA::getDeleted, "0");
        queryWrapper.last("LIMIT 1");
        KitchenSignA dto = this.getOne(queryWrapper);
        String id = "";
        if (StringUtils.isBlank(param.getId())) {
            if (dto != null) {
                return JsonResult.getFailResult("已存在相同的监测模块ID");
            }
            id = IdGenerator.getIdStr();
        } else {
            id = dto.getId();
        }
        KitchenSignA entity = new KitchenSignA();
        entity.setId(id);
        entity.setHostId(param.getHostId());
        entity.setBuildingId(param.getBuildingId());
        entity.setBuildingName(param.getBuildingName());
        entity.setWztBuilding(param.getWztBuilding());
        entity.setWztBuildingName(param.getWztBuildingName());
        entity.setFloorId(param.getFloorId());
        entity.setFloor(param.getFloor());
        entity.setFloorName(param.getFloorName());
        entity.setMerchantId(param.getMerchantId());
        entity.setMerchantCode(param.getMerchantCode());
        entity.setMerchantName(param.getMerchantName());
        entity.setMerchantTypeCode(param.getMerchantTypeCode());
        entity.setMerchantTypeName(param.getMerchantTypeName());
        if (StringUtils.isNotBlank(param.getExpiredTime())) {
            entity.setExpiredTime(sdf.parse(param.getExpiredTime()));
        }
        entity.setEquipAPosition(param.getEquipAPosition());
        entity.setCzmDesc(param.getCzmDesc());
        entity.setDhlrDesc(param.getDhlrDesc());
        entity.setRqbjDesc(param.getRqbjDesc());
        entity.setDhJoinNameOne(param.getDhJoinNameOne());
        entity.setDhJoinNameTwo(param.getDhJoinNameTwo());
        entity.setDhJoinNameThree(param.getDhJoinNameThree());
        entity.setLrJoinNameOne(param.getLrJoinNameOne());
        entity.setLrJoinNameTwo(param.getLrJoinNameTwo());
        entity.setLrJoinNameThree(param.getLrJoinNameThree());
        entity.setChannelJoinStatusOne(param.getChannelJoinStatusOne());
        entity.setChannelJoinStatusTwo(param.getChannelJoinStatusTwo());
        entity.setChannelJoinStatusThree(param.getChannelJoinStatusThree());
        entity.setDeleted("0");
        entity.setCreateTime(new Date());
        entity.setCreateBy(param.getUserId());
        entity.setCreateByName(param.getUserName());
        entity.setFromMark(param.getFromMark());
        entity.setStatus("1");
        this.saveOrUpdate(entity);
        
        //保存B通道数据
        kitchenSignBService.addPointBInfo(id, param.getList());

        //保存基础点位信息
//        try {
        kitchenDeviceBasicService.generateKitchenPoint(entity);
//        } catch (Exception e) {
//            log.error("[KitchenSignAServiceImpl][调用generateKitchenPoint方法失败！]失败原因{}, 入参kitchenSignA:{}", e.getMessage(), entity);
//            throw new RuntimeException("[KitchenSignAServiceImpl][调用generateKitchenPoint方法失败], 失败原因" + e.getMessage());
//        }

        // 厨房监测统计表中设备数量、监测接入商户数量+1
        kitchenEquipStatService.addPointCount(param.getBuildingId());
        JSONObject object = new JSONObject();
        object.put("msg", "新增基础信息成功");
        object.put("id", id);
        return JsonResult.getSuccessResult(object);
    }

    /**
     * 查看
     *
     * @param param
     * @return
     */
    public JsonResult getPointDetail(JSONObject param) {
        KitchenSignFvo res = new KitchenSignFvo();
        String hostId = param.getString("hostId");
        String buildingId = param.getString("buildingId");
        LambdaQueryWrapper<KitchenSignA> qw = new LambdaQueryWrapper<>();
        qw.eq(KitchenSignA::getBuildingId, buildingId);
        qw.eq(KitchenSignA::getHostId, hostId);
        qw.eq(KitchenSignA::getDeleted, "0");
        qw.last("LIMIT 1");
        KitchenSignA dto = this.getOne(qw);
        if (dto == null) {
            return JsonResult.getSuccessResult(res);
        }
        List<KitchenSignB> listB = kitchenSignBService.getKitchenSignBList(dto.getId());
        res.setId(dto.getId());
        res.setHostId(dto.getHostId());
        res.setBuildingId(dto.getBuildingId());
        res.setBuildingName(dto.getBuildingName());
        res.setWztBuilding(dto.getWztBuilding());
        res.setWztBuildingName(dto.getWztBuildingName());
        res.setFloorId(dto.getFloorId());
        res.setFloor(dto.getFloor());
        res.setFloorName(dto.getFloorName());
        res.setMerchantId(dto.getMerchantId());
        res.setMerchantCode(dto.getMerchantCode());
        res.setMerchantName(dto.getMerchantName());
        res.setMerchantTypeCode(dto.getMerchantTypeCode());
        res.setMerchantTypeName(dto.getMerchantTypeName());
        res.setExpiredTime(dto.getExpiredTime() != null ? sdf.format(dto.getExpiredTime()) : null);
        res.setEquipAPosition(dto.getEquipAPosition());
        res.setCzmDesc(dto.getCzmDesc());
        res.setDhlrDesc(dto.getDhlrDesc());
        res.setRqbjDesc(dto.getRqbjDesc());
        res.setDhJoinNameOne(dto.getDhJoinNameOne());
        res.setDhJoinNameTwo(dto.getDhJoinNameTwo());
        res.setDhJoinNameThree(dto.getDhJoinNameThree());
        res.setLrJoinNameOne(dto.getLrJoinNameOne());
        res.setLrJoinNameTwo(dto.getLrJoinNameTwo());
        res.setLrJoinNameThree(dto.getLrJoinNameThree());
        res.setChannelJoinStatusOne(dto.getChannelJoinStatusOne());
        res.setChannelJoinStatusTwo(dto.getChannelJoinStatusTwo());
        res.setChannelJoinStatusThree(dto.getChannelJoinStatusThree());
        res.setStatus(dto.getStatus());
        res.setAudibleVisualAlarm(dto.getAudibleVisualAlarm());
        res.setDelayedAlarm(dto.getDelayedAlarm());
        res.setKitchenSignBList(listB);
        final val list = kitchenDeviceBasicService.list(new LambdaQueryWrapper<KitchenDeviceBasic>().eq(KitchenDeviceBasic::getHostId, dto.getHostId()));
        res.setKitchenDeviceBasicDataList(list);
        return JsonResult.getSuccessResult(res);
    }

    /**
     * 补充内容
     *
     * @param paramList
     * @return
     */
    public JsonResult supplyPoint(List<KitchenSupplyFvo> paramList) throws ParseException {
        if(CollectionUtils.isNotEmpty(paramList)) {
            for (KitchenSupplyFvo param : paramList) {
                String type = param.getType();
                if(StringUtils.isBlank(param.getId())) {
                    return JsonResult.getFailResult("id不能为空");
                }

                if(StringUtils.isBlank(type)) {
                    return JsonResult.getFailResult("type不能为空");
                }

                if (StringUtils.isNotBlank(type) && "A".equals(type)) {
                    //校验商户是否存在
                    if(StringUtils.isNotBlank(param.getMerchantId())) {
                        KitchenSignA checkMerchantSignA = getMerchantInfo(param);
                        if(checkMerchantSignA != null) {
                            if(!param.getId().equals(checkMerchantSignA.getId())) {
                                return JsonResult.getFailResult("修改的商户已经绑定其他A版模块");
                            }
                        }
                    }
                    final val byId = this.getById(param.getId());
                    if (ObjectUtil.isNotEmpty(byId)) {
                        String addressHex = HexUtil.intToHexString(Integer.parseInt(byId.getHostId()),4);
                        List<String> pidList = Arrays.asList("CF" + addressHex + "_" + "3001" + "_" + "1",
                                "CF" + addressHex + "_" + "3002" + "_" + "1","CF" + addressHex + "_" + "3003" + "_" + "1");
                        final val list = baseDevicePointService.list(
                                new LambdaQueryWrapper<BaseDevicePoint>().in(BaseDevicePoint::getPid, pidList));
                        if (CollUtil.isNotEmpty(list)) {
                            for (BaseDevicePoint point : list) {
                                BaseDevicePoint update = new BaseDevicePoint();
                                final val pointDesc = point.getPointDesc();
                                final val split = pointDesc.split("-");
                                split[3] = param.getEquipAPosition();
                                update.setId(point.getId());
                                update.setPointDesc(String.join("-", split));
                                baseDevicePointService.updateById(update);
                            }
                        }
                    }
                }
                if (StringUtils.isNotBlank(type) && "B".equals(type)) {
                    final val byId = kitchenSignBService.getById(param.getId());
                    if (ObjectUtil.isNotEmpty(byId)) {
                        String addressHex = HexUtil.intToHexString(Integer.parseInt(byId.getHostId()),4);
                        List<String> pidList = Arrays.asList("CF" + addressHex + "_" + "3001" + "_" + byId.getHostType(),
                                "CF" + addressHex + "_" + "3002" + "_" + byId.getHostType(),"CF" + addressHex + "_" + "3003" + "_" + byId.getHostType());
                        final val list = baseDevicePointService.list(
                                new LambdaQueryWrapper<BaseDevicePoint>().in(BaseDevicePoint::getPid, pidList));
                        if (CollUtil.isNotEmpty(list)) {
                            for (BaseDevicePoint point : list) {
                                BaseDevicePoint update = new BaseDevicePoint();
                                final val pointDesc = point.getPointDesc();
                                final val split = pointDesc.split("-");
                                split[3] = param.getEquipAPosition();
                                update.setId(point.getId());
                                update.setPointDesc(String.join("-", split));
                                baseDevicePointService.updateById(update);
                            }
                        }
                    }
                    kitchenSignBService.updatePointB(param);
                }
            }
        }
        return JsonResult.getSuccessResult("编辑成功");
    }

    private KitchenSignA getMerchantInfo(KitchenSupplyFvo param) {
        LambdaQueryWrapper<KitchenSignA> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenSignA::getMerchantId, param.getMerchantId());
        queryWrapper.eq(KitchenSignA::getDeleted, "0");
        queryWrapper.last("LIMIT 1");
        return  this.getOne(queryWrapper);
    }

    private void updatePointA(KitchenSupplyFvo param) throws ParseException {
        LambdaUpdateWrapper<KitchenSignA> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KitchenSignA::getId, param.getId());
        String buildingId = param.getBuildingId();
        if (StringUtils.isNotBlank(buildingId)) {
            updateWrapper.set(KitchenSignA::getBuildingId, buildingId);
        }
        String buildingName = param.getBuildingName();
        if (StringUtils.isNotBlank(buildingName)) {
            updateWrapper.set(KitchenSignA::getBuildingName, buildingName);
        }
        String floorId = param.getFloorId();
        if (StringUtils.isNotBlank(floorId)) {
            updateWrapper.set(KitchenSignA::getFloorId, floorId);
        }
        String floor = param.getFloor();
        if (StringUtils.isNotBlank(floor)) {
            updateWrapper.set(KitchenSignA::getFloor, floor);
        }
        String floorName = param.getFloorName();
        if (StringUtils.isNotBlank(floorName)) {
            updateWrapper.set(KitchenSignA::getFloorName, floorName);
        }
        String merchantId = param.getMerchantId();
        if (StringUtils.isNotBlank(merchantId)) {
            updateWrapper.set(KitchenSignA::getMerchantId, merchantId);
        }
        String merchantCode = param.getMerchantCode();
        if (StringUtils.isNotBlank(merchantCode)) {
            updateWrapper.set(KitchenSignA::getMerchantCode, merchantCode);
        }
        String merchantName = param.getMerchantName();
        if (StringUtils.isNotBlank(merchantName)) {
            updateWrapper.set(KitchenSignA::getMerchantName, merchantName);
        }
        String merchantTypeCode = param.getMerchantCode();
        if (StringUtils.isNotBlank(merchantTypeCode)) {
            updateWrapper.set(KitchenSignA::getMerchantTypeCode, merchantTypeCode);
        }
        String merchantTypeName = param.getMerchantTypeName();
        if (StringUtils.isNotBlank(merchantTypeName)) {
            updateWrapper.set(KitchenSignA::getMerchantTypeName, merchantTypeName);
        }
        String expiredTime = param.getExpiredTime();
        if (StringUtils.isNotBlank(expiredTime)) {
            Date expiredDate = sdf.parse(expiredTime);
            updateWrapper.set(KitchenSignA::getExpiredTime, expiredDate);
        }
        String equipAPosition = param.getEquipAPosition();
        if (StringUtils.isNotBlank(equipAPosition)) {
            updateWrapper.set(KitchenSignA::getEquipAPosition, equipAPosition);
        }
        String dhJoinNameOne = param.getDhJoinNameOne();
        if (StringUtils.isNotBlank(dhJoinNameOne)) {
            updateWrapper.set(KitchenSignA::getDhJoinNameOne, dhJoinNameOne);
        }
        String dhJoinNameTwo = param.getDhJoinNameTwo();
        if (StringUtils.isNotBlank(dhJoinNameTwo)) {
            updateWrapper.set(KitchenSignA::getDhJoinNameTwo, dhJoinNameTwo);
        }
        String dhJoinNameThree = param.getDhJoinNameThree();
        if (StringUtils.isNotBlank(dhJoinNameThree)) {
            updateWrapper.set(KitchenSignA::getDhJoinNameThree, dhJoinNameThree);
        }
        String lrJoinNameOne = param.getLrJoinNameOne();
        if (StringUtils.isNotBlank(lrJoinNameOne)) {
            updateWrapper.set(KitchenSignA::getLrJoinNameOne, lrJoinNameOne);
        }
        String lrJoinNameTwo = param.getLrJoinNameTwo();
        if (StringUtils.isNotBlank(lrJoinNameTwo)) {
            updateWrapper.set(KitchenSignA::getLrJoinNameTwo, lrJoinNameTwo);
        }
        String lrJoinNameThree = param.getLrJoinNameThree();
        if (StringUtils.isNotBlank(lrJoinNameThree)) {
            updateWrapper.set(KitchenSignA::getLrJoinNameThree, lrJoinNameThree);
        }
        Integer audibleVisualAlarm = param.getAudibleVisualAlarm();
        if (audibleVisualAlarm != null) {
            updateWrapper.set(KitchenSignA::getAudibleVisualAlarm, audibleVisualAlarm);
        }
        Integer delayedAlarm = param.getDelayedAlarm();
        if (delayedAlarm != null) {
            updateWrapper.set(KitchenSignA::getDelayedAlarm, delayedAlarm);
        }
        this.update(updateWrapper);
    }

    /**
     * 删除注册A版表数据
     * @param hostId
     */
    public void delInfo(String hostId) {
        LambdaUpdateWrapper<KitchenSignA> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KitchenSignA::getHostId, hostId);
        updateWrapper.set(KitchenSignA::getDeleted, "1");
        this.update(updateWrapper);
    }
}
