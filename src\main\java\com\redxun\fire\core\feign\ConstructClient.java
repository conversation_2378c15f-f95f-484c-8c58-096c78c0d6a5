package com.redxun.fire.core.feign;

import com.redxun.api.model.dto.ExternalServiceProjectDto;
import com.redxun.api.model.param.FireQueryBuildFloorParam;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.api.FwUser;
import com.redxun.fire.core.entity.api.Merchant;
import com.redxun.fire.core.pojo.qo.CloseStoreTimerQO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 项目构建feignClient
 * <AUTHOR>
 */
@FeignClient(name = "jpaas-construction")
public interface ConstructClient {
    @PostMapping({"/construction/core/nanWanUser/save"})
    JsonResult createFwUser(@RequestBody FwUser fwUser);

    @PostMapping({"/construction/core/nanWanUser/save"})
    JsonResult updateFwUser(@RequestBody FwUser fwUser);

    @PostMapping({"/construction/core/nanWanUser/saveBatch"})
    JsonResult saveBatchFwUser(@RequestBody List<FwUser> fwUserList, @RequestParam("tenantId") String TenantId);

    @PostMapping({"/construction/core/nanWanUser/updateBatch"})
    JsonResult updateBatchFwUser(@RequestBody List<FwUser> fwUserList);

//    @PostMapping({"/construction/core/nanWanUser/del"})
//    JsonResult deleteFwUser(@RequestParam("ids") String ids);

    @PostMapping({"/construction/core/nanWanUser/queryByIds"})
    JsonResult queryFwUserByIds(@RequestBody List<String> ids);

    @GetMapping({"/construction/core/nanWanUser/get"})
    JsonResult getFwUser(@RequestParam("pkId") String id);

    @PostMapping({"/construction/core/merchant/save"})
    JsonResult createMerchant(@RequestBody Merchant merchant);

    @PostMapping({"/construction/core/merchant/save"})
    JsonResult updateMerchant(@RequestBody Merchant merchant);

//    @PostMapping({"/construction/core/merchant/del"})
//    JsonResult deleteMerchant(@RequestParam("ids") String ids);

    @PostMapping({"/construction/core/merchant/deleteBatch"})
    JsonResult deleteBatchMerchant(@RequestBody List<Merchant> merchants);

    @GetMapping({"/construction/core/merchant/get"})
    JsonResult getMerchant(@RequestParam("pkId") String id);

    @PostMapping({"/construction/wproject/wProject/saveBatchExternalServiceProject"})
    JsonResult<?> saveBatchExternalServiceProject(@RequestBody List<ExternalServiceProjectDto> externalServiceProjectDtoList);

    @PostMapping({"/construction/wproject/wProject/saveOrUpdateExternalServiceProject"})
    JsonResult<?> saveOrUpdateExternalServiceProject(@RequestBody ExternalServiceProjectDto projectDto);
    @PostMapping({"/construction/core/storeHoursSetting/getStoreHoursSettingByOrgId"})
    JsonResult getStoreHoursSettingByOrgId(@RequestBody CloseStoreTimerQO qo);

}