package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.redxun.common.base.entity.BaseExtEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


@Setter
@Getter
@Accessors(chain = true)
@TableName(value = "stat_pump_real")
public class StatPumpReal  extends BaseExtEntity<java.lang.String> {

    @JsonCreator
    public StatPumpReal() {
    }

    //主键
    @TableId(value = "id",type = IdType.INPUT)
    private String id;

    //区域编号
    @TableField(value = "area_code")
    private String areaCode;
    //区域名称
    @TableField(value = "area_name")
    private String areaName;
    //建筑物id
    @TableField(value = "build_id")
    private String buildId;
    //build_regional
    @TableField(value = "build_regional")
    private String buildRegional;
    //运营中心编号
    @TableField(value = "center_code")
    private String centerCode;
    //运营中心名称
    @TableField(value = "center_name")
    private String centerName;
    //故障水泵数
    @TableField(value = "fault_pumps")
    private Integer faultPumps;
    //手动水泵数
    @TableField(value = "hands_pumps")
    private Integer handsPumps;
    //正常水泵数
    @TableField(value = "normal_pumps")
    private Integer normalPumps;
    //离线水泵数
    @TableField(value = "off_pumps")
    private Integer offPumps;
    //断电水泵数
    @TableField(value = "outage_pumps")
    private Integer outagePumps;
    //水泵数量
    @TableField(value = "pump_count")
    private Integer pumpCount;
    //regional_id
    @TableField(value = "regional_id")
    private String regionalId;
    //运行水泵数
    @TableField(value = "run_pumps")
    private Integer runPumps;
    //广场编号
    @TableField(value = "square_code")
    private String squareCode;
    //广场名称
    @TableField(value = "square_name")
    private String squareName;

    @Override
    public String getPkId() {
        return id;
    }

    @Override
    public void setPkId(String pkId) {
        this.id=pkId;
    }

}
