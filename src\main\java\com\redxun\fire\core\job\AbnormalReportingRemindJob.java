package com.redxun.fire.core.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.redxun.api.feign.BpmClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.pojo.dto.WdReportingCountDto;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.MessageUtil;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 异常报备待办流程定时提醒任务
 * 每十分钟运行一次
 * cron 0 0/10 * * * ?
 */
@Slf4j
@Service
public class AbnormalReportingRemindJob extends IJobHandler {

    static String msg = "【智慧消防】你新增{}请及时登录惠达云手机端APP处理。（如已处理请忽略）";

    @Autowired
    private BpmClient bpmClient;

    @Autowired
    private MessageUtil messageUtil;

    @Override
    @XxlJob("AbnormalReportingRemindJob")
    public void execute() throws Exception {
        //已调试完成
        Date endTime = new Date();
        Date startTime = DateUtils.addMinute(endTime, -10);
        JsonResult jsonResult = bpmClient.getUserTaskNumberByTreeId("1775339963661766658", DateUtils.formatDatetime(startTime), DateUtils.formatDatetime(endTime));
        if(!jsonResult.isSuccess()){
            log.error("异常报备待办流程定时提醒任务，查询中台接口报错[{}]", jsonResult);
            return;
        }
        List<WdReportingCountDto> list = JSONArray.parseArray(JSON.toJSONString(jsonResult.getData()), WdReportingCountDto.class);
        if(CollectionUtils.isNotEmpty(list)){
            //根据用户id分组
            Map<String, List<WdReportingCountDto>> map = list.stream().collect(Collectors.groupingBy(WdReportingCountDto::getUserId));
            //遍历所有需要发送信息的用户
            Iterator<Map.Entry<String, List<WdReportingCountDto>>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()){
                Map.Entry<String, List<WdReportingCountDto>> next = iterator.next();
                //拼装消息体
                StringBuilder stringBuilder = new StringBuilder();
                List<WdReportingCountDto> dtoList = next.getValue();
                for (WdReportingCountDto wdReportingCountDto : dtoList) {
                    stringBuilder.append(wdReportingCountDto.getBpmNum() + "条" + wdReportingCountDto.getDefName() + "待办，");
                }
                if(StringUtils.isNotEmpty(stringBuilder)){
                    List<String> userIdList = new ArrayList<>();
                    userIdList.add(next.getKey());
                    messageUtil.sendMessage("2", "异常待办消息提醒", StringUtils.replace(msg, "{}", stringBuilder.toString()), userIdList);
                }
            }
        }
    }
}
