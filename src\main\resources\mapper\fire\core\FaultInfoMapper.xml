<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.FaultInfoMapper">

    <resultMap id="FaultInfo" type="com.redxun.fire.core.entity.FaultInfo">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="applyStatus" column="apply_status" jdbcType="NUMERIC"/>
        <result property="belongDep" column="belong_dep" jdbcType="VARCHAR"/>
        <result property="buildingId" column="building_id" jdbcType="VARCHAR"/>
        <result property="buildingName" column="building_name" jdbcType="VARCHAR"/>
        <result property="buildingStatus" column="building_status" jdbcType="VARCHAR"/>
        <result property="buildingStatusStr" column="building_status_str" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY_" jdbcType="VARCHAR"/>
        <result property="createDepId" column="CREATE_DEP_ID_" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME_" jdbcType="TIMESTAMP"/>
        <result property="devId" column="dev_id" jdbcType="VARCHAR"/>
        <result property="devNum" column="dev_num" jdbcType="VARCHAR"/>
        <result property="devStatusCode" column="dev_status_code" jdbcType="VARCHAR"/>
        <result property="devStatusName" column="dev_status_name" jdbcType="VARCHAR"/>
        <result property="devTypeCode" column="dev_type_code" jdbcType="VARCHAR"/>
        <result property="devTypeName" column="dev_type_name" jdbcType="VARCHAR"/>
        <result property="emergencyStatus" column="emergency_status" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
        <result property="fasCode" column="fas_code" jdbcType="VARCHAR"/>
        <result property="fasName" column="fas_name" jdbcType="VARCHAR"/>
        <result property="faultSource" column="fault_source" jdbcType="VARCHAR"/>
        <result property="faultStatus" column="fault_status" jdbcType="VARCHAR"/>
        <result property="faultStatusStr" column="fault_status_str" jdbcType="VARCHAR"/>
        <result property="faultType" column="fault_type" jdbcType="VARCHAR"/>
        <result property="flowStatus" column="flow_status" jdbcType="VARCHAR"/>
        <result property="handleStatus" column="handle_status" jdbcType="VARCHAR"/>
        <result property="handleTime" column="handle_time" jdbcType="VARCHAR"/>
        <result property="handleUser" column="handle_user" jdbcType="VARCHAR"/>
        <result property="handleUserId" column="handle_user_id" jdbcType="VARCHAR"/>
        <result property="hostId" column="host_id" jdbcType="VARCHAR"/>
        <result property="isLoopFault" column="is_loop_fault" jdbcType="VARCHAR"/>
        <result property="isManualReport" column="is_manual_report" jdbcType="VARCHAR"/>
        <result property="isOverLook" column="is_over_look" jdbcType="VARCHAR"/>
        <result property="isPick" column="is_pick" jdbcType="VARCHAR"/>
        <result property="lastTime" column="last_time" jdbcType="VARCHAR"/>
        <result property="locationDescribe" column="location_describe" jdbcType="VARCHAR"/>
        <result property="loopCode" column="loop_code" jdbcType="VARCHAR"/>
        <result property="loopFaultId" column="loop_fault_id" jdbcType="VARCHAR"/>
        <result property="pid" column="pid" jdbcType="VARCHAR"/>
        <result property="pointCode" column="point_code" jdbcType="VARCHAR"/>
        <result property="pointDesc" column="point_desc" jdbcType="VARCHAR"/>
        <result property="pointId" column="point_id" jdbcType="VARCHAR"/>
        <result property="pointType" column="point_type" jdbcType="VARCHAR"/>
        <result property="repairEndTime" column="repair_end_time" jdbcType="VARCHAR"/>
        <result property="repairStartTime" column="repair_start_time" jdbcType="VARCHAR"/>
        <result property="repairVerify" column="repair_verify" jdbcType="VARCHAR"/>
        <result property="repairman" column="repairman" jdbcType="VARCHAR"/>
        <result property="repairmanId" column="repairman_id" jdbcType="VARCHAR"/>
        <result property="reportDesc" column="report_desc" jdbcType="VARCHAR"/>
        <result property="reportUser" column="report_user" jdbcType="VARCHAR"/>
        <result property="reportUserId" column="report_user_id" jdbcType="VARCHAR"/>
        <result property="reportedTime" column="reported_time" jdbcType="TIMESTAMP"/>
        <result property="sendTime" column="send_time" jdbcType="VARCHAR"/>
        <result property="sendUser" column="send_user" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR"/>
        <result property="updateBy" column="UPDATE_BY_" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME_" jdbcType="TIMESTAMP"/>
        <result property="zoneId" column="zone_id" jdbcType="VARCHAR"/>
        <result property="zoneName" column="zone_name" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="query" resultType="com.redxun.fire.core.entity.FaultInfo" parameterType="java.util.Map">
        select apply_status,belong_dep,building_id,building_name,building_status,building_status_str,comment,CREATE_BY_,CREATE_DEP_ID_,CREATE_TIME_,dev_id,dev_num,dev_status_code,dev_status_name,dev_type_code,dev_type_name,emergency_status,end_time,fas_code,fas_name,fault_source,fault_status,fault_status_str,fault_type,flow_status,handle_status,handle_time,handle_user,handle_user_id,host_id,id,is_loop_fault,is_maintenance_fault,is_manual_report,is_over_look,is_pick,last_time,location_describe,loop_code,loop_fault_id,pid,point_code,point_desc,point_id,point_type,repair_end_time,repair_start_time,repair_verify,repairman,repairman_id,report_desc,report_user,report_user_id,reported_time,send_time,send_user,start_time,TENANT_ID_,UPDATE_BY_,UPDATE_TIME_,zone_id,zone_name from fault_info
        <where>
            <if test="@rx.Ognl@isNotEmpty(w.whereSql)">
                ${w.whereSql}
            </if>
        </where>
        <if test="@rx.Ognl@isNotEmpty(w.orderBySql)">
            ORDER BY ${w.orderBySql}
        </if>
        <if test="@rx.Ognl@isEmpty(w.orderBySql)">
            ORDER BY  id DESC
        </if>
    </select>

    <!-- 根据建筑id 点位id  首报时间和最后上报时间 统计火警信息 -->
    <select id="countFireInfo" resultType="int" parameterType="com.redxun.fire.core.entity.FaultInfo">
        select count(1) from fire_info
        where
            building_id = #{buildingId}
          and
            point_code = #{pointCode}
          and
            last_time <![CDATA[ >= ]]> STR_TO_DATE(#{reportedTimeStr},'%Y-%m-%d %H:%i:%s')
          and
            last_time <![CDATA[ <= ]]> STR_TO_DATE(now(),'%Y-%m-%d %H:%i:%s')

    </select>


    <!--根据建筑id 点位id  首报时间和最后上报时间 统计联动信息 -->
    <select id="countLinkage" resultType="int" parameterType="com.redxun.fire.core.entity.FaultInfo">
        select count(1) from base_device_linkage
        where
            building_id = #{buildingId}
          and
            point_code = #{pointCode}
          and
                reported_time <![CDATA[>=]]> STR_TO_DATE(#{reportedTimeStr},'%Y-%m-%d %H:%i:%s')
          and
            reported_time <![CDATA[<=]]>STR_TO_DATE(now(),'%Y-%m-%d %H:%i:%s')

    </select>
    <!--根据建筑id 点位id  首报时间和最后上报时间 统计监管信息 -->
    <select id="countSupervise" resultType="int" parameterType="com.redxun.fire.core.entity.FaultInfo">
        select count(1) from base_dev_supervise
        where
            build_id = #{buildingId}
          and
            point_code = #{pointCode}
          and
                reported_time <![CDATA[>=]]>STR_TO_DATE(#{reportedTimeStr},'%Y-%m-%d %H:%i:%s')
          and
            reported_time <![CDATA[<=]]>STR_TO_DATE(now(),'%Y-%m-%d %H:%i:%s')

    </select>

    <!--根据建筑id 点位id  首报时间和最后上报时间 统计预警信息 -->
    <select id="countEarlywarning" resultType="int" parameterType="com.redxun.fire.core.entity.FaultInfo">
        select count(1) from earlywarning
        where
            building_id = #{buildingId}
          and
            point_code = #{pointCode}
          and
            report_time <![CDATA[>=]]> STR_TO_DATE(#{reportedTimeStr},'%Y-%m-%d %H:%i:%s')
          and
            report_time <![CDATA[<=]]>STR_TO_DATE(now(),'%Y-%m-%d %H:%i:%s')

    </select>
    <!--根据建筑id 点位id  首报时间和最后上报时间 统计水异常信息 -->
    <select id="countWater" resultType="int" parameterType="com.redxun.fire.core.entity.FaultInfo">
        select count(1) from water_abnormal
        where
            building_id = #{buildingId}
          and
            point_code = #{pointCode}
          and
                reported_time <![CDATA[>=]]> STR_TO_DATE(#{reportedTimeStr},'%Y-%m-%d %H:%i:%s')
          and
            reported_time <![CDATA[<=]]> STR_TO_DATE(now(),'%Y-%m-%d %H:%i:%s')

    </select>

    <!--根据设备类型 查询报警类型 -->
    <select id="selectDevType" parameterType="string" resultType="string">
        select police_type FROM wd_dev_config
        where dev_code = #{devTypeCode}
    </select>

    <!--根据设备类型 修改点位信息-->
    <update id="updatePoint" parameterType="com.redxun.fire.core.entity.FaultInfo">
        update base_device_point
        set point_status = '0'
        where id = #{pointId}
          and reported_time = STR_TO_DATE(#{reportedTime},'%Y-%m-%d %H:%i:%s')
          and dev_type_code = #{devTypeCode}
    </update>

    <!--根据设备类型修改故障信息状态-->
    <update id="updateFault" parameterType="com.redxun.fire.core.entity.FaultInfo">
        update fault_info
        set fault_status = '0'
        where
            point_id = #{pointId}
          and
            reported_time = STR_TO_DATE(#{reportedTime},'%Y-%m-%d %H:%i:%s')
          and
            dev_type_code = #{devTypeCode}
    </update>

    <select id="getFaultToDeal" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id as id
             ,t1.building_id as buildingId
             , t2.building_name as buildingName
             , '0' as faultType
             , t3.point_number as pointCode
             , t3.point_desc as locationDescribe
             , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
             , t1.last_time as endTime
             , '待处理' as faultStatusStr
             , t3.dev_type_name as devTypeName
             , t1.fault_status as faultStatus
             , t3.dev_type_code as devType
             , t3.fas_code as fasCode
             , t3.fas_name as fasName
             , '故障' as dataType
             , t1.emergency_status as isEmergency
             , t1.comment as comment
             ,t1.is_loop_fault as isLoopFault
        from fault_info t1
                 left join
             base_building t2 on t1.building_id = t2.id
                 left join
             base_device_point t3 on t1.point_id = t3.id
        where t1.building_id = #{param.buildingId}
          and t1.is_manual_report = '0'
          and t1.fault_status = '0'
        order by t1.last_time desc
    </select>

    <select id="getFaultToDealCountAllBuild" resultType="java.lang.Integer">
        select
        count(*)
        from fault_info t1
        where t1.is_manual_report = '0'
        and t1.fault_status = '0'
        and t1.building_status_str in ('正常')
        and t1.last_time > DATE_FORMAT(NOW(),'%Y-%m-%d')
        and t1.building_id in
        <foreach item="item" index="index" collection="param.belongDeps" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFaultCasing" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        (select t1.id                                             as id
              ,t1.building_id                                as buildingId
              , t2.building_name                                   as buildingName
              , '0'                                      as faultType
              , t3.point_number                                    as pointCode
              , t3.point_desc                                      as locationDescribe
              , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
              , t1.last_time                                       as endTime
              , case
                    when (t1.is_manual_report = '0') then '故障处理中'
                    when (t1.is_manual_report = '1') then '维修处理中'
                end                                                   as faultStatusStr
              , t1.dev_type_name                                   as devTypeName
              , t1.fault_status                                    as faultStatus
              , t1.dev_type_code                                   as devType
              , t3.fas_code                                        as fasCode
              , t3.fas_name                                        as fasName
              , '故障'                                              as dataType
              , t1.emergency_status                                as isEmergency
              , t1.comment                                         as comment
              ,t1.is_loop_fault as isLoopFault
         from fault_info t1
                  left join
              base_building t2 on t1.building_id = t2.id
                  left join
              base_device_point t3 on t1.point_id = t3.id
         where t1.building_id = #{param.buildingId}
           and t1.fault_status = '2'
         order by t1.last_time desc
        )

        union all

        (select t1.id                                              as id
              ,t1.building_id                                as buildingId
              , t2.building_name                                   as buildingName
              , '1'                                        as faultType
              , t3.point_number                                      as pointCode
              , t3.point_desc                                      as locationDescribe
              , t1.first_time                                      as startTime
              , t1.last_time                                       as endTime
              , '预警处理中'                                        as faultStatusStr
              , t1.dev_name                                        as devTypeName
              , t1.fire_status                                     as faultStatus
              , t1.dev_type                                        as devType
              , t3.fas_code                                        as fasCode
              , t3.fas_name                                        as fasName
              , '预警'                                              as dataType
              , t1.emergency_status                                as isEmergency
              , t1.comment                                         as comment
              ,'' as isLoopFault
         from fire_info t1
                  left join
              base_building t2 on t1.building_id = t2.id
                  left join
              base_device_point t3 on t1.point_id = t3.id
         where t1.building_id = #{param.buildingId}
           and t1.type = '6'
           and t1.fire_status = '2'
         order by t1.execute_time desc
        )

    </select>

    <select id="getFaultDone" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id as id
             ,t1.building_id as buildingId
             , t2.building_name as buildingName
             , '0'  as faultType
             , t3.point_number as pointCode
             , t3.point_desc as locationDescribe
             , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
             , t1.last_time as endTime
             , '已完成' as faultStatusStr
             , t1.dev_type_name as devTypeName
             , t1.fault_status as faultStatus
             , t1.dev_type_code as devType
             , t3.fas_code as fasCode
             , t3.fas_name as fasName
             , '故障' as dataType
             , t1.emergency_status as isEmergency
             , t1.comment as comment
             ,t1.is_loop_fault as isLoopFault
        from fault_info t1
                 left join
             base_building t2 on t1.building_id = t2.id
                 left join
             base_device_point t3 on t1.point_id = t3.id
        where t1.building_id = #{param.buildingId}
          and t1.fault_status = '1'
          and t1.last_time <![CDATA[ >= ]]> date_format(date_sub(now(),interval 1 day),'%Y-%m-%d %H:%i:%s')
        order by t1.last_time desc
    </select>

    <select id="getWbTeam" resultType="map">
        select
            t1.id as wbTeamId
             ,t1.wb_team_name as wbTeamName
        from wb_team t1
        where t1.building_id = #{param.buildingId}
    </select>

    <select id="getWbPerson" resultType="map">
        select
        t1.wb_per_name as wbPerName
        ,t1.id as wbPerId
        ,t1.wb_per_tel as wbPerTel
        ,t1.
        ,t2.wb_team_name as wbTeamName
        from wb_personnel t1
        left join wb_team t2
        on t1.wb_team_id = t2.id
        where
        t2.building_id = #{param.buildingId}
        <if test="param.wbPerName !=null and param.wbPerName !=''">
            and t1.wb_per_name like concat('%',#{param.wbPerName},'%')
        </if>
        <if test="param.wbTeamId !=null and param.wbTeamId !=''">
            and t2.id = #{param.wbTeamId}
        </if>
    </select>

    <!--关联故障表 字段:来源/时间(?) 指派记录/时间 维修人/时间-->
    <select id="selectTimeLine" resultType="map">
        select t1.id                                              as id
             , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as reportedTime
             , t1.fas_name                                        as fasName
             , t1.send_time                                       as sendTime
             , t1.send_user                                       as sendUser
             , t2.start_user_id                                   as startUserId
             , t2.transfer_time                                   as transferTime
             , t2.transfer_status                                 as transferStatus
             , t1.start_time                                      as startTime
             , t2.end_user_id                                     as endUserId
             , t1.repairman_id                                    as repairmanId
             , t1.repairman                                       as repairman
             , t1.end_time                                        as endTime
        from fault_info t1
                 left join fault_warning_transfer_log t2 on t1.id = t2.info_id
        where t1.id = #{param.id};
    </select>

    <select id="selectFaultDetails" resultType="map">
        select t1.building_name   as buildingName
             , t1.point_code      as pointCode
             , t1.point_desc      as pointDesc
             , t1.dev_type_name   as devTypeName
             , t1.start_time      as startTime
             , t1.end_time        as endTime
             , t1.repairman       as repairman
             , t1.handle_time      as repairmanTime
             , t2.end_user_id     as finishMan
             , t2.transfer_time   as finishTime
             , t2.end_user_id     as assignList
             , t2.start_user_id   as sendUser
             , t2.transfer_status as faultStatus
        from fault_info t1
                 left join fault_warning_transfer_log t2 on t1.id = t2.info_id
        where t1.id = #{param.id}
          and t1.fault_status = '1'
          and t2.info_type = '0'
    </select>

    <select id="selectDealingInfo" resultType="map">
        select t1.building_name as buildingName
             , t1.point_code    as pointCode
             , t1.point_desc    as locationDescribe
             , t1.start_time    as startTime
             , t1.end_time      as endTime
             , t1.send_user     as sendUser
             , t1.fault_type    as faultType
             , t1.send_time     as sendTime
             , t1.repairman     as repairman
        from fault_info t1
        where t1.id = #{param.faultId} and t1.fault_status = '2'
    </select>

    <select id="selectRepairmen" resultType="map">
        select
            t1.wb_per_name as name
        from wb_personnel t1
        where t1.id = #{id}
    </select>

    <select id="getFaultOrFireInfo" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        (select t1.id as id
        , t1.building_id as buildingId
        , t2.building_name as buildingName
        , '0' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
        , t1.last_time as endTime
        , case
        when (t1.is_manual_report = '0') then '故障待处理'
        when (t1.is_manual_report = '1') then '维修待处理'
        end as faultStatusStr
        , t1.dev_type_name as devTypeName
        , t1.fault_status as faultStatus
        , t1.dev_type_code as devType
        , t1.fas_code as fasCode
        , t1.fas_name as fasName
        , case
        when (t1.is_manual_report = '0') then '故障'
        when (t1.is_manual_report = '1') then '维修'
        end as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        ,t1.is_loop_fault as isLoopFault
        from fault_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id = #{param.buildingId}
        and t1.fault_status = '2'
        <if test="param.wbId != null and param.wbId != ''">
            and t1.repairman_id like concat('%',#{param.wbId},'%')
        </if>
        )

        union all

        (select t1.id as id
        , t1.building_id as buildingId
        , t2.building_name as buildingName
        , '1' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , t1.first_time as startTime
        , t1.last_time as endTime
        , '预警待处理' as faultStatusStr
        , t1.dev_name as devTypeName
        , t1.fire_status as faultStatus
        , t1.dev_type as devType
        , t3.fas_code as fasCode
        , t3.fas_name as fasName
        , '预警' as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        ,'' as isLoopFault
        from fire_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id = #{param.buildingId}
        and t1.type = '6'
        and t1.fire_status = '2'
        <if test="param.wbId != null and param.wbId != ''">
            and t1.repairman_id like concat('%',#{param.wbId},'%')
        </if>
        )
        order by endTime desc

    </select>

    <!-- 维保人员首页展示条数-->
    <select id="getFaultOrFireInfoCountAllBuild" resultType="java.lang.Integer">
        select
        count(*)
        from
        ((select
        t1.id as id
        from fault_info t1
        where t1.fault_status = '2'
        and t1.building_status_str in ('正常')
        and t1.last_time > DATE_FORMAT(NOW(),'%Y-%m-%d')
        <if test="param.wbId != null and param.wbId != ''">
            and t1.repairman_id like concat('%',#{param.wbId},'%')
        </if>
        and t1.building_id in
        <foreach item="item" index="index" collection="param.belongDeps" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        union all
        (select
        t1.id as id
        from fire_info t1
        where t1.type = '6'
        and t1.fire_status = '2'
        and t1.building_status_str in ('正常')
        and t1.last_time > DATE_FORMAT(NOW(),'%Y-%m-%d')
        <if test="param.wbId != null and param.wbId != ''">
            and t1.repairman_id like concat('%',#{param.wbId},'%')
        </if>
        and t1.building_id in
        <foreach item="item" index="index" collection="param.belongDeps" open="(" separator="," close=")">
            #{item}
        </foreach>
        )) as a


    </select>

    <select id="selectFaultToDealDetail" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id                                              as id
             , t2.building_name                                   as buildingName
             , t1.fault_type                                      as faultType
             , t3.point_number                                      as pointCode
             , t3.point_desc                                      as locationDescribe
             , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
             , t1.last_time                                        as endTime
             , case
                   when t1.is_manual_report = '1' then '维修 待处理'
                   when t1.is_manual_report != '1' then '故障 待处理'
            end                                                   as faultStatusStr
             , t1.dev_type_name                                   as devTypeName
             , t1.fault_status                                    as faultStatus
             , t1.dev_type_code                                   as devType
             , t3.fas_code                                        as fasCode
             , t3.fas_name                                        as fasName
             , '故障'                                              as dataType
             , t1.comment                                         as comment
        from fault_info t1
                 left join
             base_building t2 on t1.building_id = t2.id
                 left join
             base_device_point t3 on t1.point_id = t3.id
        where t1.id = #{param.id}
          and t1.fault_status = '0'
        order by t1.reported_time desc
    </select>


    <select id="getFaultOrFireInfoDone" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id as id
             ,t1.building_id as buildingId
             , t2.building_name as buildingName
             , '0'  as faultType
             , t3.point_number as pointCode
             , t3.point_desc as locationDescribe
             , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
             , t1.last_time as endTime
             , '已完成' as faultStatusStr
             , t1.dev_type_name as devTypeName
             , t1.fault_status as faultStatus
             , t1.dev_type_code as devType
             , t3.fas_code as fasCode
             , t3.fas_name as fasName
             , '故障' as dataType
             , t1.emergency_status                                as isEmergency
             , t1.comment                                         as comment
             ,t1.is_loop_fault as isLoopFault
        from fault_info t1
                 left join
             base_building t2 on t1.building_id = t2.id
                 left join
             base_device_point t3 on t1.point_id = t3.id
        where t1.building_id = #{param.buildingId}
          and t1.fault_status = '1'
        order by t1.last_time desc
    </select>


    <select id="getFaultToDealAll" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id as id
        ,t1.building_id as buildingId
        , t2.building_name as buildingName
        , '0' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
        , t1.last_time as endTime
        , '故障 待处理' as faultStatusStr
        , t1.dev_type_name as devTypeName
        , t1.fault_status as faultStatus
        , t1.dev_type_code as devType
        , t3.fas_code as fasCode
        , t3.fas_name as fasName
        , '故障' as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        from fault_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id in
        <foreach item="item1" index="index" collection="param.buildingIds"
                 open="(" separator="," close=")">
            #{item1}
        </foreach>
        and t1.is_manual_report = '0'
        and t1.fault_status = '0'
        order by t1.reported_time desc
    </select>

    <select id="getFaultCasingAll" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        (select t1.id as id
        ,t1.building_id as buildingId
        , t2.building_name as buildingName
        , '0' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
        , t1.last_time as endTime
        , case
        when (t1.is_manual_report = '0') then '故障 处理中'
        when (t1.is_manual_report = '1') then '维修 处理中'
        end as faultStatusStr
        , t1.dev_type_name as devTypeName
        , t1.fault_status as faultStatus
        , t1.dev_type_code as devType
        , t3.fas_code as fasCode
        , t3.fas_name as fasName
        , '故障' as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        from fault_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id in
        <foreach item="item1" index="index" collection="param.buildingIds"
                 open="(" separator="," close=")">
            #{item1}
        </foreach>
        and t1.fault_status = '2'
        )

        union all

        (select t1.id as id
        ,t1.building_id as buildingId
        , t2.building_name as buildingName
        , '1' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , t1.first_time as startTime
        , t1.last_time as endTime
        , '预警 处理中' as faultStatusStr
        , t1.dev_name as devTypeName
        , t1.fire_status as faultStatus
        , t1.dev_type as devType
        , t3.fas_code as fasCode
        , t3.fas_name as fasName
        , '预警' as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        from fire_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id in
        <foreach item="item1" index="index" collection="param.buildingIds"
                 open="(" separator="," close=")">
            #{item1}
        </foreach>
        and t1.type = '6'
        and t1.fire_status = '2')
        order by endTime desc
    </select>

    <select id="getFaultDoneAll" resultType="com.redxun.fire.core.pojo.dto.app.FaultListDto">
        select t1.id as id
        ,t1.building_id as buildingId
        , t2.building_name as buildingName
        , '0' as faultType
        , t3.point_number as pointCode
        , t3.point_desc as locationDescribe
        , date_format(t1.reported_time, '%Y-%m-%d %H:%i:%s') as startTime
        , t1.last_time as endTime
        , '已完成' as faultStatusStr
        , t1.dev_type_name as devTypeName
        , t1.fault_status as faultStatus
        , t1.dev_type_code as devType
        , t3.fas_code as fasCode
        , t3.fas_name as fasName
        , '故障' as dataType
        , t1.emergency_status as isEmergency
        , t1.comment as comment
        from fault_info t1
        left join
        base_building t2 on t1.building_id = t2.id
        left join
        base_device_point t3 on t1.point_id = t3.id
        where t1.building_id in
        <foreach item="item1" index="index" collection="param.buildingIds"
                 open="(" separator="," close=")">
            #{item1}
        </foreach>
        and t1.fault_status = '1'
        order by t1.reported_time desc
    </select>
    <select id="selectTodayFaultInfo" resultType="com.redxun.fire.core.entity.FaultInfo">
        select id as id,
        building_id as buildingId
        from
        fault_info
        where
        fault_status = '0'
        <if test="param.buildingId !=null and param.buildingId !=''">
            and building_id = #{param.buildingId}
        </if>
        and last_time <![CDATA[>]]> #{param.startTime}
        and last_time <![CDATA[<=]]> #{param.endTime}
    </select>
    <select id="selectFaultToScore" resultType="int">
        select count(point_id) as pointTotle
        from
            (
                select
                    t1.point_id
                from fault_info t1
                where t1.building_id = #{param.buildingId}
                  and t1.last_time <![CDATA[<=]]> #{param.endTime}
                  and t1.fault_status in ('0','2')
                group by t1.point_id
            ) t2

    </select>
    <select id="selectFaultInfoToExport" resultType="java.util.Map">
        SELECT
        id
        ,building_id
        ,point_id
        ,pid
        ,dev_num
        ,point_desc
        ,fas_code
        ,fas_name
        ,dev_type_code
        ,dev_type_name
        ,dev_status_code
        ,dev_status_name
        ,fault_type
        ,fault_status
        ,flow_status
        ,fault_source
        ,`comment`
        ,is_pick
        ,start_time
        ,end_time
        ,send_time
        ,`repairman`
        ,repairman_id
        ,report_user
        ,report_user_id
        ,report_desc
        ,handle_user
        ,handle_user_id
        ,handle_time
        ,handle_status
        ,emergency_status
        ,zone_id
        ,zone_name
        ,building_name
        ,building_status
        ,point_type
        ,location_describe
        ,send_user
        ,DATE_FORMAT(reported_time,'%Y-%m-%d %H:%i:%s') as reported_time
        ,point_code
        ,building_status_str
        ,fault_status_str
        ,dev_id
        ,repair_start_time
        ,repair_end_time
        ,is_over_look
        ,last_time
        ,is_manual_report
        ,belong_dep
        ,is_loop_fault
        ,host_id
        ,loop_code
        ,loop_fault_id
        ,case when is_loop_fault='1' then '回路故障'  else '单点故障' end as fault_type_str
        ,case when fault_status = 1 then
        case when repair_verify = 1
        then '总部验证'
        when repair_verify = 0 then '非总部验证'
        else '审批验证'  end
        else '' end   repair_verify,
        (select jurisdiction_val from base_building where id = fault_info.building_id ) jurisdiction_val
        FROM fault_info
        where 1=1
        <if test="param.boListKey != null and param.boListKey == 'ddgl_dwgl_gzdw'.toString()">
            and (fault_status = '0' or fault_status ='2')
        </if>
        <if test="param.dev_type_code !=null and param.dev_type_code !=''">
            and dev_type_code = #{param.dev_type_code}
        </if>
        <if test="param.fault_status_str !=null and param.fault_status_str !=''">
            and fault_status_str = #{param.fault_status_str}
        </if>

        <if test="param.faultStatus !=null and param.faultStatus !=''">
            and fault_status = #{param.faultStatus}
        </if>
        <if test="param.buildingStatus !=null and param.buildingStatus !=''">
            and building_status = #{param.buildingStatus}
        </if>
        <if test="param.locationDescribe !=null and param.locationDescribe !=''">
            and location_describe = #{param.locationDescribe}
        </if>
        <if test="param.type !=null and param.type !=''">
            and `type` = #{param.type}
        </if>
        <if test="param.buildingId !=null and param.buildingId !=''">
            and building_id = #{param.buildingId}
        </if>
        <if test="param.pointCode !=null and param.pointCode !=''">
            and point_code = #{param.pointCode}
        </if>
        <if test="param.devTypeCode !=null and param.devTypeCode !=''">
            and dev_type_code = #{param.devTypeCode}
        </if>
        <if test="param.lastStartTime !=null and param.lastStartTime !=''">
            and last_time <![CDATA[>=]]> #{param.lastStartTime}
        </if>
        <if test="param.lastEndTime !=null and param.lastEndTime !=''">
            and last_time <![CDATA[<=]]> #{param.lastEndTime}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        ORDER BY last_time desc

    </select>


    <select id="getFaultList" resultType="java.lang.String">
        select distinct point_id as pointId from fault_info where building_id = #{param.id}
                                                              and fault_status = '1'
    </select>
    <select id="selectFault" resultType="com.redxun.fire.core.entity.FaultExport">
        select
            id as id
             ,fault_status_str as faultStatusStr
             ,building_id as buildingId
             ,building_name as buildingName
             ,point_code as pointCode
             ,point_desc as pointDesc
             ,dev_type_name as devTypeName
             ,reported_time as reportedTime
             ,send_user as sendUser
             ,handle_user as handleUser
             ,repair_verify as repairVerify
        from fault_info
        where building_id = #{param.buildingId}
          and (UNIX_TIMESTAMP(now()) - UNIX_TIMESTAMP(last_time)) /60 > 24 * 60
          and fault_status in ('0','2')

    </select>

    <select id="getLatestTimeByLoopId" resultType="java.util.Date">
        select max(reported_time)
        from fault_info
        where loop_fault_id = #{loopFaultId}
        and fault_type in ('0','2','3','4')
    </select>
    <select id="getFaultCountForScreen" resultType="com.redxun.fire.core.pojo.dto.FaultListScreenDto">
        SELECT
            a.point_id pointId,
            a.fas_name fasName
        FROM
            fault_info a
                LEFT JOIN base_building b ON b.id = a.building_id
        WHERE
            a.fault_status IN ( '0', '2' )
          AND b.project_type = '1'
          AND fas_name IN ( '火灾报警系统', '可燃气体探测报警系统', '厨房设备灭火装置', '电气火灾监控系统' )
        GROUP BY
            a.point_id
    </select>
</mapper>



