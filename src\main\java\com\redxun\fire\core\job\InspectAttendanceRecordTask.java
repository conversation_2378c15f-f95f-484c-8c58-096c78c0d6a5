package com.redxun.fire.core.job;

import com.redxun.fire.core.service.inspect.InspectAttendanceRecordService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: 范宇航
 * @since: 2024/4/26 10:23
 * @description: 打卡任务
 * @cron: 0 0 1 * * ?
 */
@Slf4j
@Service
public class InspectAttendanceRecordTask extends IJobHandler {

    @Resource
    private InspectAttendanceRecordService service;

    @Async
    public void clearTempDir() {
        log.info("打卡任务生成开始");
        try {
            service.generateRecord();
        } catch (Exception e) {
            log.error("打卡任务生成失败:", e);
        }
        log.info("打卡任务生成结束");
    }


    @Override
    @XxlJob("InspectAttendanceRecordTaskHandler")
    public void execute() throws Exception {
        clearTempDir();
    }
}
