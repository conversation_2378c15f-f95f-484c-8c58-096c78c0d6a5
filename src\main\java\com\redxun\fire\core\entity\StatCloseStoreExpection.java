package com.redxun.fire.core.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 闭店监测设备异常表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatCloseStoreExpection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 点位号
     */
    private String pointCode;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 设备类型
     */
    private String devType;

    /**
     * 异常类型（0异常报警,1电量过低,4设备故障,5设备离线，6其他）
     */
    private String expectionType;

    /**
     * 异常上报时间
     */
    private String reportTime;

    /**
     * 异常状态（0未处理，1已处理，2暂不处理,3 处理中）
     */
    private String expectionStatus;

    /**
     * 暂不处理结束时间
     */
    private String endTime;

    /**
     * 暂不处理原因
     */
    private String reason;

    /**
     * 建筑物id
     */
    private String buildingId;

    /**
     * 处理情况
     */
    private String handlingInfo;

    /**
     * 暂不处理天
     */
    private String day;

    /**
     * 暂不处理小时
     */
    private String hour;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 最后上报时间
     */
    private String reportEndTime;

    /**
     * 处理人时间
     */
    private String handlingTime;

    /**
     * 处理人id
     */
    private String handlingId;

    /**
     * 处理人姓名
     */
    private String handlingName;

    /**
     * 所属部门的组织机构id
     */
    private String belongDep;

    /**
     * 是否发送监控台(0未发送,1已发送)
     */
    private String isSend;

    /**
     * 审批状态（0 未审批，1 审批通过，2 审批拒绝）
     */
    private String approveStatus;

    /**
     * 审批时间
     */
    private String approveTime;

    /**
     * 消息推送历史类型(只记录最新一次记录 1,2,3) 对应config_relation.sort
     */
    private Integer sendHistoryType;

    /**
     * 图片地址
     */
    private String picUrls;

    /**
     * 报警次数
     */
    private Integer times;


}
