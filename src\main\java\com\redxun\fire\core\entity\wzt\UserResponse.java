package com.redxun.fire.core.entity.wzt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> guo shiqi
 * @createTime : 2022/12/29
 * @Description :
 */
@NoArgsConstructor
@Data
public class UserResponse {

    /**
     * code
     */
    @JsonProperty("code")
    private Integer code;
    /**
     * data
     */
    @JsonProperty("data")
    private DataDTO data ;
    /**
     * detailMsg
     */
    @JsonProperty("detailMsg")
    private String detailMsg;
    /**
     * message
     */
    @JsonProperty("message")
    private String message;
    /**
     * show
     */
    @JsonProperty("show")
    private Boolean show;
    /**
     * success
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * userId
         */
        @JsonProperty("userId")
        private String userId;
        /**
         * instId
         */
        @JsonProperty("instId")
        private String instId;
        /**
         * actualUnitId
         */
        @JsonProperty("actualUnitId")
        private String actualUnitId;
        /**
         * administrativeRank
         */
        @JsonProperty("administrativeRank")
        private String administrativeRank;
        /**
         * birthday
         */
        @JsonProperty("birthday")
        private String birthday;
        /**
         * brandName
         */
        @JsonProperty("brandName")
        private String brandName;
        /**
         * certificate
         */
        @JsonProperty("certificate")
        private String certificate;
        /**
         * degreeType
         */
        @JsonProperty("degreeType")
        private String degreeType;
        /**
         * emergencyContact
         */
        @JsonProperty("emergencyContact")
        private String emergencyContact;
        /**
         * emil
         */
        @JsonProperty("emil")
        private String emil;
        /**
         * employeesType
         */
        @JsonProperty("employeesType")
        private Integer employeesType;
        /**
         * emptype
         */
        @JsonProperty("emptype")
        private String emptype;
        /**
         * entryTime
         */
        @JsonProperty("entryTimes")
        private String entryTimes;

        /**
         * entryTime
         */
        @JsonProperty("entryTime")
        private String entryTime;
        /**
         * fullName
         */
        @JsonProperty("fullName")
        private String fullName;
        /**
         * jobTitle
         */
        @JsonProperty("jobTitle")
        private String jobTitle;
        /**
         * maritalStatus
         */
        @JsonProperty("maritalStatus")
        private String maritalStatus;
        /**
         * mobile
         */
        @JsonProperty("mobile")
        private String mobile;
        /**
         * password
         */
        @JsonProperty("password")
        private String password;
        /**
         * gender
         */
        @JsonProperty("gender")
        private String gender;
        /**
         * quitTime
         */
        @JsonProperty("quitTime")
        private String quitTime;
        /**
         * reportNumber
         */
        @JsonProperty("reportNumber")
        private String reportNumber;
        /**
         * specialPerson
         */
        @JsonProperty("specialPerson")
        private String specialPerson;
        /**
         * status
         */
        @JsonProperty("status")
        private String status;
        /**
         * userNo
         */
        @JsonProperty("userNo")
        private String userNo;
        /**
         * orgId
         */
        @JsonProperty("orgId")
        private String orgId;
        /**
         * unitId
         */
        @JsonProperty("unitId")
        private String unitId;
        /**
         * unitName
         */
        @JsonProperty("unitName")
        private String unitName;
        /**
         * orgName
         */
        @JsonProperty("orgName")
        private String orgName;
        /**
         * positionId
         */
        @JsonProperty("positionId")
        private String positionId;
        /**
         * phoneDecrypt
         */
        @JsonProperty("phoneDecrypt")
        private String phoneDecrypt;
        /**
         * phoneEncrypt
         */
        @JsonProperty("phoneEncrypt")
        private String phoneEncrypt;
        /**
         * 岗位名称
         */
        @JsonProperty("positionName")
        private String positionName;
        /**
         * 组织全路径
         */
        @JsonProperty("unitFullPath")
        private String unitFullPath;
        /**商户类型**/
        @JsonProperty("merchantType")
        private String merchantType;
        /**员工职务**/
        @JsonProperty("positionType")
        private String positionType;
        /**用户类型名称*/
        @JsonProperty("merchantTypeName")
        private String merchantTypeName;

        /**职位名称*/
        @JsonProperty("positionTypeName")
        private String positionTypeName;
        /**租户Id*/
        @JsonProperty("tenantId")
        private String tenantId;

    }
}
