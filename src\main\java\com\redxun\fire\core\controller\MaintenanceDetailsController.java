package com.redxun.fire.core.controller;


import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.entity.PicConfig;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.pojo.vo.MaintenancePlanDetailListVo;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.service.maintenance.IMaintenanceDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 设备维保检查计划详情表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@RestController
@RequestMapping("/maintenance-details")
@Slf4j
@Api(tags = "维保检查项")
public class MaintenanceDetailsController {

    @Resource
    private IMaintenanceDetailsService maintenanceDetailsService;


    @ResponseBody
    @PostMapping(value = "getMaintenanceDetails")
    @ApiOperation("获取维保检查项中")
    public JsonResult getMaintenanceSysByParam(@RequestBody @Validated(Select.class) MaintenancePlanSysDto maintenancePlanSysDto) throws Exception {
        try {
            MaintenancePlanDetailListVo maintenancePlanSysListVo = maintenanceDetailsService.getMaintenanceSysByParam(maintenancePlanSysDto);
            if (maintenancePlanSysListVo != null) {
                return JsonResult.Success().setData(maintenancePlanSysListVo);
            } else {
                return JsonResult.Fail("当月无生成计划");
            }
        } catch (Exception e) {
            log.error("getMaintenanceSysByParam获取失败", e);
            //return JsonResult.Fail(ExceptionUtil.getExceptionMessage(e));
            return JsonResult.Fail("当月无生成计划或正在生成计划中");
        }
    }

    /**
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/upload")
    public JsonResult upload(HttpServletRequest request, String url, String detailId) throws Exception {

        if (url != null) {
            return maintenanceDetailsService.uploadPhotos(request, url, detailId);
        } else {
            return JsonResult.Fail();
        }
    }

    /**
     * @param detailId
     * @return
     * @throws Exception
     */
    @RequestMapping("/selectPhotos")
    public JsonResult selectPhotos(String detailId) throws Exception {

        List<PicConfig> picConfigs = maintenanceDetailsService.selectPhotos(detailId);

        return JsonResult.Success().setData(picConfigs);
    }

    /**
     * 导出excel
     *
     * @return
     */
    @ResponseBody
    @MethodDefine(title = "导出EXCEL", path = "/exportDetail", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "导出检查项", varName = "detail")})
    @PostMapping("/exportDetail")
    @ApiOperation("导出系统")
    public void exportDetail(@RequestBody @Validated(Select.class) MaintenancePlanSysDto maintenancePlanSysDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        maintenanceDetailsService.exportDetail(maintenancePlanSysDto, request, response);
    }

}
