package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.StatCloseStoreExpection;
import com.redxun.fire.core.pojo.dto.CloseMerchantExceptionCountDto;
import com.redxun.fire.core.pojo.dto.CloseMerchantExceptionDto;
import com.redxun.fire.core.pojo.dto.ClosePlazaExceptionCountTodayDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 闭店监测设备异常表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface StatCloseStoreExpectionMapper extends BaseMapper<StatCloseStoreExpection> {

    Map<String, Object> selectCloseStoreException(@Param("param") Map param);

    List<Map<String, Object>> getCloseStoreExpectionHandleAllListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getCloseStoreExpectionHandleAllListByPageForApp(Page<?> page, @Param("param") Map param);

    Map<String, Object> selectMerchantIdAndBizType(@Param("param") Map param);

    int getCountsAllBuild(@Param("param") Map param);

    List<CloseMerchantExceptionDto> getExceptionListByBuildingId(@Param("buildingId") String buildingId, @Param("time") String time);
    List<CloseMerchantExceptionCountDto> getExceptionCountByBuildingId(@Param("buildingId") String buildingId, @Param("time") String time);

    List<ClosePlazaExceptionCountTodayDto> getMerchantExceptionTodayGroup(@Param("time") String time);

    List<CloseMerchantExceptionCountDto> getExceptionCountGroup(@Param("time") String time);

    List<Map<String,Long>> getPowerExceptionCount(@Param("param") Map<String, Object> qo);
}
