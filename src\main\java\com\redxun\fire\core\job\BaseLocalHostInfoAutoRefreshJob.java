package com.redxun.fire.core.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.redxun.fire.core.consts.RedisKeys;
import com.redxun.fire.core.entity.BaseLocalHostInfo;
import com.redxun.fire.core.mapper.BaseLocalHostInfoMapper;
import com.redxun.fire.core.service.device.IBaseLocalHostInfoService;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @createTime 2024/8/30
 * @description
 */
@Slf4j
@Service
public class BaseLocalHostInfoAutoRefreshJob extends IJobHandler {

    @Resource
    private BaseLocalHostInfoMapper baseLocalHostInfoMapper;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    IBaseLocalHostInfoService baseLocalHostInfoService;

    @Override
    @XxlJob("BaseLocalHostInfoAutoRefreshJob")
    public void execute() {
        try {
            long begin = System.currentTimeMillis();
            log.info("-------------本地主机号自动刷新开始-----------------");
            final val jobParam = XxlJobHelper.getJobParam();
            if (StrUtil.isNotBlank(jobParam)) {
                Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, jobParam));
                log.info("删除本地实际主机成功,建筑物id为:【{}】,删除数量为:【{}】", jobParam, num);
                log.info("初始化本地主机数据，建筑物id为:【{}】", jobParam);
//                baseLocalHostInfoMapper.initLocalHostInfo(jobParam);
                baseLocalHostInfoService.initLocalHostInfoByBuildingId(jobParam);
                log.info("初始化本地主机数据成功，建筑物id为:【{}】", jobParam);
                return;
            }
            final val o = redisUtils.get(RedisKeys.LOCAL_HOST_INIT_BUILD_LIST);
            if (ObjectUtil.isNotEmpty(o)) {
                List<String> res = (List<String>)o;
                Set<String> set = new HashSet<>();
                for (String re : res) {
                    if (!set.add(re)) {
                        continue;
                    }
                    Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, re));
                    log.info("删除本地实际主机成功,建筑物id为:【{}】,删除数量为:【{}】", re, num);
                    log.info("初始化本地主机数据，建筑物id为:【{}】", re);
//                    baseLocalHostInfoMapper.initLocalHostInfo(re);
                    baseLocalHostInfoService.initLocalHostInfoByBuildingId(re);
                    log.info("初始化本地主机数据成功，建筑物id为:【{}】", re);
                }
            }
            redisUtils.delete(RedisKeys.LOCAL_HOST_INIT_BUILD_LIST);
            long end = System.currentTimeMillis();
            log.info("-------------本地主机号自动刷新开始任务结束，共用时{}-----------------", (end - begin));
        } catch (Exception e) {
            log.error("本地主机号自动刷新异常:", e);
        }
    }
}
