CREATE TABLE device_monitor_info
(
     id varchar(32) NOT NULL COMMENT '记录主键ID（可按业务规则自定义生成，如16进制等格式）' PRIMARY KEY,
     base_point_id varchar(32) NOT NULL COMMENT '关联的基础点位ID，对应 base_device_point.id',
     loop_type tinyint COMMENT '监测回路类型，0-非24小时回路，1-24小时回路',
     close_store_remind_time tinyint COMMENT '断电提醒时间类型，0-闭店期间，1-24小时',
     device_real_id varchar(100) COMMENT '设备id',
     device_real_name varchar(100) COMMENT '设备名称',
     merchant_manager varchar(50) COMMENT '商户店长姓名',
     manager_phone varchar(20) COMMENT '店长电话',
     phone_decrypt VARCHAR(20) COMMENT '店长电话（脱敏）',
     create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
     update_time datetime COMMENT '更新时间'
)
COMMENT = '闭店监测设备信息表' row_format = DYNAMIC;

