package com.redxun.fire.core.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.fire.core.entity.AppointmentApply;
import com.redxun.fire.core.entity.WbTask;
import com.redxun.fire.core.entity.WeibaoDto;
import com.redxun.fire.core.mapper.AppointmentApplyMapper;
import com.redxun.fire.core.server.handler.WbSuccessScheduleServer;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.IMessageLogService;
import com.redxun.fire.core.service.maintenance.IWbTaskService;
import com.redxun.fire.core.service.other.IAppointmentPointService;
import com.redxun.fire.core.utils.DelayTaskManager;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * @author: 苏志俊
 * @since: 2024/4/26 15:09
 * @description: 扫描维保任务
 * @cron: 0 0/10 * * * ?
 */
@Slf4j
@Service
public class WbTaskSchedule extends IJobHandler {

    @Autowired
    IWbTaskService iWbTaskService;

    @Resource
    AppointmentApplyMapper appointmentApplyMapper;

    @Resource
    IBaseBuildingService baseBuildingService;

    @Autowired
    IAppointmentPointService iAppointmentPointService;

    @Resource
    private IMessageLogService messageLogService;

    @Resource
    private WbSuccessScheduleServer wbSuccessScheduleServer;

    @Autowired
    private RedisUtils redisUtils;

    private boolean taskFlag = true;

    @Override
    @XxlJob("wbTaskSchedule")
    public void execute() throws Exception {
        //已调试完成
        if (taskFlag) {
            log.info("维保定时任务开始");
            taskFlag = false;
            DelayTaskManager delayTaskManager = DelayTaskManager.getInstance();
            // 获取维保任务
            List<WbTask> list = iWbTaskService.list(new QueryWrapper<WbTask>().in("status", "1", "2"));
            if (list != null && list.size() > 0) {
                for (WbTask wbTask : list) {
                    // 维保结束任务
                    if ("1".equals(wbTask.getStatus()) && !delayTaskManager.existenceByTaskName(wbTask.getTaskKey())) {
                        log.info("维保定时任务，维保结束任务不存在");
                        AppointmentApply appointment = appointmentApplyMapper.selectById(wbTask.getAppointmentId());
                        if (ObjectUtil.isEmpty(appointment)) {
                            log.warn("维保定时任务，未查询到维保申请信息，id为{}", wbTask.getAppointmentId());
                            continue;
                        }
                        long end_now = Duration.between(LocalDateTime.now(), wbTask.getDate()).getSeconds();
                        if (end_now >= 0) {
                            log.info("维保定时任务，创建维保结束延迟任务===" + wbTask.getTaskKey());
                            new Thread(new Runnable() {
                                @SneakyThrows
                                @Override
                                public void run() {
                                    //如  已過結束時間   則  全部入庫  為  未爲保
                                    delayTaskManager.putTask(new Runnable() {
                                        @SneakyThrows
                                        @Override
                                        public void run() {
                                            wbSuccessScheduleServer.wbEnd(appointment, delayTaskManager);
                                        }
                                    }, end_now, TimeUnit.SECONDS, "weibao-" + wbTask.getBuildingId());
                                }
                            }).start();
                        } else {
                            log.info("维保定时任务，维保审批超时结束:" + wbTask.getAppointmentId());
                            final val applicationStatus = appointment.getApplicationStatus();
                            //维保任务审批通过超时结束添加计算维保进度逻辑
                            if ("2".equals(applicationStatus)) {
                                wbSuccessScheduleServer.wbEnd(appointment, delayTaskManager);
                            } else {
                                //维保结束的时候 修改建筑物状态 为正常
                                baseBuildingService.updateBuildingStatus(appointment.getBuildingId(), 0);
                                log.info("维保定时任务，修改预约申请记录为已结束");
                                if (System.currentTimeMillis() >= appointment.getEndTime().getTime()) {
                                    appointment.setRealEndTime(appointment.getEndTime());
                                } else {
                                    appointment.setRealEndTime(new Date());
                                }
                                appointment.setMaintenanceStart("4");
                                appointmentApplyMapper.updateById(appointment);
                            }
                        }

//                        AppointmentApply appointment1 = appointmentApplyMapper.selectById(wbTask.getAppointmentId());

                        log.info("维保定时任务，维保即将结束推送消息记录开始");
//                        if (appointment1 != null) {
//                            if (end_now > 0 && end_now <= 600) {
//                                //维保计划到时前10分钟内，推送维保即将结束消息记录
//                                messageLogService.wbEndNode(appointment1.getBuildingId(), appointment1.getStartTime(), appointment1.getEndTime(), end_now, appointment1.getId());
//                            } else if (end_now > 600) {
//                                long runTime = end_now - 600;
//                                //维保计划到期前10分钟之前，推送维保即将结束消息记录
//                                wbSuccessScheduleServer.getTimer().schedule(new TimerTask() {
//                                    @Override
//                                    public void run() {
//                                        messageLogService.wbEndNode(appointment1.getBuildingId(), appointment1.getStartTime(), appointment1.getEndTime(), 599, appointment1.getId());
//                                    }
//                                }, runTime * 1000L, TimeUnit.MILLISECONDS);
//                            }
//                        }
                        log.info("维保定时任务，维保即将结束推送消息记录结束");
                        //维保无上传记录关闭维保任务
                    } else if ("2".equals(wbTask.getStatus()) && !delayTaskManager.existenceByTaskName(wbTask.getTaskKey() + "Close")) {
                        log.info("维保定时任务，维保无上传记录关闭维保任务不存在");
                        long start = Duration.between(LocalDateTime.now(), wbTask.getDate()).getSeconds();
                        if (start >= 0) {
                            // 启动延迟任务执行
                            log.info("维保定时任务，创建维保无上传记录关闭维保任务===" + wbTask.getTaskKey());
                            new Thread(new Runnable() {
                                @SneakyThrows
                                @Override
                                public void run() {
                                    delayTaskManager.putTask(new Runnable() {
                                        @Override
                                        public void run() {
                                            noUpload(wbTask);
                                        }
                                    }, start, TimeUnit.SECONDS, "weibao-" + wbTask.getBuildingId() + "Close");
                                }
                            }).start();
                        }
                    }
                }
            }
            log.info("维保定时任务结束");
        }
    }

    public void noUpload(WbTask wbTask) {
        // (30分钟内无上传记录)条件执行
        log.info("维保定时任务，建筑" + wbTask.getBuildingId() + "(30分钟内无上传记录)条件执行");
        QueryWrapper<WbTask> wbtaskRemoveWrapper = new QueryWrapper<>();
        wbtaskRemoveWrapper.eq("task_key", "weibao-" + wbTask.getBuildingId());
        wbtaskRemoveWrapper.eq("status", "2");
        iWbTaskService.remove(wbtaskRemoveWrapper);
        // 获取维保数据
        WeibaoDto weibaoDto1 = JSON.parseObject(redisUtils.get("weibao-" + wbTask.getBuildingId()).toString(), WeibaoDto.class);
        if (weibaoDto1 == null) {
            log.info("维保定时任务，维保数据为null");
            return;
        }
        Boolean stateFlag = weibaoDto1.getStateFlag();
        // 如果30分钟没有上传数据 结束维保
        if (!stateFlag) {
            log.info("维保定时任务，建筑" + weibaoDto1.getBuildingId() + "当前没有数据上传，暂停维保！");
//            AppointmentApply appointmentApply = appointmentApplyMapper.selectById(weibaoDto1.getAppointmentApplyId());
            AppointmentApply appointmentApply = appointmentApplyMapper.queryDataById(weibaoDto1.getAppointmentApplyId());
            AppointmentApply appointmentApplyUpdate = new AppointmentApply();
            appointmentApplyUpdate.setId(weibaoDto1.getAppointmentApplyId());
            appointmentApplyUpdate.setMaintenanceStart("3");
            appointmentApplyMapper.updateById(appointmentApplyUpdate);
            //修改建筑物状态为正常嗎，1·    。   /34
            baseBuildingService.updateBuildingStatus(wbTask.getBuildingId(), 0);
            weibaoDto1.setMaintenanceStart("3");
//            redisUtils.set("weibao-" + wbTask.getBuildingId(), FastJSONUtils.toJSONString(weibaoDto1));
            redisUtils.updateWbData("weibao-" + wbTask.getBuildingId(), weibaoDto1);
            //配置时间内无数据上传推送消息记录
            messageLogService.wbAutoOutNode(appointmentApply.getBuildingId(), appointmentApply.getStartTime(), appointmentApply.getEndTime(), appointmentApply.getId());
        }
    }
}
