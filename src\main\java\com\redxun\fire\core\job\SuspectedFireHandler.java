package com.redxun.fire.core.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.service.alarm.IFireEventRelationService;
import com.redxun.fire.core.service.alarm.impl.FirePageWindowServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.redxun.fire.core.utils.UUIDUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @author: 范宇航
 * @since: 2024/4/26 15:13
 * @description: 疑似火警
 * @cron: 0 0/1 * * * ?
 */
@Slf4j
@Service
public class SuspectedFireHandler extends IJobHandler {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private SuspectedFireMapper suspectedFireMapper;

    @Resource
    private BaseBuildingMapper baseBuildingMapper;

    @Resource
    private SuspectedFireQuartzMapper suspectedFireQuartzMapper;

    @Resource
    private FirePageWindowServiceImpl firePageWindowServiceImpl;

    @Resource
    private ReceivingPointMapper receivingPointMapper;

    @Resource
    private BaseEventStatisticsMapper baseEventStatisticsMapper;

    @Resource
    private FireInfoMapper fireInfoMapper;

    @Resource
    private StoreDbMapper storeDbMapper;
    @Resource
    private FireEventRelationMapper fireEventRelationMapper;

    @Override
    @XxlJob("SuspectedFire")
    public void execute() throws Exception {
        Long startTime = System.currentTimeMillis();
        //开始时间
        Date now = new Date();
        log.info("疑似火警任务开始-开始时间：" + DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss"));
        //查询建筑表
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(new QueryWrapper<BaseBuilding>());
        //查询主逻辑
        List<SuspectedFire> suspectedFires = suspectedFireMapper.selectList(new QueryWrapper<SuspectedFire>().eq("main_logic", "1"));
        if (suspectedFires != null && suspectedFires.size() > 0) {
            //查询所有子逻辑
            new QueryWrapper<SuspectedFire>();
            List<SuspectedFire> suspectedFiresList = suspectedFireMapper.selectList(new QueryWrapper<SuspectedFire>().isNotNull("parent_id"));
            Map<String, List<SuspectedFire>> suspectedFiresMap = new HashMap<>();
            if (suspectedFiresList != null && suspectedFiresList.size() > 0) {
                suspectedFiresMap = suspectedFiresList.stream().collect(Collectors.groupingBy(e -> e.getParentId()));
            }
            //log.info("疑似火警-规则数量：" + suspectedFires.size());

            //查询为调试状态的建筑
            List<String> buildIds = storeDbMapper.findDubegBuild();
            //创建固定线程池
            ExecutorService fixedPool = Executors.newFixedThreadPool(suspectedFires.size());
            for (SuspectedFire suspectedFire : suspectedFires) {
                Map<String, List<SuspectedFire>> finalSuspectedFiresMap = suspectedFiresMap;
                fixedPool.submit(new Runnable() {
                    @Override
                    public void run() {
                        //log.info("疑似火警-规则id为{}--------", suspectedFire.getId());
                        //创建线程池
                        ExecutorService executorService = new ThreadPoolExecutor(0, 5,
                                30L, TimeUnit.SECONDS,
                                new LinkedBlockingQueue<>());
                        //处理每一个建筑
                        for (BaseBuilding baseBuilding : baseBuildings) {
                            //建筑为调试状态为调试或者（维保模式推送关闭且建筑物在维保模式下）则不推送疑似火警
//                            if (!firePageWindowServiceImpl.getBuildStatus(baseBuilding.getId())) {
//                                log.info("疑似火警-建筑{}为调试状态为调试或者（维保模式推送关闭且建筑物在维保模式下）则不推送疑似火警------", baseBuilding.getId());
//                                continue;
//                            }
                            if (buildIds.contains(baseBuilding.getId())) {
                                //log.info("疑似火警-建筑{}为调试状态为调试或者（维保模式推送关闭且建筑物在维保模式下）则不推送疑似火警------", baseBuilding.getId());
                                continue;
                            }

                            //log.info("疑似火警-规则id为{},广场id:{}--------", suspectedFire.getId(),baseBuilding.getId());

                            executorService.submit(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        suspectedFireTimeTask(suspectedFire, baseBuilding, finalSuspectedFiresMap);
                                    } catch (Exception e) {
                                        log.error("疑似火警-建筑{}开始进行疑似火警判定异常，规则id为{}------", baseBuilding.getId(), suspectedFire.getId());
                                        e.printStackTrace();
                                    }
                                }
                            });
                        }
                        try {
                            // 关闭启动线程
                            executorService.shutdown();
                            // 等待子线程结束，再继续执行下面的代码
                            if (!executorService.awaitTermination(2, TimeUnit.MINUTES)) {
                                executorService.shutdownNow();
                            }
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                            log.info("疑似火警，子线程池关闭异常");
                            executorService.shutdownNow();
                        }
                    }
                });
            }
            try {
                // 关闭启动线程
                fixedPool.shutdown();
                // 等待子线程结束，再继续执行下面的代码
                if (!fixedPool.awaitTermination(2, TimeUnit.MINUTES)) {
                    fixedPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
                log.info("疑似火警，主线程池关闭异常");
                fixedPool.shutdownNow();
            }
        }
        Long endTime = System.currentTimeMillis();
        log.info("疑似火警任务结束，执行用时:" + ((endTime - startTime) / 1000) + "秒");
    }

    /**
     * @Description: 查找子逻辑
     * @Param:
     * @return:
     * @Author: zhupj
     * @Date:
     */
    public void suspectedFireTimeTask(SuspectedFire suspectedFire, BaseBuilding baseBuilding, Map<String, List<SuspectedFire>> suspectedFiresMap) throws Exception {
        //log.info("--进入疑似火警查询逻辑，建筑id为{}，判断规则id为{}--", baseBuilding.getId(), suspectedFire.getId());
        Map<String, String> param = new HashMap<>();
        param.put("buildingId", baseBuilding.getId());
        //维保模式下推送开关
        String isOpen = suspectedFire.getModeOpen();
        param.put("open", isOpen);
        String time = suspectedFireQuartzMapper.selectTime(param);
        if (StringUtils.isEmpty(time)) {
            log.info("疑似火警-建筑{}未查询到最新推送时间，直接返回,传参open为{}-------", baseBuilding.getId(), isOpen);
            return;
        }
        Date now = DateUtil.parseDate(time, "yyyy-MM-dd HH:mm:ss");
        //主逻辑处理
        List<Boolean> booleans = new ArrayList<>();
        //疑似火警集合
        Set<FireInfo> fireInfos = new HashSet<>();
        //防火分区集合
        Set<String> zoneNames = new HashSet<>();
        //主逻辑id
        String mainId = suspectedFire.getId();
        suspectedFireLogic(suspectedFire, baseBuilding, booleans, now, fireInfos, zoneNames, mainId, isOpen);
        //查询子逻辑
        //List<SuspectedFire> suspectedFires = suspectedFireMapper.selectList(new QueryWrapper<SuspectedFire>().eq("parent_id", suspectedFire.getId()));
        if (suspectedFiresMap != null && suspectedFiresMap.size() > 0) {
            List<SuspectedFire> suspectedFires = suspectedFiresMap.get(suspectedFire.getId());
            if (CollectionUtil.isNotEmpty(suspectedFires)) {
                suspectedFires.forEach(susFire -> {
                    try {
                        suspectedFireLogic(susFire, baseBuilding, booleans, now, fireInfos, zoneNames, mainId, isOpen);
                    } catch (Exception e) {
                        log.info("疑似火警，子逻辑出错");
                        e.printStackTrace();
                        booleans.add(false);
                    }
                });
            }
        }
        //log.info("--全部逻辑执行完毕，执行结果为{}，建筑id为{}，规则id为{}--", JSONObject.toJSONString(booleans), baseBuilding.getId(), suspectedFire.getId());
        //逻辑全部成立
        if (booleans != null && booleans.size() > 0 && !booleans.contains(false)) {
            //redis插入当前疑似火警时间
            redisUtils.set(baseBuilding.getId() + mainId + "_yisi", DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss"));
            //入接警中心
            zoneNames.forEach(zoneName -> {
                redisUtils.set(baseBuilding.getId() + zoneName + mainId + "_yisi", DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss"));
            });
            String uuid = UUIDUtils.getUUID().replaceAll("-", "");
            StringBuilder pointCodes = new StringBuilder();
            StringBuilder pointDescs = new StringBuilder();
            StringBuilder checkImgs = new StringBuilder();
            StringBuilder pointIds = new StringBuilder();
            StringBuilder zoneIds = new StringBuilder();
            StringBuilder fireInfoIds = new StringBuilder();
            StringBuilder devTypeName = new StringBuilder();
            StringBuilder zoneName = new StringBuilder();
            List<FireEventRelation> fireEventRelations = new ArrayList<>();
            if (fireInfos != null && fireInfos.size() > 0) {
                //log.info("疑似火警 --->" + baseBuilding.getId() + ":" + fireInfos.toString());
            }
            for (FireInfo fireInfo : fireInfos) {
                //存入关联表
                ReceivingPoint receivingPoint = new ReceivingPoint();
                receivingPoint.setReceivingId(uuid);
                receivingPoint.setPointCode(fireInfo.getPointCode());
                receivingPoint.setPointDescribe(fireInfo.getPointDesc());
                receivingPoint.setPointType(fireInfo.getDevType());
                receivingPoint.setPointTypeStr(fireInfo.getDevName());
                receivingPoint.setEventType(fireInfo.getFireType());
                receivingPoint.setEventTypeStr(fireInfo.getFireTypeStr());
                receivingPoint.setEventId(fireInfo.getId());
                receivingPoint.setReportTime(LocalDateTime.parse(fireInfo.getFirstTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                receivingPoint.setProcessResult(fireInfo.getExecuteResult());
                receivingPoint.setProcessResultStr(fireInfo.getExecuteResult());
                receivingPoint.setTel(baseBuilding.getCtrlPhone());
                receivingPoint.setPointName(fireInfo.getDevName());
                receivingPointMapper.insert(receivingPoint);

                FireEventRelation fireEventRelation = new FireEventRelation();
                if (StringUtils.isNotEmpty(fireInfo.getPointCode())) {
                    pointCodes.append(fireInfo.getPointCode()).append(",");
                    fireEventRelation.setPointCode(fireInfo.getPointCode());
                }
                if (StringUtils.isNotEmpty(fireInfo.getPointDesc())) {
                    pointDescs.append(fireInfo.getPointDesc()).append(",");
                    fireEventRelation.setPointDesc(fireInfo.getPointDesc());
                }
                if (StringUtils.isNotEmpty(fireInfo.getCheckImg())) {
                    checkImgs.append(fireInfo.getCheckImg()).append(",");
                }
                if (StringUtils.isNotEmpty(fireInfo.getPointId())) {
                    pointIds.append(fireInfo.getPointId()).append(",");
                    fireEventRelation.setPointId(fireInfo.getPointId());
                }
                if (StringUtils.isNotEmpty(fireInfo.getZoneId())) {
                    //火警异常id
                    zoneIds.append(fireInfo.getZoneId()).append(",");
                }
                if (StringUtils.isNotEmpty(fireInfo.getDevName()) && !devTypeName.toString().contains(fireInfo.getDevName())) {
                    devTypeName.append(fireInfo.getDevName()).append(",");
                    fireEventRelation.setPointTypeStr(fireInfo.getDevName());
                }
                if (StringUtils.isNotEmpty(fireInfo.getId())) {
                    //火警异常id
                    fireInfoIds.append(fireInfo.getId()).append(",");
                }
                if (StringUtils.isNotEmpty(fireInfo.getZoneName())) {
                    //火警异常id
                    zoneName.append(fireInfo.getZoneName()).append(",");
                    fireEventRelation.setZoneName(fireInfo.getZoneName());
                }
                fireEventRelation.setPointType(fireInfo.getDevType());
                fireEventRelation.setBuildId(baseBuilding.getId());
                fireEventRelation.setBuildName(baseBuilding.getBuildingName());
                fireEventRelation.setEventTypeStr("疑似火警");
                fireEventRelation.setEventId(uuid);
                fireEventRelation.setCreateTime(now);
//                log.info("插入火警关联事件，{}", FastJSONUtils.toJSONString(fireEventRelation));
                fireEventRelationMapper.insert(fireEventRelation);

            }

            //插入接警中心
            BaseEventStatistics baseEventStatistics = new BaseEventStatistics();
            baseEventStatistics.setId(uuid);
            baseEventStatistics.setBuildId(baseBuilding.getId());
            baseEventStatistics.setBuildName(baseBuilding.getBuildingName());
            baseEventStatistics.setEventType("1");
            baseEventStatistics.setEventTypeStr("疑似火警");
            baseEventStatistics.setEventId(StringUtils.isEmpty(fireInfoIds.toString()) ? "" : fireInfoIds.substring(0, fireInfoIds.length() - 1));
            baseEventStatistics.setReportTime(DateUtil.formatDatetime(now, "yyyy-MM-dd HH:mm:ss"));
            baseEventStatistics.setPointCode(StringUtils.isEmpty(pointCodes.toString()) ? "" : pointCodes.substring(0, pointCodes.length() - 1));
            baseEventStatistics.setPointDesc(StringUtils.isEmpty(pointDescs.toString()) ? "" : pointDescs.substring(0, pointDescs.length() - 1));
            baseEventStatistics.setPointId(StringUtils.isEmpty(pointIds.toString()) ? "" : pointIds.substring(0, pointIds.length() - 1));
            baseEventStatistics.setZone_id(StringUtils.isEmpty(zoneIds.toString()) ? "" : zoneIds.substring(0, zoneIds.length() - 1));
            baseEventStatistics.setZoneName(StringUtils.isEmpty(zoneName.toString()) ? "" : zoneName.substring(0, zoneName.length() - 1));
            baseEventStatistics.setTel(baseBuilding.getCtrlPhone());
            baseEventStatistics.setType("0");
            baseEventStatistics.setJjType(baseBuilding.getFormat());
            baseEventStatistics.setAttach(StringUtils.isEmpty(checkImgs.toString()) ? "" : checkImgs.substring(0, checkImgs.length() - 1));
            baseEventStatistics.setPointTypeStr(StringUtils.isEmpty(devTypeName.toString()) ? "" : devTypeName.substring(0, devTypeName.length() - 1));
            baseEventStatistics.setBelongDep(baseBuilding.getBelongDep());
            //log.info("疑似火警 数据为--->"+ baseBuilding.getId() +":" + JSON.toJSONString(baseEventStatistics));
            baseEventStatisticsMapper.insert(baseEventStatistics);
        }
    }

    /**
     * @Description: 疑似火警判定逻辑
     * @Param:
     * @return:
     * @Author: zhupj
     * @Date:
     */
    @Async
    public void suspectedFireLogic(SuspectedFire suspectedFire, BaseBuilding baseBuilding, List<Boolean> booleans, Date now, Set<FireInfo> fireInfosSet, Set<String> zoneNames, String mainId, String isOpen) throws Exception {
        boolean flg = false;
        String nowTime = DateUtil.formatDate(now, "yyyy-MM-dd HH:mm:ss");
        //几分钟内
        Double modeMins = suspectedFire.getModeMins();
        //大于值
        Double modeGtVal = suspectedFire.getModeGtVal();
        //开始时间
        String startTime = getDatebyTime(0.0, 0.0, modeMins, nowTime, "0", "yyyy-MM-dd HH:mm:ss");

        if (StringUtils.equals("同一建筑内", suspectedFire.getModeName())) {
            //查询该建筑上一次疑似火警时间
            long timeDiff = 0;
            String sT = null;//开始时间时间戳（用于比较）
            String eT = null;//结束时间时间戳（用于比较）
            String redisTime = (String) (redisUtils.get(baseBuilding.getId() + mainId + "_yisi"));
            if (redisTime != null && !"".equals(redisTime)) {
                //log.info("redis查询到的时间为{}，查询key为{}", redisTime, baseBuilding.getId() + mainId + "_yisi");
                if (StringUtils.equals(redisTime, nowTime)) {
                    booleans.add(flg);
                    return;
                }
                timeDiff = now.getTime() - new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(redisTime).getTime();
                if (timeDiff <= (modeMins * 60 * 1000)) {
                    startTime = redisTime;
                }
            }
            sT = startTime;
            eT = nowTime;

            //log.info("疑似火警 同一建筑数据为[fireInfoQuery]" + sT + eT);
            QueryWrapper queryWrapper = new QueryWrapper<FireInfo>();
            queryWrapper.select("id","dev_type","dev_name","point_code","point_desc","fire_type","fire_type_str","first_time","execute_result","check_img","point_id","zone_id","zone_name");
            queryWrapper.eq("building_id", baseBuilding.getId());
            queryWrapper.gt("last_time", sT);
            queryWrapper.le("last_time", eT);
            queryWrapper.ne("building_status_str", "调试");
            queryWrapper.ne("building_status_str", "特批");
            queryWrapper.ne("building_status_str", "全覆盖模式");
            if (StringUtils.equals("0", isOpen)) {
                queryWrapper.ne("building_status_str", "维保");
                queryWrapper.ne("building_status_str", "检查模式");
            }
            List<FireInfo> fireInfos = fireInfoMapper.selectList(queryWrapper);
            if (fireInfos != null && fireInfos.size() > 0) {
                //log.info("疑似火警 查询出的数据--->" + baseBuilding.getId() + ":" + fireInfos.size());
            } else {
                booleans.add(flg);
                //log.info("--建筑{}fireInfoMapper.selectList查询结果为{}--查询条件为【{}】【{}】【{}】",  baseBuilding.getId(), fireInfos.size(), sT, eT, isOpen);
                return;
            }
            int yg = 0, wg = 0, sl = 0, yk = 0, sb = 0, xb = 0, gwdl = 0, hwds = 0, cfzd = 0, qbxh = 0, rqzj = 0;
            Iterator<FireInfo> fireInfoIterator = fireInfos.iterator();
            while (fireInfoIterator.hasNext()) {
                FireInfo fireInfo = fireInfoIterator.next();
                if (StringUtils.equals(fireInfo.getDevType(), "YG") || StringUtils.equals(fireInfo.getDevType(), "DPQ")) {
                    //烟感
                    yg++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "WG")) {
                    //温感
                    wg++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "SL")) {
                    //水流
                    sl++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "YK")) {
                    //压开
                    yk++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "SB")) {
                    //手报
                    sb++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "XHSBJ")) {
                    //消报
                    xb++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "DYQQ")) {
                    //感温电缆
                    gwdl++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "HWDS")) {
                    //红外对射
                    hwds++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "CZM")) {
                    //厨房自动灭火
                    cfzd++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "SP") || StringUtils.equals(fireInfo.getDevType(), "PLB") || StringUtils.equals(fireInfo.getDevType(), "SPHJ")) {
                    //起泵信号
                    qbxh++;
                } else if (StringUtils.equals(fireInfo.getDevType(), "RQZJ") || StringUtils.equals(fireInfo.getDevType(), "ROTT") || StringUtils.equals(fireInfo.getDevType(), "RQMJ")) {
                    //燃气报警
                    rqzj++;
                } else {
                    fireInfoIterator.remove();
                }
            }
            BigDecimal result =
                    new BigDecimal(0).add(new BigDecimal(suspectedFire.getModeSmoke()).multiply(new BigDecimal(yg)))//+烟感
                            .add(new BigDecimal(suspectedFire.getModeSense()).multiply(new BigDecimal(wg)))//+温感
                            .add(new BigDecimal(suspectedFire.getModeWater()).multiply(new BigDecimal(sl)))//+水流
                            .add(new BigDecimal(suspectedFire.getModePress()).multiply(new BigDecimal(yk)))//+压开
                            .add(new BigDecimal(suspectedFire.getModeHand()).multiply(new BigDecimal(sb)))//+手报
                            .add(new BigDecimal(suspectedFire.getModeDispel()).multiply(new BigDecimal(xb)))//+消报
                            .add(new BigDecimal(suspectedFire.getModeCable()).multiply(new BigDecimal(gwdl)))//+感温电缆
                            .add(new BigDecimal(suspectedFire.getModeInfrared()).multiply(new BigDecimal(hwds)))//+红外对射
                            .add(new BigDecimal(suspectedFire.getModeAutomatic()).multiply(new BigDecimal(cfzd)))//+厨房自动灭火
                            .add(new BigDecimal(suspectedFire.getModePump()).multiply(new BigDecimal(qbxh))
                                    .add(new BigDecimal(suspectedFire.getModeRqzj()).multiply(new BigDecimal(rqzj))
                                    ));//+起泵信号
            //log.info("疑似火警 同一建筑计算结果--->"+ baseBuilding.getId() +":"+ result);
            //log.info("--计算对比查询的数据和规则值开始，建筑id为{}，规则id为{}，查询值为{}，规则值为{}--", baseBuilding.getId(), suspectedFire.getId(), result, modeGtVal);
            if (result.compareTo(new BigDecimal(modeGtVal)) <= 1) {
                //log.info("疑似火警 同一建筑数据为[fireInfo]--->" + fireInfos);
                //log.info("疑似火警 同一建筑数据计算为[fireInfo]--->" + baseBuilding.getId() + "------" + yg + "-" + wg + "-" + sl + "-" + yk + "-" + sb + "-" + xb + "-" + gwdl + "-" + hwds + "-" + cfzd + "-" + qbxh);
            }
            if (result.compareTo(new BigDecimal(modeGtVal)) > -1) {
                flg = true;
                //作用：插入接警中心
                fireInfosSet.addAll(fireInfos);
                //log.info("疑似火警 同一建筑数据为--->" + baseBuilding.getId() + ":" + fireInfos.toString());
            }

        } else {
            //查询参数
            Map<String, String> param = new HashMap<>();
            param.put("startTime", startTime);
            param.put("endTime", nowTime);
            param.put("buildingId", baseBuilding.getId());
            param.put("open", isOpen);
            //同一建筑下同一防火分区
            List<Map<String, String>> fireInfoMaps = suspectedFireQuartzMapper.selectFireMapByTime(param);
            for (Map<String, String> fireInfoMap : fireInfoMaps) {

                //查询该建筑上一次疑似火警时间
                long timeDiff = 0;
                String sT = null;//开始时间时间戳（用于比较）
                String eT = null;//结束时间时间戳（用于比较）
                String redisTime = (String) (redisUtils.get(baseBuilding.getId() + fireInfoMap.get("zoneName") + mainId + "_yisi"));
                if (redisTime != null && !"".equals(redisTime)) {
                    if (StringUtils.equals(redisTime, nowTime)) {
                        booleans.add(flg);
                        return;
                    }
                    timeDiff = now.getTime() - new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(redisTime).getTime();
                    if (timeDiff <= (modeMins * 60 * 1000)) {
                        startTime = redisTime;
                    }
                }
                sT = startTime;
                eT = nowTime;
                param.put("startTime", sT);
                param.put("endTime", eT);
                param.put("zoneName", fireInfoMap.get("zoneName"));

                int yg = 0, wg = 0, sl = 0, yk = 0, sb = 0, xb = 0, gwdl = 0, hwds = 0, cfzd = 0, qbxh = 0, rqzj = 0;
                //log.info("疑似火警 同一建筑同一防火分区[fireInfoQuery]" + sT + eT);
                QueryWrapper queryWrapper = new QueryWrapper<FireInfo>();
                queryWrapper.select("id","dev_type","dev_name","point_code","point_desc","fire_type","fire_type_str","first_time","execute_result","check_img","point_id","zone_id","zone_name");
                queryWrapper.eq("building_id", baseBuilding.getId());
                queryWrapper.eq("zone_name", fireInfoMap.get("zoneName"));
                queryWrapper.gt("last_time", startTime);
                queryWrapper.le("last_time", nowTime);
                queryWrapper.ne("building_status_str", "调试");
                queryWrapper.ne("building_status_str", "特批");
                queryWrapper.ne("building_status_str", "全覆盖模式");
                if (StringUtils.equals("0", isOpen)) {
                    queryWrapper.ne("building_status_str", "维保");
                    queryWrapper.ne("building_status_str", "检查模式");
                }

                List<FireInfo> fireInfos = fireInfoMapper.selectList(queryWrapper);
                //log.info("--建筑{}fireInfoMapper.selectList查询结果为{}--查询条件为【{}】【{}】【{}】",  baseBuilding.getId(), fireInfos.size(), sT, eT, isOpen);
                if (fireInfos != null && fireInfos.size() > 0) {
                    //log.info("疑似火警 同一防火分区 查询出的数据--->" + baseBuilding.getId() + ":" + fireInfos.size());
                } else {
                    booleans.add(false);
                    //log.info("--建筑{}fireInfoMapper.selectList查询结果为{}--查询条件为【{}】【{}】【{}】",  baseBuilding.getId(), fireInfos.size(), sT, eT, isOpen);
                    return;
                }
                //循环报警信息
                Iterator<FireInfo> fireInfoIterator = fireInfos.iterator();
                while (fireInfoIterator.hasNext()) {
                    FireInfo fireInfo = fireInfoIterator.next();
                    if (StringUtils.equals(fireInfo.getDevType(), "YG") || StringUtils.equals(fireInfo.getDevType(), "DPQ")) {
                        //烟感
                        yg++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "WG")) {
                        //温感
                        wg++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "SL")) {
                        //水流
                        sl++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "YK")) {
                        //压开
                        yk++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "SB")) {
                        //手报
                        sb++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "XHSBJ")) {
                        //消报
                        xb++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "DYQQ")) {
                        //感温电缆
                        gwdl++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "HWDS")) {
                        //红外对射
                        hwds++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "CZM")) {
                        //厨房自动灭火
                        cfzd++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "SP") || StringUtils.equals(fireInfo.getDevType(), "PLB") || StringUtils.equals(fireInfo.getDevType(), "SPHJ")) {
                        //起泵信号
                        qbxh++;
                    } else if (StringUtils.equals(fireInfo.getDevType(), "RQZJ") || StringUtils.equals(fireInfo.getDevType(), "ROTT") || StringUtils.equals(fireInfo.getDevType(), "RQMJ")) {
                        //燃气报警
                        rqzj++;
                    } else {
                        fireInfoIterator.remove();
                    }
                }
                BigDecimal result =
                        new BigDecimal(0).add(new BigDecimal(suspectedFire.getModeSmoke()).multiply(new BigDecimal(yg)))//+烟感
                                .add(new BigDecimal(suspectedFire.getModeSense()).multiply(new BigDecimal(wg)))//+温感
                                .add(new BigDecimal(suspectedFire.getModeWater()).multiply(new BigDecimal(sl)))//+水流
                                .add(new BigDecimal(suspectedFire.getModePress()).multiply(new BigDecimal(yk)))//+压开
                                .add(new BigDecimal(suspectedFire.getModeHand()).multiply(new BigDecimal(sb)))//+手报
                                .add(new BigDecimal(suspectedFire.getModeDispel()).multiply(new BigDecimal(xb)))//+消报
                                .add(new BigDecimal(suspectedFire.getModeCable()).multiply(new BigDecimal(gwdl)))//+感温电缆
                                .add(new BigDecimal(suspectedFire.getModeInfrared()).multiply(new BigDecimal(hwds)))//+红外对射
                                .add(new BigDecimal(suspectedFire.getModeAutomatic()).multiply(new BigDecimal(cfzd)))//+厨房自动灭火
                                .add(new BigDecimal(suspectedFire.getModePump()).multiply(new BigDecimal(qbxh))
                                        .add(new BigDecimal(suspectedFire.getModeRqzj()).multiply(new BigDecimal(rqzj))
                                        ));//+起泵信号
                //log.info("--计算对比查询的数据和规则值开始，建筑id为{}，规则id为{}，查询值为{}，规则值为{}--", baseBuilding.getId(), suspectedFire.getId(), result, modeGtVal);
                if (result.compareTo(new BigDecimal(modeGtVal)) <= 1) {
                    //log.info("疑似火警 同一建筑同一防火分区数据为[fireInfo]--->" + fireInfos);
                    //log.info("疑似火警 同一建筑同一防火分区数据计算为[fireInfo]--->" + baseBuilding.getId() + "::::::" + yg + "-" + wg + "-" + sl + "-" + yk + "-" + sb + "-" + xb + "-" + gwdl + "-" + hwds + "-" + cfzd + "-" + qbxh);
                }
                //log.info("疑似火警 同一建筑同一防火分区计算结果--->" + baseBuilding.getId() +":"+ result);
                if (result.compareTo(new BigDecimal(modeGtVal)) > -1) {
                    flg = true;
                    //作用：插入接警中心
                    fireInfosSet.addAll(fireInfos);
                    //作用：设置redis
                    zoneNames.add(fireInfoMap.get("zoneName"));
                    //log.info("疑似火警 同一建筑统一防火分区数据为--->" + baseBuilding.getId() + ":" + fireInfoMap.get("zoneName"));
                }
            }

        }
        //作用：判断该条逻辑是否满足
        booleans.add(flg);
    }

    /**
     * @param day      天
     * @param hour     小时
     * @param min      分钟
     * @param date     Date格式日期
     * @param type     0过去，1未来
     * @param timeType 时间格式
     * @return
     */
    public String getDatebyTime(Double day, Double hour, Double min, String date, String type, String timeType) {
        long time = 0;
        try {
            Long second = Double.valueOf((day * 24 * 60 * 60 + hour * 60 * 60 + min * 60) * 1000).longValue();
            Long dateTime = new SimpleDateFormat(timeType).parse(date).getTime();

            if ("0".equals(type)) {
                time = dateTime - second;

            } else if ("1".equals(type)) {
                time = dateTime + second;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new SimpleDateFormat(timeType).format(new Date(time));
    }
}
