package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.utils.ExceptionUtil;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.pojo.dto.MaintenanceScopeTestDto;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.service.maintenance.MaintenanceScopeTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * <p>
 * 维保计划范围表前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-11-08
 */
@Slf4j
@Api(value = "维保计划范围表", tags = "维保计划范围表")
@RestController
@RequestMapping("/maintenanceScopeTest")
public class MaintenanceScopeTestController {

    private String message;

    @Resource
    MaintenanceScopeTestService service;

    /**
     * APP根据计划id和消防系统编码获取维保范围
     *
     * @param maintenanceScopeTestDto
     * @return JsonResult
     */

    @PostMapping(value = "getScopeTest")
    public JsonResult getScopeTest(@RequestBody @Validated(Select.class) MaintenanceScopeTestDto maintenanceScopeTestDto) throws Exception {
        try {
            return JsonResult.Success().setData(service.getScopeTest(maintenanceScopeTestDto));
        } catch (Exception e) {
            message = "获取失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    /**
     * WEB根据计划id和消防系统编码获取维保范围
     *
     * @param maintenanceScopeTestDto
     * @return JsonResult
     */
    @PostMapping(value = "getWebScopeTest")
    public JsonResult getWebScopeTest(@RequestBody MaintenanceScopeTestDto maintenanceScopeTestDto) throws Exception {
        try {
            return JsonResult.Success().setData(service.getWebScopeTest(maintenanceScopeTestDto));
        } catch (Exception e) {
            message = "获取失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    /**
     * WEB根据维保范围查看点位
     *
     * @param scopeTestId
     * @return JsonResult
     */
    @GetMapping(value = "getWebScopeTestPoint")
    public JsonResult getWebScopeTestPoint(String scopeTestId, String scopeTestName,@RequestParam(required = false) String effectiveTime) throws Exception {
        try {
            return JsonResult.Success().setData(service.getWebScopeTestPoint1(scopeTestId, scopeTestName, effectiveTime));
        } catch (Exception e) {
            message = "获取失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    /**
     * WEB根据计划ID和设备类型获取设备信息
     *
     * @param
     * @return JsonResult
     */

    @PostMapping(value = "selectDeviceInfoPage")
    public JsonPageResult selectDevicePointInfoPage(@RequestBody QueryData queryData) throws Exception {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.selectDevicePointInfoPage(queryData);
            if (page != null) {
                jsonResult.setPageData(page);
            } else {
                JsonPageResult.Fail("当前月无生成计划");
            }
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            //jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage("当月无生成计划或正在生成计划中");
        }
        return jsonResult;
    }

    /**
     * APP根据计划ID和设备类型获取设备统计
     *
     * @param maintenancePlanSysDto
     * @return JsonResult
     */
    @PostMapping(value = "getDeviceStatistics")
    public JsonResult getDeviceStatistics(@RequestBody MaintenancePlanSysDto maintenancePlanSysDto) throws Exception {
        return JsonResult.Success("返回数据成功!").setData(service.getDeviceStatistics(maintenancePlanSysDto));
    }

    /**
     * APP维保计划详情 设备类型级数据
     */
    @PostMapping("getScopeTestDetails")
    public JsonResult getScopeTestDetails(@RequestBody MaintenancePlanSysDto maintenancePlanSysDto) {
        return JsonResult.Success("返回数据成功!").setData(service.getScopeTestDetails(maintenancePlanSysDto));
    }

    /**
     * APP维保计划详情 设备点位级别
     */
    @PostMapping("getScopeTestPoint/{scopeTestId}")
    public JsonResult getScopeTestPoint(@PathVariable String scopeTestId) {
        return JsonResult.Success("返回数据成功!").setData(service.getScopeTestPoint(scopeTestId));
    }


    /**
     * APP查看测试|联动计划详情 点位数量信息
     */
    @PostMapping("getPointNum/{appointmentApplyId}")
    public JsonResult getPointNum(@PathVariable String appointmentApplyId) {
        return JsonResult.Success("返回数据成功!").setData(service.getPointNum(appointmentApplyId));
    }

    /**
     * APP查看测试|联动计划详情 查看系统类型
     *
     * @param
     * @return JsonResult
     */

    @PostMapping(value = "getTestFireproofSysName/{appointmentApplyId}")
    public JsonResult getTestFireproofSysName(@PathVariable String appointmentApplyId) throws Exception {
        return JsonResult.Success("返回数据成功!").setData(service.getTestFireproofSysName(appointmentApplyId));
    }


    /**
     * APP查看测试|联动计划详情 设备点位信息
     *
     * @return JsonResult
     * @paramdeviceInfoResultMap
     */

    @PostMapping(value = "getTestDeviceInfo/{appointmentApplyId}")
    public JsonResult getTestDeviceInfo(@PathVariable String appointmentApplyId, String fireproofSysName) throws Exception {
        return JsonResult.Success("返回数据成功!").setData(service.getTestDeviceInfo(appointmentApplyId, fireproofSysName));
    }

    /**
     * 导出excel
     *
     * @return
     */
    @ResponseBody
    @MethodDefine(title = "导出EXCEL", path = "/exportscope", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "导出测试项", varName = "detail")})
    @PostMapping("/exportscope")
    @ApiOperation("导出测试项")
    public void exportscope(@RequestBody MaintenancePlanSysDto maintenancePlanSysDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        service.exportscope(maintenancePlanSysDto, request, response);
    }


}
