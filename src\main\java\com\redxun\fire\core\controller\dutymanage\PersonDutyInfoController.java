package com.redxun.fire.core.controller.dutymanage;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.entity.OsUser;
import com.redxun.fire.core.service.dutymanage.DutyPersonImportProcessService;
import com.redxun.fire.core.service.dutymanage.PersonDutyInfoService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @createTime 2025/1/21
 * @description
 */
@RestController
@RequestMapping("/personDutyInfo")
@Slf4j
public class PersonDutyInfoController {

    @Autowired
    PersonDutyInfoService personDutyInfoService;

    @Autowired
    DutyPersonImportProcessService dutyPersonImportProcessService;

    @Autowired
    IOsUserService userService;

    /**
     * 导入排班表
     *
     * @return
     */
    @PostMapping("/importRoaster")
    public JsonResult<String> importRoaster(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws IOException {
        return personDutyInfoService.importRoaster(request, jsonObject);
    }

    /**
     * 排班列表
     *
     * @return {@link JsonResult}
     */
    @PostMapping("/getDutyPersonList")
    public JsonResult list(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        // 按月份查找该建筑物下所有的值班管理信息
        return personDutyInfoService.listAllDutyRoaster(jsonObject, request);
    }


    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return personDutyInfoService.flowEvent(dataJson);
    }

    @PostMapping("/approvePass")
    public JsonResult approvePass(@RequestBody JSONObject jsonObject) {
        try {
            return personDutyInfoService.approvePass(jsonObject);
        } catch (Exception e) {
            log.error("值班人员表审批通过异常：", e);
            return JsonResult.getFailResult(e.getMessage());
        }
    }

    /**
     * @return {@link JsonResult}
     */
    @GetMapping("/getImportProcessDetails")
    public JsonResult getImportProcessDetails(@RequestParam String processId) {
        JsonResult result = JsonResult.Success();
        final val byId = dutyPersonImportProcessService.getById(processId);
        if (ObjectUtil.isNotEmpty(byId)) {
            final val one = userService.getOne(new LambdaQueryWrapper<OsUser>().eq(OsUser::getWztUserId, byId.getCreateUserId()));
            byId.setCreateUserName(one.getFullname());
        }
        result.setData(byId);
        return result;
    }

    @GetMapping("/judgePermission")
    public JsonResult judgePermission(HttpServletRequest request) {
        return personDutyInfoService.judgePermission(request);
    }


}
