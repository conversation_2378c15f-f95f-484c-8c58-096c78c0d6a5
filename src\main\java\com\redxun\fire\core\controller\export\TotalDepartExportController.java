package com.redxun.fire.core.controller.export;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.MisReportes;
import com.redxun.fire.core.pojo.vo.ConsoleExportAllVo;
import com.redxun.fire.core.pojo.vo.DeviceLedgerInfoScreenVo;
import com.redxun.fire.core.pojo.vo.DeviceLedgerInfoVo;
import com.redxun.fire.core.pojo.vo.MisReporteExportAllVo;
import com.redxun.fire.core.service.other.TotalDepartExportService;
import com.redxun.fire.core.utils.ExcelUtil;
import com.redxun.fire.core.utils.ServletsUtil;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @program jpaas
 * @description 总部导出
 * @create 2021-02-03 10:28
 **/

@RestController
@RequestMapping("/totalDepart")
@SuppressWarnings("unchecked")
public class TotalDepartExportController {


    @Resource
    private TotalDepartExportService totalDepartExportService;


    /**
     * 反复误报导出列表
     *
     * @param request
     * @return
     */
    @PostMapping("/queryMisReportesList")
    public JsonResult queryMisReportesList(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        List<MisReportes> misReportes = totalDepartExportService.queryMisReportesList(request,param);
        JsonResult<List<MisReportes>> result = JsonResult.Success();
        result.setData(misReportes);
        return result;
    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/misReportes/export")
    @ResponseBody
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map param = ServletsUtil.getParameters(request);
        //查询反复误报点位
        List<MisReportes> list = totalDepartExportService.queryMisReportesList(request,param);
        List<MisReporteExportAllVo> misReporteExportAllVos = new ArrayList<>();
        //设置单元格值
        list.forEach(misReportes -> {
            MisReporteExportAllVo misReporteExportAllVo = new MisReporteExportAllVo();
            misReporteExportAllVo.setBuildingName(misReportes.getBuildingName());
            misReporteExportAllVo.setPointCode(misReportes.getPointCode());
            misReporteExportAllVo.setPointDesc(misReportes.getPointDesc());
            misReporteExportAllVo.setSevenDayCount(misReportes.getSevenDayCount());
            misReporteExportAllVo.setFourteenDayCount(misReportes.getFourteenDayCount());
            misReporteExportAllVo.setDevTypeName(misReportes.getDevTypeName());
            misReporteExportAllVo.setReportTime(misReportes.getReportTime());
            misReporteExportAllVo.setSeriesDays(misReportes.getSeriesDays());
            misReporteExportAllVos.add(misReporteExportAllVo);
        });
        ExcelUtil.writeExcel(response, misReporteExportAllVos, "反复误报导出数据", "sheet1", new ConsoleExportAllVo());
    }

    /**
     * 添加excel单元格内容并居中
     *
     * @author: lf
     * @param: workbook
     * @return: org.apache.poi.hssf.usermodel.HSSFCell
     */
    public HSSFCell cellCenter(HSSFWorkbook workbook, HSSFCell cell, String str, int rowIndx) {
        //单元格样式
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);//居中
        if (rowIndx == 0) {
            HSSFFont font = workbook.createFont();
            font.setBold(true);//加粗
            style.setFont(font);
        }
        //设置值和样式
        cell.setCellStyle(style);
        cell.setCellValue(str);
        return cell;
    }

    /**
     * 设备台账列表
     *
     * @param request
     * @return
     */
    @PostMapping("/listDeviceLedgers")
    public JsonResult listDeviceLedgers(HttpServletRequest request,@RequestBody Map param) {
        JsonResult result = JsonResult.Success();
        List<DeviceLedgerInfoVo> misReportes = totalDepartExportService.listDeviceLedgers(request,param);
        result.setData(misReportes);
        return result;
    }

    /**
     * 通过当前建筑物id或当前登录用户(建筑物id为-1)来进行设备台账信息的导出
     *
     * @return JsonResult
     */
    @GetMapping("/deviceLedgers/export")
    public void export(HttpServletResponse response, HttpServletRequest request) throws Exception {
        Map param = ServletsUtil.getParameters(request);
        totalDepartExportService.export(request,param, response);
    }


    /**
     * 大屏获取报警设备状态情况
     *
     * @return
     */
    @GetMapping("/listDeviceLedgersForScreen")
    public JsonResult listDeviceLedgersForScreen() {
        JsonResult result = JsonResult.Success();
        List<DeviceLedgerInfoScreenVo> misReportes = totalDepartExportService.listDeviceLedgersForScreen();
        result.setData(misReportes);
        return result;
    }
}
