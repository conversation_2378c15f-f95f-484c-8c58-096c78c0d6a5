package com.redxun.fire.core.service.alarm.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.api.feign.EquipmentClient;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.tool.StringUtils;
import com.redxun.common.utils.ExcelUtil;
import com.redxun.fire.core.consts.CloseStoreRedisConstants;
import com.redxun.fire.core.consts.ConstantUtil;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.influxdb.dao.InfluxDataDao;
import com.redxun.fire.core.influxdb.entity.CloseControlInfluxData;
import com.redxun.fire.core.influxdb.entity.CloseInfluxData;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.StatCloseStoreExpectionMapper;
import com.redxun.fire.core.mapper.StatCloseStoreRealMapper;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.pojo.base.TablePageData;
import com.redxun.fire.core.pojo.qo.CloseStorePointQO;
import com.redxun.fire.core.pojo.qo.MerchantQO;
import com.redxun.fire.core.pojo.vo.CloseStorePointVO;
import com.redxun.fire.core.pojo.vo.CloseStoreVPointVo;
import com.redxun.fire.core.pojo.vo.MetadataCodeVO;
import com.redxun.fire.core.service.IMetadataCodeService;
import com.redxun.fire.core.service.alarm.CloseStoreService;
import com.redxun.fire.core.service.alarm.ICloseStoreHostService;
import com.redxun.fire.core.service.alarm.ICloseStoreTimerService;
import com.redxun.fire.core.service.alarm.IStatCloseStoreRealService;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.building.IMidMerchantService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.utils.*;
import com.redxun.fire.core.utils.okhttp.OkHttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024-1-9 16:12
 */
@Slf4j
@Service
public class CloseStoreServiceImpl implements CloseStoreService {

    @Resource
    private IBaseDevicePointService iBaseDevicePointService;
    @Resource
    private IBaseBuildingService baseBuildingService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    BaseDevicePointMapper baseDevicePointMapper;
    @Resource
    IStatCloseStoreRealService iStatCloseStoreRealService;

    @Resource
    IMidMerchantService midMerchantService;
    @Resource
    private ICloseStoreTimerService closeStoreTimerService;
    @Resource
    StatCloseStoreExpectionMapper statCloseStoreExpectionMapper;
    @Resource
    StatCloseStoreRealMapper statCloseStoreRealMapper;
    @Resource
    InfluxDataDao influxDataDao;
    @Resource
    ICloseStoreHostService closeStoreHostService;
    @Resource
    private IBaseBuildingFloorService iBaseBuildingFloorService;

    @Resource
    IMetadataCodeService metadataCodeService;

    @Resource
    private EquipmentClient equipmentClient;
    @Autowired
    private CloseStoreInitializationMapper closeStoreInitializationMapper;
    @Autowired
    private CloseStoreModuleMapper closeStoreModuleMapper;
    @Autowired
    private DeviceMonitorInfoMapper deviceMonitorInfoMapper;


    @Resource
    IBaseBuildingFloorService baseBuildingFloorService;

    // 添加线程池成员变量
    private ScheduledExecutorService timer = new ScheduledThreadPoolExecutor(2);


    private BaseDevicePoint buildDevicePoint(CloseStorePointQO qo) {
        BaseBuilding baseBuilding = getBuildingById(qo);
        BaseBuildingFloor buildingFloor = getBuildingFloorById(qo);
        String dtuNo = formatDtuNumber(qo.getDtuNo());
        String devTypeName = resolveDeviceTypeName(qo.getDevTypeCat());
        String joinCode = qo.getJoinCode();
        BaseDevicePoint devicePoint = new BaseDevicePoint();
        devicePoint.setHostId(dtuNo);
        devicePoint.setDid(joinCode.split("_")[1]);
        devicePoint.setJoinCode(joinCode);
        devicePoint.setFloorId(qo.getFloorId());
        devicePoint.setBuildingId(baseBuilding.getId());
        devicePoint.setBuildName(baseBuilding.getBuildingName());
        devicePoint.setDeviceNo(qo.getDeviceNo());
        devicePoint.setWztPointId(qo.getWztPointId());
        devicePoint.setPositionDesc(qo.getPointName());
        devicePoint.setDeviceName(devTypeName + qo.getDeviceNo());
        devicePoint.setJoinName(joinCode);
        devicePoint.setHasCompletion("0");
        devicePoint.setDevTypeCode("BDJC");
        devicePoint.setDevTypeName(devTypeName);
        devicePoint.setPointDesc(qo.getPointDesc());
        devicePoint.setPointNumber(dtuNo + "_" + qo.getDeviceNo());
        qo.setFloorId(buildingFloor.getId());
        qo.setFloorName(buildingFloor.getFloorName());
        qo.setFloor(buildingFloor.getFloor());
        qo.setWztFloorId(buildingFloor.getWztFloorId());
        devicePoint.setFloorName(buildingFloor.getFloorName());
        devicePoint.setFloor(buildingFloor.getFloor());
        devicePoint.setWztFloorId(buildingFloor.getWztFloorId());
        devicePoint.setSimNum(qo.getSimNum());
        devicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_CLOSE_STORE);
        devicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
        devicePoint.setDtuNo(dtuNo);
        devicePoint.setCreateTime(new Date());
        devicePoint.setUpdateTime(new Date());
        devicePoint.setMidMerchantId(qo.getMidMerchantId());
        devicePoint.setExpiredTime(qo.getExpiredTime());

        return devicePoint;
    }

    //格式化dtu编号
    private String formatDtuNumber(String dtuNo) {
        dtuNo = dtuNo.trim();
        if (dtuNo.length() > 8) {
            return String.format("%012d", Long.valueOf(dtuNo));
        } else {
            return String.format("%08d", Integer.parseInt(dtuNo));
        }
    }

    private String resolveDeviceTypeName(String devTypeCat) {
        if ("1".equals(devTypeCat)) {
            return "闭店监测设备B";
        } else if ("2".equals(devTypeCat)) {
            return "闭店监测设备A2";
        } else {
            return "闭店监测设备";
        }
    }

    //获取建筑
    private BaseBuilding getBuildingById(CloseStorePointQO qo) {
        String buildingId = qo.getBuildId();
        BaseBuilding baseBuilding = null;
        if(StrUtil.isBlank(buildingId)){
            baseBuilding = baseBuildingService.getBuildIdByProjectId(qo.getProjectId());
        }else{
            baseBuilding = baseBuildingService.getBuildingInfoById(buildingId);
        }
        if(null == baseBuilding){
            throw new RuntimeException("该项目不存在");
        }
        return baseBuilding;

    }

    //获取楼层
    private BaseBuildingFloor getBuildingFloorById(CloseStorePointQO qo) {
        String floorId = qo.getFloorId();
        List<BaseBuildingFloor> buildingFloors = new ArrayList<>();
        if(StrUtil.isBlank(floorId) ){
            buildingFloors = baseBuildingFloorService.list( new LambdaQueryWrapper<BaseBuildingFloor>().eq(BaseBuildingFloor::getWztFloorId, qo.getWztFloorId()));
        }else{
            buildingFloors = baseBuildingFloorService.list( new LambdaQueryWrapper<BaseBuildingFloor>().eq(BaseBuildingFloor::getId, floorId));
        }

        if(buildingFloors.isEmpty()){
            throw new RuntimeException("该楼层ID不存在");
        }

        return buildingFloors.get(0);
    }

    //校验必填参数
    public JsonResult validate(CloseStorePointQO qo) {
        if (StringUtils.isEmpty(qo.getBuildId()) && StringUtils.isEmpty(qo.getProjectId())) {
            return JsonResult.Fail("请输入项目id或建筑物id");
        }

        if (StringUtils.isEmpty(qo.getFloorId()) && StringUtils.isEmpty(qo.getWztFloorId())) {
            return JsonResult.Fail("请输入中台楼层ID或消防楼层ID");
        }

        if (StringUtils.isEmpty(qo.getDtuNo())) {
            return JsonResult.Fail("DTU编号不能为空");
        }

        if (StringUtils.isEmpty(qo.getJoinCode())) {
            return JsonResult.Fail("接入点不能为空");
        }

        if (StringUtils.isEmpty(qo.getDeviceNo())) {
            return JsonResult.Fail("设备编码不能为空");
        }
        return JsonResult.Success();
    }

    private JsonResult exists(BaseDevicePoint devicePoint){
        //检查接入点唯一
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_CLOSE_STORE);
        queryMap.put("host_id", devicePoint.getHostId());
        queryMap.put("did", devicePoint.getDid());
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null != pointList && pointList.size() > 0) {
            return JsonResult.Fail("DTU编号:" + devicePoint.getDtuNo() + ","+pointList.get(0).getBuildName()+"已注册，不能重复添加");
        }
        return JsonResult.Success();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult pointRegister(CloseStorePointQO qo) {
        // Step 1: 校验输入参数
        JsonResult validateResult = validate(qo);
        if (!validateResult.getSuccess()) {
            return JsonResult.getFailResult(validateResult.getMessage());
        }
        // Step 2: 构建设备对象
        BaseDevicePoint devicePoint = buildDevicePoint(qo);

        // Step 3: 检查是否重复注册
        JsonResult existsResult = exists(devicePoint);
        if (!existsResult.getSuccess()) {
            return JsonResult.getFailResult(existsResult.getMessage());
        }

        Boolean flag = false;
        try {
            flag = iBaseDevicePointService.savePointInfo(devicePoint);
            if("闭店监测设备B".equals(devicePoint.getDevTypeName())){
                //初始化数据
                closeStoreBInit(devicePoint);
            }
            midMerchantService.updateInfo(devicePoint.getMidMerchantId(), qo.getMidMerchantTypeCode(),qo.getMidMerchantTypeName(),qo.getBizType());
        }catch (Exception e){
            return JsonResult.getFailResult("点位重复无法新增！");
        }
        if (flag) {
            // 构建保存监测回路信息
            saveDeviceMonitorInfo(qo, devicePoint);
            //关联设备建筑
            closeStoreHostService.relatedBuilding(devicePoint,devicePoint.getBuildingId());
            // 添加设备上传信息缓存
            setPointInfoToRedis(devicePoint,qo);
            // 更新设备统计信息
            editStatCloseStoreTotal(devicePoint.getBuildingId(), 1,devicePoint.getDevTypeName());
            //设备闭店时间处理
            closeStoreTimerService.selectPointTimerAndUpdateQueue(devicePoint,qo.getBizType());
            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationContent("新增" + devicePoint.getBuildName() + "的闭店监测设备设备编号" +
                    devicePoint.getDeviceNo());
            journalizing.setOperationTypeCode(LogTypeEnums.CLOSE_STORE.getType());
//            journalizingService.setLogInfo(journalizing);
            return new JsonResult(true,devicePoint.getId(),"保存成功");
        }
        return JsonResult.getFailResult("保存失败");
    }
    /**
     * 保存设备监测信息
     * @param qo
     * @param devicePoint
     */
    private  void saveDeviceMonitorInfo(CloseStorePointQO qo, BaseDevicePoint devicePoint) {
        //删除旧的
        deviceMonitorInfoMapper.delete(new LambdaQueryWrapper<DeviceMonitorInfo>().eq(DeviceMonitorInfo::getBasePointId, devicePoint.getId()));

        //新增新的
        DeviceMonitorInfo deviceMonitorInfo = new DeviceMonitorInfo();
        deviceMonitorInfo.setBasePointId(devicePoint.getId());
        deviceMonitorInfo.setDeviceRealName(qo.getDeviceRealName());
        deviceMonitorInfo.setMerchantManager(qo.getMerchantManager() == null ? "" : qo.getMerchantManager());
        deviceMonitorInfo.setManagerPhone(qo.getManagerPhone() == null ? "" : qo.getManagerPhone());
        deviceMonitorInfo.setPhoneDecrypt(qo.getPhoneDecrypt() == null ? "" : qo.getPhoneDecrypt());
        deviceMonitorInfo.setLoopType( qo.getLoopType());
        deviceMonitorInfo.setCloseStoreRemindTime(qo.getCloseStoreRemindTime());
        deviceMonitorInfo.setCreateTime(new Date());
        deviceMonitorInfo.setUpdateTime(new Date());

        deviceMonitorInfoMapper.insert(deviceMonitorInfo);

    }


    /**
     * 闭店B设置初始化数据
     * @param devicePoint
     * @return
     */
    private void closeStoreBInit(BaseDevicePoint devicePoint) {
        CloseStoreInitialization closeStoreInitialization = new CloseStoreInitialization();
        closeStoreInitialization.setPointId(devicePoint.getId());
        closeStoreInitialization.setBuildId(devicePoint.getBuildingId());
        closeStoreInitializationMapper.insert(closeStoreInitialization);

        //初始化数据
        CloseStoreModule closeStoreModule = new CloseStoreModule();
        closeStoreModule.setPointId(devicePoint.getId());
        closeStoreModule.setInitializationId(closeStoreInitialization.getId());
        closeStoreModule.setHasCompletion("0");
        closeStoreModule.setModuleInitializationStatus("0");
        closeStoreModule.setDataManualConfiguration("0");
        closeStoreModuleMapper.insert(closeStoreModule);
    }


    @Override
    public CloseStorePointVO getPointInfoById(CloseStorePointQO qo) {
        String pointId = qo.getId();
        if (StringUtils.isEmpty(pointId)) {
            throw new RuntimeException("点位id为空");
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", pointId);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null == pointList || pointList.size() < 1 || null == pointList.get(0)) {
            throw new RuntimeException("该点位信息查询为空");
        }
        BaseDevicePoint baseDevicePoint = pointList.get(0);
        CloseStorePointVO vo = BeanCopyUtil.copyBeanClass(baseDevicePoint, CloseStorePointVO.class);
        MidMerchant midMerchant = midMerchantService.getById(vo.getMidMerchantId());
        vo.setMidMerchantCode(midMerchant.getMerchantCode());
        vo.setMidMerchantName(midMerchant.getMerchantName());
        vo.setMidMerchantTypeCode(midMerchant.getTypeCode());
        vo.setMidMerchantTypeName(midMerchant.getTypeName());
        vo.setBuildId(baseDevicePoint.getBuildingId());
        vo.setBizType(midMerchant.getBizType());

        // 获取设备监测信息
        DeviceMonitorInfo deviceMonitorInfo = getDeviceMonitorInfo(baseDevicePoint);
        if (deviceMonitorInfo != null) {
            vo.setDeviceRealName(deviceMonitorInfo.getDeviceRealName());
            vo.setMerchantManager(deviceMonitorInfo.getMerchantManager() == null ? "" : deviceMonitorInfo.getMerchantManager());
            vo.setManagerPhone(deviceMonitorInfo.getManagerPhone() == null ? "" : deviceMonitorInfo.getManagerPhone());
            vo.setPhoneDecrypt(deviceMonitorInfo.getPhoneDecrypt() == null ? "" : deviceMonitorInfo.getPhoneDecrypt());
            vo.setLoopType(deviceMonitorInfo.getLoopType());
            vo.setCloseStoreRemindTime(deviceMonitorInfo.getCloseStoreRemindTime());
        }

        return vo;
    }

    /**
     * 从 Redis 或数据库中获取 DeviceMonitorInfo
     *
     * @param baseDevicePoint 设备点对象
     * @return DeviceMonitorInfo 对象或 null
     */
    private DeviceMonitorInfo getDeviceMonitorInfo(BaseDevicePoint baseDevicePoint) {
        return deviceMonitorInfoMapper.selectOne(new LambdaQueryWrapper<DeviceMonitorInfo>()
                .eq(DeviceMonitorInfo::getBasePointId, baseDevicePoint.getId()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult editPointInfoById(CloseStorePointQO qo) {
        String id = qo.getId();
        if (StringUtils.isEmpty(id)) {
            return JsonResult.getFailResult("点位id不能为空");
        }
        //校验
        validate(qo);
        //生成点位信息
        BaseDevicePoint baseDevicePoint = buildDevicePoint(qo);
        baseDevicePoint.setId(id);

        //校验是否已存在
        BaseDevicePoint oldPoint = baseDevicePointMapper.selectById(id);
        if(!oldPoint.getHostId().equals(baseDevicePoint.getDtuNo()) && !oldPoint.getDid().equals(baseDevicePoint.getDid())) {
            exists(baseDevicePoint);
        }

        boolean flag = false;
        try {
            flag = iBaseDevicePointService.editPressurePointInfoById(baseDevicePoint);
            //根据商户类型更新商户数据
            midMerchantService.updateInfo(baseDevicePoint.getMidMerchantId(),qo.getMidMerchantTypeCode(),qo.getMidMerchantTypeName(),qo.getBizType());
            // 构建保存监测回路信息
            saveDeviceMonitorInfo(qo, baseDevicePoint);
        }catch (Exception e){
            return JsonResult.getFailResult("点位更新异常！");
        }
        if (flag) {
            //关联设备建筑
            closeStoreHostService.relatedBuilding(baseDevicePoint,baseDevicePoint.getBuildingId());
            //更新redis
            String hostId = oldPoint.getHostId();
            String did = oldPoint.getDid();
            updateRedis(hostId, did, baseDevicePoint, qo);
            //更新商户统计
            List<StatCloseStoreReal> list = iStatCloseStoreRealService.list(new QueryWrapper<StatCloseStoreReal>().
                    eq("building_id", baseDevicePoint.getBuildingId()));
            if (list != null && list.size() > 0) {
                StatCloseStoreReal real = list.get(0);
                Integer closeStoreCount = statCloseStoreRealMapper.selectCloseStoreCountByBuildingId(baseDevicePoint.getBuildingId());
                real.setCloseStoreCount(closeStoreCount);
                iStatCloseStoreRealService.updateById(real);
            }
            //设备闭店时间处理
            closeStoreTimerService.selectPointTimerAndUpdateQueue(baseDevicePoint,qo.getBizType());

            Journalizing journalizing = new Journalizing();
            String result = "编辑" + baseDevicePoint.getBuildName() + "的闭店监测设备设备编号" + baseDevicePoint.getDeviceNo() + ":";
            result+= qo.toString();
            result = result.substring(0, result.length() - 1);
            journalizing.setOperationContent(result);
            journalizing.setOperationTypeCode(LogTypeEnums.CLOSE_STORE.getType());
            //journalizingService.setLogInfo(journalizing);
            return JsonResult.Success();
        }
        return JsonResult.getFailResult("保存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult deletePointInfo(CloseStorePointQO qo) {
        String id = qo.getId();
        if (StringUtils.isEmpty(id)) {
            return JsonResult.getFailResult("点位id不能为空");

        }
        BaseDevicePoint baseDevicePoint = iBaseDevicePointService.getDevicePointById(id);
        if (null == baseDevicePoint) {
            return JsonResult.getFailResult("该点位不存在");
        }
        boolean date = iBaseDevicePointService.deletePointInfo(id);
        if(date){
            String result = "删除" + baseDevicePoint.getBuildName();
            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.CLOSE_STORE.getType());
            String key = CloseStoreRedisConstants.getPointInfoKey(baseDevicePoint.getBuildingId(), baseDevicePoint.getId());
            Map<String, Object> deleteMap = new HashMap<>();
            deleteMap.put("point_id", baseDevicePoint.getId());
            statCloseStoreExpectionMapper.deleteByMap(deleteMap);
            deleteStatCloseStoreTotalByPoint(key, baseDevicePoint.getBuildingId(), baseDevicePoint.getDevTypeName());
            deleteRedis(baseDevicePoint.getBuildingId(),baseDevicePoint,baseDevicePoint.getSuperType());
            result += ("的闭店监测设备设备编号" + baseDevicePoint.getDeviceNo());
            journalizing.setOperationTypeCode(LogTypeEnums.CLOSE_STORE.getType());

            journalizing.setOperationContent(result);
            return JsonResult.Success();
        }

        return JsonResult.getFailResult("保存失败");
    }

    private void deleteStatCloseStoreTotalByPoint(String key, String buildingId, String devTypeName) {
        //更新redis
        Object o = redisUtils.get(key);
        if (o != null) {
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(o), FarEastoneCache.class);
            String deviceStatus = farEastoneCache.getDeviceStatus();
            String faultStatus = farEastoneCache.getFaultStatus();
            String electricalStatus = farEastoneCache.getElectricalStatus();

            List<StatCloseStoreReal> list = iStatCloseStoreRealService.list(new QueryWrapper<StatCloseStoreReal>().eq("building_id", buildingId));
            if (list != null && list.size() > 0) {
                StatCloseStoreReal real = list.get(0);
                //正常设备判断
                if ("0".equals(deviceStatus) && "0".equals(faultStatus) && "0".equals(faultStatus)) {
                    Integer i = real.getNormalPoint();
                    real.setNormalPoint(i - 1);
                }
                if ("1".equals(deviceStatus)) {
                    Integer i = real.getOfflinePoint();
                    real.setOfflinePoint(i - 1);
                }
                if ("1".equals(faultStatus)) {
                    Integer i = real.getFaultPoint();
                    real.setFaultPoint(i - 1);
                }
                if ("1".equals(electricalStatus)) {
                    Integer i = real.getElectricalAnomalyPoint();
                    real.setFaultPoint(i - 1);
                }
                Integer current = real.getPointCount();

                if(devTypeName.equals("闭店监测设备")){
                    real.setTypeA(cn.hutool.core.util.ObjectUtil.isNull(real.getTypeA())? 0 :real.getTypeA() - 1);
                }
                if(devTypeName.equals("闭店监测设备B")){
                    real.setTypeB(cn.hutool.core.util.ObjectUtil.isNull(real.getTypeB())? 0 : real.getTypeB() - 1);
                }
                if(devTypeName.equals("闭店监测设备A2")){
                    real.setTypeC(cn.hutool.core.util.ObjectUtil.isNull(real.getTypeC())? 0 : real.getTypeC() - 1);
                }
                real.setPointCount(current - 1);
                Integer closeStoreCount = statCloseStoreRealMapper.selectCloseStoreCountByBuildingId(buildingId);
                real.setCloseStoreCount(closeStoreCount);
                iStatCloseStoreRealService.updateById(real);
            }
        }
        //删除redis数据
        redisUtils.delete(key);
    }

    /**
     * 当新增点位时，得同时往redis中写
     *
     * @param baseDevicePoint
     */
    public void setPointInfoToRedis(BaseDevicePoint baseDevicePoint,CloseStorePointQO closeStorePointQO) {
        handleCloseAToRedis(baseDevicePoint,closeStorePointQO);
    }

    private void handleCloseAToRedis(BaseDevicePoint baseDevicePoint,CloseStorePointQO closeStorePointQO){
        String key = CloseStoreRedisConstants.getPointInfoKey(baseDevicePoint.getBuildingId(), baseDevicePoint.getId());
        //获取旧缓存
        String value = (String)redisUtils.get(key);
        FarEastoneCache farEastoneCache = new FarEastoneCache();
        if(StrUtil.isNotBlank(value)){
            farEastoneCache = FastJSONUtils.toBean(value, FarEastoneCache.class);
            if(closeStorePointQO !=null){
                farEastoneCache.setLoopType(closeStorePointQO.getLoopType());
                farEastoneCache.setCloseStoreRemindTime(closeStorePointQO.getCloseStoreRemindTime());
            }
        }else{
            farEastoneCache.setLastHeartbeatTime("");
            farEastoneCache.setPushTime("");
            //设备状态 0-正常，1-离线
            farEastoneCache.setDeviceStatus("1");
            farEastoneCache.setFaultStatus("0");
            farEastoneCache.setOutageStatus("0");
            farEastoneCache.setElectricalStatus("0");
            farEastoneCache.setSignals("2");
            farEastoneCache.setValue("");
            farEastoneCache.setDeviceType(baseDevicePoint.getDevTypeName());
            farEastoneCache.setAlarmStatus(0);
            farEastoneCache.setBatteryValue("50");
            farEastoneCache.setConstantCurrentStatus("0");
            farEastoneCache.setMutantCurrentStatus("0");
            farEastoneCache.setRecoveryTimeStatus("0");
            if(closeStorePointQO !=null){
                farEastoneCache.setLoopType(closeStorePointQO.getLoopType());
                farEastoneCache.setCloseStoreRemindTime(closeStorePointQO.getCloseStoreRemindTime());
            }
        }
        String hostId = baseDevicePoint.getHostId();
        String did = baseDevicePoint.getDid();
        if (StringUtils.isNotEmpty(key)) {
            redisUtils.set(key, JSON.toJSONString(farEastoneCache));
            //更新设备缓存
            setEquipmentToRedis(hostId, did, baseDevicePoint.getBuildingId(), baseDevicePoint.getId(), baseDevicePoint.getSuperType());
        }
    }

    public void updateRedis(String oldHostId, String oldDid, BaseDevicePoint baseDevicePoint,CloseStorePointQO closeStorePointQO) {
        String oldKey = CloseStoreRedisConstants.getEquipmentKey(oldHostId,Integer.parseInt(oldDid));
        redisUtils.remove(oldKey);
        handleCloseAToRedis(baseDevicePoint,closeStorePointQO);


    }

    public void deleteRedis(String buildingId, BaseDevicePoint baseDevicePoint, String superType) {
        String key = CloseStoreRedisConstants.getEquipmentKey(baseDevicePoint.getHostId(),Integer.parseInt(baseDevicePoint.getDid()));
        redisUtils.remove(key);
    }

    /**
     * @param hostId  设备号
     * @param did 从设备号
     * @param buildId   建筑物id
     * @param id        点位表主键id
     */
    private void setEquipmentToRedis(String hostId, String did, String buildId, String id, String deviceType) {
        String key = CloseStoreRedisConstants.getEquipmentKey(hostId,Integer.parseInt(did));
        String value = buildId + "-" + id;
        redisUtils.set(key, value);
    }

    /**
     * 更新stat_pump_real表中总点位数
     *
     * @param buildId
     * @param num
     */
    private void editStatCloseStoreTotal(String buildId, int num, String devTypeName) {
        Map<String, Object> map = new HashMap<>();
        map.put(buildId, num);
        iStatCloseStoreRealService.editCloseStore(map,devTypeName);
    }

    @Override
    public List<CloseStorePointVO> getPointList(CloseStorePointQO qo) {
        //建筑物id
        String buildId = qo.getBuildId();
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物id为空");
        }
        qo.setDevTypeName("闭店监测设备");
        List<CloseStorePointVO> pointList = baseDevicePointMapper.selectCloseStorePointList(qo);
        //获取设备上报最新状态
        selectPointsRedisInfo(pointList,qo);
        // 获取商户开闭店时间及状态
        closeStoreTimerService.selectTimerInfoByPoints(pointList);
        return pointList;
    }

    @Override
    public List<CloseStorePointVO> getA2PointList(CloseStorePointQO qo) {
        //建筑物id
        String buildId = qo.getBuildId();
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物id为空");
        }
        qo.setDevTypeName("闭店监测设备A2");
        List<CloseStorePointVO> pointList = baseDevicePointMapper.selectCloseStorePointList(qo);
        //获取设备上报最新状态
        selectPointsRedisInfo(pointList,qo);
        // 获取商户开闭店时间及状态
        closeStoreTimerService.selectTimerInfoByPoints(pointList);
        return pointList;
    }

    @Override
    public JsonResult getA2PointDataInfo(QueryData queryData) {
        Date startTime;
        Date endTime;
        if (cn.hutool.core.util.ObjectUtil.isNotEmpty(queryData.getParams().get("startTime")) && cn.hutool.core.util.ObjectUtil.isNotEmpty(queryData.getParams().get("endTime"))) {
            startTime = cn.hutool.core.date.DateUtil.parseDateTime(queryData.getParams().get("startTime"));
            endTime = cn.hutool.core.date.DateUtil.parseDateTime(queryData.getParams().get("endTime"));
        }else{
            startTime = cn.hutool.core.date.DateUtil.offsetDay(new Date(), -7);
            endTime = new Date();
        }

        List<CloseControlInfluxData> dataList = influxDataDao.findCloseControlData(queryData.getParams().get("buildingId"),
                queryData.getParams().get("pointId"),
                startTime,
                endTime,CloseControlInfluxData.class);
        return JsonResult.getSuccessResult(dataList);
    }

    @Override
    public void selectPointsRedisInfo(List<CloseStorePointVO> pointList,CloseStorePointQO qo) {
        if (ObjectUtil.listNotEmptyVerify(pointList)) {
            String buildId = pointList.get(0).getBuildId();
            for (CloseStorePointVO vo : pointList) {
                String key = CloseStoreRedisConstants.getPointInfoKey(buildId, vo.getId());
                StringBuilder ext = new StringBuilder();

                if(vo.getDevTypeName().equals("闭店监测设备B")){
                    //获取设备初始化状态
                    CloseStoreModule closeStoreModule = closeStoreModuleMapper.selectOne(new LambdaQueryWrapper<CloseStoreModule>()
                            .eq(CloseStoreModule::getPointId, vo.getId()));
                    if (closeStoreModule != null) {
                        ext.append(closeStoreModule.getModuleInitializationStatus());
                        vo.setModuleInitializationStatus(closeStoreModule.getModuleInitializationStatus());
                    }
                }
                String object = (String)redisUtils.get(key);
                FarEastoneCache farEastoneCache = null;
                if (StringUtils.isNotEmpty(object)) {
                    if(isValidJson(object)){
                        farEastoneCache = FastJSONUtils.toBean(object, FarEastoneCache.class);
                    }else {
                        log.info("{}设备状态：{}",key, object);
                    }
                }
                setStatusFields(vo, farEastoneCache, ext);

            }
        }
    }

    public static boolean isValidJson(String json) {
        try {
            JSON.parse(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void setStatusFields(CloseStorePointVO vo, FarEastoneCache farEastoneCache, StringBuilder ext) {
        if (farEastoneCache == null) {
            return;
        }
        // 公共字段设置
        setCommonFields(vo, farEastoneCache);
        // 根据设备类型设置特定字段
        String devTypeName = vo.getDevTypeName();
        switch (devTypeName) {
            case "闭店监测设备B":
                setBTypeFields(vo, farEastoneCache);
                break;
            case "闭店监测设备A2":
                setA2TypeFields(vo, farEastoneCache);
                break;
            default:
                // 默认只保留公共字段
                break;
        }
        // 设置扩展字段
        appendStatusToExt(ext, farEastoneCache.getFaultStatus(), "4");
        appendStatusToExt(ext, farEastoneCache.getDeviceStatus(), "5");
        appendStatusToExt(ext, farEastoneCache.getConstantCurrentStatus(), "7");
        appendStatusToExt(ext, farEastoneCache.getMutantCurrentStatus(), "9");
        appendStatusToExt(ext, farEastoneCache.getRecoveryTimeStatus(), "8");
        appendStatusToExt(ext, farEastoneCache.getElectricalStatus(), "12");
        appendStatusToExt(ext, String.valueOf(farEastoneCache.getAlarmStatus()), "11");
        vo.setExt(ext.toString());
        vo.setCloseStoreBStatus(vo.calculateCloseStoreBStatus());
    }

    private void appendStatusToExt(StringBuilder ext, String status, String code) {
        if (StrUtil.isNotEmpty(status) && "1".equals(status)) {
            if (ext.length() > 0) {
                ext.append(",").append(code);
            } else {
                ext.append(code);
            }
        }
    }

    // 设置通用字段
    private void setCommonFields(CloseStorePointVO vo, FarEastoneCache farEastoneCache) {
        vo.setOutageStatus(farEastoneCache.getOutageStatus());
        vo.setFaultStatus(farEastoneCache.getFaultStatus());
        vo.setDeviceStatus(farEastoneCache.getDeviceStatus());
        vo.setElectricalStatus(farEastoneCache.getElectricalStatus());
        vo.setSignals(farEastoneCache.getSignals());
        vo.setPushTime(farEastoneCache.getPushTime());
        vo.setAlarmStatus(farEastoneCache.getAlarmStatus());
        vo.setBatteryValue(farEastoneCache.getBatteryValue());
        vo.setHostId(vo.getHostId());
    }

    // 设置 B 类型字段
    private void setBTypeFields(CloseStorePointVO vo, FarEastoneCache farEastoneCache) {
        vo.setConstantCurrentStatus(farEastoneCache.getConstantCurrentStatus());
        vo.setMutantCurrentStatus(farEastoneCache.getMutantCurrentStatus());
        vo.setCloseStoreCurrent(farEastoneCache.getLowCurrent() + "~" + farEastoneCache.getHighCurrent());
        vo.setRecoveryTimeStatus(farEastoneCache.getRecoveryTimeStatus());
        vo.setRecoveryTime(farEastoneCache.getRecoveryTime());
    }

    // 设置 A2 类型字段
    private void setA2TypeFields(CloseStorePointVO vo, FarEastoneCache farEastoneCache) {
        vo.setAVoltage(farEastoneCache.getAVoltage());
        vo.setATemperature(farEastoneCache.getATemperature());
        vo.setACurrent(farEastoneCache.getACurrent());
        vo.setLeakageProtection(farEastoneCache.getLeakageProtection());
        vo.setLockStatus(farEastoneCache.getLowStatus());
        vo.setSwitchStatus(farEastoneCache.getSwitchStatus());
    }



    @Override
    public IPage<CloseStorePointVO> getPointListForApp(CloseStorePointQO qo) {
        //建筑物id
        if (StringUtils.isEmpty(qo.getBuildId())) {
            throw new RuntimeException("建筑物id为空");
        }
        Page<CloseStorePointVO> page = new Page<>();
        page.setCurrent(qo.getPageNo());
        page.setSize(qo.getPageSize());
        // 分页查询
        IPage<CloseStorePointVO> listPage = baseDevicePointMapper.selectCloseStorePointListPage(page,qo);
        List<CloseStorePointVO> list = listPage.getRecords();
        //获取设备上报最新状态
        selectPointsRedisInfo(list,qo);
        // 获取商户开闭店时间及状态
        closeStoreTimerService.selectTimerInfoByPoints(list);
        return listPage;
    }


    @Override
    public IPage<CloseStoreVPointVo> getPointListForAppNew(CloseStorePointQO qo) {
        //建筑物id
        if (StringUtils.isEmpty(qo.getBuildId()) && StringUtils.isEmpty(qo.getProjectId())) {
            throw new RuntimeException("建筑物id为空");
        }

        if(StringUtils.isEmpty(qo.getBuildId())){
            BaseBuilding baseBuilding = baseBuildingService.getBuildIdByProjectId(qo.getProjectId());
            if(null == baseBuilding){
                throw new RuntimeException("该项目不存在");

            }
            qo.setBuildId(baseBuilding.getId());
        }

        if(StrUtil.isNotBlank(qo.getMerchantId())){
            qo.setMerchantIdList(Arrays.asList(qo.getMerchantId().split(",")));
        }

        Page<CloseStorePointVO> page = new Page<>(qo.getPageNo(),qo.getPageSize());
        // 分页查询
        IPage<CloseStorePointVO> listPage = baseDevicePointMapper.selectCloseStorePointListPage(page,qo);
        IPage<CloseStoreVPointVo> listPageNew = new Page<>();
        BeanUtil.copyProperties(listPage, listPageNew);

        List<CloseStoreVPointVo> listNew = new ArrayList<>();
        List<CloseStorePointVO> list = listPage.getRecords();
        //获取设备上报最新状态
        selectPointsRedisInfo(list,qo);
        // 获取商户开闭店时间及状态
        closeStoreTimerService.selectTimerInfoByPoints(list);
        list.stream().forEach(vo -> {
            CloseStoreVPointVo closeStoreVPointVo = new CloseStoreVPointVo();
            BeanUtil.copyProperties(vo,closeStoreVPointVo);
            closeStoreVPointVo.setPointId(vo.getId())
                    .setMerchantCode(vo.getMidMerchantCode())
                    .setMerchantName(vo.getMidMerchantName())
                    .setRealTimeCurrent(vo.getBatteryValue())
                    .setReturnTime(vo.getRecoveryTime()+"s")
                    .setRealTimeCurrent(vo.getBatteryValue())
                    .setPowerStatus(vo.getOutageStatus())
                    .setBusinessStatus(vo.getMerchantState())
                    .setDeviceStatus(StrUtil.isBlank(vo.getExt())? "10" : vo.getExt())
                    .setDeviceStatusFora(vo.getDeviceStatus());
            listNew.add(closeStoreVPointVo);
        });
        listPageNew.setRecords(listNew);
        return listPageNew;
    }



    @Override
    public List<CloseInfluxData> queryCloseStoreStatusHistoryData(CloseStorePointQO qo) throws ParseException {
        String buildId = qo.getBuildId();
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("buildId不能为空");
        }
        String id = qo.getId();
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位id不能为空");
        }
        String beginTime = qo.getBeginTime();
        String endTime = qo.getEndTime();
        Date beginDate;
        Date endDate;
        if (StringUtils.isNotEmpty(beginTime)&&StringUtils.isNotEmpty(endTime)) {
            beginDate = DateUtils.parseDatetime(beginTime);
            endDate = DateUtils.parseDatetime(endTime);
        } else {
            beginDate = DateUtils.getBeforeWeekDate();
            endDate = new Date();
        }
        List<CloseInfluxData> dataList = influxDataDao.findCloseStoreDataByTime(buildId, id, beginDate, endDate, qo.getOutageStatus(),
              qo.getDeviceStatus(),qo.getElectricalStatus(),qo.getFaultStatus());

        return dataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchImportPointInfo(MultipartFile multipartFile, String buildingId) {
        String fileName = multipartFile.getOriginalFilename();
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            throw new RuntimeException("文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        InputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            try {
                fileInputStream = multipartFile.getInputStream();
                if (isExcel2003) {
                    workbook = new HSSFWorkbook(fileInputStream);
                } else {
                    workbook = new XSSFWorkbook(fileInputStream);
                }
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("解析文件失败");
            }
            BaseBuilding baseBuilding = baseBuildingService.getById(buildingId);
            // 获取建筑下所有楼层
            QueryWrapper<BaseBuildingFloor> floorQueryWrapper = new QueryWrapper<>();
            floorQueryWrapper.eq("building_id", buildingId);
            List<BaseBuildingFloor> list1 = iBaseBuildingFloorService.list(floorQueryWrapper);
            if (list1 == null || list1.size() == 0) {
                throw new RuntimeException("没有楼层无法新增");
            }

            //楼层分组 key = floor/floorName
            Map<String, List<BaseBuildingFloor>> floorMap = groupBillingDataByFooor(list1);
            MerchantQO merchantQO = new MerchantQO();
            merchantQO.setBuildId(buildingId);
            // 获取广场下商户
            List<MerchantQO> merchantList = midMerchantService.getMerchantList(merchantQO);
            //跟新商户数据
            Map<String, MidMerchant> updateMerchantMap = new HashMap<>();
            if (merchantList.size() < 1) {
                throw new RuntimeException("当前广场无商户信息,无法添加设备");
            }
            Map<String, List<MerchantQO>> merchantMap = merchantList.stream()
                    .collect(Collectors.groupingBy(
                            merchant -> merchant.getMidMerchantCode() + "-" + merchant.getMidMerchantName()
                    ));
            //商户类型
            List<MetadataCodeVO> data = metadataCodeService.selectList("SH_TYPE");
            Map<String, String> metadataCodeMap = data.stream().collect(Collectors.toMap(MetadataCodeVO::getName, MetadataCodeVO::getCode));

            Sheet sheet = workbook.getSheetAt(0);
            List<CloseStorePointQO> qoList = new ArrayList<>();
            for (Row row : sheet) {
                //如果当前行的行号（从0开始）未达到2（第三行）则从新循环
                int rowNum = row.getRowNum();
                if (rowNum < 1) {
                    continue;
                }
                //建筑名称
                CloseStorePointQO qo = new CloseStorePointQO();
                qo.setBuildId(buildingId);
                qo.setBuildName(baseBuilding.getBuildingName());

                String errorMsg = "";
                //点位描述(配电箱位置)
                String pointDesc = POIExcelUtil.getCellValueByCell(row.getCell(3));
                if (ObjectUtil.isEmpty(pointDesc)) {
                    errorMsg += "点位描述不能为空,";
                } else {
                    qo.setPointDesc(pointDesc);
                }
                //楼层信息
                String floorInfo = POIExcelUtil.getCellValueByCell(row.getCell(8));
                if (StringUtils.isNotEmpty(floorInfo) && floorInfo.contains("/")) {
                    String[] split = floorInfo.split("/");
                    qo.setFloor(Integer.parseInt(split[0]));
                    qo.setFloorName(split[1]);
                    if (floorMap != null && floorMap.size() > 0 && floorMap.get(floorInfo) != null && floorMap.get(floorInfo).size() > 0) {
                        List<BaseBuildingFloor> baseBuildingFloors1 = floorMap.get(floorInfo);
                        qo.setFloorId(baseBuildingFloors1.get(0).getId());
                    } else {
                        errorMsg += "楼层信息无法查询有效数据,";
                    }
                } else {
                    errorMsg += "楼层信息不能为空或格式异常,";
                }
                //商户铺位号
                String midMerchantCode = POIExcelUtil.getCellValueByCell(row.getCell(6));
                //商户名称
                String midMerchantName = POIExcelUtil.getCellValueByCell(row.getCell(7));
                if (merchantMap.containsKey(midMerchantCode + "-" + midMerchantName)) {
                    List<MerchantQO> qos = merchantMap.get(midMerchantCode + "-" + midMerchantName);
                    if (qos.size() > 1) {
                        errorMsg += "商户存在重复数据,";
                    } else {
                        MerchantQO merchantQO1 = qos.get(0);
                        qo.setMidMerchantId(merchantQO1.getId());
                        qo.setMidMerchantCode(midMerchantCode);
                        qo.setMidMerchantName(midMerchantName);
                    }
                } else {
                    errorMsg += "未获取有效商户,";
                }
                //dtu号
                String dtuNo = POIExcelUtil.getCellValueByCell(row.getCell(0));
                if (ObjectUtil.isEmpty(dtuNo)) {
                    errorMsg += "DTU编号不能为空,";
                } else {
                    if (!dtuNo.matches("\\d{1,7}")) {
                        errorMsg += "DTU编号必须为小于8位的数字,";
                    } else {
                        qo.setDtuNo(dtuNo);
                    }
                }
                //接入点
                String point = POIExcelUtil.getCellValueByCell(row.getCell(1));
                if (ObjectUtil.isEmpty(point)) {
                    errorMsg += "接入点不能为空,";
                } else if (!point.matches("\\d{1,3}")) {
                    errorMsg += "接入点必须为小于3位的数字,";
                }
                String deviceNo = POIExcelUtil.getCellValueByCell(row.getCell(2));
                if (ObjectUtil.isEmpty(deviceNo)) {
                    errorMsg += "设备编码不能为空,";
                } else {
                    qo.setDeviceNo(deviceNo);
                }
                //商业类型 0大商业 1娱乐楼
                String bizTypeName = POIExcelUtil.getCellValueByCell(row.getCell(4));
                if (ObjectUtil.isEmpty(bizTypeName)) {
                    errorMsg += "商业类型不能为空,";
                } else if (Objects.equals(bizTypeName, "大商业")) {
                    qo.setBizType(0);
                } else if (Objects.equals(bizTypeName, "娱乐楼")) {
                    qo.setBizType(1);
                } else {
                    errorMsg += bizTypeName + " 商业类型不存在,";
                }
                //商户类型
                String midMerchantTypeName = POIExcelUtil.getCellValueByCell(row.getCell(5));
                if (ObjectUtil.isEmpty(bizTypeName)) {
                    errorMsg += "商户类型不能为空,";
                } else if (metadataCodeMap.containsKey(midMerchantTypeName)) {
                    String midMerchantTypeCode = metadataCodeMap.get(midMerchantTypeName);
                    qo.setMidMerchantTypeCode(midMerchantTypeCode);
                    qo.setMidMerchantTypeName(midMerchantTypeName);
                } else {
                    errorMsg += midMerchantTypeName + " 商户类型不存在,";
                }
                if (ObjectUtil.isNotEmpty(errorMsg)) {
                    throw new RuntimeException("第" + (rowNum + 1) + "行:" + errorMsg + "请检查数据");
                }

                String midMerchantId = qo.getMidMerchantId();
                Integer bizType = qo.getBizType();
                String merchantTypeCode = qo.getMidMerchantTypeCode();
                if (updateMerchantMap.containsKey(midMerchantId)) {
                    MidMerchant midMerchant = updateMerchantMap.get(midMerchantId);
                    if(!bizType.equals(midMerchant.getBizType()) || !merchantTypeCode.equals(midMerchant.getTypeCode())){
                        throw new RuntimeException("第" + (rowNum + 1) + "行:表格中("+midMerchantName+")同一商户对应商户位置,商户位置需一致" + errorMsg + "请检查数据");
                    }
                } else {
                    MidMerchant midMerchant = new MidMerchant();
                    midMerchant.setId(midMerchantId);
                    midMerchant.setBizType(bizType);
                    midMerchant.setTypeCode(merchantTypeCode);
                    midMerchant.setTypeName(qo.getMidMerchantTypeName());
                    updateMerchantMap.put(midMerchantId, midMerchant);
                }

                String address = HexUtil.intToHexString(Integer.parseInt(dtuNo), 4);
                String hex = "BD" + address + "_" + point;

                //检查接入点唯一
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_CLOSE_STORE);
                queryMap.put("pid", hex);
                List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
                if (null != pointList && pointList.size() > 0) {
                    throw new RuntimeException("第" + (rowNum + 1) + "行:" + "DTU编号&接入点:" + dtuNo + "_" + point + "已存在，不能重复添加");
                }

                qo.setJoinCode(hex);
                qoList.add(qo);
            }

            if (qoList.size() > 0) {
                List<BaseDevicePoint> pointList = qoList.stream().map(qo -> {
//                    System.out.println(JSONObject.toJSONString(qo));
                    String deviceNo = qo.getDeviceNo();
                    String dtuNo = String.format("%08d", Integer.parseInt(qo.getDtuNo()));
                    String buildName = qo.getBuildName();
                    String devTypeName = "闭店监测设备";
                    String midMerchantId = qo.getMidMerchantId();
                    String joinCode = qo.getJoinCode();
                    BaseDevicePoint devicePoint = new BaseDevicePoint();
                    devicePoint.setBuildingId(buildingId);
                    devicePoint.setBuildName(buildName);
                    devicePoint.setDeviceNo(deviceNo);
                    devicePoint.setPid(joinCode);
                    String[] split = joinCode.split("_");
                    devicePoint.setHostId(dtuNo);
                    devicePoint.setDid(split[1]);
                    devicePoint.setJoinCode(joinCode);
                    devicePoint.setDeviceName(devTypeName + deviceNo);
                    devicePoint.setJoinName(joinCode);
                    //设置闭店监测类型
                    devicePoint.setDevTypeCode("BDJC");
                    devicePoint.setDevTypeName(devTypeName);
                    devicePoint.setPointDesc(qo.getPointDesc());
                    devicePoint.setFloorId(qo.getFloorId());
                    // 点位号存入输入信息
                    String pointNumber = dtuNo + "_" + deviceNo;
                    devicePoint.setPointNumber(pointNumber);
                    devicePoint.setFloor(qo.getFloor());
                    devicePoint.setFloorName(qo.getFloorName());
                    devicePoint.setSimNum(qo.getSimNum());
                    devicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_CLOSE_STORE);
                    devicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
                    devicePoint.setDtuNo(dtuNo);
                    devicePoint.setCreateTime(new Date());
                    devicePoint.setUpdateTime(new Date());
                    devicePoint.setMidMerchantId(midMerchantId);
                    devicePoint.setExpiredTime(qo.getExpiredTime());
                    return devicePoint;
                }).collect(Collectors.toList());
                Boolean flag = false;
                List<MidMerchant> merchants = updateMerchantMap.values().stream().collect(Collectors.toList());
                try {
                    flag = iBaseDevicePointService.batchInsertPointInfo(pointList);
                    //根据商户类型更新商户数据
                    midMerchantService.updateBatchById(merchants);
                } catch (Exception e) {
                    throw new RuntimeException("点位重复无法新增！");
                }
                if (flag) {
                    pointList.forEach(devicePoint->{
                        //关联设备建筑
                        CloseStoreHost closeStoreHost = closeStoreHostService.relatedBuilding(devicePoint, buildingId);
                        //获取设备iccid和注册时间
                        if(closeStoreHost != null){
                            devicePoint.setSimNum(closeStoreHost.getIccid());
                            String date = DateUtil.getDateAfterYear(closeStoreHost.getActivationTime(), 5);
                            Date date1 = DateUtil.getDate(date,"yyyy-MM-dd");
                            devicePoint.setExpiredTime(date1);
                        }
                        // 添加设备上传信息缓存
                        setPointInfoToRedis(devicePoint,null);
                        //设备闭店时间处理
                        MidMerchant midMerchant = updateMerchantMap.get(devicePoint.getMidMerchantId());
                        closeStoreTimerService.selectPointTimerAndUpdateQueue(devicePoint,midMerchant.getBizType());

                        // 更新设备统计信息
                        editStatCloseStoreTotal(buildingId, 1,devicePoint.getDevTypeName());
                    });
                    //更新数据
                    iBaseDevicePointService.updateBatchById(pointList);

                    //设备闭店时间处理
                    return true;
                }

            }
        } finally {
            try {
                workbook.close();
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public Map<String, List<BaseBuildingFloor>> groupBillingDataByFooor(List<BaseBuildingFloor> billingList) {
        Map<String, List<BaseBuildingFloor>> resultMap = new HashMap<String, List<BaseBuildingFloor>>();
        try {
            for (BaseBuildingFloor sysUser : billingList) {
                if (null != sysUser.getFloorName() && sysUser.getFloor() != null) {
                    //map中某值已存在，将该数据存放到同一个key（key存放的是该值）的map中
                    if (resultMap.containsKey(sysUser.getFloor() + "/" + sysUser.getFloorName())) {
                        resultMap.get(sysUser.getFloor() + "/" + sysUser.getFloorName()).add(sysUser);
                    } else {//map中不存在，新建key，用来存放数据
                        List<BaseBuildingFloor> sysUserList = new ArrayList<BaseBuildingFloor>();
                        sysUserList.add(sysUser);
                        resultMap.put(sysUser.getFloor() + "/" + sysUser.getFloorName(), sysUserList);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        return resultMap;
    }

    @Override
    public IPage<CloseStorePointVO> getPointPage(CloseStorePointQO qo) {
        //建筑物id
        if (StringUtils.isEmpty(qo.getBuildId())) {
            throw new RuntimeException("建筑物id为空");
        }
        Page<CloseStorePointVO> page = new Page<>();
        page.setCurrent(qo.getPageNo());
        page.setSize(qo.getPageSize());
        // 分页查询
        IPage<CloseStorePointVO> listPage = baseDevicePointMapper.selectCloseStorePointPage(page,qo);
        return listPage;
    }

    @Override
    public void exportForPointPage(CloseStorePointQO qo, HttpServletResponse response) throws IOException {
        //建筑物id
        if (StringUtils.isEmpty(qo.getBuildId())) {
            throw new RuntimeException("建筑物id为空");
        }
        Page<CloseStorePointVO> page = new Page<>();
        page.setCurrent(1);
        page.setSize(10000);
        // 分页查询
        IPage<CloseStorePointVO> listPage = baseDevicePointMapper.selectCloseStorePointPage(page,qo);
        List<CloseStorePointVO> list = listPage.getRecords();
        List<CloseStorePointExcel> excels = list.stream().map(i -> {
            CloseStorePointExcel excel = BeanCopyUtil.copyBeanClass(i, CloseStorePointExcel.class);
            if(i.getExpiredTime() != null  ){
                String s = DateUtil.formatTime(i.getExpiredTime());
                excel.setExpiredTime(s);
            }
            String c = DateUtil.formatTime(i.getCreateTime());
            excel.setCreateTime(c);
            return  excel;
        }).collect(Collectors.toList());
        //导出操作
        ExcelUtil.exportExcel(excels, "闭店设备导出", "闭店设备", CloseStorePointExcel.class, "闭店设备列表导出.xlsx", response);
    }


    @Value("${close-control.url}")
    private String closeControlUrl;

    @Resource
    OkHttpService okHttpService;
    /**
     * 小程序端合闸开关
     * @return
     */
    @Override
    public JsonResult gateOnOff(String hostId,String gateStatus) {
        try {
            long beginTime = System.currentTimeMillis();
            String url = closeControlUrl+"/sendCommand"+"/"+ hostId + "/" + 1 + "/" + gateStatus;
            String body = okHttpService.get(url);
            log.info("调用sendCommand方法远程请求结果--->入参{}, 耗时{}, 请求结果{}",hostId, System.currentTimeMillis() - beginTime, body);
            return JsonResult.Success().setData(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("[gateOnOff]下发失败,{}", e.getMessage());
        }
        return null;
    }

    /**
     * 端延迟报警
     * @return
     */
    @Override
    public JsonResult delayAlarm(QueryData queryData) {
        String alarmTime = queryData.getParams().get("alarmTime");
        StatCloseStoreExpection statCloseStoreExpection = statCloseStoreExpectionMapper.selectById(queryData.getParams().get("exceptionId"));
        // 将延迟时间转为毫秒
        long delayMillis = Long.parseLong(alarmTime) * 1000;
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    pushCloseStoreException(statCloseStoreExpection);
                } catch (Exception e) {
                    log.error("延迟执行pushCloseStoreException失败", e);
                }
            }
        }, delayMillis, TimeUnit.MILLISECONDS);
        return JsonResult.Success();
    }


    @Autowired
    WebSocketSendUtil webSocketSendUtil;

    @Async
    public void pushCloseStoreException(StatCloseStoreExpection statCloseStoreExpection) {
        String pointId = statCloseStoreExpection.getPointId();
        String buildingId = statCloseStoreExpection.getBuildingId();
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(pointId);
        String floorId = baseDevicePoint.getFloorId();
        String floorName = baseDevicePoint.getFloorName();
        int floor = baseDevicePoint.getFloor();
        Double pointX = baseDevicePoint.getPointX() != null ? baseDevicePoint.getPointX().doubleValue() : null;
        Double pointY = baseDevicePoint.getPointY() != null ? baseDevicePoint.getPointY().doubleValue() : null;
        WebsocketInfo websocketInfo = new WebsocketInfo();
        websocketInfo.setType("closestore-error");
        websocketInfo.setBuildingId(buildingId);
        HashMap<String, Object> data = new HashMap<>();
        FloorInfo floorInfo = new FloorInfo();
        floorInfo.setId(floorId);
        floorInfo.setFloor(floor);
        floorInfo.setName(floorName);
        floorInfo.setPointX(pointX);
        floorInfo.setPointY(pointY);
        data.put("floorInfo", floorInfo);
        data.put("alarmInfo", statCloseStoreExpection);
        websocketInfo.setData(data);
        Map<String, Object> result = new HashMap<>();
        result.put("socketType", "monitoring");
        result.put("data", websocketInfo);
        webSocketSendUtil.sendMessage(result);
    }

    @Override
    public JsonResult getPowerExceptionCount(Map<String, Object> qo) {
        List<Map<String, Long>> powerExceptionCount = statCloseStoreExpectionMapper.getPowerExceptionCount(qo);
        return JsonResult.getSuccessResult(powerExceptionCount);
    }
}
