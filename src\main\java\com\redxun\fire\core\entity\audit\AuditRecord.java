package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AuditRecord implements java.io.Serializable{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 建筑ID
     */
    private String buildingId;

    /**
     * 大区
     */
    private String region;

    /**
     * 城市公司
     */
    private String cityCompany;

    /*
    * 申请ID
     */
    private String applyId;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 待补全点位数量
     */
    private Integer waitCompleteNum;

    /**
     * 故障点位数量
     */
    private Integer faultNum;

    /**
     * 反复误报点位数量
     */
    private Integer repeatNum;

    /**
     * 测试点位数量
     */
    private Integer testNum;

    /**
     * 跑点复核点位数量
     */
    private Integer checkNum;

    /**
     * 跑点处置不合格点位数量
     */
    private Integer disposeNum;

    /**
     * 设备测试火警数量
     */
    private Integer fireNum;

    /**
     * 维保计划复核
     */
    private Integer checkPlanNum;

    /**
     * 维保计划不合格
     */
    private Integer checkPlanDisNum;

    /**
     * 值班表
     */
    private String dutyTable;

    /**
     * 检查人
     */
    private String checkUser;

    @TableField(exist = false)
    List<AuditFaultRecord> faultRecords;
    @TableField(exist = false)
    List<AuditFireRecord> fireRecords;
    @TableField(exist = false)
    List<AuditRepeatRecord> repeatRecords;
    @TableField(exist = false)
    List<AuditWaitCompleteRecord> waitCompleteRecords;
    @TableField(exist = false)
    List<AuditTestRecord> testRecords;

}
