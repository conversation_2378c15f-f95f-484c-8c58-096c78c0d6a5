package com.redxun.fire.core.job;

import com.gexin.fastjson.JSON;
import com.redxun.fire.core.consts.RedisKeys;
import com.redxun.fire.core.entity.FaultInfo;
import com.redxun.fire.core.mapper.FaultInfoMapper;
import com.redxun.fire.core.service.alarm.impl.FaultInfoServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: 程国栋
 * @since: 2024/4/26 15:33
 * @description: 生成回路故障
 * @cron: 0 0/1 * * * ?
 */
@Slf4j
@Service
public class FaultTimeTaskHandler extends IJobHandler {

    @Resource
    private FaultInfoServiceImpl faultInfoService;

    @Resource
    private FaultInfoMapper faultInfoMapper;

    @Resource
    private RedisUtils redisUtils;

    @Override
    @XxlJob("FaultJobHandler")
    public void execute() throws Exception {
        //已调试完成
        log.info("生成回路故障任务开始");
        long startTime = System.currentTimeMillis();
        //移除过期故障
        //Set<String> keys = redisUtils.keysPattern(RedisKeys.XF_JUDGE_SET + "*");

        //新增hashSet记录key，避免模糊查询影响性能
        Set<Object> keys = redisUtils.setMembers(RedisKeys.XF_LOOP_FAULT_SET);
        log.info("生成回路故障任务开始,keys大小:{},查询redis耗时:{}", keys.size(), System.currentTimeMillis() - startTime);

        for (Object key : keys) {
            String keyStr = key.toString();
            Set<Object> objSet = redisUtils.setMembers(keyStr);
            log.info("生成回路故障任务,key:{},单个故障大小:{},耗时:{}", keyStr, objSet.size(), System.currentTimeMillis() - startTime);
            if (objSet.size() > 0) {
                for (Object obj : objSet) {
                    FaultInfo faultInfo = FastJSONUtils.toBean((String) obj, FaultInfo.class);
                    String lastTime = faultInfo.getLastTime();
                    Date lastDate = DateUtil.parseDate(lastTime, "yyyy-MM-dd HH:mm:ss");
                    Date date = new Date();

                    //对于历史的数据，超过12小时，不发送故障
                    if (Math.abs(DateUtils.minusSecond(date, lastDate)) >= 3600 * 12) {
                        log.info("生成回路故障任务,移除历史key:{}", keyStr);
                        redisUtils.setRemove(keyStr, obj);
                    }

                    if (Math.abs(DateUtils.minusSecond(date, lastDate)) >= 240) {
                        redisUtils.add("remove_fault_info", obj);
                        redisUtils.setRemove(keyStr, obj);
                    }
                }
            } else {
                redisUtils.setRemove(RedisKeys.XF_LOOP_FAULT_SET, key);
                log.info("生成回路故障任务,移除key:{}", keyStr);
            }

        }
        log.info("生成回路故障任务耗时:{}", System.currentTimeMillis() - startTime);

        //过期故障入库
        Set<Object> objects = redisUtils.setMembers("remove_fault_info");
        List<FaultInfo> faultInfos = new ArrayList<>();
        for (Object obj : objects) {
            FaultInfo faultInfo = FastJSONUtils.toBean((String) obj, FaultInfo.class);
            FaultInfo faultInfo1 = faultInfoMapper.selectById(faultInfo.getId());
            if (faultInfo1 == null) {
                faultInfos.add(faultInfo);
            }
            Long len = redisUtils.setRemove("remove_fault_info", obj);
            log.info("回路故障:redis移除判断故障入数据库->" + len + ":" + JSON.toJSONString(obj));
        }
        faultInfoService.saveBatch(faultInfos);
        log.info("生成回路故障任务结束,耗时:{}", System.currentTimeMillis() - startTime);
    }
}
