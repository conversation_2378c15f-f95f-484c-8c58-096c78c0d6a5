package com.redxun.fire.core.feign;

import com.alibaba.fastjson.JSONObject;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.pojo.bo.MajorDangerBo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 履职
 */
@FeignClient(name = "security-duty-core")
public interface SecurityDutyClient {


    /**
     * 智慧消防获取三级分类
     * @param jsonObject
     * @return
     */
    @PostMapping("/securityDuty/core/dict/taskTmpDict")
    JsonResult taskTmpDict(JSONObject jsonObject);

    /**
     * 隐患入库前检查
     * @param majorDangerBos
     * @return
     */
    @PostMapping("/securityDuty/core/danger/mainRisk/checkRiskBefore")
    JsonResult checkRiskBefore(List<MajorDangerBo> majorDangerBos);


    /**
     * 消防隐患入库保存
     * @param majorDangerBos
     * @return
     */
    @PostMapping("/securityDuty/core/danger/mainRisk/saveFireRisk")
    JsonResult saveFireRisk(@RequestParam("type") String type, List<MajorDangerBo> majorDangerBos);
}
