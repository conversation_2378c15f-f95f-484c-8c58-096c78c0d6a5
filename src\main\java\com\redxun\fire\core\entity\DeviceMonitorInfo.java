package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.redxun.fire.config.LongToJsonSerializer;
import lombok.Data;
import org.influxdb.annotation.Column;
import org.springframework.data.annotation.Id;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 闭店监测设备信息表
 */
@Data
public class DeviceMonitorInfo {

    @JsonSerialize(using = LongToJsonSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 关联的基础点位ID
     */
    private String basePointId;

    /**
     * 回路类型 0 非24小时回路故障、 1 24小时回路故障
     */
    private String loopType;

    /**
     * 24小时回路类型情况下，断电提醒时间 闭店期间 24小时
     * 0 闭店中 124小时
     */
    private String closeStoreRemindTime;

    /**
     * 设备ID
     */
    private String deviceRealId;

    /**
     * 设备名称
     */
    private String deviceRealName;

    /**
     * 商户店长姓名
     */
    private String merchantManager;

    /**
     * 店长电话
     */
    private String managerPhone;

    /**
     * 店长电话（脱敏）
     */
    private String phoneDecrypt;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
