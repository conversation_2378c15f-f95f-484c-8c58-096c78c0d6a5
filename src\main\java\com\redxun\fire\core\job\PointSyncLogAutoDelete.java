package com.redxun.fire.core.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.redxun.fire.core.mapper.PointSyncLogMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @createTime 2024/8/13
 * @description
 */

@Slf4j
@Service
public class PointSyncLogAutoDelete extends IJobHandler {

    @Resource
    PointSyncLogMapper pointSyncLogMapper;

    @Override
    @XxlJob("PointSyncLogAutoDelete")
    public void execute() throws Exception {
        try {
            log.info("-------------自动删除点位调改同步记录开始-----------------");
            final val jobParam = XxlJobHelper.getJobParam();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, -7);
            if (StrUtil.isNotBlank(jobParam)) {
                log.info("---------------自动删除点位调改同步记录任务,手动执行调整时间--------------------------");
                final val parse = DateUtil.parse(jobParam, "yyyy-MM-dd");
                calendar.setTime(parse);
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            String time = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
            int deleteNum = 1000;
            int count = 0;
            while (deleteNum > 0 && count < 100) {
                deleteNum = pointSyncLogMapper.deleteByTime(time);
                count++;
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }
            }
            log.info("-------------自动删除点位调改同步记录结束-----------------");
        } catch (Exception e) {
            log.error("自动删除点位调改同步记录异常:", e);
        }
    }

}
