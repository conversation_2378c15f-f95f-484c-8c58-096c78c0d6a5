package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.*;

/**
 * <p>
 * 设备联动信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BaseDeviceLinkage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 建筑物id
     */
    private String buildingId;

    /**
     * 主机编码
     */
    private String hostId;

    /**
     * 硬件编号
     */
    private String pid;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 点位号
     */
    private String pointCode;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 消防系统编号
     */
    private String fasCode;

    /**
     * 消防系统名称
     */
    private String fasName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceName;

    /**
     * 记录时间
     */
    private String logTime;

    /**
     * 设备状态
     */
    private String deviceStatus;

    /**
     * 首次联动时间
     */
    private String firstTime;

    /**
     * 最近一次时间
     */
    private String lastTime;

    /**
     * 描述
     */
    private String comment;

    /**
     * 发生次数
     */
    private Integer count;

    /**
     * 防火分区id
     */
    private String zoneId;

    /**
     * 防火分区名称
     */
    private String zone;

    /**
     * 是否联动
     */
    private String isLink;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 建筑名
     */
    private String buildingName;

    /**
     * 建筑状态
     */
    private String buildingStatus;

    /**
     * 点位类型
     */
    private String pointType;

    /**
     * 位置描述
     */
    private String locationDescribe;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportedTime;

    /**
     * 建筑状态名
     */
    private String buildingStatusStr;

    /**
     * 点位类型名
     */
    private String pointTypeStr;

    private String isRead;

}
