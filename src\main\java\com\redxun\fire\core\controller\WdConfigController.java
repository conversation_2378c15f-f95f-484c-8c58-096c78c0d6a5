package com.redxun.fire.core.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.consts.TsmMessagesConstants;
import com.redxun.fire.core.dto.user.OsUserDto;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.OsGroup;
import com.redxun.fire.core.entity.StatPumpExpection;
import com.redxun.fire.core.job.WbPlanScopeUpdateJob;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.PointSyncLogMapper;
import com.redxun.fire.core.mapper.StatPumpExpectionMapper;
import com.redxun.fire.core.queue.consumer.AlarmPointConsumer;
import com.redxun.fire.core.service.building.impl.BaseBuildingServiceImpl;
import com.redxun.fire.core.service.common.ITsmMessagesService;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.service.common.impl.TsmMessagesServiceImpl;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.exam.IPracticalExamApplyService;
import com.redxun.fire.core.service.other.IWdConfigService;
import com.redxun.fire.core.service.other.impl.PracticalExamApplyServiceImpl;
import com.redxun.fire.core.service.user.IOsGroupService;
import com.redxun.fire.core.utils.ByteArrayUtil;
import com.redxun.fire.core.utils.ConfigRelationUtil;
import com.redxun.fire.core.utils.WdMessageComponent;
import com.redxun.message.entity.ToSysMessage;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * Created on 2020/10/27 0027.
 */
@RestController
@RequestMapping("/wd-config")
@Slf4j
public class WdConfigController {


    @Autowired
    private IWdConfigService wdConfigService;

    /**
     * 判断水压水泵延时处理申请是否超过基础时长
     *
     * @param days  申请天数
     * @param hours 申请小时数
     * @param type  PRESS 水压相关   PUMP 水泵相关
     * @return
     */
    @GetMapping("/judgeWaterSystemDelayTime")
    public JsonResult<Boolean> judgeWaterSystemDelayTime(@RequestParam Integer days, @RequestParam Integer hours, @RequestParam String type) {
        JsonResult result = JsonResult.Success();
        result.setData(wdConfigService.judgeWaterSystemDelayTime(days, hours, type));
        return result;
    }

    @Resource
    private IBaseDeviceTemporaryService baseDeviceTemporaryService;
    @Autowired
    private AlarmPointConsumer alarmPointConsumer;
    @PostMapping("/test2")
    public void test2(@RequestBody ToSysMessage toSysMessage){
        alarmPointConsumer.handMessage(toSysMessage);
    }
    @PostMapping("/test")
    public Object test(@RequestBody Map<String, String> map) {
        log.info("方法0");
        String hostId;
        String did;
//        String  hostCode = "01009"; //0000000400050600
        String  hostCode = map.get("hostCode");
        String  localHost = map.get("localHost");
        String  localLoop = map.get("localLoop");
        String  pointCode = map.get("pointCode");
//        String  hostCode = "47205"; // 040702000500  0000000400050600
//        String  localHost = "2";
//        String  localLoop = "3";
//        String  pointCode = "87";
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidMethodZero(localHost, localLoop, pointCode);
        log.info("-------------进入方法0生成的did为：{}---------------", did);
        log.info("-------------进入方法0生成的hostId为：{}---------------", hostId);
        log.info("进入方法1");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidMethodOne(localHost, localLoop, pointCode);
        log.info("-------------进入方法1生成的did为：{}---------------", did);
        log.info("-------------进入方法1生成的hostId为：{}---------------", hostId);
        log.info("进入方法2");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidMethodTwo(localHost, localLoop, pointCode);
        log.info("-------------进入方法2生成的did为：{}---------------", did);
        log.info("-------------进入方法2生成的hostId为：{}---------------", hostId);
        log.info("进入方法3和方法4和方法6");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidXimenzi(localHost, localLoop, pointCode);
        log.info("-------------进入方法3和方法4和方法6生成的did为：{}---------------", did);
        log.info("-------------进入方法3和方法4和方法6生成的hostId为：{}---------------", hostId);
        log.info("进入方法5");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidMethodFive(localHost, localLoop, pointCode);
        log.info("-------------进入方法5生成的did为：{}---------------", did);
        log.info("-------------进入方法5生成的hostId为：{}---------------", hostId);
        log.info("进入赋安5116");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidFuan(localHost, localLoop, pointCode);
        log.info("-------------进入赋安5116生成的did为：{}---------------", did);
        log.info("-------------进入赋安5116生成的hostId为：{}---------------", hostId);
        log.info("进入海湾和城安800");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(localHost, 4) + baseDeviceTemporaryService.transferString(localLoop, 6) + baseDeviceTemporaryService.transferString(pointCode, 6);
        log.info("-------------进入海湾和城安800生成的did为：{}---------------", did);
        log.info("-------------进入海湾和城安800生成的hostId为：{}---------------", hostId);
        log.info("进入海湾西门子720");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(pointCode, 16);
        log.info("-------------进入海湾西门子720生成的did为：{}---------------", did);
        log.info("-------------进入海湾西门子720生成的hostId为：{}---------------", hostId);
        log.info("进入海湾6000D");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(localHost, 6) + baseDeviceTemporaryService.transferString(localLoop, 4) + baseDeviceTemporaryService.transferString(pointCode, 6);
        log.info("-------------进入海湾6000D生成的did为：{}---------------", did);
        log.info("-------------进入海湾6000D生成的hostId为：{}---------------", hostId);
        log.info("进入海湾安舍");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(localLoop, 12) + baseDeviceTemporaryService.transferString(pointCode, 4);
        log.info("-------------进入海湾安舍生成的did为：{}---------------", did);
        log.info("-------------进入海湾安舍生成的hostId为：{}---------------", hostId);
        log.info("进入城安8000D");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(localLoop, 8) + baseDeviceTemporaryService.transferString(pointCode, 6) + "00";
        log.info("-------------进入城安8000D生成的hostId为：{}，did为：{}---------------", hostId, did);
        log.info("进入泰科");
        // hostId : 传输装置编号 后两位补0 前面一个占两位
        hostId = baseDeviceTemporaryService.transferString(hostCode, 10) + "00";
        // did:   主机号 前四位 一个两字节 补0    回路号 中间6位 一个两字节 补0  点位号  最后六位 补0  一个两字节
        did = baseDeviceTemporaryService.transferString(localHost, 4) + baseDeviceTemporaryService.transferString(localLoop, 4) + baseDeviceTemporaryService.transferString(pointCode, 8);
        log.info("-------------进入泰科生成的did为：{}---------------", did);
        log.info("-------------进入泰科生成的hostId为：{}---------------", hostId);
        log.info("进入北大青鸟");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferStringQingniao(localHost, localLoop, pointCode);
        log.info("-------------进入北大青鸟生成的did为：{}---------------", did);
        log.info("-------------进入北大青鸟生成的hostId为：{}---------------", hostId);
        log.info("进入方法7");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferDidMethodSeven(Integer.parseInt(localHost), Integer.parseInt(localLoop), Integer.parseInt(pointCode));
        log.info("-------------进入方法7生成的did为：{}---------------", did);
        log.info("-------------进入方法7生成的hostId为：{}---------------", hostId);
        log.info("进入海湾9000H");
        hostId = ByteArrayUtil.hex10To16Desc(Integer.parseInt(hostCode), 12);
        did = baseDeviceTemporaryService.transferHaiWanMethodSeven(pointCode);
        log.info("-------------进入海湾9000H生成的did为：{}---------------", did);
        log.info("-------------进入海湾9000H生成的hostId为：{}---------------", hostId);
        hostId = hostCode;
        did = localHost + localLoop + pointCode;
        log.info("-------------默认生成的did为：{}---------------", did);
        log.info("-------------默认生成的hostId为：{}---------------", hostId);
        return null;
    }

    @Autowired
    WbPlanScopeUpdateJob wbPlanScopeUpdateJob;

    @Autowired
    BaseBuildingServiceImpl baseBuildingService;

    @Resource
    PointSyncLogMapper pointSyncLogMapper;

    @Autowired
    BaseDevicePointMapper baseDevicePointMapper;

    @Autowired
    private StatPumpExpectionMapper statPumpExpectionMapper;
    @Autowired
    private BaseBuildingMapper baseBuildingMapper;

    @Autowired
    DropDownServiceImpl dropDownService;
    @Autowired
    private IOsGroupService osGroupService;
    @Resource
    private PracticalExamApplyServiceImpl practicalExamApplyService;

    @Autowired
    private WdMessageComponent wdMessageComponent;
    @Autowired
    private ITsmMessagesService tsmMessagesService;


//    @Autowired
//    IBaseDeviceTemporaryService baseDeviceTemporaryService;

    @GetMapping("/test3/{buildingId}/{exceptionId}")
    public void test3(@PathVariable(value = "buildingId") String buildingId,@PathVariable(value = "exceptionId") String exceptionId) throws ParseException {
        StatPumpExpection statPumpExpection = statPumpExpectionMapper.selectById(exceptionId);
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(statPumpExpection.getPointId());
        BaseBuilding baseBuilding = baseBuildingMapper.selectById(buildingId);
        if(ObjectUtil.isNull(baseBuilding) || StrUtil.isBlank(baseBuilding.getPiazza()) || StrUtil.isBlank(baseBuilding.getCityCompany())){
            log.error("水泵处理的建筑信息或组织架构信息不存在");
            return;
        }
        String msg = com.redxun.fire.core.utils.DateUtil.formatDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(statPumpExpection.getReportTime()), "yyyy年MM月dd日HH时mm分ss秒")
                + "," + baseBuilding.getBuildingName() + "消防水泵控制柜监测系统监测到" + baseDevicePoint.getDevTypeName() + "控制柜状态异常超时未处理，请督办处理!,在测试";
        //填报超时短信通知
        List<Map<String, Object>> message = dropDownService.queryConfigMapByCode("dxtz_7_txfs");
//                    List<Map<String, Object>> message = configRelationMapper.selectByConfigCode("dxtz_7_txfs");
        List<Map<String, Object>> configRelations = dropDownService.queryConfigMapByCode("dxtz_7_txgn");
        //0短信1推送
        String method = (String) message.get(0).get(ConfigRelationUtil.CON_STR_VAL);
        //判断处理是否超时
        if (StringUtils.equals(statPumpExpection.getExpectionStatus(), "0") || (StringUtils.equals(statPumpExpection.getExpectionStatus(), "2") && !StringUtils.equals(statPumpExpection.getApproveStatus(), "1"))) {
            if (configRelations.get(1).get(ConfigRelationUtil.CON_TZR_ID) != null && !"".equals(configRelations.get(1).get(ConfigRelationUtil.CON_TZR_ID))) {
                String[] configTzrIds = (configRelations.get(1).get(ConfigRelationUtil.CON_TZR_ID) + "").split(",");

                for (String configTzrId : configTzrIds) {

                    OsGroup osGroupDto = osGroupService.getById(configTzrId);
                    if (osGroupDto == null) {
                        log.info("根据职务id查询职务信息不存在");
                        continue;
                    }
//                                Set<OsUserDto> osUsers = practicalExamApplyService.getUserByJobId(configTzrId, osGroupDto.getRankLevel(), baseBuilding);
                    Set<OsUserDto> osUsers = practicalExamApplyService.getUserByJobId(osGroupDto.getJobId(), osGroupDto.getRankLevel(), baseBuilding);

//                                OsGroupDto osGroupDto = orgClient.getGroupById(configTzrId);
//                                Set<OsUserDto> osUsers = new HashSet<>(orgClient.getUserListForMsgByDimKeys(configTzrId, baseBuilding.getBelongDep()));

                    if (osUsers != null && osUsers.size() > 0) {
                        //短信推送结果合集
                        Map<String, Boolean> SmsResultMap = new HashMap();
                        //消息推送结果合集
                        Map<String, Boolean> NewsResultMap = new HashMap();
                        for (OsUserDto osUser : osUsers) {
                            //短信通知
                            String tmp = method;
                            method = tmp.contains(",") ? "0" : method;
                            if (StringUtils.equals("0", method)) {
                                log.info("水泵超时短信通知人员：" + osUser.toString());
                                if (StringUtils.isNotEmpty(osUser.getMobile())) {
                                    SmsResultMap.put(osUser.getUserId(), wdMessageComponent.sendMessage(osUser.getMobile(), "水泵异常超时", msg, false, "0"));
                                } else if ("1".equals(osUser.getUserCategory())) {
                                    log.info("水泵超时短信通知人员：" + osUser.toString());
                                    String userNo = osUser.getUserNo();
                                    SmsResultMap.put(osUser.getUserId(), wdMessageComponent.sendMessage(userNo, "水泵异常超时", msg, false, "0"));
                                }
                            }
                            //消息推送
                            method = tmp.contains(",") ? "1" : method;
                            if (StringUtils.equals("1", method)) {
                                if(StrUtil.isNotBlank(osUser.getUserNo())){
                                    log.info("水泵超时消息通知人员：" + osUser.toString());
                                    String userNo = osUser.getUserNo();
                                    SmsResultMap.put(osUser.getUserId(), wdMessageComponent.sendMessage(userNo, "水泵异常超时", msg, false, "1"));
                                }
                            }
                        }
                        if (StringUtils.equals("0", method)) {
                            //短信通知入库
                            tsmMessagesService.addTsmMessage(osUsers, baseBuilding, osGroupDto, TsmMessagesConstants.TYPE_PUMP_EXCEPTION_TIMEOUT, msg, TsmMessagesConstants.REMINDERS_SMS, SmsResultMap);
                        }
                        method = method.contains(",") ? "1" : method;
                        if (StringUtils.equals("1", method)) {
                            //万信消息通知入库
                            tsmMessagesService.addTsmMessage(osUsers, baseBuilding, osGroupDto, TsmMessagesConstants.TYPE_PUMP_EXCEPTION_TIMEOUT, msg, TsmMessagesConstants.REMINDERS_NEWS, NewsResultMap);
                        }
                    }
                }
            }
        }
    }


    @PostMapping("/test1")
    public void test1(@RequestBody Map<String, String> map) {
        try {
            long begin = System.currentTimeMillis();
            log.info("-------------点位调改调整维保范围任务开始-----------------");
            final val jobParam = map.get("time");
            final val adjustBuildingId = map.get("buildingId");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            if (StrUtil.isNotBlank(jobParam)) {
                log.info("---------------点位调改调整维保范围任务,手动执行调整时间--------------------------");
                final val parse = DateUtil.parse(jobParam, "yyyy-MM-dd");
                calendar.setTime(parse);
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            String beginTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            String endTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");

            List<String> buildingIdList = new ArrayList<>();
            if (StrUtil.isBlank(adjustBuildingId) ) {
                buildingIdList = baseBuildingService.getBaseMapper().getBuildingIdList();
            } else {
                buildingIdList.add(adjustBuildingId);
            }
            if (CollectionUtil.isNotEmpty(buildingIdList)) {
                for (String buildingId : buildingIdList) {
                    long s = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整开始,时间为{}至{}----------------", buildingId, beginTime, endTime);
                    try {
                        log.info("建筑{}查询变更点位数量,查询时间参数为：{}至{}", buildingId, beginTime, endTime);
                        final val baseDevicePointList = pointSyncLogMapper.getPointNumberByBuildAndTime(buildingId, beginTime, endTime);
                        log.info("建筑{}查询变更点位数量为{}", buildingId, baseDevicePointList.size());
                        if (CollectionUtil.isNotEmpty(baseDevicePointList)) {
                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, baseDevicePointList, true, "");
                            continue;
                        }
                        baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, null, false, "");
//                        Integer count = pointSyncLogMapper.getDeletePointCount(buildingId, beginTime, endTime);
//                        log.info("建筑{}查询删除的点位数量为{}", buildingId, count);
//                        if (count != null && count > 0) {
//                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, null, false);
//                        }
                    } catch (Exception e) {
                        log.error("建筑{}维保范围调整异常", buildingId, e);
                    }
                    long e = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整结束,共用时{}----------------", buildingId, (e -s));
                }
            }
            long end = System.currentTimeMillis();
            log.info("-------------点位调改调整维保范围任务结束，共用时{}-----------------", (end - begin));
        } catch (Exception e) {
            log.error("点位调改调整维保范围任务异常:", e);
        }
    }

}
