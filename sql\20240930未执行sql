create table fault_daily_history
(
    id                       varchar(32)  not null comment '主键',
    building_id              varchar(32)  not null comment '建筑物id',
    point_id                 varchar(32)  not null comment '点位id',
    history_fault_status_str varchar(10)  null comment '故障状态（0 未处理 1 已处理 2 处理中）',
    fault_info_id            varchar(32)  null comment '原始故障表记录id',
    today_date               datetime     null comment '当日统计的日期',
    point_code               varchar(128) null comment '点位号',
    point_desc               varchar(128) null comment '点位描述',
    dev_type_name            varchar(50)  null comment '设备类型名称',
    reported_time            datetime     null comment '上报时间',
    send_user                varchar(50)  null comment '派发人',
    handle_user              varchar(50)  null comment '处理人'
)
    comment '每日剩余故障历史记录留存';

create index index_bid_rptime_today_date
    on fault_daily_history (building_id, reported_time, today_date);

create index index_building_id_today_date
    on fault_daily_history (building_id, today_date);
