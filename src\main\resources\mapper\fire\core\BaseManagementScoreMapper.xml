<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.BaseManagementScoreMapper">

    <select id="selectNew" resultType="com.redxun.fire.core.entity.BaseManagementScore">
            select id as id,
                   bid as bid,
                   cal_time as calTime,
                   fault_score as faultScore,
                   misinfo_score as misinfoScore,
                   pre_score as preScore,
                   pump_score as pumpScore,
                   service_score as serviceScore,
                   dispose_score as disposeScore
            from base_management_score
            where bid = #{bid}
            order by cal_time desc
            limit 1
    </select>
    <select id="midBuildingManagementScore" resultType="com.redxun.fire.core.pojo.vo.BaseManagementScoreVo">
        SELECT
            a.id,
            a.building_name,
            c.middle_id,
            cal_time as calTime,
            fault_score as faultScore,
            misinfo_score as misinfoScore,
            pre_score as preScore,
            pump_score as pumpScore,
            service_score as serviceScore,
            dispose_score as disposeScore,
            (fault_score+misinfo_score+pre_score+pump_score+service_score+dispose_score) as totalScore
        FROM base_building  a
                 LEFT JOIN  base_management_score b on a.id = b.bid
                 LEFT JOIN  mid_building c on c.id = a.id
        WHERE (b.bid, b.cal_time) IN (
            SELECT bid, MAX(cal_time) AS max_cal_time
            FROM base_management_score
            GROUP BY bid
        )
    </select>


    <select id="selectBaseManagementScoreToExport" resultType="java.util.Map">
        SELECT bb.belong_dep,
        bb.regional,
        bb.jurisdiction,
        bb.safety_belt,
        bb.jurisdiction_val,
        bb.building_name,
        bb.TENANT_ID_ AS    TENANT_ID_,
        bb.CREATE_BY_ AS    CREATE_BY_,
        bb.UPDATE_BY_ AS    UPDATE_BY_,
        bms.fault_score,
        bms.misinfo_score,
        bms.pre_score,
        bms.pump_score,
        bms.service_score,
        bms.dispose_score,
        DATE_FORMAT(bms.cal_time,'%Y-%m-%d') cal_time,
        (bms.fault_score + bms.misinfo_score + bms.pre_score + bms.pump_score + bms.service_score +
        bms.dispose_score) totalScore
        FROM `base_management_score` bms
        JOIN base_building bb ON bms.bid = bb.id
        where
        1=1
        and bb.format='1'
        <if test="param.startTime !=null and param.startTime!= ''">
            and bms.cal_time <![CDATA[>=]]> #{param.startTime}
        </if>
        <if test="param.endTime !=null and param.endTime!= ''">
            and bms.cal_time <![CDATA[<]]> #{param.endTime}
        </if>
        <if test="param.scoreTime !=null and param.scoreTime!= ''">
            and bms.cal_time = #{param.scoreTime}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and bb.id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        ORDER BY bms.cal_time DESC

    </select>


    <select id="selectNewData" resultType="com.redxun.fire.core.entity.BaseManagementScore">
        select id            as id,
               bid           as bid,
               cal_time      as calTime,
               fault_score   as faultScore,
               misinfo_score as misinfoScore,
               pre_score     as preScore,
               pump_score    as pumpScore,
               service_score as serviceScore,
               dispose_score as disposeScore
        from base_management_score
        where bid = #{bid}
          and cal_time > DATE_FORMAT(now(),'%Y-%m-%d')
        order by cal_time desc
        limit 1
    </select>
</mapper>
