package com.redxun.fire.core.service.maintenance.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.mapper.AppointmentApplyMapper;
import com.redxun.fire.core.mapper.MaintenanceConfigMapper;
import com.redxun.fire.core.mapper.MaintenanceScopeTestMapper;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.pojo.dto.MaintenanceScopeTestDto;
import com.redxun.fire.core.pojo.dto.PointNumDto;
import com.redxun.fire.core.pojo.vo.*;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.maintenance.MaintenancePlanService;
import com.redxun.fire.core.service.maintenance.MaintenanceScopeTestService;
import com.redxun.fire.core.service.other.IAppointmentApScopeService;
import com.redxun.fire.core.service.other.IAppointmentPointService;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <p>
 * 维保计划范围表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-11-08
 */
@Slf4j
@Service
@Transactional
public class MaintenanceScopeTestServiceImpl extends ServiceImpl<MaintenanceScopeTestMapper, MaintenanceScopeTest> implements MaintenanceScopeTestService {

    @Resource
    MaintenanceConfigMapper maintenanceConfigMapper;
    @Resource
    MaintenancePlanService maintenancePlanService;
    @Resource
    IAppointmentPointService appointmentPointService;
    @Resource
    AppointmentApplyMapper appointmentApplyMapper;
    @Autowired
    private IJournalizingService journalizingService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private IAppointmentApScopeService appointmentApScopeService;

    @Autowired
    @Qualifier("entityRedisTemplate")
    private RedisTemplate redisTemplate;

    private static final String SCOPE_CONFIG_RULE_TYPE_KEY = "SCOPE_CONFIG_RULE_TYPE_KEY_";

    @Override
    public List<DeviceTypeVo> getScopeTest(MaintenanceScopeTestDto maintenanceScopeTestDto) {
        //获取planId
        QueryWrapper planQuery = new QueryWrapper();
        planQuery.eq("building_id", maintenanceScopeTestDto.getBuildingId());
        planQuery.eq("scheduling", maintenanceScopeTestDto.getScheduling());
        planQuery.eq("effective_time", maintenanceScopeTestDto.getEffectiveTime());
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(planQuery);
        //查询设备类型级别数据 以及生成规则
        List<MaintenanceConfig> maintenanceConfigList = maintenanceConfigMapper.getMaintenanceConfigByPlanId(
                maintenancePlan.getId(), maintenanceScopeTestDto.getFireproofSysId());

        List<DeviceTypeVo> deviceTypeVoList = new ArrayList<>();
        maintenanceConfigList.forEach(maintenanceConfig -> {
            //根据设备类型返找到计划范围ID和计划范围所对应的 1楼层 2回路 3分区  4批次(批次)
            List<ScopeTestVo> scopeTestVoList = null;
            switch (maintenanceConfig.getGenerationRule()) {
                case "1": //查询对应楼层
                    scopeTestVoList = this.baseMapper.getScopeTestByFloor(maintenancePlan.getId(), maintenanceConfig.getDeviceCode());
                    break;
                case "2": //查询对应回路
                    scopeTestVoList = this.baseMapper.getScopeTestByLoop(maintenancePlan.getId(), maintenanceConfig.getDeviceCode());
                    break;
                case "3": //查询对应分区
                    scopeTestVoList = this.baseMapper.getScopeTestByZone(maintenancePlan.getId(), maintenanceConfig.getDeviceCode());
                    break;
            }
            if (scopeTestVoList != null && scopeTestVoList.size() > 0) {
                DeviceTypeVo deviceTypeVo = new DeviceTypeVo();
                deviceTypeVo.setDeviceTypeCode(maintenanceConfig.getDeviceCode());
                deviceTypeVo.setDeviceTypeName(maintenanceConfig.getDeviceName());
                deviceTypeVo.setScopeTestVoList(scopeTestVoList);
                deviceTypeVoList.add(deviceTypeVo);
            } else {
                DeviceTypeVo deviceTypeVo = new DeviceTypeVo();
                deviceTypeVo.setDeviceTypeCode(maintenanceConfig.getDeviceCode());
                deviceTypeVo.setDeviceTypeName(maintenanceConfig.getDeviceName());
                deviceTypeVo.setScopeTestVoList(null);
                deviceTypeVoList.add(deviceTypeVo);
            }
        });
        return deviceTypeVoList;
    }

    @Override
    public List<ScopeTestVo> getWebScopeTest(MaintenanceScopeTestDto maintenanceScopeTestDto) {
        //查询设备类型级别数据 以及生成规则
        List<ScopeTestVo> result = this.baseMapper.getScopeTestByFireSysAndPlanIdAndScheduling(
                maintenanceScopeTestDto.getFireproofSysId(), maintenanceScopeTestDto.getEffectiveTime(),
                maintenanceScopeTestDto.getBuildingId(), "0");
      /*  List<ScopeTestVo> tempResult = this.baseMapper.getScopeTestByFireSysAndPlanId(
                maintenanceScopeTestDto.getFireproofSysId(),maintenanceScopeTestDto.getEffectiveTime(),
                maintenanceScopeTestDto.getBuildingId());*/

        result.forEach(scopeTestVo -> {

     /*       tempResult.forEach(tempScopeTest -> {

                if(null == tempScopeTest.getScopeTestName() || null == scopeTestVo.getScopeTestName()){
                    return;
                }
                if(tempScopeTest.getScopeTestName().equals(scopeTestVo.getScopeTestName())){
                    scopeTestVo.setScopeTestId(tempScopeTest.getScopeTestId());
                }
            });*/

            Object weibaoJson = redisUtils.get("weibao-" + maintenanceScopeTestDto.getBuildingId());
            if (weibaoJson == null) {
                return;
            }
            WeibaoDto weibaoDto = JSON.parseObject(weibaoJson.toString(), WeibaoDto.class);
            if (weibaoDto == null) {
                return;
            }
            if (scopeTestVo.getMaintenancePoint() == null || ("0").equals(scopeTestVo.getMaintenancePoint())) {
                AtomicReference<Integer> maintenancePoint = new AtomicReference<>(0);

                Map<String, Map<String, Boolean>> scopeDevicePointMap = weibaoDto.getScopeDevicePointMap();

                if (CollectionUtils.isEmpty(scopeDevicePointMap)) {
                    return;
                }

                scopeDevicePointMap.forEach((key, value) -> {
                    String scopeTestId = scopeTestVo.getScopeTestId();
                    if (StringUtils.isEmpty(scopeTestId) || StringUtils.isEmpty(key)) {
                        return;
                    }
                    if (Arrays.asList(scopeTestId.split(",")).contains(key)) {
                        value.forEach((pointId, bool) -> {
                            if (bool) {
                                maintenancePoint.getAndSet(maintenancePoint.get() + 1);
                            }
                        });

                    }
                });
                scopeTestVo.setMaintenancePoint(maintenancePoint.get());
            }
        });
        return result;
    }

//    @Override
//    public List<DevicePointInfoVo> getWebScopeTestPoint(String scopeTestId, String scopeTestName, String scheduling) {
//        //根据scopeTestId 获取配置
//        List<DevicePointInfoVo> result = new ArrayList<>();
//
//        List<String> scopeTestIds = Arrays.asList(scopeTestId.split(","));
//
//        List<ScopeTestRuleVo> scopeTestRule = this.baseMapper.getMaintenanceConfigListByScopeTestId(scopeTestIds);
//
//        scopeTestRule.forEach(scopeRule -> {
//            String batchCode = null;
//            switch (scopeRule.getGenerationRule()) {
//                case "1":
//                    batchCode = "floor_id";
//                    break;
//                case "2":
//                    batchCode = "loop_code";
//                    break;
//                case "3":
//                    batchCode = "zone_name";
//                    break;
//            }
//
//            List<String> scopeTestIdList = Arrays.asList(scopeRule.getScopeId().split(","));
//            List<DevicePointInfoVo> devicePointInfoVoList = this.baseMapper.getWebDevicePointInfoByScopeTestIdAndScheduling(scopeTestIdList, batchCode, scheduling);
//
//            devicePointInfoVoList.forEach(devicePointInfoVo -> {
//                devicePointInfoVo.setScopeTestName(scopeTestName);
//            });
//            result.addAll(devicePointInfoVoList);
//        });
//        if (result != null && result.size() > 0) {
//            Object weibaoJson = redisUtils.get("weibao-" + result.get(0).getBuildingId());
//            if (weibaoJson != null) {
//                WeibaoDto weibaoDto = JSON.parseObject(weibaoJson.toString(), WeibaoDto.class);
//                result.forEach(devicePointInfoVo -> {
//                    weibaoDto.getBaseDevicePointMap().forEach((key, value) -> {
//                        if (devicePointInfoVo.getId().equals(key) && value) {
//                            devicePointInfoVo.setMaintenanceStatus(1);
//                        }
//                    });
//                });
//            }
//
//            //获取预约ID
//            List<AppointmentApScope> appointmentApScopeList = appointmentApScopeService.list(new LambdaQueryWrapper<AppointmentApScope>()
//                    .in(AppointmentApScope::getMaintenanceScopeTestId, scopeTestIds)
//                    .select(AppointmentApScope::getAppointmentApplyId));
//            if (appointmentApScopeList == null || appointmentApScopeList.size() <= 0) {
//                return result;
//            }
//            List<AppointmentPoint> appointmentPointList = appointmentPointService.list(new LambdaQueryWrapper<AppointmentPoint>()
//                    .in(AppointmentPoint::getAppointmentApplyId, appointmentApScopeList.stream().map(AppointmentApScope::getAppointmentApplyId).collect(Collectors.toSet()))
//                    .eq(AppointmentPoint::getMaintenanceStatus, "1")
//                    .select(AppointmentPoint::getPointNum));
//            result.forEach(devicePointInfoVo -> {
//                appointmentPointList.forEach(appointmentPoint -> {
//                    if (devicePointInfoVo.getId().equals(appointmentPoint.getPointNum())) {
//                        devicePointInfoVo.setMaintenanceStatus(1);
//                    }
//                });
//            });
//
//        }
//        return result;
//    }

    @Override
    public List<DevicePointInfoVo> getWebScopeTestPoint1(String scopeTestId, String scopeTestName, String effectiveTime) {
        List<String> scopeTestIds = Arrays.asList(scopeTestId.split(","));
//        MaintenancePlan plan = maintenancePlanService.getMaintenancePlanByScopeId(scopeTestIds.get(0));
        MaintenancePlan plan = maintenancePlanService.getMaintenancePlanDataByScopeId(scopeTestIds.get(0),effectiveTime);
        if (ObjectUtil.isNotEmpty(plan)) {
            return getWebScopeTestPointByEffTime(scopeTestId, plan.getBuildingId(), plan.getEffectiveTime(), plan.getScheduling());
        }
        return null;
    }

    @Override
    public IPage<DevicePointInfoVo> selectDevicePointInfoPage(QueryData queryData) {
        QueryWrapper planQuery = new QueryWrapper();
        planQuery.eq("building_id", queryData.getParams().get("buildingId"));
        planQuery.eq("scheduling", queryData.getParams().get("scheduling"));
        planQuery.eq("effective_time", queryData.getParams().get("effectiveTime"));
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(planQuery);
        if (maintenancePlan == null) {
            return null;
        }
        queryData.getParams().put("planId", maintenancePlan.getId());

        Page<MaintenancePlan> page = new Page<>();
        page.setCurrent(queryData.getPageNo());
        page.setSize(queryData.getPageSize());
        //如果需要写入条件传入 queryData 写sql
        return baseMapper.page(page, queryData.getParams());
    }

    @Override
    public List<DevicePointInfoVo> getDeviceStatistics(MaintenancePlanSysDto maintenancePlanSysDto) {
        QueryWrapper planQuery = new QueryWrapper();
        planQuery.eq("building_id", maintenancePlanSysDto.getBuildingId());
        planQuery.eq("scheduling", maintenancePlanSysDto.getScheduling());
        planQuery.eq("effective_time", maintenancePlanSysDto.getEffectiveTime());
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(planQuery);
        return baseMapper.getDeviceStatistics(maintenancePlan.getId(), maintenancePlanSysDto.getFireproofSysId());
    }

    @Override
    public List<MaintenancePlanSysVo> getScopeTestDetails(MaintenancePlanSysDto maintenancePlanSysDto) {
        //查询 系统类型 和 系统类型下的最小单位数据
        List<MaintenancePlanSysVo> maintenancePlanSysVoList = this.baseMapper.getSysFireNameAndScopeByEffectiveTime(maintenancePlanSysDto);

 /*      Set<String> set = new HashSet<>();

        for (MaintenancePlanSysVo maintenancePlanSysVo : maintenancePlanSysVoList) {
            set.add(maintenancePlanSysVo.getFireproofSysId());
        }*/

        //List<MaintenancePlanSysVo> tempMaintenancePlanSysVoList = new ArrayList<>();

 /*       for (String sysCode : set) {
            maintenancePlanSysDto.setFireproofSysId(sysCode);
            tempMaintenancePlanSysVoList.addAll(this.baseMapper.getSysFireNameAndScopeByEffectiveTime2(maintenancePlanSysDto));
        }*/

        //List<MaintenancePlanSysVo> tempMaintenancePlanSysVoList = this.baseMapper.getSysFireNameAndScopeByEffectiveTime2(maintenancePlanSysDto);
        maintenancePlanSysVoList.forEach(maintenancePlanSysVo -> {
            AtomicReference<Integer> finish = new AtomicReference<>(0);
            AtomicReference<Integer> total = new AtomicReference<>(0);
            maintenancePlanSysVo.getScopeTestVoList().forEach(scopeTestVo -> {
                finish.updateAndGet(v -> v + scopeTestVo.getMaintenancePoint());
                total.updateAndGet(v -> v + scopeTestVo.getPointNum());
             /*   tempMaintenancePlanSysVoList.forEach(tempMaintenancePlanSys -> {
                    if(tempMaintenancePlanSys.getFireproofSysId().equals(maintenancePlanSysVo.getFireproofSysId())){
                        tempMaintenancePlanSys.getScopeTestVoList().forEach(tempScopeTestVo ->{
                            if(scopeTestVo.getScopeTestName().equals(tempScopeTestVo.getScopeTestName())){
                                scopeTestVo.setScopeTestId(tempScopeTestVo.getScopeTestId());
                            }
                        });
                    }
                });*/
            });
            maintenancePlanSysVo.setFinishCt(finish.get());
            maintenancePlanSysVo.setTotalCt(total.get());
        });
        maintenancePlanSysVoList.forEach(maintenancePlanSysVo -> {
            Object weibaoJson = redisUtils.get("weibao-" + maintenancePlanSysDto.getBuildingId());
            if (weibaoJson == null) {
                return;
            }
            WeibaoDto weibaoDto = JSON.parseObject(weibaoJson.toString(), WeibaoDto.class);
            if (weibaoDto == null) {
                return;
            }
            if (maintenancePlanSysVo.getFinishCt() == null || ("0").equals(maintenancePlanSysVo.getFinishCt())) {
                AtomicReference<Integer> maintenancePoint = new AtomicReference<>(0);

                Map<String, Map<String, Boolean>> scopeDevicePointMap = weibaoDto.getScopeDevicePointMap();

                if (CollectionUtils.isEmpty(scopeDevicePointMap)) {
                    return;
                }
                scopeDevicePointMap.forEach((key, value) -> {

                    String id = maintenancePlanSysVo.getId();
                    if (StringUtils.isEmpty(id) || StringUtils.isEmpty(key)) {
                        return;
                    }

                    //范围在这里面的话 点位+1
                    if (Arrays.asList(id.split(",")).contains(key)) {
                        value.forEach((pointId, bool) -> {
                            if (bool) {
                                maintenancePoint.getAndSet(maintenancePoint.get() + 1);
                            }
                        });
                    }
                });
                maintenancePlanSysVo.setFinishCt(maintenancePoint.get());
            }
        });

        return maintenancePlanSysVoList;
    }

    @Override
    public List<DevicePointInfoVo> getScopeTestPoint(String scopeTestId) {
        //获取最小单位下的点位信息
        List<String> scopeTestIds = Arrays.asList(scopeTestId.split(","));
        List<DevicePointInfoVo> devicePointInfoVoList = this.baseMapper.getDevicePointInfoByScopeTestId(scopeTestIds);
        return devicePointInfoVoList;
    }

    @Override
    public PointNumDto getPointNum(String appointmentApplyId) {
        PointNumDto pointNumDto = new PointNumDto();
        //查询预约对应的点位
        List<DevicePointInfoVo> appointmentPointList = this.baseMapper.getAppointmentPoint(appointmentApplyId);
        pointNumDto.setPointNum(appointmentPointList.size());
//        //查询本月测试计划的点位
//        List<DevicePointInfoVo> devicePointInfoVoList = this.baseMapper.getPoIntNumByApplyId(appointmentApplyId);
//        //查询合集
//        final Integer[] maintenanceNum = {0};
//        devicePointInfoVoList.forEach(devicePointInfoVo -> {
//            appointmentPointList.forEach(appointmentPoint -> {
//                if(devicePointInfoVo.getDevicePointNum().equals(appointmentPoint.getDevicePointNum())){
//                    maintenanceNum[0]++;
//                }
//            });
//        });
        pointNumDto.setMaintenanceNum(0);
        return pointNumDto;
    }

    @Override
    public List<MaintenancePlanSysVo> getTestFireproofSysName(String appointmentApplyId) {
        return maintenanceConfigMapper.getTestFireproofSysName(appointmentApplyId);
    }

    @Override
    public List<DevicePointInfoVo> getTestDeviceInfo(String appointmentApplyId, String fireproofSysName) {
        return this.baseMapper.getTestDeviceInfo(appointmentApplyId, fireproofSysName);
    }

    @Override
    public void exportscope(MaintenancePlanSysDto maintenancePlanSysDto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        QueryWrapper planQuery = new QueryWrapper();
        String buildingId = maintenancePlanSysDto.getBuildingId();
        planQuery.eq("building_id", buildingId);
        planQuery.eq("scheduling", maintenancePlanSysDto.getScheduling());
        String effectiveTime = maintenancePlanSysDto.getEffectiveTime();
        planQuery.eq("effective_time", effectiveTime);
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(planQuery);

        List<DevicePointInfoVo> deviceStatistics = new ArrayList<>();


        List<MaintenanceScopeTest> scopeList = baseMapper.getScopeTestByPlanId(maintenancePlan.getId(), maintenancePlanSysDto.getFireproofSysId());

        scopeList.forEach(scopeTest -> {
            List<DevicePointInfoVo> devicePointInfoVoList = getWebScopeTestPointByEffTime(scopeTest.getId(), buildingId, effectiveTime, null);
            deviceStatistics.addAll(devicePointInfoVoList);
        });


        //List<DevicePointInfoVo> deviceStatistics = baseMapper.selectAllStatistics(maintenancePlan.getId(), maintenancePlanSysDto);

        //数据填充
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("sheet");

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);

        //表头
        HSSFRow row0 = sheet.createRow(0);
        cellCenter(workbook, row0.createCell(0), "序号", style);
        cellCenter(workbook, row0.createCell(1), "楼层", style);
        cellCenter(workbook, row0.createCell(2), "点位编号", style);
        cellCenter(workbook, row0.createCell(3), "点位描述", style);
        cellCenter(workbook, row0.createCell(4), "设备类型", style);
        cellCenter(workbook, row0.createCell(5), "维保状态", style);
        cellCenter(workbook, row0.createCell(6), "维保时间", style);
        AtomicReference<Integer> index = new AtomicReference<>(1);
        AtomicReference<Integer> num = new AtomicReference<>(0);
        deviceStatistics.forEach(ds -> {
            HSSFRow row = sheet.createRow(index.getAndSet(index.get() + 1));
            num.getAndSet(num.get() + 1);
            cellCenter(workbook, row.createCell(0), num.toString(), style);
            cellCenter(workbook, row.createCell(1), ds.getFloorName(), style);
            cellCenter(workbook, row.createCell(2), ds.getDevicePointNum(), style);
            cellCenter(workbook, row.createCell(3), ds.getPointDesc(), style);
            cellCenter(workbook, row.createCell(4), ds.getDeviceType(), style);
            cellCenter(workbook, row.createCell(5), ds.getMaintenanceStatus() == null || ds.getMaintenanceStatus().equals(0) ? "未维保" : "已维保", style);
            cellCenter(workbook, row.createCell(5), ds.getDate(), style);
        });
        OutputStream output = null;

        try {
            output = response.getOutputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String fileName = DateUtils.formatDatetime(new Date()) + ".xlsx";
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(output);
        output.close();

        Journalizing journalizing = new Journalizing();
        String buildingName = baseBuildingService.getBuildingInfoById(buildingId).getBuildingName();
        journalizing.setOperationTypeCode(LogTypeEnums.MAINTENANCE_PLAN.getType());
        String fireproofSysName = maintenanceConfigMapper.getFireproofSysName(maintenancePlanSysDto.getFireproofSysId());
        journalizing.setOperationContent("导出" + buildingName + "的" + effectiveTime
                + "的" + fireproofSysName + "的测试计划");
        journalizingService.setLogInfo(request, journalizing);


    }

    @Override
    public List<DevicePointInfoVo> getWebScopeTestPointByEffTime(String scopeTestId, String buildingId, String effectiveTime, String scheduling) {
        //根据scopeTestId 获取配置
        List<DevicePointInfoVo> result = new ArrayList<>();

        List<String> scopeTestIds = Arrays.asList(scopeTestId.split(","));

        String scopeConfigRuleKey = SCOPE_CONFIG_RULE_TYPE_KEY + scopeTestId;
        Object scopeConfigRuleObj = redisTemplate.opsForValue().get(scopeConfigRuleKey);
        List<ScopeTestRuleVo> scopeTestRule = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(scopeConfigRuleObj)) {
            scopeTestRule = (List<ScopeTestRuleVo>) scopeConfigRuleObj;
        } else {
            scopeTestRule = this.baseMapper.getMaintenanceConfigListByScopeTestId(scopeTestIds, effectiveTime);
            redisTemplate.opsForValue().set(scopeConfigRuleKey, scopeTestRule, 30L, TimeUnit.MINUTES);
        }

        scopeTestRule.forEach(scopeRule -> {
            String batchCode = null;
            switch (scopeRule.getGenerationRule()) {
                case "1":
                    batchCode = "floor_id";
                    break;
                case "2":
                    batchCode = "loop_code";
                    break;
                case "3":
                    batchCode = "zone_name";
                    break;
            }

            List<String> scopeTestIdList = Arrays.asList(scopeRule.getScopeId().split(","));

            //TODO 需优化
            List<DevicePointInfoVo> devicePointInfoVoList = this.baseMapper.getWebDevicePointInfoByScopeTestIdAndScheduling(scopeTestIdList, batchCode, null, effectiveTime);

            devicePointInfoVoList.forEach(devicePointInfoVo -> {
                devicePointInfoVo.setScopeTestName(null);
            });
            result.addAll(devicePointInfoVoList);
        });
        if (result != null && result.size() > 0) {
            Object weibaoJson = redisUtils.get("weibao-" + result.get(0).getBuildingId());
            if (weibaoJson != null) {
                WeibaoDto weibaoDto = JSON.parseObject(weibaoJson.toString(), WeibaoDto.class);
                result.forEach(devicePointInfoVo -> {
                    weibaoDto.getBaseDevicePointMap().forEach((key, value) -> {
                        if (devicePointInfoVo.getId().equals(key) && value) {
                            devicePointInfoVo.setMaintenanceStatus(1);
                        }
                    });
                });
            }

            String finishKey = "weibao-finish-" + buildingId + "=" + effectiveTime;
            Object o = redisUtils.get(finishKey);

            List<String> finishList;

            //获取本月维保的点位
            if (null != o) {
                finishList = JSON.parseArray(o.toString(), String.class);
                log.info("getFinishPointByBuildingIdAndEffTime查询缓存得到数据条数为{}，传参为：buildingId:{};effectiveTime:{}", finishList == null ? -1 : finishList.size(),
                        buildingId, effectiveTime);
            } else {
                finishList = this.baseMapper.getFinishPointByBuildingIdAndEffTime(buildingId, effectiveTime);
                log.info("getFinishPointByBuildingIdAndEffTime查询到得数据条数为{}，传参为：buildingId:{};effectiveTime:{}", finishList == null ? -1 : finishList.size(),
                        buildingId, effectiveTime);
                redisUtils.set(finishKey, JSON.toJSONString(finishList), 10L, TimeUnit.MINUTES);
            }
            if (!CollectionUtils.isEmpty(finishList)) {
                List<String> finalFinishList = finishList;
                result.forEach(devicePointInfoVo -> {
                    finalFinishList.forEach(finishKeyStr -> {
                        if (finishKeyStr.split("_")[0] != null && finishKeyStr.split("_")[0].equals(devicePointInfoVo.getId())) {
                            devicePointInfoVo.setMaintenanceStatus(1);
                            if (finishKeyStr.split("_")[1] != null) {
                                devicePointInfoVo.setDate(finishKeyStr.split("_")[1]);
                            }
                        }
                    });
//                    if(finalFinishList.contains(devicePointInfoVo.getId())){
//                        devicePointInfoVo.setMaintenanceStatus(1);
//                    }
                });
                return result.stream().sorted(Comparator.comparing(DevicePointInfoVo::getMaintenanceStatus)).collect(Collectors.toList());
            }
        }
        return result;
    }

    /**
     * 添加excel单元格内容并居中
     *
     * @author: lf
     * @param: workbook
     * @return: org.apache.poi.hssf.usermodel.HSSFCell
     */
    public HSSFCell cellCenter(HSSFWorkbook workbook, HSSFCell cell, String str, HSSFCellStyle style) {
        cell.setCellValue(str);
        return cell;
    }


}
