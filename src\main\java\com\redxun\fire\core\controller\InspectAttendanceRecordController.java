package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.utils.ExceptionUtil;
import com.redxun.fire.core.entity.InspectAttendanceRecord;
import com.redxun.fire.core.utils.validated.Insert;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.inspect.InspectAttendanceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * <p>
 * 考勤记录表前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-11-22
 */
@Slf4j
@Api(tags = "考勤记录表")
@RestController
@RequestMapping("/inspectAttendanceRecord")
public class InspectAttendanceRecordController {

    private String message;
    @Resource
    InspectAttendanceRecordService service;

    @PostMapping("select")
    @ApiOperation("查询考勤记录")
    public JsonPageResult selectInspectAttendanceRecordPage(@RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage page = service.selectInspectAttendanceRecordPage(queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    @PostMapping("insert")
    public JsonResult insert(@RequestBody @Validated(Insert.class) InspectAttendanceRecord query) throws Exception {
        try {
            service.insertInspectAttendanceRecord(query);
            return JsonResult.Success();
        } catch (Exception e) {
            message = "新增失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    @PostMapping("update")
    public JsonResult update(@RequestBody @Validated(Update.class) InspectAttendanceRecord query) throws Exception {
        try {
            service.updateInspectAttendanceRecord(query);
            return JsonResult.Success();
        } catch (Exception e) {
            message = "修改失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    @GetMapping(value = "delete/{ids}")
    public JsonResult delete(@PathVariable Integer[] ids) throws Exception {
        try {
            service.deleteInspectAttendanceRecordById(ids);
            return JsonResult.Success();
        } catch (Exception e) {
            message = "删除失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    @PostMapping("punchCard")
    @ApiOperation("打卡")
    public JsonResult punchCard(HttpServletRequest request, @RequestBody InspectAttendanceRecord attendanceRecord) throws Exception {
        try {
            boolean result = service.punchCard(request, attendanceRecord);
            if (!result) {
                return JsonResult.Success().setData(false).setMessage("打卡失败，请联系管理员！");
            } else {
                return JsonResult.Success().setData(true);
            }
        } catch (Exception e) {
            message = "打卡失败";
            log.error(message, e);
            return JsonResult.Success().setData(false).setMessage("打卡失败，请联系管理员！");
        }
    }

    @GetMapping("selectByDate")
    @ApiOperation("根据日期，人员，查打卡记录")
    public JsonResult selectByDate(HttpServletRequest request, String recordDate) {
        String userId = request.getHeader("xfUserId");//消防userId
        InspectAttendanceRecord inspectAttendanceRecord = service.getOne(new QueryWrapper<InspectAttendanceRecord>()
                .eq("record_date", recordDate).eq("user_id", userId));
        return JsonResult.Success().setData(inspectAttendanceRecord);
    }


    @GetMapping("test")
    @ApiOperation("test")
    public JsonResult test() {
        service.generateRecord();
        return JsonResult.Success().setData("1");
    }


}
