<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.FireAlarmMapper">

    <select id="getUnreported" resultType="org.apache.commons.collections4.map.LinkedMap">
        select t1.id                        as id,
               ifnull(t1.building_id, '')   as buildingId,
               ifnull(t1.first_time, '')    as firstTime,
               ifnull(t1.last_time, '')     as lastTime,
               ifnull(t1.fire_type, '')     as fireType,
               ifnull(t1.dev_type, '')      as devType,
               ifnull(t1.checkerId, '')     as checkId,
               ifnull(t1.dev_name, '')      as deviceName,
               ifnull(t1.executor, '')      as executor,
               ifnull(t1.executor_id, '')   as executorId,
               ifnull(t1.type, '')          as infoType,
               ifnull(t1.point_id, '')      as pointId,
               ifnull(t1.fire_status, '')   as fireStatus,
               ifnull(t1.report_status, '') as reportStatus,
               ifnull(t1.video_url, '')     as videoUrl,
               ifnull(t1.checker, '')       as checkUser,
               ifnull(t1.report_times, '0') as reportTimes,
               ifnull(t1.execute_time, '')  as executeTime,
               ifnull(t1.building_name, '') as buildingName,
               ifnull(t1.point_desc, '')    as pointDesc,
               ifnull(t1.point_code, '')  as pointCode,
               ifnull(t1.zone_name, '')     as zoneName,
               ifnull(t1.dev_name, '') as devTypeName,
               ifnull(t4.check_time, '')    as checkTime,
               ifnull(t4.fire_type, '')     as checkFireType,
               ifnull(t4.upload_time, '')   as uploadTime
        FROM fire_info as t1
                 left join fire_check t4 ON t1.id = t4.fire_id
        where t1.building_id = #{buildingId}
          and t1.fire_status = '1'
          and t1.type = '0'
          and t1.report_status = '0'
          and t1.building_status_str != '特批'
        order BY t1.execute_time desc
    </select>

    <select id="getUnhandled" resultType="org.apache.commons.collections4.map.LinkedMap">
        select t1.id                        as id,
               ifnull(t1.building_name, '') as buildingName,
               ifnull(t1.point_desc, '')    as pointDesc,
               ifnull(t1.point_code, '')  as pointCode,
               ifnull(t1.zone_name, '')     as zoneName,
               ifnull(t1.dev_name, '') as devTypeName,
               ifnull(t4.check_time, '')    as checkTime,
               ifnull(t4.fire_type, '')     as checkFireType,
               ifnull(t4.upload_time, '')   as uploadTime,
               ifnull(t1.building_id, '')   as buildingId,
               ifnull(t1.first_time, '')    as firstTime,
               ifnull(t1.last_time, '')     as lastTime,
               ifnull(t1.fire_type, '')     as fireType,
               ifnull(t1.dev_type, '')      as devType,
               ifnull(t1.checkerId, '')     as checkId,
               ifnull(t1.dev_name, '')      as deviceName,
               ifnull(t1.executor, '')      as executor,
               ifnull(t1.executor_id, '')   as executorId,
               ifnull(t1.type, '')          as infoType,
               ifnull(t1.point_id, '')      as pointId,
               ifnull(t1.fire_status, '')   as fireStatus,
               ifnull(t1.report_status, '') as reportStatus,
               ifnull(t1.video_url, '')     as videoUrl,
               ifnull(t1.checker, '')       as checkUser,
               ifnull(t1.report_times, '0') as reportTimes,
               ifnull(t1.execute_time, '')  as executeTime
        from fire_info t1
                 left join fire_check t4 on t1.id = t4.fire_id
        WHERE t1.building_id = #{buildingId}
          AND t1.fire_status = '0'
          and t1.building_status_str != '特批'
        order BY t1.last_time desc
    </select>

    <select id="getFinshed" resultType="org.apache.commons.collections4.map.LinkedMap">
        select t1.id                        as id,
               ifnull(t1.building_id, '')   as buildingId,
               ifnull(t1.first_time, '')    as firstTime,
               ifnull(t1.last_time, '')     as lastTime,
               ifnull(t1.fire_type, '')     as fireType,
               ifnull(t1.dev_type, '')      as devType,
               ifnull(t1.checkerId, '')     as checkId,
               ifnull(t1.dev_name, '')      as deviceName,
               ifnull(t1.executor, '')      as executor,
               ifnull(t1.executor_id, '')   as executorId,
               ifnull(t1.type, '')          as infoType,
               ifnull(t1.point_id, '')      as pointId,
               ifnull(t1.fire_status, '')   as fireStatus,
               ifnull(t1.report_status, '') as reportStatus,
               ifnull(t1.video_url, '')     as videoUrl,
               ifnull(t1.checker, '')       as checkUser,
               ifnull(t1.report_times, '0') as reportTimes,
               ifnull(t1.execute_time, '')  as executeTime,
               ifnull(t1.building_name, '') as buildingName,
               ifnull(t1.point_desc, '')    as pointDesc,
               ifnull(t1.point_code, '')  as pointCode,
               ifnull(t1.zone_name, '')     as zoneName,
               ifnull(t1.dev_name, '') as devTypeName,
               ifnull(t4.check_time, '')    as checkTime,
               ifnull(t4.fire_type, '')     as checkFireType,
               ifnull(t4.upload_time, '')   as uploadTime
        FROM fire_info as t1
                 left join fire_check t4 ON t1.id = t4.fire_id
        where t1.building_id = #{buildingId}
          and t1.fire_status = '1'
          and t1.type in
           <foreach item="item" index="index" collection="types" open="(" separator="," close=")">
             #{item}
           </foreach>

        and t1.building_status_str != '特批'
          and t1.last_time <![CDATA[ >= ]]> date_format(date_sub(now(),interval 1 day),'%Y-%m-%d %H:%i:%s')
        order BY t1.execute_time desc
    </select>

    <select id="getFireCheckInfoList" resultType="java.util.Map">
        select ifnull(t1.check_user_id, '') as checkUserId
             , ifnull(t1.check_user, '')    as checkUser
             , ifnull(t1.fire_type, '')     as fireType
             , ifnull(t1.check_time, '')    as checkTime
             , ifnull(t2.check_type, '')    as checkType
             , ifnull(t2.path, '')          as path
             , ifnull(t2.id, '')            as picId
             , ifnull(t2.real_name, '')     as realName
        from fire_check t1
                 right join
             fire_check_attach t2 on t1.id = t2.check_id
        order by t2.check_type
    </select>

    <select id="repairmanList" resultType="java.util.Map">
        select *
        from (
                 (select ifnull(t1.wb_per_name, '')  as wbPersonnelName
                       , ifnull(t1.id, '')           as wbPerId
                       , ifnull(t1.role, '')         as role
                       , ifnull(t1.wb_per_tel, '')   as wbPerTel
                       , ifnull(t1.wb_team_id, '')   as wbTeamId
                       , ifnull(t2.wb_team_name, '') as wbTeamName
                  from wb_personnel t1
                           left join wb_team t2
                                     on t1.wb_team_id = t2.id
                  where t2.building_id = #{buildingId}
                 )) as t3
                 left join
             (select count(id) as numbers
                   , wb_team_id
              from wb_personnel
              group by wb_team_id) as t4
             on t3.wbTeamId = t4.wb_team_id
    </select>

    <select id="getTeamNums" resultType="java.lang.Integer">
        select count(*)
        from wb_personnel t1
        where t1.wb_team_id = #{wbTeamId}
    </select>

    <update id="backToNormal">
        update
        fire_info t1
        set
        <if test="param.executor!=null and  param.executor!=''">
            t1.executor = #{param.executor},
        </if>
        <if test="param.executorId!=null and param.executorId!=''">
            t1.executor_id = #{param.executorId},
        </if>
        <if test="param.executeTime !=null and param.executeTime!=''">
            t1.execute_time = #{param.executeTime},
        </if>
        t1.fire_status = '1'
        where
        t1.id = #{param.id}
    </update>

    <select id="getTestFireInfo" resultType="java.util.Map">
        select ifnull(t1.point_desc, '') as pointDesc,
               ifnull(t1.point_code, '') as pointCode,
               ifnull(t1.first_time, '') as reportTime
        from fire_info as t1
        where t1.id = #{id}
    </select>

    <select id="getCounts" resultType="java.util.Map">
        select ifnull(sum(case fire_status when '0' then 1 else 0 end), 0) as fileCounts,
               ifnull(sum(case when fire_status = '1' and report_status = '0' and type = '0' then 1 else 0 end),
                      0)                                                   as notFillIn,
               ifnull(sum(case fire_status when '1' then 1 else 0 end), 0) as completed
        from fire_info
        where building_id = #{buildingId}
          and building_status_str != '特批'
    </select>

    <select id="getCompletedCount" resultType="java.lang.Integer">
        select
            ifnull(sum(case fire_status when '1' then 1 else 0 end), 0) as completed
        from fire_info
        where building_id = #{buildingId}
          and last_time <![CDATA[ >= ]]> date_format(date_sub(now(),interval 1 day),'%Y-%m-%d %H:%i:%s')
          and building_status_str != '特批'
    </select>

    <select id="queryInfo" resultType="java.util.Map">
        select ifnull(t1.first_time, '')    as firstTime,
               ifnull(t1.execute_time, '')  as executeTime,
               ifnull(t1.fire_status, '')   as fireStatus,
               ifnull(t1.report_status, '') as reportStatus
        from fire_info as t1
        where t1.last_time <![CDATA[>]]> #{param.startTime}
          and t1.last_time <![CDATA[<=]]> #{param.endTime}
          and t1.building_id = #{param.buildingId}
          and t1.type = '0'
    </select>

    <select id="statisticsDaily" resultType="java.util.Map">
            select count(*)     as counts,
                   t1.fire_type as fireType
            from fire_info as t1
            where t1.building_id = #{param.buildingId}
              and t1.execute_time <![CDATA[>]]> #{param.startTime}
              and t1.execute_time <![CDATA[<=]]> #{param.endTime}
              and t1.fire_type is not null
            group by t1.fire_type
            order by fire_type
    </select>

    <select id="selectAll" resultType="java.util.Map">
        select ifnull(t1.building_id, '')       as buildingId,
               ifnull(t1.building_name, '')     as buildingName,
               ifnull(count(t1.fire_status), 0) as fireCounts
        from fire_info t1
        where t1.fire_status = '0'
        group by t1.building_id
    </select>

    <select id="getFireCounts" resultType="java.lang.Integer">
        select ifnull(count(t1.id), 0) as counts
        from fire_info t1
        where t1.fire_status = '0'
    </select>

        <select id="selectFeed" resultType="org.apache.commons.collections4.map.LinkedMap">
        select distinct t1.id                        as id,
                        ifnull(t1.sensor_num, '')    as sensorNum,
                        ifnull(t1.building_id, '')   as buildingId,
                        ifnull(t1.building_name, '') as buildingName,
                        ifnull(t1.point_id, '')      as pointId,
                        ifnull(t1.point_desc, '')    as pointDesc,
                        ifnull(t1.point_code, '')  as pointCode,
                        ifnull(t1.first_time, '')    as firstTime,
                        ifnull(t1.last_time, '')     as lastTime,
                        ifnull(t4.fire_type, '')     as fireType,
                        ifnull(t1.dev_type, '')      as devType,
                        ifnull(t1.zone_name, '')     as zoneName,
                        ifnull(t1.dev_name, '')      as deviceName,
                        ifnull(t1.executor, '')      as executor,
                        ifnull(t1.executor_id, '')   as executorId,
                        ifnull(t1.type, '')          as infoType,
                        ifnull(t1.fire_status, '')   as fireStatus,
                        ifnull(t1.report_status, '') as reportStatus,
                        ifnull(t1.video_url, '')     as videoUrl,
                        ifnull(t1.dev_name, '') as devTypeName,
                        ifnull(t1.execute_time, '')  as executeTime,
                        ifnull(t1.report_times, '')  as reportTimes,
                        ifnull(t4.check_time, '')    as checkTime,
                        ifnull(t1.feedback_time, '') as feedBackTime
        from fire_info as t1
                 left join fire_check t4
                           on t1.id = t4.fire_id
        where t1.building_id = #{buildingId}
          and t1.type = '0'
          and t1.feedback_time is not null
          and t1.building_status_str != '特批'
        order by t1.feedback_time desc
    </select>

    <select id="getPointNum" resultType="java.lang.Integer">
        select count(distinct point_id)
        from fire_info t1
        where
        t1.building_id = #{buildingId}
        <choose>
            <when test="check != null and check != '' ">
                and t1.feedback_time is null
            </when>
            <otherwise>
                and t1.fire_status = '0'
            </otherwise>
        </choose>
        <if test="zoneName != null and zoneName != '' ">
            and t1.zone_name = #{zoneName}
        </if>
    </select>

    <select id="getLastId" resultType="java.lang.String">
        select
        t1.id
        from
        fire_info as t1
        where
        1 =1
        and t1.id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t1.last_time
        limit 1
    </select>

    <!-- 现场处置用户首页展示条数-->
    <select id="selectUnFeedCountAllBuild" resultType="java.lang.Integer">
        select count(*)
        from fire_info t1
        where t1.feedback_time is null
        and t1.type = '0'
        and t1.building_status_str in ('正常')
        and t1.last_time > DATE_FORMAT(NOW(),'%Y-%m-%d')
        and t1.building_id in
        <foreach item="item" index="index" collection="param.belongDeps" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectUnFeed" resultType="org.apache.commons.collections4.map.LinkedMap">
        select t1.id                        as id,
               ifnull(t1.sensor_num, '')    as sensorNum,
               ifnull(t1.building_id, '')   as buildingId,
               ifnull(t1.building_name, '') as buildingName,
               ifnull(t1.point_id, '')      as pointId,
               ifnull(t1.point_desc, '')    as pointDesc,
               ifnull(t1.point_code, '')  as pointCode,
               ifnull(t1.first_time, '')    as firstTime,
               ifnull(t1.last_time, '')     as lastTime,
               ifnull(t4.fire_type, '')     as fireType,
               ifnull(t1.dev_type, '')      as devType,
               ifnull(t1.zone_name, '')     as zoneName,
               ifnull(t1.dev_name, '')      as deviceName,
               ifnull(t1.executor, '')      as executor,
               ifnull(t1.executor_id, '')   as executorId,
               ifnull(t1.type, '')          as infoType,
               ifnull(t1.video_url, '')     as videoUrl,
               ifnull(t1.fire_status, '')   as fireStatus,
               ifnull(t1.report_status, '') as reportStatus,
               ifnull(t1.dev_name, '') as devTypeName,
               ifnull(t1.report_times, '')  as reportTimes,
               ifnull(t1.execute_time, '')  as executeTime
        from fire_info as t1
                 left join fire_check t4
                           on t1.id = t4.fire_id
        where t1.building_id = #{buildingId}
          and t1.type = '0'
          and t1.feedback_time is null
          and t1.building_status_str != '特批'
        order by t1.last_time desc
    </select>

    <select id="getUnFeed" resultType="java.util.Map">
        select ifnull(count(*), 0) as unFeed
        from fire_info as t1
        where t1.building_id = #{buildingId}
          and t1.type = '0'
          and t1.feedback_time is null
          and t1.building_status_str != '特批'
    </select>


    <select id="getSyncIds" resultType="java.lang.String">
        select id
        from fire_info as t4
        where last_time <![CDATA[>]]> #{timeSection}
          and last_time <![CDATA[<=]]> #{lastTime}
          and t4.point_id in
              (select t1.id as pointId
               from base_device_point as t1
                        left join
                    (select t2.building_id as buildingId,
                            t2.loop_code   as loopCode,
                            t2.host_num    as hostNum
                     from base_device_point as t2
                     where 1 = 1
                       and t2.id = #{pointId})
                        as t3
                    on t1.building_id = t3.buildingId
               where t1.host_num = t3.hostNum
                 and t1.loop_code = t3.loopCode)
    </select>

</mapper>

