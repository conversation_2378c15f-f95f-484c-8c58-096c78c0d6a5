package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.common.base.db.BaseDao;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.FireInfo;
import com.redxun.fire.core.pojo.dto.FireTrendDto;
import com.redxun.fire.core.pojo.sync.dto.SyncFIreInfoDTO;
import com.redxun.fire.core.pojo.sync.param.SyncFireInfoQO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 火警信息表数据库访问层
*/
@Mapper
public interface FireInfoMapper extends BaseDao<FireInfo> {

    FireInfo selectInfoById(@Param("fireId") String fireId);

    List<Map<String, Object>> selectWaitFillInfo(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectWaitFillInfoAll(Page<?> page, @Param("param") Map param);

    List<FireInfo> selectFireExpection(@Param("param") Map param);

    List<FireInfo> selectFireExpectionAll(@Param("param") Map param);

    /**
     * @description: 今日之前火警信息
     * @param: param
     * @return: java.util.List<com.redxun.fire.core.entity.FireInfo>
     */
    List<FireInfo> selectFireExpectionAllBefore(@Param("param") Map param);

    List<FireInfo> selectWaitFill(@Param("param") Map param);

    List<Map<String, Object>> selectTodayFireInfo(@Param("param") Map param);

    //List<Map<String, Object>> selectBeforeFireInfo(@Param("param") Map param);

    List<FireInfo> selectTodayFire(@Param("param") Map fireParam);

    Integer selectFireToScore(@Param("param") Map param);

    Double selectEmergencyToScore(@Param("param") Map param);

    List<Map<String, Object>> getByFireid(String fireId);

    /**
     *  用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectFireInfoToExport(@Param("param") Map param);

    IPage<SyncFIreInfoDTO> selectSyncPage(Page<BaseDevicePoint> page, @Param("qo") SyncFireInfoQO qo);



    int getWbPointDays(@Param("pointId") String pointId, @Param("type") String type, @Param("fourteenTime")String fourteenTime,
                       @Param("endTime") String endTime,@Param("buildingStatus") String buildingStatus);


    /*List<Map<String,Object>> selectFillInOutTime(@Param("param") Map param);*/
    List<FireTrendDto> listFireTrendInfo(@Param("time") String time);
}
