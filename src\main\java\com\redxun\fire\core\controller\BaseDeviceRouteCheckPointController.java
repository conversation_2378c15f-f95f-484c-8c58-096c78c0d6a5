package com.redxun.fire.core.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.BaseDeviceRouteCheckPoint;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.device.IBaseDeviceRouteCheckPointService;
import com.redxun.fire.core.service.alarm.IStatRouteCheckExceptionService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */

@Slf4j
@RestController
@RequestMapping("/base-device-route-check-point")
public class BaseDeviceRouteCheckPointController {

    @Resource
    IBaseDeviceRouteCheckPointService baseDeviceRouteCheckPointService;
    @Resource
    IBaseDevicePointService baseDevicePointService;
    @Resource
    IStatRouteCheckExceptionService statRouteCheckExceptionService;

    @Resource
    private IBaseBuildingFloorService iBaseBuildingFloorService;

    /**
     * 新增检线设备
     */
    @PostMapping("saveBaseDeviceRouteCheckPoint")
    public JsonResult<String> add(HttpServletRequest request, @RequestBody BaseDeviceRouteCheckPoint baseDeviceRouteCheckPoint) {
        return baseDeviceRouteCheckPointService.add(request, baseDeviceRouteCheckPoint);
    }

    /**
     * 修改检线设备
     */
    @PostMapping("updateBaseDeviceRouteCheckPoint")
    public JsonResult<String> update(HttpServletRequest request, @RequestBody BaseDeviceRouteCheckPoint baseDeviceRouteCheckPoint) {
        return baseDeviceRouteCheckPointService.update(request, baseDeviceRouteCheckPoint);
    }

    /**
     * 删除检线设备
     */
    @ResponseBody
    @GetMapping("delete/{id}")
    public JsonResult<String> delete(HttpServletRequest request, @PathVariable(name = "id") String id) {
        BaseDeviceRouteCheckPoint baseDeviceRouteCheckPoint = new BaseDeviceRouteCheckPoint();
        baseDeviceRouteCheckPoint.setId(id);
        baseDeviceRouteCheckPoint.setFlag("1");
        return baseDeviceRouteCheckPointService.update(request, baseDeviceRouteCheckPoint);
    }

    /**
     * 删除检线设备
     */
    @PostMapping("delete")
    public JsonResult<String> deleteByIds(HttpServletRequest request, @RequestBody List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            for (String id : ids) {
                BaseDeviceRouteCheckPoint baseDeviceRouteCheckPoint = new BaseDeviceRouteCheckPoint();
                baseDeviceRouteCheckPoint.setId(id);
                baseDeviceRouteCheckPoint.setFlag("1");
                baseDeviceRouteCheckPointService.update(request, baseDeviceRouteCheckPoint);
            }
        }
        return JsonResult.Success("修改成功!");
    }


    /**
     * 检线设备查询
     */
    @ResponseBody
    @GetMapping("queryBaseDeviceRouteCheckPointById/{id}")
    public JsonResult<BaseDeviceRouteCheckPoint> queryBaseDeviceRouteCheckById(@PathVariable(name = "id") String id) {
        return new JsonResult<BaseDeviceRouteCheckPoint>().setData(baseDeviceRouteCheckPointService.getById(id));
    }

    /**
     * 点位根据id查询
     */
    @ResponseBody
    @GetMapping("queryPointById")
    public JsonResult<BaseDevicePoint> queryPointById(String id) {
        if (!StringUtils.isEmpty(id)) {
            final val point = baseDevicePointService.getById(id);
            if (ObjectUtil.isNotEmpty(point) && StrUtil.isNotBlank(point.getFloorId())) {
                final val floor = iBaseBuildingFloorService.getBaseMapper().selectById(point.getFloorId());
                if (ObjectUtil.isNotEmpty(floor)) {
                    point.setWztFloorId(floor.getWztFloorId());
                    point.setWztFloorName(floor.getWztFloorName());
                    point.setWztBuilding(floor.getWztConstructId());
                    point.setWztBuildingName(floor.getWztConstructName());
                }
            }
            return new JsonResult<BaseDevicePoint>().setData(point);
        } else {
            return new JsonResult<BaseDevicePoint>().setData(null);
        }
    }

    /**
     * 根据建筑id加载各种主机信息以及离线信息
     * normal
     */
    @ResponseBody
    @GetMapping("getBaseDeviceRouteCheckPointByBuildingId")
    public JsonResult<List<BaseDeviceRouteCheckPoint>> getBaseDeviceRouteCheckPointByBuildingId(String buildingId, String port, String normalPort) {
        if (!StringUtils.isEmpty(buildingId)) {
            LambdaQueryWrapper<BaseDeviceRouteCheckPoint> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BaseDeviceRouteCheckPoint::getBuildingId, buildingId);
            lambdaQueryWrapper.orderByDesc(BaseDeviceRouteCheckPoint::getHostNumber);
            if (!StringUtils.isEmpty(port)) {
                lambdaQueryWrapper.in(BaseDeviceRouteCheckPoint::getPort, Arrays.asList(port.split(",")));
            } else {
                lambdaQueryWrapper.eq(BaseDeviceRouteCheckPoint::getIsAuto, "1");
            }
            List<BaseDeviceRouteCheckPoint> baseDeviceRouteCheckPointList = baseDeviceRouteCheckPointService.list(lambdaQueryWrapper);
            List<String> normalPortArray = normalPort == null ? new ArrayList<String>() : Arrays.asList(normalPort.split(","));
            baseDeviceRouteCheckPointList.forEach(baseDeviceRouteCheckPoint -> {
                if (!CollectionUtils.isEmpty(normalPortArray)) {
                    if (normalPortArray.contains(baseDeviceRouteCheckPoint.getPort())) {
                        baseDeviceRouteCheckPoint.setStatus(1);
                    } else {
                        baseDeviceRouteCheckPoint.setStatus(0);
                    }
                }
                if (baseDeviceRouteCheckPoint.getStatus() == null) {
                    baseDeviceRouteCheckPoint.setStatus(0);
                }
            });
            return new JsonResult<List<BaseDeviceRouteCheckPoint>>().setData(baseDeviceRouteCheckPointList);
        }
        return new JsonResult<List<BaseDeviceRouteCheckPoint>>().setData(null);
    }

    /**
     * 全体自动/全体手动
     */
    @ResponseBody
    @GetMapping("updateIsAuto")
    public JsonResult<BaseDeviceRouteCheckPoint> updateIsAuto(Integer isAuto) {
        baseDeviceRouteCheckPointService.updateIsAuto(isAuto);
        return new JsonResult<BaseDeviceRouteCheckPoint>().setData(null).setShow(false);
    }

    /**
     * 检线时长统一设置
     */
    @ResponseBody
    @GetMapping("updateCheckTime")
    public JsonResult<BaseDeviceRouteCheckPoint> updateCheckTime(Integer checkTime) {
        baseDeviceRouteCheckPointService.updateCheckTime(checkTime);
        return new JsonResult<BaseDeviceRouteCheckPoint>().setData(null).setShow(false);
    }

    /**
     * 检线时长统一设置
     */
    @ResponseBody
    @GetMapping("sendRouteCheck")
    public JsonResult<String> sendRouteCheck(String id) {
        BaseDeviceRouteCheckPoint baseDeviceRouteCheckPoint = baseDeviceRouteCheckPointService.getById(id);
//        if(baseDeviceRouteCheckPoint.isAuto.equals(1)){
//            return  new JsonResult<String>().setData(null).setMessage("该端口处于自动状态，无法检线").setShow(false);
//        }
        Map<String, String> param = new HashMap<>();
        param.put("deviceCode", baseDeviceRouteCheckPoint.getDeviceNumber());
        param.put("port", baseDeviceRouteCheckPoint.getPort());
        return new JsonResult<String>().setData(null).setMessage(statRouteCheckExceptionService.sendRouteCheck(param)).setShow(false);
    }
}
