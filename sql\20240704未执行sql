ALTER TABLE os_group ADD `WZT_MERCHANT_ID` VARCHAR(64) DEFAULT NULL COMMENT '万中台商户Id';

ALTER TABLE os_user ADD `WZT_MERCHANT_ID` VARCHAR(64) DEFAULT NULL COMMENT '万中台商户Id';
ALTER TABLE os_user ADD `WZT_MERCHANT_NAME` VARCHAR(255) DEFAULT NULL COMMENT '万中台商户名称';

-- 20240730
alter table point_sync_log add index `idx_time_and_build` (`create_time`,`building_id`,`sync_state`);
alter table maintenance_plan add COLUMN `plan_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维保计划编号';
alter table maintenance_plan add COLUMN `manage_org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物业管理单位id';
alter table maintenance_plan add COLUMN `manage_org_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物业管理单位名称';
alter table maintenance_plan add COLUMN  `contract_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维保合同id';
alter table maintenance_plan add COLUMN `contract_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维保合同名称';

-- 20240821
alter table base_device_point add index `index_build_number_code` (`building_id`,`point_number`) USING BTREE;

-- 20240918
alter table base_device_point add index `index_build_code_loop` (building_id, point_code, host_num, loop_code) USING BTREE;

-- 20240923 人员表新增字段
alter table os_user add column `WX_POSITION_NAME_` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万信岗位';

-- 需要晚上添加
ALTER TABLE `wd_iot_fire`.`base_device_linkage`
ADD INDEX `IDX_BUILDING_ID_IS_READ`(`building_id`, `is_read`) USING BTREE;


route_check_record create_time_

