package com.redxun.fire.core.entity.pointalarmstatistic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 点位年度报警统计
 * @TableName point_yearly_alarm_statistic
 */
@TableName(value ="point_yearly_alarm_statistic")
@Data
public class PointYearlyAlarmStatistic implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 点位id
     */
    @TableField(value = "point_id")
    private String pointId;

    /**
     * 测试次数
     */
    @TableField(value = "test_num")
    private Long testNum;

    /**
     * 报警次数
     */
    @TableField(value = "alarm_num")
    private Long alarmNum;

    /**
     * 点位状态
     */
    @TableField(value = "point_state")
    private String pointState;

    /**
     * 年份
     */
    @TableField(value = "year")
    private String year;

    @TableField(value = "building_id")
    private String buildingId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}