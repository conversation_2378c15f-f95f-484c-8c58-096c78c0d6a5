package com.redxun.fire.core.controller.pointstatistic;

import com.alibaba.fastjson.JSONObject;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.service.pointalarmstatistic.PointYearlyAlarmStatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @createTime 2025/6/18
 * @description
 */
@RestController
@RequestMapping("/pointStatistic")
public class PointStatisticController {

    @Autowired
    PointYearlyAlarmStatisticService statisticService;


    @GetMapping("/getPointStatistic")
    public JsonResult getPointStatistic(@RequestParam String buildingId) {
        return statisticService.getPointStatistic(buildingId);
    }

    @PostMapping("/refreshPointStatistic")
    public JsonResult refreshPointStatistic(@RequestBody JSONObject jsonObject) {
        return statisticService.refreshPointStatistic(jsonObject);
    }

    @GetMapping("/syncFireInfo")
    public JsonResult syncFireInfo(@RequestParam(required = false) String tableName) {
        return statisticService.syncFireInfo(tableName);
    }

    @PostMapping("/refreshAll")
    public JsonResult refreshAll() {
        return statisticService.refreshAll();
    }
}
