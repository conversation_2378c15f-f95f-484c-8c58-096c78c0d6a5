package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.PointBatch;
import com.redxun.fire.core.entity.PointSyncLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 点位同步日志(PointSyncLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-08 08:34:32
 */
public interface PointSyncLogMapper extends BaseMapper<PointSyncLog> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PointSyncLog queryById(String id);

    /**
     * 查询指定行数据
     *
     * @param pointSyncLog 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<PointSyncLog> queryAllByLimit(PointSyncLog pointSyncLog, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param pointSyncLog 查询条件
     * @return 总行数
     */
    long count(PointSyncLog pointSyncLog);

    /**
     * 新增数据
     *
     * @param pointSyncLog 实例对象
     * @return 影响行数
     */
    int insert(PointSyncLog pointSyncLog);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PointSyncLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PointSyncLog> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PointSyncLog> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PointSyncLog> entities);

    /**
     * 修改数据
     *
     * @param pointSyncLog 实例对象
     * @return 影响行数
     */
    int update(PointSyncLog pointSyncLog);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    List<BaseDevicePoint> getPointNumberByBuildAndTime(@Param("buildingId") String buildingId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    Integer getDeletePointCount(@Param("buildingId") String buildingId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<Long> getIdByTime(@Param("time") String time);

    int deleteByTime(@Param("time") String time);
}

