package com.redxun.fire.core.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.pojo.vo.AppointmentApplyVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 维保计划表_new
 * </p>
 *
 * <AUTHOR> @since 2020-11-07
 */

@Data
@ToString
public class MaintenancePlan extends Model<MaintenancePlan> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Excel(name = "ID")
	@ApiModelProperty(value = "主键" )
	@TableId
	private String id;
    /**
     * 维保合_主键
     */
	@ApiModelProperty(value = "维保合_主键" )
	private String wbId;
    /**
     * 万达维_主键
     */
	@ApiModelProperty(value = "万达维_主键" )
	private String wbId2;
    /**
     * 建筑名称
     */
    @Excel(name = "建筑物名称")
	@ApiModelProperty(value = "建筑名称" )
	private String buildingName;
    /**
     * 建筑id
     */
	@ApiModelProperty(value = "建筑id" )
	private String buildingId;
    /**
     * 维保单位id
     */
	@ApiModelProperty(value = "维保单位id" )
	private String maintenanceId;
    /**
     * 维保团队名
     */
    @Excel(name = "维保团队")
	@ApiModelProperty(value = "维保团队名" )
	private String maintenanceName;
    /**
     * 维保系统个数
     */
    @Excel(name = "维保系统个数")
	@ApiModelProperty(value = "维保系统个数" )
	private String maintenanceNum;
    /**
     * 年月份
     */
    @Excel(name = "月份")
	@ApiModelProperty(value = "年月份" )
	private String effectiveTime;
    /**
     * 月度计划完成率
     */
    @Excel(name = "月度计划完成率")
	@ApiModelProperty(value = "月度计划完成率" )
	private String percentageMon;
    /**
     * 年度计划完成率
     */
    @Excel(name = "年度计划完成率")
	@ApiModelProperty(value = "年度计划完成率" )
	private String percentageYear;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
	@ApiModelProperty(value = "创建时间" )
	private Object creationTime;
    /**
     * 维保是否调改
     */
	@ApiModelProperty(value = "维保是否调改" )
	private String maintenanceUpdm;
    /**
     * 维保合同id
     */
	@ApiModelProperty(value = "维保合同id" )
	private String maintenanceContractId;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "开始时间" )
	private Date startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "结束时间" )
	private Date endTime;
    /**
     * 计划分类
     */
	@ApiModelProperty(value = "计划分类" )
	private String scheduling;
    /**
     * 已维保点位数
     */
	@ApiModelProperty(value = "已维保点位数" )
	private String maintenancePoint;
    /**
     * 总点位数
     */
	@ApiModelProperty(value = "总点位数" )
	private String pointNum;
    /**
     * 创建部门ID
     */
	@ApiModelProperty(value = "创建部门ID" )
	@TableField("CREATE_DEP_ID_")
	private String createDepId;
    /**
     * 创建人ID
     */
	@ApiModelProperty(value = "创建人ID" )
	@TableField("CREATE_BY_")
	private String createBy;
    /**
     * 创建时间2
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间2" )
	@TableField("CREATE_TIME_")
	private Object createTime;
    /**
     * 更新人ID
     */
	@ApiModelProperty(value = "更新人ID" )
	@TableField("UPDATE_BY_")
	private String updateBy;
    /**
     * 更新时间
     */
	@ApiModelProperty(value = "更新时间" )
	@TableField("UPDATE_TIME_")
	private Object updateTime;



	@TableField(exist = false)
	private String maintenanceStart;

	@ApiModelProperty(value = "计划状态，1：进行中，2，已完成" )
	private String state;


	@TableField(exist = false)
	private String fireproofSysName;
	/**
     * 用于存放本月剩余维保时长
     * */
    @TableField(exist = false)
    private String time;

    @TableField(exist = false)
    private Integer second;

	@TableField(exist = false)
    private List<AppointmentApplyVo> appointmentApplyVoList;

	@TableField(exist = false)
	private List<AppointmentApply> appointmentApplyList;

	@TableField("plan_code")
	private String planCode;

	@TableField("manage_org_id")
	private String manageOrgId;

	@TableField("manage_org_name")
	private String manageOrgName;

	@TableField("contract_id")
	private String contractId;

	@TableField("contract_name")
	private String contractName;
}
