package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.SuspectedFire;

import java.util.List;

/**
 * <p>
 * 接警中心-功能设置-疑似火警-逻辑生成表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-28
 */
public interface SuspectedFireMapper extends BaseMapper<SuspectedFire> {

    /**
     * 疑似火警-逻辑生成列表--查询
     * @return
     */
    List<SuspectedFire> querySuspectedFireList(String mainLogic);

    List<String> selectIdList(String parentId);


}
