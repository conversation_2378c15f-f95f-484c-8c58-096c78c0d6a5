<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.BaseDevicePointDotMapper">
    <insert id="insertList">
        INSERT INTO base_device_point
        (id,
        host_id,
        host_num,
        loop_code,
        point_code,
        dev_type_cat,
        local_host_num,
        local_loop_code,
        transmission_number,
        point_number,
        dev_type_code,
        dev_type_name,
        point_desc,
        zone_name,
        tall,
        wide,
        building_id,
        build_name,
        floor_id,
        did,
        apply_user,
        point_type,
        fas_code,
        fas_name,
        floor_name,
        floor,
        pid,
        zone_id,
        CREATE_TIME_,
        has_tracing,
        UPDATE_TIME_,
        super_type,
        wzt_building,
        wzt_building_name,
        wzt_floor_id,
        wzt_snap_id,
        wzt_compartment_id,
        wzt_compartment_name,
        wzt_point_id,
        wzt_floor_name,
        wzt_snap_name
         )
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id},
            #{item.hostId},
            #{item.hostNum},
            #{item.loopCode},
            #{item.pointCode},
            #{item.devTypeCat},
            #{item.localHostNum},
            #{item.localLoopCode},
            #{item.transmissionNumber},
            #{item.pointNumber},
            #{item.devTypeCode},
            #{item.devTypeName},
            #{item.pointDesc},
            #{item.zoneName},
            #{item.tall},
            #{item.wide},
            #{item.buildingId},
            #{item.buildName},
            #{item.floorId},
            #{item.did},
            #{item.applyUser},
            #{item.pointType},
            #{item.fasCode},
            #{item.fasName},
            #{item.floorName},
            #{item.floor},
            #{item.pid},
            #{item.zoneId},
            #{item.createTime},
            #{item.hasTracing},
            #{item.updateTime},
            #{item.superType},
            #{item.wztBuilding},
            #{item.wztBuildingName},
            #{item.wztFloorId},
            #{item.wztSnapId},
            #{item.wztCompartmentId},
            #{item.wztCompartmentName},
            #{item.wztPointId},
            #{item.wztFloorName},
            #{item.wztSnapName}
             )
        </foreach>
    </insert>

    <select id="getDevInfoByIdAndType" parameterType="com.redxun.fire.core.entity.BaseDevicePoint"
            resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select p.* from base_device_point p
            join fault_info f
            on p.dev_id = f.dev_id
            and
             f.dev_type_code = p.dev_type_code
            and f.reported_time = p.reported_time
            and p.dev_id =f.dev_id
            where
            p.id = #{id}
    </select>
    <select id="queryDotAll" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select p.* from base_device_point p where p.point_type = '1'
    </select>

    <delete id="deletePoint" parameterType="com.redxun.fire.core.entity.BaseDevicePoint">

        delete base_device_point
        where
        building_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.buildingId}
        </foreach>

        and point_code in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.pointCode}
        </foreach>
    </delete>

    <!-- 根据申请ID更新点位信息-->
    <update id="updatePointByBuildId" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="" separator=";" close="">
            update base_device_point
            set point_code = #{item.pointCode},
            zone_id = #{item.zoneId},
            zone_name = #{item.zoneName},
            point_desc =#{item.pointDesc},
            floor_id = #{item.floorId},
            floor_addr = #{item.floorAddr},
            dev_type_code = #{item.devTypeCode},
            dev_type_name = #{item.devTypeName}
            where host_num = #{item.hostNum}
            and building_id = #{item.buildingId}
        </foreach>
    </update>
    <update id="updatePointPid">
         update base_device_point
              set  pid = #{baseDevicePoint.pid},
                   host_id = #{baseDevicePoint.hostId},
                   did = #{baseDevicePoint.did},
                   host_num = #{baseDevicePoint.hostNum},
                   loop_code = #{baseDevicePoint.loopCode},
                   point_code = #{baseDevicePoint.pointCode},
                   point_number = #{baseDevicePoint.pointNumber},
                   point_type = #{baseDevicePoint.pointType},
                   fas_code = #{baseDevicePoint.fasCode},
                   fas_name = #{baseDevicePoint.fasName},
                   super_type = #{baseDevicePoint.superType},
                   dev_type_code = #{baseDevicePoint.devTypeCode},
                   dev_type_name = #{baseDevicePoint.devTypeName},
                   dev_type_cat = #{baseDevicePoint.devTypeCat},
                   local_host_num = #{baseDevicePoint.localHostNum},
                   local_loop_code = #{baseDevicePoint.localLoopCode},
                   point_desc = #{baseDevicePoint.pointDesc},
                   zone_name = #{baseDevicePoint.zoneName},
                   zone_id = #{baseDevicePoint.zoneId},
                   floor_name = #{baseDevicePoint.floorName},
                   has_tracing = #{baseDevicePoint.hasTracing},
                   tall = #{baseDevicePoint.tall},
                   wide = #{baseDevicePoint.wide},
                   floor_id = #{baseDevicePoint.floorId},
                   floor = #{baseDevicePoint.floor}
            where pid = #{baseDevicePoint.pid}
    </update>

    <!--根据建筑ID和设备类型查询点位-->
    <select id="queryDotByBuildIdAndDevType" resultType="com.redxun.fire.core.entity.BaseDevicePoint"
            parameterType="java.lang.String">
      SELECT * from base_device_point where dev_type_code = #{devType} and building_id = #{buildId} and point_type !='1'
    </select>
    <!--根据建筑ID和消防系统查询点位-->
    <select id="queryDotByBuildIdAndFireproof" resultType="com.redxun.fire.core.entity.BaseDevicePoint"
            parameterType="java.lang.String">
      SELECT * from base_device_point where fas_code = #{fasCode} and building_id = #{buildId}
    </select>

    <!--根据建筑id和设备类型编号查询-->
    <select id="quaryPoint" resultType="com.redxun.fire.core.pojo.dto.BaseDevPointDot"
            parameterType="java.lang.String">
        SELECT
        a.id,
        a.host_num,
        a.loop_code,
        a.point_code,
        a.dev_type_cat,
        a.transmission_number,
        a.local_host_num,
        a.local_loop_code,
        a.point_number,
        a.dev_type_name,
        a.point_desc,
        a.zone_name,
        a.floor_name,
        a.floor,
        a.wide as point_x,
        a.tall as point_y
        FROM base_device_point a
        WHERE a.building_id = #{buildingId}
        AND a.super_type = '0'
        <if test="devTypeCode!=null and devTypeCode != '' ">
            and a.dev_type_code = #{devTypeCode}
        </if>
        <if test="fasName!=null and fasName != '' ">
            and a.fas_name = #{fasName}
        </if>
        <if test="pointNumber!=null and pointNumber != '' ">
            and a.point_number like concat('%', #{pointNumber},'%')
        </if>
        <if test="floorId!=null and floorId != '' ">
            and a.floor_id = #{floorId}
        </if>
        <if test="zoneId!=null and zoneId != '' ">
            and a.zone_id = #{zoneId}
        </if>

    </select>

    <!--根据建筑ID和设备类型查询点位-->
    <select id="queryDotByBuildIdAndSuperType" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
      SELECT * from base_device_point where super_type = #{superType} and building_id = #{buildingId} and point_type !='1'
    </select>

    <select id="queryPointByParams" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select *
        from base_device_point
        where (building_id, point_code, host_num, loop_code, transmission_number)
        in
        <foreach collection="devicePoints" index="index" item="item" open="(" separator="," close=")">
            (#{item.buildingId},#{item.pointCode},#{item.hostNum},#{item.loopCode},#{item.transmissionNumber})
        </foreach>
    </select>

    <select id="queryPointByTemParams" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select *
        from base_device_point
        where (building_id, point_code, host_num, loop_code, transmission_number)
        in
        <foreach collection="devicePoints" index="index" item="item" open="(" separator="," close=")">
            (#{item.buildingId},#{item.pointCode},#{item.hostNum},#{item.loopCode},#{item.transmissionNumber})
        </foreach>
    </select>

    <select id="queryPointByIndexHostIdAndDidAndSuper" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select * from base_device_point where (host_id, did, super_type) in
        <foreach collection="devicePoints" index="index" item="item" open="(" separator="," close=")"> (#{item.hostId},#{item.did},#{item.superType}) </foreach>
    </select>

</mapper>
