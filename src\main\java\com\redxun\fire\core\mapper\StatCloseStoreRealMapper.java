package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.StatCloseStoreReal;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 闭店监测设备广场统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface StatCloseStoreRealMapper extends BaseMapper<StatCloseStoreReal> {

    /**
     * 查询闭店设备总数
     *
     * @param params
     * @return
     */
    Map<String, BigDecimal> getStatCloseDeviceData(@Param("params") Map params);

    /**
     * 查询闭店设备异常总数
     *
     * @param params
     * @return
     */
    Map<String, BigDecimal> queryCloseDeviceAbnormalPoint(@Param("params") Map params);

    /**
     * 闭店检测：查询设备异常排行榜数据
     *
     * @param params
     * @return
     */
    List<Map<String, Object>> getCloseStoreAbnormalList(@Param("params") Map params);

    StatCloseStoreReal selectByBuildingId(@Param("buildingId") String buildingId);

    Integer selectCloseStoreCountByBuildingId(@Param("buildingId") String buildingId);

    List<Map<String, Object>> selectNotSetTimeBuild();

    List<StatCloseStoreReal> listRealListByProjectType(@Param("projectType") String projectType);

    List<StatCloseStoreReal> listRealListByProjectTypeOrder(@Param("projectType") String projectType);
}
