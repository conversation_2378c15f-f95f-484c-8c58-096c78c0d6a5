package com.redxun.fire.core.entity.dutymanage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 值班班次管理
 * @TableName duty_shift_info
 */
@TableName(value ="duty_shift_info")
@Data
public class DutyShiftInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 业态  1广场  0 其他
     */
    @TableField(value = "format")
    private String format;

    /**
     * 大区
     */
    @TableField(value = "region")
    private String region;

    /**
     * 城市公司
     */
    @TableField(value = "city_company")
    private String cityCompany;

    /**
     * 大区
     */
    @TableField(value = "group_org")
    private String groupOrg;

    /**
     * 城市公司
     */
    @TableField(value = "group_org_name")
    private String groupOrgName;

    /**
     * 业态  1广场  0 其他
     */
    @TableField(value = "format_name")
    private String formatName;

    /**
     * 大区
     */
    @TableField(value = "region_name")
    private String regionName;

    /**
     * 城市公司
     */
    @TableField(value = "city_company_name")
    private String cityCompanyName;

    /**
     * 广场
     */
    @TableField(value = "piazza")
    private String piazza;

    /**
     * 建筑物
     */
    @TableField(value = "building_id")
    private String buildingId;

    /**
     * 建筑物
     */
    @TableField(value = "building_name")
    private String buildingName;

    /**
     * 月份
     */
    @TableField(value = "shift_month")
    private String shiftMonth;

    /**
     * 月份
     */
    @TableField(exist = false)
    private String beginMonth;

    /**
     * 月份
     */
    @TableField(exist = false)
    private String endMonth;

    /**
     * 审批人
     */
    @TableField(value = "approve_user_id")
    private String approveUserId;

    /**
     * 审批人
     */
    @TableField(value = "approve_user_name")
    private String approveUserName;

    /**
     * 审批状态
     */
    @TableField(value = "approve_state")
    private String approveState;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 创建人id
     */
    @TableField(exist = false)
    private String createUserName;

    /**
     * 更新人id
     */
    @TableField(exist = false)
    private List<DutyShiftTimeInfo> shiftTimeInfoList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DutyShiftInfo other = (DutyShiftInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFormat() == null ? other.getFormat() == null : this.getFormat().equals(other.getFormat()))
            && (this.getRegion() == null ? other.getRegion() == null : this.getRegion().equals(other.getRegion()))
            && (this.getCityCompany() == null ? other.getCityCompany() == null : this.getCityCompany().equals(other.getCityCompany()))
            && (this.getFormatName() == null ? other.getFormatName() == null : this.getFormatName().equals(other.getFormatName()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getCityCompanyName() == null ? other.getCityCompanyName() == null : this.getCityCompanyName().equals(other.getCityCompanyName()))
            && (this.getPiazza() == null ? other.getPiazza() == null : this.getPiazza().equals(other.getPiazza()))
            && (this.getBuildingId() == null ? other.getBuildingId() == null : this.getBuildingId().equals(other.getBuildingId()))
            && (this.getBuildingName() == null ? other.getBuildingName() == null : this.getBuildingName().equals(other.getBuildingName()))
            && (this.getShiftMonth() == null ? other.getShiftMonth() == null : this.getShiftMonth().equals(other.getShiftMonth()))
            && (this.getApproveUserId() == null ? other.getApproveUserId() == null : this.getApproveUserId().equals(other.getApproveUserId()))
            && (this.getApproveUserName() == null ? other.getApproveUserName() == null : this.getApproveUserName().equals(other.getApproveUserName()))
            && (this.getApproveState() == null ? other.getApproveState() == null : this.getApproveState().equals(other.getApproveState()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreateUserId() == null ? other.getCreateUserId() == null : this.getCreateUserId().equals(other.getCreateUserId()))
            && (this.getUpdateUserId() == null ? other.getUpdateUserId() == null : this.getUpdateUserId().equals(other.getUpdateUserId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFormat() == null) ? 0 : getFormat().hashCode());
        result = prime * result + ((getRegion() == null) ? 0 : getRegion().hashCode());
        result = prime * result + ((getCityCompany() == null) ? 0 : getCityCompany().hashCode());
        result = prime * result + ((getFormatName() == null) ? 0 : getFormatName().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getCityCompanyName() == null) ? 0 : getCityCompanyName().hashCode());
        result = prime * result + ((getPiazza() == null) ? 0 : getPiazza().hashCode());
        result = prime * result + ((getBuildingId() == null) ? 0 : getBuildingId().hashCode());
        result = prime * result + ((getBuildingName() == null) ? 0 : getBuildingName().hashCode());
        result = prime * result + ((getShiftMonth() == null) ? 0 : getShiftMonth().hashCode());
        result = prime * result + ((getApproveUserId() == null) ? 0 : getApproveUserId().hashCode());
        result = prime * result + ((getApproveUserName() == null) ? 0 : getApproveUserName().hashCode());
        result = prime * result + ((getApproveState() == null) ? 0 : getApproveState().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreateUserId() == null) ? 0 : getCreateUserId().hashCode());
        result = prime * result + ((getUpdateUserId() == null) ? 0 : getUpdateUserId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", format=").append(format);
        sb.append(", region=").append(region);
        sb.append(", cityCompany=").append(cityCompany);
        sb.append(", formatName=").append(formatName);
        sb.append(", regionName=").append(regionName);
        sb.append(", cityCompanyName=").append(cityCompanyName);
        sb.append(", piazza=").append(piazza);
        sb.append(", buildingId=").append(buildingId);
        sb.append(", buildingName=").append(buildingName);
        sb.append(", shiftMonth=").append(shiftMonth);
        sb.append(", approveUserId=").append(approveUserId);
        sb.append(", approveUserName=").append(approveUserName);
        sb.append(", approveState=").append(approveState);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", updateUserId=").append(updateUserId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}