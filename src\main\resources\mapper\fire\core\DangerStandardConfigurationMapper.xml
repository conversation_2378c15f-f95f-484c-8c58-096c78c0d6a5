<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.DangerStandardConfigurationMapper">

    <select id="getExceptionInfo" resultType="com.redxun.fire.core.pojo.vo.DangerDataInfo">
        SELECT wa.point_id, wa.building_id,
                        wa.abnormal_type, wa.reported_time, wa.point_code,
                        wa.location_describe,wa.abnormal_type_str,wa.dev_type_name,wa.building_name,
                        wa.id,bdp.wzt_compartment_name
        FROM water_abnormal wa
        INNER JOIN base_device_point bdp ON bdp.id = wa.point_id
        LEFT JOIN danger_info_detail did ON wa.id = did.exception_id AND did.danger_type not like '重大隐患%'
        WHERE wa.process_status = '0' and did.exception_id is null and wa.building_status_str = '正常' and wa.process_result is null
        AND wa.reported_time between #{startTime}  and #{time}
        AND wa.abnormal_type_str IN
        <foreach item="type" collection="dangerEquimentExceptionType" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND wa.dev_type_name IN
        <foreach item="type" collection="dangerEquimentDevType" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND wa.building_id IN
        <foreach item="buildingId" collection="buildingIds" open="(" separator="," close=")">
            #{buildingId}
        </foreach>
    </select>

<!--    <select id="getExceptionInfo" resultType="com.redxun.fire.core.entity.FireEventRelation">-->
<!--        SELECT-->
<!--        wa.point_id,-->
<!--        wa.building_id,-->
<!--        wa.abnormal_type,-->
<!--        wa.reported_time as CREATE_TIME_,-->
<!--        wa.point_code,-->
<!--        wa.location_describe,-->
<!--        wa.abnormal_type_str,-->
<!--        wa.dev_type_name,-->
<!--        wa.building_name,-->
<!--        wa.id,-->
<!--        bdp.wzt_compartment_name-->
<!--        FROM-->
<!--        (SELECT-->
<!--        point_id,-->
<!--        building_id,-->
<!--        abnormal_type,-->
<!--        reported_time,-->
<!--        point_code,-->
<!--        location_describe,-->
<!--        abnormal_type_str,-->
<!--        dev_type_name,-->
<!--        building_name,-->
<!--        id,-->
<!--        ROW_NUMBER() OVER (PARTITION BY point_id ORDER BY reported_time DESC) as rn-->
<!--        FROM-->
<!--        water_abnormal-->
<!--        WHERE-->
<!--        process_status = '0'-->
<!--        AND building_status_str = '正常'-->
<!--        AND process_result IS NULL-->
<!--        AND reported_time &lt; #{time}-->
<!--        AND reported_time > '2024-01-01 00:00:00'-->
<!--        AND abnormal_type_str IN-->
<!--        <foreach item="type" collection="dangerEquimentExceptionType" open="(" separator="," close=")">-->
<!--            #{type}-->
<!--        </foreach>-->
<!--        AND dev_type_name IN-->
<!--        <foreach item="type" collection="dangerEquimentDevType" open="(" separator="," close=")">-->
<!--            #{type}-->
<!--        </foreach>-->
<!--        AND building_id IN-->
<!--        <foreach item="buildingId" collection="buildingIds" open="(" separator="," close=")">-->
<!--            #{buildingId}-->
<!--        </foreach>-->
<!--        ) wa-->
<!--        INNER JOIN-->
<!--        base_device_point bdp ON bdp.id = wa.point_id-->
<!--        LEFT JOIN-->
<!--        danger_info_detail did ON wa.id = did.exception_id AND did.danger_type not like '重大隐患%'-->
<!--        WHERE-->
<!--        wa.rn = 1-->
<!--        AND did.exception_id IS NULL-->
<!--        AND wa.building_id IN (-->
<!--        SELECT-->
<!--        building_id-->
<!--        FROM-->
<!--        (SELECT-->
<!--        point_id,-->
<!--        building_id,-->
<!--        ROW_NUMBER() OVER (PARTITION BY point_id ORDER BY reported_time DESC) as rn-->
<!--        FROM-->
<!--        water_abnormal-->
<!--        WHERE-->
<!--        process_status = '0'-->
<!--        AND building_status_str = '正常'-->
<!--        AND process_result IS NULL-->
<!--        AND reported_time &lt; #{time}-->
<!--        AND reported_time > '2024-01-01 00:00:00'-->
<!--        AND abnormal_type_str IN-->
<!--        <foreach item="type" collection="dangerEquimentExceptionType" open="(" separator="," close=")">-->
<!--            #{type}-->
<!--        </foreach>-->
<!--        AND dev_type_name IN-->
<!--        <foreach item="type" collection="dangerEquimentDevType" open="(" separator="," close=")">-->
<!--            #{type}-->
<!--        </foreach>-->
<!--        AND building_id IN-->
<!--        <foreach item="buildingId" collection="buildingIds" open="(" separator="," close=")">-->
<!--            #{buildingId}-->
<!--        </foreach>-->
<!--        ) sub-->
<!--        WHERE-->
<!--        sub.rn = 1-->
<!--        GROUP BY-->
<!--        sub.building_id-->
<!--        HAVING-->
<!--        COUNT(DISTINCT sub.point_id) >= #{exceptionEquimentNum}-->
<!--        )-->
<!--    </select>-->


    <select id="checkDangerStatus" resultType="int">
        SELECT COUNT(*)
        FROM water_abnormal wa
        INNER JOIN danger_info_detail did ON did.exception_id = wa.id
        INNER JOIN danger_info di ON did.danger_info_id = di.id
        WHERE wa.process_result is not null  and di.lz_id = #{infoId}
    </select>


    <select id="getDeviceType" resultType="string">
        SELECT DISTINCT dev_type_name FROM water_abnormal where reported_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) AND dev_type_name is not null
    </select>


    <select id="getExceptionType" resultType="string">
        SELECT DISTINCT abnormal_type_str FROM water_abnormal where reported_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) AND  abnormal_type_str is not null
    </select>
</mapper>