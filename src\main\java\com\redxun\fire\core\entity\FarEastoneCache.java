package com.redxun.fire.core.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @program firetest
 * @description redis设备缓存dto
 * @create 2020-09-29 15:34
 **/
@Data
public class FarEastoneCache implements Serializable {

    /**
     * 点位号
     */
    private String code;
    /**
     * 最后一次心跳时间
     */
    private String lastHeartbeatTime;
    /**
     * 上传时间 yyyy-MM-dd HH:mm:ss
     */
    private String pushTime;
    /**
     * 离线状态 0-正常，1-离线
     */
    private String deviceStatus;
    /**
     * 高状态 0-无，1-有
     */
    private String highStatus;
    /**
     * 低状态 0-无，1-有
     */
    private String lowStatus;

    /**
     * 分析异常状态 0-正常，1-分析异常（压力表）
     */
    private String analyseAbnormalStatus;
    /**
     * 信号量值
     * * 范围1-4，1信号差/2信号中等/3信号好/4信号很好
     */
    private String signals;

    /**
     * 数值（水压值、液位高度值等）
     */
    private String value;
    /**
     * 数值单位
     */
    private String unit;

    /**
     * 手动状态，0-手动，1-自动（水泵）
     */
    private String manualStatus;
    /**
     * 断电状态，0-断电，1-已上电（水泵）
     */
    private String outageStatus;

    /**
     * 运行状态，0-停止，1-运行（水泵）
     */
    private String operationStatus;

    /**
     * 故障状态 0-正常，1-故障"
     */
    private String faultStatus;

    /**
     * 屏蔽状态 0-正常，1-屏蔽"
     */
    private String shieldingStatus;

    /**
     * 火警处理状态 0 未处理 1 已处理
     */
    private String fireHandleStatus;

    /**
     * 故障处理状态 0 未处理 1 已处理
     */
    private String faultHandleStatus;

    /**
     * 水压液位处理状态 0 未处理 1 已处理
     */
    private String waterHandleStatus;

    /**
     * 水泵处理状态 0 未处理 1 已处理
     */
    private String pumpHandleStatus;


    /**
     * 火警id
     */
    private String fireId;

    /**
     * 故障id
     */
    private String faultId;

    /**
     * 火警上报次数
     */
    private int fireReportTimes;

    /**
     * 故障上报次数
     */
    private int faultReportTimes;
    /**
     * 火警类型
     */
    private String policeType;


    /**
     * 火警类型
     */
    private String lastTime;


    /**
     * 是否无数据超时
     */
    private String noDataOverTime;
    /**
     * 电量状态 0-正常，1-低电量
     */
    private String electricalStatus;

    /**
     * 闭店设备异常状态 0正常 1闭店异常报警
     */
    private int alarmStatus;

    /**
     * 电量值(%)
     */
    private String batteryValue;


    //闭店电流区间
    private String closeStoreCurrent;

    /**
     * 设备型号
     */
    private String deviceType;

    /**
     * 最低电流值
     */
    private String lowCurrent;


    /**
     * 最高电流值
     */
    private String highCurrent;
    /**
     * 回归时长
     */
    private String recoveryTime;
    /**
     * 恒定电流状态
     */
    private String constantCurrentStatus;

    /**
     * 变动电流状态
     */
    private String mutantCurrentStatus;

    /**
     * 回归时长状态
     */
    private String recoveryTimeStatus;

    /**
     * 回归时长临时标记状态
     */
    private String recoveryTempStatus;

    /**
     * 回路类型 0 非24小时回路故障、 1 24小时回路故障
     */
    private String loopType;

    /**
     * 24小时回路类型情况下，断电提醒时间 闭店期间 24小时
     * 0 闭店中 124小时
     */
    private String closeStoreRemindTime;

    /////////////////////////////////////////////////闭店设备A2//////////////////////////////////////////////////
    //A相电压
    private String aVoltage;
    //A相电流
    private String aCurrent;
    //开关闭锁状态
    private String lockStatus;
    //开关状态
    private String switchStatus;
    //漏电保护
    private String leakageProtection;
    //A相温度
    private String aTemperature;

}
