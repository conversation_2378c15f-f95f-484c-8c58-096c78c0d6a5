package com.redxun.fire.core.feign;

import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.QueryData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 培训接口
 */
@FeignClient(name = "jpaas-train")
public interface TrainClient {


    @PostMapping("/train/org/middleCommon/getShopUserPage")
    JsonPageResult getShopUserPage(@RequestBody QueryData queryData);
}
