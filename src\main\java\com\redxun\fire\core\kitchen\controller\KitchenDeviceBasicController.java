
package com.redxun.fire.core.kitchen.controller;

import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceBasic;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceCheckRecord;
import com.redxun.fire.core.kitchen.service.KitchenDeviceBasicServiceImpl;
import com.redxun.web.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/fire/kitchenDeviceBasic")
@Api(tags = "厨房监测设备基础信息")
@ClassDefine(title = "厨房监测设备基础信息", alias = "KitchenDeviceBasicController", path = "/fire/kitchenDeviceBasic", packages = "core", packageName = "子系统名称")
public class KitchenDeviceBasicController extends BaseController<KitchenDeviceBasic> {

    @Autowired
    KitchenDeviceBasicServiceImpl kitchenDeviceBasicService;


    @Override
    public BaseService getBaseService() {
        return kitchenDeviceBasicService;
    }

    @Override
    public String getComment() {
        return "厨房监测设备基础信息";
    }

    /**
     * 保存厨房设备检查记录信息
     */
    @PostMapping(value = "/saveCheckRecord")
    public JsonResult saveCheckRecord(@RequestBody  KitchenDeviceCheckRecord kitchenDeviceCheckRecord) {
        return kitchenDeviceBasicService.saveCheckRecord(kitchenDeviceCheckRecord);
    }


    /**
     * 厨房设备检查校验信息
     */
    @GetMapping(value = "/check/{hostId}")
    public JsonResult check(@PathVariable("hostId") String hostId) {
        return kitchenDeviceBasicService.check(hostId);
    }

    /**
     * 厨房设备检查验证
     */
    @GetMapping(value = "/verify/{hostId}/{type}")
    public JsonResult verify(@PathVariable("hostId") String hostId, @PathVariable("type") String type) {
        return kitchenDeviceBasicService.verify(hostId, type);
    }

}

