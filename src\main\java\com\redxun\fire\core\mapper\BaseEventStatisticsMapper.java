package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.pojo.vo.DangerInfoVo;
import com.redxun.fire.core.pojo.vo.app.RouteDeviceVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 事件统计表 为了配合Jpaas来实现 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-25
 */
public interface BaseEventStatisticsMapper extends BaseMapper<BaseEventStatistics> {


    /**
     * 查询所有数据
     *
     * @param page
     * @return
     */
    List<BaseEventStatistics> queryAll(Page page);



    List<DangerInfoVo> queryDangerInfo(Page page,
                                       @Param("type") String type,
                                       @Param("buildingName") String buildingName,
                                       @Param("securityDutyName") String securityDutyName,
                                       @Param("securityDutyLevelName") String securityDutyLevelName,
                                       @Param("eventType") String eventType);

    /**
     * 查询事件类型
     *
     * @param
     * @return
     */
    List<BaseEventStatistics> queryEventType(String jjType);

    /**
     * 查询火警类型
     *
     * @param page
     * @param
     * @return
     */
    List<BaseEventStatistics> queryFireAlarmType(Page page, @Param("jjType") String jjType);

    /**
     * 根据建筑id查询数据
     *
     * @param page
     * @param baseEventStatistics
     * @return
     */
    List<BaseEventStatistics> queryDataByBuildId(Page page, @Param("baseEventStatistics") BaseEventStatistics baseEventStatistics);

    /**
     * 根据id查询
     *
     * @param baseEventStatisticsList
     * @return
     */
    List<BaseEventStatistics> queryDataById(BaseEventStatisticsList baseEventStatisticsList);

    /**
     * 批量修改数据
     *
     * @param baseEventStatistics
     * @return
     */
    int updateData(BaseEventStatistics baseEventStatistics);

    /**
     * 根据事件类型id查询点位表
     *
     * @return
     */
    List<ReceivingPoint> queryPointByEventTypeId(BaseEventStatisticsList baseEventStatisticsList);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    BaseEventStatistics queryById(String id);

    /**
     * 根据类型查询
     * @param jjType    业态
     * @param eventType 事件类型
     * @return
     */
    List<BaseEventStatistics> selectByType(BaseEventStatistics baseEventStatistics);

    /**
     *  用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectBaseEventStatisticsToExport(@Param("param") Map param);

    Map<String,Object> selectEventIdById(@Param("id") String id);

    List<BaseEventStatisticsVo> queryFireAlarmTypeNew(Page page, String jjType);
    int selectAccumulatedFireCount(@Param("date")String date);


    List<BaseEventStatistics> selectCloseStoreEventIdAndEventTypeAndTypeAndPointTypeStr(@Param("eventId") String eventId);

    IPage<BaseEventStatistics> queryPage(Page<BaseEventStatistics> page, @Param("params") Map<String, String> params);

    BaseEventStatistics selectById(@Param("id") String id);

    void insertData(BaseEventStatistics baseEventStatistics);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    List<BaseEventStatistics> queryByFaceId(@Param("faceId") String faceId);

    /**
     * 查询事件统计
     * @param type
     * @param startTime
     * @param endTime
     * @param buildingIds
     * @return
     */
    List<Map<String, Object>> countFireAlarms(@Param("type") String type,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("buildingIds") List<String> buildingIds);

    Long countFireInfo(@Param("startTime") Date startTime,
                       @Param("endTime") Date endTime,
                       @Param("buildingIds") List<String> buildingIds);
    /**
     *  统计超时故障
     * @param buildingIds
     * @return
     */
    Map<String, Object> countFault(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    /**
     * 远传设备异常统计
     * @param buildingIds
     * @param endTime
     * @return
     */
    Long countRouteDeviceException(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    /**
     * 远传设备统计
     */
    Long countRouteDevice(@Param("buildingIds") List<String> buildingIds);

    /**
     * 消防主机异常统计
     */
    Long countFireHostException(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    /**
     * 消防主机设备统计
     */
    Long countFireHost(@Param("buildingIds") List<String> buildingIds);

    /**
     * 统计水压液压异常
     */
    Long countWaterException(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    List<StatWaterExpection> batchWaterExceptions(@Param("buildingId") String buildingId, @Param("endTime") Date endTime);

    /**
     * 统计水泵异常
     * @param buildingIds
     * @param endTime
     * @return
     */
    Long countPumpException(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    List<StatPumpExpection> batchPumpExceptions(@Param("buildingId") String buildingId, @Param("endTime") Date endTime);
    /**
     * 统计闭店设备异常
     */
    Long countCloseShopException(@Param("buildingIds") List<String> buildingIds,@Param("endTime") Date endTime);

    List<StatCloseStoreExpection> batchCloseShopExceptions(@Param("buildingId") String buildingId, @Param("endTime") Date endTime);


    /**
     * 统计水压水泵设备
     */
    Long countDevicePoint(@Param("buildingIds") List<String> buildingIds,@Param("superType") List<String> superType);

    List<Map<String,Object>> countBuildingsWithMainTimeout(@Param("buildingIds") List<String> buildingIds,
                                                           @Param("todayStart") Date todayStart,
                                                           @Param("todayEnd") Date todayEnd);
}
