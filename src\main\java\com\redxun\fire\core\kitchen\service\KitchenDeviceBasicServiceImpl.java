
package com.redxun.fire.core.kitchen.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redxun.api.feign.ConstructClient;
import com.redxun.api.model.param.AlarmPointParam;
import com.redxun.common.base.db.BaseDao;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.service.impl.SuperServiceImpl;
import com.redxun.common.tool.IdGenerator;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.kitchen.entity.*;
import com.redxun.fire.core.kitchen.mapper.KitchenDeviceBasicMapper;
import com.redxun.fire.core.kitchen.mapper.KitchenDeviceCheckRecordMapper;
import com.redxun.fire.core.kitchen.mapper.KitchenEquipStatMapper;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.kitchen.KitchenDeviceRedisService;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.HexUtil;
import com.redxun.fire.core.utils.RedisUtils;
import com.redxun.fire.core.utils.okhttp.OkHttpService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* [厨房监测设备基础信息]业务服务类
*/
@Service
@Slf4j
public class KitchenDeviceBasicServiceImpl extends SuperServiceImpl<KitchenDeviceBasicMapper, KitchenDeviceBasic> implements BaseService<KitchenDeviceBasic> {

    @Resource
    private KitchenDeviceBasicMapper kitchenDeviceBasicMapper;

    @Autowired
    KitchenDeviceRedisService kitchenDeviceRedisService;

    @Autowired
    RedisUtils redisUtils;

    @Resource
    private IBaseDevicePointService iBaseDevicePointService;

    @Autowired
    KitchenSignBServiceImpl kitchenSignBService;

    @Resource
    KitchenEquipStatMapper kitchenEquipStatMapper;

    @Autowired
    IBaseBuildingFloorService iBaseBuildingFloorService;

    @Autowired
    ConstructClient constructClient;

    @Autowired
    IBaseBuildingService baseBuildingService;

    @Value("${kitchen.url:http://**************:8501}")
    private String kitchenUrl;

    @Autowired
    private KitchenDeviceCheckRecordMapper kitchenDeviceCheckRecordMapper;


    @Resource
    OkHttpService okHttpService;

    @Override
    public BaseDao<KitchenDeviceBasic> getRepository() {
        return kitchenDeviceBasicMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public void generateKitchenPoint(KitchenSignA kitchenSignA) {
        if (StrUtil.isNotBlank(kitchenSignA.getId())) {
            String stateKey = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(kitchenSignA.getHostId(), "1");
            final val o = redisUtils.get(stateKey);
            if (o != null) {
                KitchenDeviceBasic basicInfo = FastJSONUtils.toBean(o.toString(), KitchenDeviceBasic.class);
                final val list = this.list(new LambdaQueryWrapper<KitchenDeviceBasic>()
                        .eq(KitchenDeviceBasic::getHostId, basicInfo.getHostId())
                        .eq(KitchenDeviceBasic::getHostType, "1"));
                if (CollUtil.isNotEmpty(list)) {
                    basicInfo.setId(list.get(0).getId());
                    basicInfo.setBuildingId(kitchenSignA.getBuildingId());
                    basicInfo.setHostType("1");
                    basicInfo.setUpdateTime(new Date());
                    this.updateById(basicInfo);
                } else {
                    basicInfo.setId(IdGenerator.getIdStr());
                    basicInfo.setBuildingId(kitchenSignA.getBuildingId());
                    basicInfo.setHostType("1");
                    basicInfo.setCreateTime(new Date());
                    this.save(basicInfo);
                }
                generatePoint(basicInfo, kitchenSignA);
            }
            for (int i = 2; i < 5; i++) {
                String stateKey1 = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(kitchenSignA.getHostId(), i + "");
                final val o1 = redisUtils.get(stateKey1);
                if (o1 != null) {
                    KitchenDeviceBasic basicInfo = FastJSONUtils.toBean(o1.toString(), KitchenDeviceBasic.class);
                    final val list1 = this.list(new LambdaQueryWrapper<KitchenDeviceBasic>()
                            .eq(KitchenDeviceBasic::getHostId, basicInfo.getHostId())
                            .eq(KitchenDeviceBasic::getHostType, i + ""));
                    if (CollUtil.isNotEmpty(list1)) {
                        basicInfo.setId(list1.get(0).getId());
                        basicInfo.setBuildingId(kitchenSignA.getBuildingId());
                        basicInfo.setHostType(i + "");
                        basicInfo.setUpdateTime(new Date());
                        this.updateById(basicInfo);
                    } else {
                        basicInfo.setId(IdGenerator.getIdStr());
                        basicInfo.setBuildingId(kitchenSignA.getBuildingId());
                        basicInfo.setCreateTime(new Date());
                        basicInfo.setHostType(i + "");
                        this.save(basicInfo);
                    }
                    final val list = kitchenSignBService.list(new LambdaQueryWrapper<KitchenSignB>()
                            .eq(KitchenSignB::getHostId, basicInfo.getHostId())
                            .eq(KitchenSignB::getHostType, i + ""));
                    if (CollUtil.isNotEmpty(list)) {
                        generatePoint(basicInfo, kitchenSignA, list.get(0));
                    } else {
                        generatePoint(basicInfo, kitchenSignA, new KitchenSignB());
                    }
                }
            }
            try {
                statisticalKitchen(kitchenSignA.getBuildingId());
            } catch (Exception e) {
                log.error("更新厨房设备统计信息失败", e);
            }
//            final val list = kitchenSignBService.list(new LambdaQueryWrapper<KitchenSignB>()
//                    .eq(KitchenSignB::getRefId, kitchenSignA.getId()));
//            if (CollUtil.isNotEmpty(list)) {
//                for (KitchenSignB kitchenSignB : list) {
//                    String stateKey1 = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(kitchenSignA.getHostId(), kitchenSignB.getHostType());
//                    final val o1 = redisUtils.get(stateKey1);
//                    if (o1 != null) {
//                        KitchenDeviceBasic basicInfo = FastJSONUtils.toBean(o1.toString(), KitchenDeviceBasic.class);
//                        final val list1 = this.list(new LambdaQueryWrapper<KitchenDeviceBasic>()
//                                .eq(KitchenDeviceBasic::getHostId, basicInfo.getHostId()));
//                        if (CollUtil.isNotEmpty(list1)) {
//                            basicInfo.setId(list1.get(0).getId());
//                            basicInfo.setBuildingId(kitchenSignA.getBuildingId());
//                            basicInfo.setUpdateTime(new Date());
//                        } else {
//                            basicInfo.setId(IdGenerator.getIdStr());
//                            basicInfo.setCreateTime(new Date());
//                            this.save(basicInfo);
//                        }
//                        generatePoint(basicInfo, kitchenSignA, kitchenSignB);
//                    }
//                }
//            }
        }
    }

    public void statisticalKitchen(String buildingId) {
        long s = System.currentTimeMillis();
        log.info("---------------厨房监测设备更新设备统计信息开始-----------------------------");
        List<KitchenEquipStat> realList = kitchenEquipStatMapper.selectList(new LambdaQueryWrapper<KitchenEquipStat>()
                .eq(KitchenEquipStat::getBuildingId, buildingId));
        for (KitchenEquipStat equipStat : realList) {
                log.info("------------------厨房监测设备定时更新设备统计信息，建筑id为{}---------------------", buildingId);
                if (StringUtils.isBlank(buildingId)) {
                    return;
                }
                final val kitchenDeviceBasicInfos = this.baseMapper.selectList(new LambdaQueryWrapper<KitchenDeviceBasic>()
                        .eq(KitchenDeviceBasic::getBuildingId, buildingId)
                        .eq(KitchenDeviceBasic::getHostType, "1")
                        .select(KitchenDeviceBasic::getId, KitchenDeviceBasic::getOnlineState,
                                KitchenDeviceBasic::getFaultState, KitchenDeviceBasic::getBatteryState,
                                KitchenDeviceBasic::getPowerState));
                int offlineNum = 0;
                int faultNum = 0;
                int outageNum = 0;
                int batteryAbnormalNum = 0;
                int normalNum = 0;
                if (!CollectionUtils.isEmpty(kitchenDeviceBasicInfos)) {
                    for (KitchenDeviceBasic kitchenDeviceBasicInfo : kitchenDeviceBasicInfos) {
                        boolean normalFlag = true;
                        if ("2".equals(kitchenDeviceBasicInfo.getOnlineState())) {
                            offlineNum++;
                            normalFlag = false;
                        } else {
                            if ("30".equals(kitchenDeviceBasicInfo.getFaultState())) {
                                faultNum++;
                                normalFlag = false;
                            }
                            if ("50".equals(kitchenDeviceBasicInfo.getPowerState())) {
                                outageNum++;
                                normalFlag = false;
                            }
                            if ("21".equals(kitchenDeviceBasicInfo.getBatteryState())) {
                                batteryAbnormalNum++;
                                normalFlag = false;
                            }
                        }
                        if (normalFlag) {
                            normalNum++;
                        }
                    }
                }
                KitchenEquipStat updateStat = new KitchenEquipStat();
                updateStat.setId(equipStat.getId());
                updateStat.setUpdateTime(new Date());
                updateStat.setPointCount(kitchenDeviceBasicInfos.size());
                updateStat.setFaultPoint(faultNum);
                updateStat.setOfflinePoint(offlineNum);
                updateStat.setElectricalAnomalyPoint(batteryAbnormalNum);
                updateStat.setOutagePoint(outageNum);
                updateStat.setNormalPoint(normalNum);
                kitchenEquipStatMapper.updateById(updateStat);
                log.info("------------------厨房监测设备更新设备统计信息结束，建筑id为{},更新的数据为{}---------------------", buildingId, JSONObject.toJSONString(equipStat));
        }
        long e = System.currentTimeMillis();
        log.info("厨房监测设备更新设备统计信息结束，耗时{}ms", e - s);
    }

    private void generatePoint(KitchenDeviceBasic basicInfo, KitchenSignA kitchenSignA, KitchenSignB kitchenSignB) {
        String address = kitchenSignA.getHostId();
        String addressHex = HexUtil.intToHexString(Integer.parseInt(address), 4);
        BaseDevicePoint devicePoint = new BaseDevicePoint();
        devicePoint.setHostId(kitchenSignA.getHostId());
        devicePoint.setInputPoint(basicInfo.getHostType());
        devicePoint.setJoinCode(basicInfo.getHostType());
        devicePoint.setSuperType("30");
        devicePoint.setPointType("0");
        devicePoint.setBuildingId(kitchenSignA.getBuildingId());
        devicePoint.setBuildName(kitchenSignA.getBuildingName());
        devicePoint.setPointDesc(kitchenSignB.getEquipPosition());
        devicePoint.setMidMerchantId(kitchenSignA.getMerchantId());
        devicePoint.setDtuNo(address);
        devicePoint.setHasCompletion("0");

        devicePoint.setExpiredTime(kitchenSignA.getExpiredTime());
        if (StrUtil.isNotBlank(kitchenSignA.getFloor())) {
            devicePoint.setFloor(Integer.parseInt(kitchenSignA.getFloor()));

        }
        devicePoint.setFloorId(kitchenSignA.getFloorId());
        devicePoint.setFloorName(kitchenSignA.getFloorName());
        String desc = kitchenSignA.getFloorName();
        final val byId = iBaseBuildingFloorService.getById(kitchenSignA.getFloorId());
        if (ObjectUtil.isNotEmpty(byId)) {
            desc = byId.getWztConstructName() + "-" + kitchenSignA.getFloorName();
            devicePoint.setWztFloorId(byId.getWztFloorId());
            devicePoint.setWztFloorName(byId.getWztFloorName());
            devicePoint.setWztBuilding(byId.getWztConstructId());
            devicePoint.setWztBuildingName(byId.getWztConstructName());
        }
        String finalDesc  = String.join("-", desc, kitchenSignA.getMerchantName(), kitchenSignB.getEquipPosition());
        devicePoint.setPointDesc(finalDesc);
        if ("1".equals(basicInfo.getCzmJoinSignal())) {
            devicePoint.setPid("CF" + addressHex + "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setDid("3001" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXCZM");
            devicePoint.setDevTypeName("无线厨自灭设备");
            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setUpdateTime(new Date());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
        if ("1".equals(basicInfo.getRqAlarmJoinSignal())) {
            devicePoint.setPid("CF" + addressHex + "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXRQZJ");
            devicePoint.setDid("3002" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeName("无线燃气报警设备");
            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setUpdateTime(new Date());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
        int count = countDhlr(basicInfo);
        final val dhlrJoinSignal = basicInfo.getDhlrJoinSignal();
        int dhlrJoinSignalInt = 0;
        if (StrUtil.isNotBlank(dhlrJoinSignal)) {
            dhlrJoinSignalInt += Integer.parseInt(dhlrJoinSignal);
        }

        if (count > 0 || dhlrJoinSignalInt > 0) {
            devicePoint.setPid("CF" + addressHex + "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXDHLRMK");
            devicePoint.setDid("3003" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeName("无线动火离人设备");
            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setUpdateTime(new Date());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
    }

    private void generatePoint(KitchenDeviceBasic basicInfo, KitchenSignA kitchenSignA) {
        String address = kitchenSignA.getHostId();
        String addressHex = HexUtil.intToHexString(Integer.parseInt(address), 4);
        BaseDevicePoint devicePoint = new BaseDevicePoint();
        devicePoint.setHostId(kitchenSignA.getHostId());

        devicePoint.setInputPoint(basicInfo.getHostType());
        devicePoint.setJoinCode(basicInfo.getHostType());
        devicePoint.setSuperType("30");
        devicePoint.setPointType("0");
        devicePoint.setBuildingId(kitchenSignA.getBuildingId());
        devicePoint.setBuildName(kitchenSignA.getBuildingName());

        devicePoint.setPointDesc(kitchenSignA.getEquipAPosition());
        devicePoint.setMidMerchantId(kitchenSignA.getMerchantId());
        devicePoint.setDtuNo(address);
        devicePoint.setHasCompletion("0");

        devicePoint.setExpiredTime(kitchenSignA.getExpiredTime());
        if (StrUtil.isNotBlank(kitchenSignA.getFloor())) {
            devicePoint.setFloor(Integer.parseInt(kitchenSignA.getFloor()));
        }
        devicePoint.setFloorId(kitchenSignA.getFloorId());
        devicePoint.setFloorName(kitchenSignA.getFloorName());
        String desc = kitchenSignA.getFloorName();
        final val byId = iBaseBuildingFloorService.getById(kitchenSignA.getFloorId());
        if (ObjectUtil.isNotEmpty(byId)) {
            desc = byId.getWztConstructName() + "-" + kitchenSignA.getFloorName();
            devicePoint.setWztFloorId(byId.getWztFloorId());
            devicePoint.setWztFloorName(byId.getWztFloorName());
            devicePoint.setWztBuilding(byId.getWztConstructId());
            devicePoint.setWztBuildingName(byId.getWztConstructName());
        }
        String finalDesc  = String.join("-", desc, kitchenSignA.getMerchantName(), kitchenSignA.getEquipAPosition());
        devicePoint.setPointDesc(finalDesc);
        if ("1".equals(basicInfo.getCzmJoinSignal())) {
            devicePoint.setPid("CF" + addressHex + "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3001" + "_" + basicInfo.getHostType());
            devicePoint.setDid("3001" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXCZM");
            devicePoint.setDevTypeName("无线厨自灭设备");

            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setUpdateTime(new Date());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
        if ("1".equals(basicInfo.getRqAlarmJoinSignal())) {
            devicePoint.setPid("CF" + addressHex + "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3002" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXRQZJ");
            devicePoint.setDid("3002" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeName("无线燃气报警设备");
            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setUpdateTime(new Date());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
        int count = countDhlr(basicInfo);
        final val dhlrJoinSignal = basicInfo.getDhlrJoinSignal();
        int dhlrJoinSignalInt = 0;
        if (StrUtil.isNotBlank(dhlrJoinSignal)) {
            dhlrJoinSignalInt += Integer.parseInt(dhlrJoinSignal);
        }

        if (count > 0 || dhlrJoinSignalInt > 0) {
            devicePoint.setPid("CF" + addressHex + "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setPointNumber(address +  "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setPointCode(address +  "_" + "3003" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeCode("WXDHLRMK");
            devicePoint.setDid("3003" + "_" + basicInfo.getHostType());
            devicePoint.setDevTypeName("无线动火离人设备");
            final val list = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                    .eq(BaseDevicePoint::getPid, devicePoint.getPid()));
            if (CollUtil.isNotEmpty(list)) {
                devicePoint.setId(list.get(0).getId());
                devicePoint.setWztPointId(list.get(0).getWztPointId());
                devicePoint.setUpdateTime(new Date());
                iBaseDevicePointService.updateById(devicePoint);
                transforMid(devicePoint);
            } else {
                devicePoint.setCreateTime(new Date());
                devicePoint.setId(IdGenerator.getIdStr());
                iBaseDevicePointService.save(devicePoint);
                transforMid(devicePoint);
            }
        }
    }

    @Async
    public void transforMid(BaseDevicePoint devicePoint) {
        AlarmPointParam alarmPointParam = new AlarmPointParam();
        alarmPointParam.setSpeHost("0");
        alarmPointParam.setBuildingId(devicePoint.getWztBuilding());
        alarmPointParam.setPointName(devicePoint.getPointDesc());
        alarmPointParam.setPointCode(devicePoint.getPointCode());
        alarmPointParam.setPointNumber(devicePoint.getPointNumber());
        alarmPointParam.setZoneName(devicePoint.getZoneName());
        alarmPointParam.setDeviceType(devicePoint.getDevTypeCode());
        alarmPointParam.setDeviceTypeName(devicePoint.getDevTypeName());
        alarmPointParam.setFloorId(devicePoint.getWztFloorId());
        if (StrUtil.isNotBlank(devicePoint.getBuildingId())) {
            final val byId = baseBuildingService.getById(devicePoint.getBuildingId());
            if (ObjectUtil.isNotEmpty(byId)) {
                alarmPointParam.setProjectId(byId.getProjectId());
            }
        }
        alarmPointParam.setOriginalId(devicePoint.getId());
        if (StrUtil.isNotBlank(devicePoint.getWztPointId())) {
            alarmPointParam.setId(devicePoint.getWztPointId());
        }
        final val jsonResult = constructClient.saveAlarmPoint(alarmPointParam);
        log.info("-------------房监测报警点位同步中台,返回结果为{}, 入参为{}-----------------------------------",
                JSONObject.toJSONString(jsonResult), JSONObject.toJSONString(alarmPointParam));
        if (ObjectUtil.isNotEmpty(jsonResult) && jsonResult.getSuccess()) {
            Map res = (Map)jsonResult.getData();
            BaseDevicePoint updatePoint = new BaseDevicePoint();
            updatePoint.setId(devicePoint.getId());
            if (ObjectUtil.isNotEmpty(res)) {
                updatePoint.setWztPointId(StrUtil.toString(res.get("id")));
            }
            iBaseDevicePointService.updateById(updatePoint);
            return;
        }
        throw new RuntimeException("厨房监测报警点位同步中台失败");
    }

    private int countDhlr(KitchenDeviceBasic basicInfo) {
        int res = 0;
        final val lrDetectionSignalOne = basicInfo.getLrJoinSignalOne();
        final val lrDetectionSignalTwo = basicInfo.getLrJoinSignalTwo();
        final val dhJoinSignalOne = basicInfo.getDhJoinSignalOne();
        final val dhJoinSignalThree = basicInfo.getDhJoinSignalThree();
        final val dhJoinSignalTwo = basicInfo.getDhJoinSignalTwo();
        if (StrUtil.isNotBlank(lrDetectionSignalOne)) {
            res += Integer.parseInt(lrDetectionSignalOne);
        }
        if (StrUtil.isNotBlank(lrDetectionSignalTwo)) {
            res += Integer.parseInt(lrDetectionSignalTwo);
        }
        if (StrUtil.isNotBlank(dhJoinSignalOne)) {
            res += Integer.parseInt(dhJoinSignalOne);
        }
        if (StrUtil.isNotBlank(dhJoinSignalThree)) {
            res += Integer.parseInt(dhJoinSignalThree);
        }
        if (StrUtil.isNotBlank(dhJoinSignalTwo)) {
            res += Integer.parseInt(dhJoinSignalTwo);
        }
        return res;
    }

    /**
     * 判断设备是否已经建立连接
     * @param hostId
     * @return
     */
    public JsonResult check(String hostId) {
        // 校验 hostId 是否为 8 位 10 进制字符串
        if (!hostId.matches("\\d{8}")) {
            return JsonResult.getFailResult("hostId 必须为 8 位数字字符串");
        }
        String url = kitchenUrl + "/closeStore/channel/kitchen/isline/"+hostId;
        JsonResult failResult = JsonResult.getFailResult(okHttpService.get(url));
        failResult.setShow(false);
        return failResult;
    }

    /**
     * 校验设备状态
     * @param hostId
     * @param type
     * @return
     */
    public JsonResult verify(String hostId, String type) {
        // 校验 hostId 是否为 8 位 10 进制字符串
        if (!hostId.matches("\\d{8}")) {
            return JsonResult.getFailResult("hostId 必须为 8 位数字字符串");
        }
        String url = kitchenUrl + "/closeStore/channel/kitchen/sendCheckCommand/"+hostId+"/"+type;
        JsonResult failResult = JsonResult.getFailResult(okHttpService.get(url));
        failResult.setShow(false);
        return failResult;
    }

    /**
     * 保存设备检验记录
     * @param kitchenDeviceCheckRecord
     * @return
     */
    public JsonResult saveCheckRecord(KitchenDeviceCheckRecord kitchenDeviceCheckRecord) {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setShow(false);
        log.info("保存设备检验记录入参为{}", JSONObject.toJSONString(kitchenDeviceCheckRecord));
//        KitchenDeviceCheckRecord checkDeviceIsChecked = checkDeviceIsChecked(kitchenDeviceCheckRecord.getHostId());
//        if (checkDeviceIsChecked != null) {
//            checkDeviceIsChecked.setTestCount(checkDeviceIsChecked.getTestCount() + 1);
//            checkDeviceIsChecked.setCheckTime(new Date());
//            setSignalAndPower(checkDeviceIsChecked);
//            jsonResult.setData(kitchenDeviceCheckRecordMapper.updateById(checkDeviceIsChecked));
//            return jsonResult;
//        }
//        setSignalAndPower(kitchenDeviceCheckRecord);
//        kitchenDeviceCheckRecord.setTestCount(1);
        kitchenDeviceCheckRecord.setCheckTime(new Date());
        jsonResult.setData(kitchenDeviceCheckRecordMapper.insert(kitchenDeviceCheckRecord));
        return jsonResult;
    }

    /**
     * 赋值电量和信号强度
     */
    private void setSignalAndPower(KitchenDeviceCheckRecord kitchenDeviceCheckRecord) {
        String stateKey = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(kitchenDeviceCheckRecord.getHostId(),
                kitchenDeviceCheckRecord.getNumber());
        String object = (String) redisUtils.get(stateKey);
        if(object != null){
            KitchenDeviceBasic kitchenDeviceBasicInfo = JSONObject.parseObject(object, KitchenDeviceBasic.class);
            kitchenDeviceCheckRecord.setBattery(kitchenDeviceBasicInfo.getBatteryState());
            kitchenDeviceCheckRecord.setSignalLevel(kitchenDeviceBasicInfo.getSignalLevel());
        }
    }


    /**
     * 判断该设备是否已经校验过
     */
    private KitchenDeviceCheckRecord checkDeviceIsChecked(String hostId) {
        LambdaQueryWrapper<KitchenDeviceCheckRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenDeviceCheckRecord::getHostId, hostId);
        return kitchenDeviceCheckRecordMapper.selectOne(queryWrapper);
    }
}
