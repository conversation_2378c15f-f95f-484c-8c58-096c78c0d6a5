package com.redxun.fire.core.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.PointSyncLogMapper;
import com.redxun.fire.core.service.building.impl.BaseBuildingServiceImpl;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.utils.HaiWanSevenUtil;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 2024/7/25
 * @description
 */
@Slf4j
@Service
public class WbPlanScopeUpdateJob extends IJobHandler {

    @Autowired
    BaseBuildingServiceImpl baseBuildingService;

    @Resource
    PointSyncLogMapper pointSyncLogMapper;

    @Autowired
    BaseDevicePointMapper baseDevicePointMapper;

    @Autowired
    IBaseDeviceTemporaryService baseDeviceTemporaryService;

    @Autowired
    RedisUtils redisUtils;

    public static final String POINT_SYNC_KEY = "POINT_SYNC_KEY";


    @Override
    @XxlJob("WbPlanScopeUpdateJobHandler")
    public void execute() {
        try {
            long begin = System.currentTimeMillis();
            log.info("-------------点位调改调整维保范围任务开始-----------------");
            final val jobParam = XxlJobHelper.getJobParam();


            final val o = redisUtils.get(POINT_SYNC_KEY);
            Calendar calendar = Calendar.getInstance();
            String endTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
            if (o != null) {
                calendar.add(Calendar.HOUR_OF_DAY, -13);
            } else {
                calendar.add(Calendar.HOUR_OF_DAY, -25);
            }
            redisUtils.set(POINT_SYNC_KEY, "1");
            String beginTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
            if (StrUtil.isNotBlank(jobParam)) {
                log.info("---------------点位调改调整维保范围任务,手动执行调整时间{}--------------------------", jobParam);
                final val split = jobParam.split("&");
                if (split.length == 2) {
                    beginTime = split[0];
                    endTime = split[1];
                }
            }
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(Calendar.DAY_OF_YEAR, -1);
//            calendar.set(Calendar.HOUR_OF_DAY, 0);
//            calendar.set(Calendar.MINUTE, 0);
//            calendar.set(Calendar.SECOND, 0);
//            String beginTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
//            calendar.set(Calendar.HOUR_OF_DAY, 23);
//            calendar.set(Calendar.MINUTE, 59);
//            calendar.set(Calendar.SECOND, 59);
//            String endTime = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");

            final val buildingIdList = baseBuildingService.getBaseMapper().getBuildingIdList();
            if (CollectionUtil.isNotEmpty(buildingIdList)) {
                String yearMonthString = DateUtil.format(new Date(), "yyyy-MM");
                Map<String, List<BaseDevicePoint>> pointListMap = new HashMap<>();
                Map<String, Integer> deleteCountMap = new HashMap<>();
                log.info("当前年月份字符串:{}", yearMonthString);
                log.info("-------调整当前月份维保计划范围开始，月份为{}-----------", yearMonthString);
                long ss = System.currentTimeMillis();
                for (String buildingId : buildingIdList) {
                    long s = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整开始----------------", buildingId);
                    try {
                        log.info("建筑{}查询变更点位数量,查询时间参数为：{}至{},月份为{}", buildingId, beginTime, endTime, yearMonthString);
                        final val baseDevicePointList = pointSyncLogMapper.getPointNumberByBuildAndTime(buildingId, beginTime, endTime);
                        log.info("建筑{}查询变更点位数量为{}", buildingId, baseDevicePointList.size());
                        pointListMap.put(buildingId, baseDevicePointList);
                        if (CollectionUtil.isNotEmpty(baseDevicePointList)) {
                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, baseDevicePointList, true, yearMonthString);
                            continue;
                        }
                        Integer count = pointSyncLogMapper.getDeletePointCount(buildingId, beginTime, endTime);
                        deleteCountMap.put(buildingId, count);
                        log.info("建筑{}查询删除的点位数量为{}", buildingId, count);
                        if (count != null && count > 0) {
                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, null, false, yearMonthString);
                        }
                    } catch (Exception e) {
                        log.error("建筑{}维保范围调整异常", buildingId, e);
                    }
                    long e = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整结束,共用时{}----------------", buildingId, (e -s));
                }
                long ee = System.currentTimeMillis();
                log.info("-------调整月份维保计划范围结束，月份为{}，共用时{}-----------", yearMonthString, (ee -ss));
                log.info("-------调整全部月份维保计划范围开始-----------");
                for (String buildingId : buildingIdList) {
                    long s = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整开始----------------", buildingId);
                    try {
                        log.info("建筑{}查询变更点位数量,查询时间参数为：{}至{}", buildingId, beginTime, endTime);
//                        final val baseDevicePointList = pointSyncLogMapper.getPointNumberByBuildAndTime(buildingId, beginTime, endTime);
                        final val baseDevicePointList = pointListMap.get(buildingId);
                        log.info("建筑{}查询变更点位数量为{}", buildingId, baseDevicePointList.size());

                        if (CollectionUtil.isNotEmpty(baseDevicePointList)) {
                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, baseDevicePointList, true, yearMonthString);
                            continue;
                        }
//                        Integer count = pointSyncLogMapper.getDeletePointCount(buildingId, beginTime, endTime);
                        final val count = deleteCountMap.get(buildingId);
                        log.info("建筑{}查询删除的点位数量为{}", buildingId, count);
                        if (count != null && count > 0) {
                            baseDeviceTemporaryService.adjustMaintenancePlan(buildingId, null, false, yearMonthString);
                        }
                    } catch (Exception e) {
                        log.error("建筑{}维保范围调整异常", buildingId, e);
                    }
                    long e = System.currentTimeMillis();
                    log.info("----------建筑{}维保范围调整结束,共用时{}----------------", buildingId, (e -s));
                }
                log.info("-------调整全部月份维保计划范围结束-----------");
            }
            long end = System.currentTimeMillis();
            log.info("-------------点位调改调整维保范围任务结束，共用时{}-----------------", (end - begin));
        } catch (Exception e) {
            log.error("点位调改调整维保范围任务异常:", e);
        }
    }
}
