
package com.redxun.fire.core.kitchen.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.common.base.db.BaseDao;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.service.impl.SuperServiceImpl;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.kitchen.dto.KitchenEquipStatDto;
import com.redxun.fire.core.kitchen.dto.SyncKitchenEquipDTO;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceBasic;
import com.redxun.fire.core.kitchen.entity.KitchenEquipStat;
import com.redxun.fire.core.kitchen.fvo.KitchenEquipStatFvo;
import com.redxun.fire.core.kitchen.mapper.KitchenEquipStatMapper;
import com.redxun.fire.core.service.monitor.IMonitorConsoleService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * [厨房设备检测数据统计表]业务服务类
 */
@Slf4j
@Service
public class KitchenEquipStatServiceImpl extends SuperServiceImpl<KitchenEquipStatMapper, KitchenEquipStat> implements BaseService<KitchenEquipStat> {

    @Resource
    private KitchenEquipStatMapper kitchenEquipStatMapper;

    @Resource
    private OrgMiddleServiceImpl orgMiddleService;

    @Resource
    OrgManageClient orgManageClient;

    private static String zbLevel = "4";

    private static String dqLevel = "3";

    private static String cityLevel = "2";

    private static String piazzaLevel = "1";


    @Resource
    private IMonitorConsoleService monitorConsoleService;

    @Override
    public BaseDao<KitchenEquipStat> getRepository() {
        return kitchenEquipStatMapper;
    }


    /**
     * 查询厨房设备检测信息
     *
     * @param param
     * @return
     */
    public JsonResult queryKitchenEquipStatInfo(HttpServletRequest request, KitchenEquipStatFvo param) {
        String wztUserId = request.getHeader("Wzt-Userid");
        if (StringUtils.isBlank(wztUserId)) {
            return JsonResult.getFailResult("[KitchenEquipStatServiceImpl]获取当前登录人信息为空!");
        }
        getUserRight(wztUserId, param);
        Integer pageNumber = param.getPageNumber();
        Integer pageSize = param.getPageSize();
        JSONObject object = new JSONObject();
        object.put("pageNumber", pageNumber);
        object.put("pageSize", pageSize);
        List<KitchenEquipStatDto> pageList = kitchenEquipStatMapper.queryKitchenEquipStatInfo(param, null, null);
        if(CollectionUtil.isEmpty(pageList)) {
            object.put("total", 0);
            object.put("list", null);
            return JsonResult.getSuccessResult(object);
        }
        List<KitchenEquipStatDto> dtoList = kitchenEquipStatMapper.queryKitchenEquipStatInfo(param, (pageNumber - 1) * pageSize, pageSize);
        object.put("total", pageList.size());
        object.put("list", dtoList);
        return JsonResult.getSuccessResult(object);
    }

    private void getUserRight(String wztUserId, KitchenEquipStatFvo param) {
        List<String> regionList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();
        List<String> piazzaList = new ArrayList<>();

        // 获取当前人对应权限
        JsonResult jsonResult = orgManageClient.getOrgManageByUser(Arrays.asList(wztUserId));
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(jsonResult.getData()));
        JSONObject orgInfo = jsonArray.getJSONObject(0);
        String level = orgInfo.getString("level");
        if (zbLevel.equals(level)) {
            //总部权限能看到所有大区
            JSONArray arr = listRegion();
            if (arr.size() > 0) {
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject obj = (JSONObject) arr.get(i);
                    String areaCode = (String) obj.get("areaCode");
                    regionList.add(areaCode);
                }
            }
        } else if (dqLevel.equals(level)) {
            //大区id
            if (StringUtils.isNotEmpty(orgInfo.getString("region"))) {
                regionList.add(orgInfo.getString("region"));
            }
        } else if (cityLevel.equals(level)) {
            //城市公司Id
            if (StringUtils.isNotEmpty(orgInfo.getString("city"))) {
                cityList.add(orgInfo.getString("city"));
            }
        } else if (piazzaLevel.equals(level)) {
            //广场ID
            if (StringUtils.isNotEmpty(orgInfo.getString("piazza"))) {
                piazzaList.add(orgInfo.getString("piazza"));
            }
        }
        param.setRegionList(regionList);
        param.setCityList(cityList);
        param.setPiazzaList(piazzaList);
    }

    /**
     * 调用中台接口, 获取大区列表
     */
    public JSONArray listRegion() {
        // 调用中台接口, 获取大区列表
        JsonResult resp = orgManageClient.getOrgByLevel(3, "", "1");
        // 封装响应列表
        JSONArray regionArr = populateValue("areaCode", "areaValue", resp);
        return regionArr;
    }

    /**
     * 处理中台响应报文, 只保留需要的字段
     */
    private JSONArray populateValue(String key, String value, JsonResult resp) {
        JSONArray result = new JSONArray();
        // 响应报文转数组
        JSONArray list = JSONArray.parseArray(JSON.toJSONString(resp.getData()));
        if (list != null && !list.isEmpty()) {
            // 遍历, 去除多余字段
            for (int i = 0; i < list.size(); i++) {
                JSONObject oldObj = list.getJSONObject(i);
                JSONObject newObj = new JSONObject();
                newObj.put(key, oldObj.getString("orgId"));
                newObj.put(value, oldObj.getString("shortName"));
                // 添加到数组中
                result.add(newObj);
            }
        }
        return result;
    }

    /**
     * 查询厨房设备数量
     *
     * @param request
     * @param param
     * @return
     */
    public JsonResult queryKitchenCountInfo(HttpServletRequest request, KitchenEquipStatFvo param) {
        String wztUserId = request.getHeader("Wzt-Userid");
        //根据大区查城市公司
        String regionReq = param.getRegion();
        //根据城市公司查广场
        String cityReq = param.getCity();
        if (StringUtils.isBlank(wztUserId)) {
            return JsonResult.getFailResult("[KitchenEquipStatServiceImpl]获取当前登录人信息为空!");
        }
        getUserRight(wztUserId, param);

        List<KitchenEquipStatDto> dtoList = kitchenEquipStatMapper.queryKitchenCountInfo(param);
        //设备总数
        Integer totalPointCount = 0;
        //正常设备数
        Integer totalNormalCount = 0;
        //异常设备数
        Integer totalExceptionCount = 0;

        // 用于存储每个分类下每个大区的设备数量
        Map<String, Integer> offlinePointsMap = new HashMap<>();
        Map<String, Integer> outagePointMap = new HashMap<>();
        Map<String, Integer> electricalAnomalyPointMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(dtoList)) {
            for (KitchenEquipStatDto dto : dtoList) {
                totalPointCount += dto.getPointCount(); // 累加设备点位数量
                int exceptionPoint = dto.getOfflinePoint() + dto.getFaultPoint() + dto.getElectricalAnomalyPoint() + dto.getOutagePoint();
                totalExceptionCount += exceptionPoint; // 累加异常点位数量
                totalNormalCount += dto.getNormalPointCount();

                String key = "";
                if(StringUtils.isBlank(regionReq) && StringUtils.isBlank(cityReq)) {
                    key = dto.getRegion(); // 获取大区字段
                } else if(StringUtils.isNotBlank(regionReq) && StringUtils.isBlank(cityReq)) {
                    key = dto.getCityCompany(); // 获取大区字段
                } else if(StringUtils.isNotBlank(cityReq)) {
                    key = dto.getPiazza();
                }

                // 获取不同分类的点位数量
                int offlinePoint = dto.getOfflinePoint();
                int outagePoint = dto.getOutagePoint();
                int electricalAnomalyPoint = dto.getElectricalAnomalyPoint();

                // 初始化并累加“设备离线”的点位数量
                offlinePointsMap.putIfAbsent(key, 0);
                offlinePointsMap.put(key, offlinePointsMap.get(key) + offlinePoint);

                // 初始化并累加“设备断电”的点位数量
                outagePointMap.putIfAbsent(key, 0);
                outagePointMap.put(key, outagePointMap.get(key) + outagePoint);

                // 初始化并累加“电池异常”的点位数量
                electricalAnomalyPointMap.putIfAbsent(key, 0);
                electricalAnomalyPointMap.put(key, electricalAnomalyPointMap.get(key) + electricalAnomalyPoint);
            }
        }
        JSONObject object = new JSONObject();
        object.put("totalPointCount", totalPointCount);
        object.put("totalNormalCount", totalNormalCount);
        object.put("totalExceptionCount", totalExceptionCount);

        object.put("offlinePointInfo", offlinePointsMap);
        object.put("faultPointInfo", outagePointMap);
        object.put("electricalAnomalyPointInfo", electricalAnomalyPointMap);
        return JsonResult.getSuccessResult(object);
    }

    /**
     * 注册后厨房监测统计表中设备数量、监测接入商户数量+1
     * @param buildingId
     */
    public void addPointCount(String buildingId) {
        LambdaQueryWrapper<KitchenEquipStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenEquipStat::getBuildingId, buildingId);
        queryWrapper.eq(KitchenEquipStat::getDeleted, "0");
        queryWrapper.last("LIMIT 1");
        KitchenEquipStat kitchenEquipStat = this.getOne(queryWrapper);
        if(kitchenEquipStat != null) {
            Integer pointCount = kitchenEquipStat.getPointCount();
            Integer equipStoreCount = kitchenEquipStat.getEquipStoreCount();
            Integer storeCount = kitchenEquipStat.getStoreCount();
            kitchenEquipStat.setPointCount(pointCount + 1);
            kitchenEquipStat.setEquipStoreCount(equipStoreCount + 1);
            kitchenEquipStat.setStoreCount(storeCount + 1);
            this.updateById(kitchenEquipStat);
        }
    }

    /**
     * 更新统计表中的数据信息
     * @param kitchenDeviceBasic
     * @param buildingId
     */
    public void updateKitchenEquipStat(KitchenDeviceBasic kitchenDeviceBasic, String buildingId) {
        String onlineState = kitchenDeviceBasic.getOnlineState();
        String faultState = kitchenDeviceBasic.getFaultState();
        String powerState = kitchenDeviceBasic.getPowerState();
        String batteryState = kitchenDeviceBasic.getBatteryState();
        LambdaQueryWrapper<KitchenEquipStat> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(KitchenEquipStat::getBuildingId, buildingId);
        lambdaQueryWrapper.eq(KitchenEquipStat::getDeleted, "0");
        lambdaQueryWrapper.orderByDesc(KitchenEquipStat::getCreateTime);
        lambdaQueryWrapper.last("LIMIT 1");
        KitchenEquipStat kitchenEquipStat = this.getOne(lambdaQueryWrapper);
        if (kitchenEquipStat != null) {
            kitchenEquipStat.setPointCount(kitchenEquipStat.getPointCount() -1);
            kitchenEquipStat.setEquipStoreCount(kitchenEquipStat.getEquipStoreCount() -1);
            kitchenEquipStat.setStoreCount(kitchenEquipStat.getStoreCount() -1);

            Integer offlinePoint = kitchenEquipStat.getOfflinePoint(); //离线点位数
            Integer faultPoint = kitchenEquipStat.getFaultPoint(); //故障点位数
            Integer outagePoint = kitchenEquipStat.getOutagePoint();//断电点位数
            Integer electricalAnomalyPoint = kitchenEquipStat.getElectricalAnomalyPoint(); //电量异常点位数
            if ("2".equals(onlineState)) {
                kitchenEquipStat.setOfflinePoint(offlinePoint - 1);
            }
            if("30".equals(faultState)) {
                kitchenEquipStat.setFaultPoint(faultPoint - 1);
            }
            if("50".equals(powerState)) {
                kitchenEquipStat.setOutagePoint(outagePoint -1);
            }
            if("21".equals(batteryState)) {
                kitchenEquipStat.setElectricalAnomalyPoint(electricalAnomalyPoint -1);
            }
        }
        this.update(kitchenEquipStat);
    }

    /**
     * 初始化统计表脚本
     * @param dataJson
     * @return
     */
    public JsonResult syncKitchenEquipStat(JSONObject dataJson) {
        String id = dataJson.getString("id");
        List<SyncKitchenEquipDTO> dtoList = kitchenEquipStatMapper.syncKitchenEquipStat(id);
        List<KitchenEquipStat> batchList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(dtoList)) {
            for (SyncKitchenEquipDTO dto : dtoList) {
                KitchenEquipStat entity = new KitchenEquipStat();
                entity.setId(IdGenerator.getRedisIdStr());
                entity.setBuildingId(dto.getBuildingId());
                entity.setCenterCode(dto.getCenterCode());
                entity.setCenterName(dto.getCenterName());
                entity.setAreaCode(dto.getAreaCode());
                entity.setAreaName(dto.getAreaName());
                entity.setRegion(dto.getRegion());
                entity.setRegionName(dto.getRegionName());
                entity.setBuildingCode(dto.getBuildingCode());
                entity.setBuildingName(dto.getBuildingName());
                entity.setPointCount(0);
                entity.setStoreCount(0);
                entity.setEquipStoreCount(0);
                entity.setOutagePoint(0);
                entity.setFaultPoint(0);
                entity.setOfflinePoint(0);
                entity.setElectricalAnomalyPoint(0);
                entity.setNormalPoint(0);
                entity.setDeleted("0");
                entity.setCreateTime(new Date());
                batchList.add(entity);
            }
        }
        if(batchList.size() > 0) {
            this.saveOrUpdateBatch(batchList);
        }
        return JsonResult.getSuccessResult("初始化生成成功，执行数量为" + batchList.size());
    }


    public Map<String, Object> getKitchenRankList(HttpServletRequest request, Map<String, Object> paramsMap) {
        String type = (String) paramsMap.get("type");
        List<String> buildingList = monitorConsoleService.getUserBuildingList(request,paramsMap);
        paramsMap.put("buildList", buildingList);
        List<Map<String, Object>> dataList = this.baseMapper.getKitchenRankList(paramsMap);
        //1 设备离线, 2 设备故障  3电量异常
        List<Map<String, Object>> resultList = analyseDataList(dataList,Integer.parseInt(type));
        Map<String, Object> resultMap = new HashMap<>();
        filterList(resultList);
        resultMap.put("abnormalList", resultList);
        return resultMap;

    }

    private void filterList(List<Map<String, Object>> list) {
        removeDataOfZoreAbnormal(list);
        sortList(list);
        for (Map<String, Object> centerMap : list) {
            List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
            sortList(areaList);
            for (Map<String, Object> areaMap : areaList) {
                List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                sortList(buildList);
            }
        }
    }

    private void sortList(List<Map<String, Object>> list) {
        sortList(list, "abnormalNum");
    }

    private void sortList(List<Map<String, Object>> list, String keyName) {
        if (null == list || list.size() < 1) {
            return;
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                return ((Comparable) o2.get(keyName)).compareTo(
                        (o1.get(keyName))
                );
            }
        });
    }

    /**
     * 过滤掉异常为0的数据
     *
     * @param dataList
     */
    private void removeDataOfZoreAbnormal(List<Map<String, Object>> dataList) {
        Iterator<Map<String, Object>> centerIterator = dataList.iterator();
        while (centerIterator.hasNext()) {
            Map<String, Object> centerMap = centerIterator.next();
            Integer abnormalNum = getNumber(centerMap);
            if (null == abnormalNum || 0 == abnormalNum) {
                centerIterator.remove();
            } else {
                List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
                if (null != areaList && areaList.size() > 0) {
                    Iterator<Map<String, Object>> areaIterator = areaList.iterator();
                    while (areaIterator.hasNext()) {
                        Map<String, Object> areaMap = areaIterator.next();
                        abnormalNum = getNumber(areaMap);
                        if (null == abnormalNum || 0 == abnormalNum) {
                            areaIterator.remove();
                        } else {
                            List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                            if (null != buildList && buildList.size() > 0) {
                                Iterator<Map<String, Object>> buildIterator = buildList.iterator();
                                while (buildIterator.hasNext()) {
                                    Map<String, Object> buildMap = buildIterator.next();
                                    abnormalNum = getNumber(buildMap);
                                    if (null == abnormalNum || 0 == abnormalNum) {
                                        buildIterator.remove();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private Integer getNumber(Map<String, Object> dataMap) {
        return (Integer) dataMap.get("abnormalNum");
    }

    private List<Map<String, Object>> analyseDataList(List<Map<String, Object>> dataList, int flag) {
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (Map<String, Object> tempMap : dataList) {
                if (null == tempMap) {
                    continue;
                }
                boolean isExists = false;
                //运营中心
                String centerCode = (String) tempMap.get("regional");
                Map<String, Object> centerMap = new HashMap<>();
                for (Map<String, Object> centerTempMap : resultList) {
                    if (Objects.equals(centerCode, centerTempMap.get("code"))) {
                        isExists = true;
                        centerMap = centerTempMap;
                        break;
                    }
                }
                if (!isExists) {
                    centerMap.put("code", centerCode);
                    centerMap.put("title", tempMap.get("safety_belt"));
                    centerMap.put("abnormalNum", 0);
                    resultList.add(centerMap);
                }
                //区域列表
                List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
                if (null == areaList) {
                    areaList = new ArrayList<>();
                    centerMap.put("children", areaList);
                }
                //区域编号
                String areaCode = (String) tempMap.get("jurisdiction");
                Map<String, Object> areaMap = new HashMap<>();
                isExists = false;
                for (Map<String, Object> areaTempMap : areaList) {
                    if (Objects.equals(areaCode, areaTempMap.get("code"))) {
                        isExists = true;
                        areaMap = areaTempMap;
                        break;
                    }
                }
                if (!isExists) {
                    areaMap.put("code", areaCode);
                    areaMap.put("title", tempMap.get("jurisdiction_val"));
                    areaMap.put("abnormalNum", 0);
                    areaList.add(areaMap);
                }
                //建筑物id
                String buildId = (String) tempMap.get("building_id");
                //建筑物列表
                List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                if (null == buildList) {
                    buildList = new ArrayList<>();
                    areaMap.put("children", buildList);
                }
                Map<String, Object> buildMap = new HashMap<>();
                buildMap.put("code", buildId);
                buildMap.put("title", tempMap.get("build_name"));
                //闭店监测排行榜导出
                buildMap.put("offlinePointNum", getKitchenTotalNum(tempMap, 1));
                buildMap.put("faultPointNum", getKitchenTotalNum(tempMap, 2));
                buildMap.put("electricalAnomalyPointNum", getKitchenTotalNum(tempMap, 3));
                buildMap.put("abnormalNum", getKitchenTotalNum(tempMap, flag));
                buildList.add(buildMap);
                areaMap.put("abnormalNum", (Integer) areaMap.get("abnormalNum") + (Integer) buildMap.get("abnormalNum"));
                centerMap.put("abnormalNum", (Integer) centerMap.get("abnormalNum") + (Integer) buildMap.get("abnormalNum"));
            }
        }
        return resultList;
    }

    private int getKitchenTotalNum(Map<String, Object> paramsMap, int flag) {
        int data1 = 0;
        if (null != paramsMap.get("OFFLINE_POINT_")) {
            data1 = Integer.parseInt(String.valueOf(paramsMap.get("OFFLINE_POINT_")));
        }
        int data2 = 0;
        if (null != paramsMap.get("FAULT_POINT_")) {
            data2 = Integer.parseInt(String.valueOf(paramsMap.get("FAULT_POINT_")));
        }
        int data3 = 0;
        if (null != paramsMap.get("ELECTRICAL_ANOMALY_POINT_")) {
            data3 = Integer.parseInt(String.valueOf(paramsMap.get("ELECTRICAL_ANOMALY_POINT_")));
        }

        int total = 0;
        if (1 == flag) {
            total += data1;
        } else if (2 == flag) {
            total += data2;
        } else if (3 == flag) {
            total += data3;
        }
        return total;
    }
}
