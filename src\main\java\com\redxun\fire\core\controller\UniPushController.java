package com.redxun.fire.core.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.feign.org.OsUserClient;
//import com.redxun.fire.core.dto.user.OsUserDto;
import com.redxun.fire.core.dto.user.OsUserDto;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.JPushMsg;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.utils.UniPushUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 极光推送相关控制器
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/UniPush")
public class UniPushController {

    @Resource
    OsUserClient osUserClient;
    @Resource
    IBaseBuildingService baseBuildingService;

    /**
     * 根据建筑id 和 角色的GroupId推送信息给用户
     *功能没用  不要问啦
     * @param
     * @return
     */
    @PostMapping("/sendMsgToUser")
    public JsonResult sendMsgToUser(@RequestBody JPushMsg jPushMsg) {

        /*List<String> groupIds = new ArrayList<>();
        groupIds.add(jPushMsg.getGroupId());

        BaseBuilding baseBuilding = baseBuildingService.getById(jPushMsg.getBuildingId());
        if (baseBuilding == null || baseBuilding.getBelongDep() == null) {
            log.info("UniPush推送:buildingId{}不存在", jPushMsg.getBuildingId());
            return JsonResult.Success();
        }
        groupIds.add(baseBuilding.getBelongDep());
        List<OsUserDto> osUserDtos = osUserClient.getUnionUsersByGroups(groupIds);
        Set<String> registrationIdSet = osUserDtos.stream().map(OsUserDto::getRegistrationId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(registrationIdSet)) {
            log.info("UniPush推送:推送用户不存在，参数{},{}", groupIds.get(0), groupIds.get(1));
            return JsonResult.Success();
        }
        List<String> registrationIdList = new ArrayList<>(registrationIdSet);
        UniPushUtil.uniPushOnline(registrationIdList, jPushMsg.getMsgTitle(), jPushMsg.getMsgContent());
        UniPushUtil.uniPushOffLine(registrationIdList, jPushMsg.getMsgTitle(), jPushMsg.getMsgContent());*/

        return JsonResult.Success();
    }
}
