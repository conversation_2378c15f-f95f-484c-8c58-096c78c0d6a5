package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.common.base.db.BaseDao;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.pojo.dto.NormalPointSearchDto;
import com.redxun.fire.core.pojo.dto.PointCountDto;
import com.redxun.fire.core.pojo.qo.CloseStorePointQO;
import com.redxun.fire.core.pojo.vo.CloseStorePointVO;
import com.redxun.fire.core.pojo.vo.CloseStroeStatusLz;
import com.redxun.fire.core.pojo.vo.LoopPointVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 建筑物设备资产信息表数据库访问层
 */
@Mapper
public interface BaseDevicePointMapper extends BaseDao<BaseDevicePoint> {

    Map countPiontsById(String id);

    List<PointCountDto> countTypePionts(String id);

    List<String> selectFasName(@Param(value = "buildingId")String buildingId);

    List<Map<String, Object>> selectLoopName(@Param("param0") Map<String, Object> param0);

    List<Map<String, Object>> selectTreePoint(@Param(value = "buildingId")String buildingId, @Param(value = "fasName")String fasName, @Param(value = "loopName")String loopName);

    List<Map<String, Object>> selectFasNameByStep(@Param(value = "param") Map<String, Object> param);

    List<Map<String, Object>> selectLoopNameByStep(@Param(value = "param") Map<String, Object> param);

    List<Map<String, Object>> selectPointInfoByStep(@Param(value = "param") Map<String, Object> param);

    /**
     *  用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectBaseDevicePointToExport(@Param("param") Map param);

    /**
     *  用于导出 非消防主机点位
     *
     * @param param
     * @return
     */
    List<Map> selectBaseDevicePoint2ToExport(@Param("param") Map param);
    IPage<BaseDevicePoint> normalPointPage(IPage<BaseDevicePoint> page, @Param("param") NormalPointSearchDto param);

    IPage<BaseDevicePoint> specialPointPage(IPage<BaseDevicePoint> page, @Param("param") NormalPointSearchDto param);

    List<Map<String,Object>> selectNormalList(@Param("param") Map<String, Object> param);

    List<BaseDevicePoint> selectByParam(@Param(value = "param") Map<String,Object> param);

    List<String> selectIdByParam(@Param(value = "param") Map<String,Object> param);

    List<LoopPointVo> groupByBuildingAndLoopAndDeviceType(@Param("buildId")String buildId, @Param("deviceType")List<String> deviceType);

    List<CloseStorePointVO> selectCloseStorePointList(CloseStorePointQO qo);

    IPage<CloseStorePointVO> selectCloseStorePointListPage(Page<CloseStorePointVO> page, @Param("qo")CloseStorePointQO qo);

    List<CloseStorePointVO> selectCloseStorePointListByMid(@Param("qo")CloseStorePointQO qo);

    List<BaseDevicePoint> getPointDataByBuildAndPointNumber(@Param("buildingId") String buildingId, @Param("pointNumberList") List<String> pointNumberList);

    List<CloseStorePointVO> selectCloseStorePointVOListByMid(@Param("qo")CloseStorePointQO qo);

    IPage<CloseStorePointVO> selectCloseStorePointPage(Page<CloseStorePointVO> page, @Param("qo")CloseStorePointQO qo);

    String getCameraDomain(String ipAddress);

}
