<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.BaseLocalHostInfoMapper">
    <insert id="initLocalHostInfo">
insert into base_local_host_info
select replace(t1.id, '-', '') as id,
               t1.host_id              as hid,
               t1.local_host_num       as dev_no,
               t2.building_id          as building_id,
               t2.building_name        as building_name,
               t2.transmission_code    as transmission_code,
               t2.terminal_brand_name  as terminal_brand_name,
               t2.host_model_name      as host_model_name
from base_device_point t1
left join base_host_info t2 on t1.host_id = t2.hid and  t1.building_id = t2.building_id
where t1.local_host_num is not null
    and t2.building_id =  #{buildId}
	and t1.super_type = '0'
    and t1.local_host_num is not null
    and t1.local_host_num != '0'
    and t1.host_id is not null
group by host_id, local_host_num
    </insert>

    <select id="selectByBuildIds" resultType="com.redxun.fire.core.entity.BaseLocalHostInfo">
        select * from base_local_host_info where 1=1
        <if test="param.buildList != null and param.buildList.size > 0">
            and building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
    </select>
    <select id="selectByBuildId" resultType="com.redxun.fire.core.entity.BaseLocalHostInfo">
        select * from base_local_host_info where 1=1
        and building_id = #{buildId}


    </select>

    <select id="selectLocalHostInfoByBuildingId" resultType="com.redxun.fire.core.entity.BaseLocalHostInfo">
        SELECT REPLACE( t1.id, '-', '' ) AS id,
               t1.host_id AS hid,
               t1.local_host_num AS dev_no,
               t2.building_id AS building_id,
               t2.building_name AS building_name,
               t2.transmission_code AS transmission_code,
               t2.terminal_brand_name AS terminal_brand_name,
               t2.host_model_name AS host_model_name
        FROM
            base_device_point t1
        LEFT JOIN base_host_info t2 ON t1.host_id = t2.hid
        AND t1.building_id = t2.building_id
        WHERE
            t1.local_host_num IS NOT NULL
          AND t2.building_id = #{buildId}
          AND t1.super_type = '0'
          AND t1.local_host_num IS NOT NULL
          AND t1.local_host_num != '0'
	    AND t1.host_id IS NOT NULL
        GROUP BY
            host_id,
            local_host_num
    </select>
</mapper>
