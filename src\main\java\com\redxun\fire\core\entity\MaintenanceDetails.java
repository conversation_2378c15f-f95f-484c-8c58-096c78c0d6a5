package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备维保检查计划详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaintenanceDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.UUID)
    @TableField(value = "id")
    private String id;
    /**
     * 建筑id
     */
    private String buildingId;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 点位号
     */
    private String pointCode;

    /**
     * 维保项id
     */
    private String maintenanceNapeId;

    /**
     * 消防系统ID
     */
    private String fireproofSysId;

    /**
     * 消防系统名称
     */
    private String fireproofSysName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 检查结果
     */
    private String checkResult;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间2
     */
    @TableField("CREATE_TIME_")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME_")
    private LocalDateTime updateTime;
    /**
     * 照片数量
     */
    private String photographBig;
    /**
     * 维保项和计划id关联表ID
     */
    private String maintenanceItmesRPlanId;

    /**
     * 已完成数量
     */
    private String finishedPhoto;
}
