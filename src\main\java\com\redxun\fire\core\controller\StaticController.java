package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.service.monitor.IStaticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/app/static/")
public class StaticController {

    @Autowired
    private IStaticService staticService;

    /**
     * 建筑统计
     */
    @RequestMapping("getBuildingCount")
    @PostMapping("getBuildingCount")
    public JsonResult getBuildingCount() {
        return staticService.getBuildingCount();
    }

    /**
     * 火警实时数据统计
     */
    @PostMapping("getFireAlarmRealCount")
    public JsonResult getFireAlarmRealCount(@RequestBody QueryData queryData) {
        return staticService.getFireAlarmRealCount(queryData);
    }

    /**
     * 故障统计超时统计
     */
    @PostMapping("getFaultTimeoutCount")
    public JsonResult getFaultTimeoutCount(@RequestBody QueryData queryData) {
        return staticService.getFaultTimeoutCount(queryData);
    }

    /**
     * 火警历史数据统计
     */
    @PostMapping("getFireAlarmHistoryCount")
    public JsonResult getFireAlarmHistoryCount(@RequestBody QueryData queryData) {
        return staticService.getFireAlarmHistoryCount(queryData);
    }


    /**
     * 获取实时火警统计列表
     */
    @PostMapping("getFireAlarmRealCountList")
    public JsonResult getFireAlarmRealCountList(@RequestBody QueryData queryData) {
        return staticService.getFireAlarmRealCountList(queryData);
    }

    /**
     * 获取故障监管数据统计列表
     */
    @PostMapping("getFaultSupervisionCountList")
    public JsonResult getFaultSupervisionCountList(@RequestBody QueryData queryData) {
        return staticService.getFaultSupervisionCountList(queryData);
    }

    /**
     * 远传设备监测统计
     */
    @PostMapping("getRemoteDeviceMonitorCount")
    public JsonResult getRemoteDeviceMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"1");
    }

    /**
     * 消防主机设备监测统计
     */
    @PostMapping("getFireHostMonitorCount")
    public JsonResult getFireHostMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"2");
    }

    /**
     * 水压监测
     */
    @PostMapping("getWaterMonitorCount")
    public JsonResult getWaterMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"3");
    }

    /**
     * 水泵监测
     */
    @PostMapping("getPumpMonitorCount")
    public JsonResult getPumpMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"4");
    }

    /**
     * 闭店设备监测
     */
    @PostMapping("getCloseShopMonitorCount")
    public JsonResult getCloseShopMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"5");
    }

    /**
     * 万安厨设备监测
     */
    @PostMapping("getWanAnMonitorCount")
    public JsonResult getWanAnMonitorCount(@RequestBody QueryData queryData) {
        return staticService.getRemoteDeviceMonitorCount(queryData,"6");
    }

    /**
     * 维保统计列表
     */
    @PostMapping("getMaintainCountList")
    public JsonResult getMaintainCountList(@RequestBody QueryData queryData) {
        return staticService.getMaintainCountList(queryData);
    }

    /**
     * 维保统计列表
     */
    @PostMapping("getMaintainCount")
    public JsonResult getMaintainCount(@RequestBody QueryData queryData) {
        return staticService.getMaintainCount(queryData);
    }

    /**
     * 人员持证统计
     */
    @PostMapping("getPersonCertificateCount")
    public JsonResult getPersonCertificateCount() {
        return staticService.getPersonCertificateCount();
    }

    /**
     * 火警趋势统计
     */
    @GetMapping("getFireAlarmTrendCount/{type}")
    public JsonResult getFireAlarmTrendCount(@PathVariable("type") String type) {
        return staticService.getFireAlarmTrendCount(type);
    }

    /**
     * 统计区域/大区/城市广场的得分排名
     */
    @GetMapping("getScoreRank/{type}")
    public JsonResult getScoreRank(@PathVariable("type") String type) {
        return staticService.getScoreRank(type);
    }

    /**
     * 根据用户ID获取用户信息
     */
    @GetMapping("getUserInfo")
    public JsonResult getUserInfo(HttpServletRequest request) {
        return staticService.getUserInfo(request);
    }
}
