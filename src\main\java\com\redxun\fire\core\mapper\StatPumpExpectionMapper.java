package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.StatPumpExpection;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface StatPumpExpectionMapper extends BaseMapper<StatPumpExpection> {

    List<LinkedMap<String, Object>> getPumpExpectionList(Page<?> page, @Param("param") Map<String, Object> param);

    Integer getCounts(@Param("param") Map<String, Object> param);
    Integer getCountsAllBuild(@Param("param") Map<String, Object> param);

    Integer selectExpection(@Param("param") Map<String, Object> param);
}
