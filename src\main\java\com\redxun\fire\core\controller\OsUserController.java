package com.redxun.fire.core.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gexin.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.redxun.api.feign.JpaasUserClient;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.api.FwUser;
import com.redxun.fire.core.entity.wzt.OrgManageResponse;
import com.redxun.fire.core.entity.wzt.ShopUsersResponse;
import com.redxun.fire.core.entity.wzt.UserRoleResponse;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.feign.ConstructClient;
import com.redxun.fire.core.mapper.OsGroupMapperNew;
import com.redxun.fire.core.mapper.OsUserMapper;
import com.redxun.fire.core.pojo.vo.UserInfoVo;
import com.redxun.fire.core.pojo.vo.WdOrgUserExportVo;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.ISysDictItemService;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.service.user.IOsGroupService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.fire.core.service.user.IUserDataPowerService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.FastJSONUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Slf4j
@RestController
@RequestMapping("/os-user")
public class OsUserController {
    @Autowired
    private IOsUserService osUserService;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;

    @Value("${zhxf.appId}")
    private String appId;
    @Value("${group.role.key}")
    private String groupRoleKey;
    @Resource
    private JpaasUserClient jpaasUserClient;
    @Autowired
    private IJournalizingService journalizingService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IOsGroupService osGroupService;
    @Autowired
    private ISysDictItemService sysDictItemService;
    Gson gson = new Gson();

    @Autowired
    OsGroupMapperNew osGroupMapper;
    @Autowired
    private IUserDataPowerService userDataPowerService;
    @Autowired
    private OsUserMapper osUserMapper;

    /**
     * 人员信息列表
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/userList")
    @ApiOperation("人员信息列表")
    public JsonPageResult userList(@RequestBody @Validated QueryData queryData) {
        JsonPageResult jsonPageResult = JsonPageResult.getSuccess("");
        IPage<OsUser> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
        //log.info("人员信息列表-------------------------userList-queryData:" + JSON.toJSONString(queryData));
        try {
            Map<String, String> params = queryData.getParams();
            String wztUserId=params.get("wztUserId");
            String name = params.get("name");
            String userNo = params.get("userNo");
            String rankLevel = params.get("rankLevel");
            String group = params.get("group");
            String region = params.get("region");
            String city = params.get("city");
            String piazza = params.get("piazza");
            String userCategory = params.get("userCategory");//1万信用户,0平台用户
            String refGroup = params.get("refGroup");
            QueryWrapper<OsUser> queryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(name)) {
                queryWrapper.like("FULLNAME_", "%" + name + "%");
            }
            if (StringUtils.isNotBlank(refGroup)) {
                queryWrapper.eq("REF_GROUP_", refGroup);
            }
            if (StringUtils.isNotBlank(userNo)) {
                queryWrapper.eq("USER_NO_", userNo);
            }
            if (StringUtils.isNotBlank(rankLevel)) {
                queryWrapper.eq("RANK_LEVEL_", rankLevel);//层级
            }
            if (StringUtils.isNotBlank(userCategory)) {
                queryWrapper.eq("USER_CATEGORY_", userCategory);//1万信用户,0平台用户
            }
            //queryWrapper.eq("USER_CATEGORY_",1);//万信用户
            if (StringUtils.isNotEmpty(piazza)) {
                queryWrapper.eq("PIAZZA_", piazza);
            } else if (StringUtils.isNotEmpty(city)) {
                queryWrapper.eq("CITY_", city);
            } else if (StringUtils.isNotEmpty(region)) {
                queryWrapper.eq("REGION_", region);
            } else if (StringUtils.isNotEmpty(group)) {
                queryWrapper.eq("GROUP_ORG_", group);
            }
            queryWrapper.orderByDesc("CREATE_TIME_");

            page = osUserService.page(page, queryWrapper);
            List<OsUser> list = page.getRecords();
            if (list == null || list.size() <= 0) {
                page.setRecords(new ArrayList<>());
            } else {
                /*QueryWrapper queryRoleJobWrapper = new QueryWrapper<FireInfo>();
                queryRoleJobWrapper.eq("dict_code","ROLE_JOB");
                List<SysDictItem> sysDictItemList = sysDictItemService.list(queryRoleJobWrapper);
                Map<String, String> roleJobMap = sysDictItemList.stream().collect(Collectors.toMap(e -> e.getDictName(), e -> e.getDictDesc()));*/
                for (OsUser osUser : list) {
                    List<UserRoleResponse.DataDTO> roles = new ArrayList<>();
                    String userFw = osUser.getUserFw();
                    if ("3".equals(userFw)) {
                        roles = orgMiddleService.getRoleByAppIdAndWztUserId("SH", osUser.getWztUserId(), osUser.getTenantId());
                    } else {
                        roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", osUser.getWztUserId(), osUser.getTenantId());
                    }
                    String level = osUser.getLevelAyer();
                    if ("1".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName() + "/" + osUser.getCityName() + "/" + osUser.getPiazzaName());
                    } else if ("2".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName() + "/" + osUser.getCityName());
                    } else if ("3".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName());
                    } else if ("4".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName());
                    }
                    if (roles != null && roles.size() > 0) {
                        String roleIds = roles.stream().map(e -> e.getGroupId()).collect(Collectors.joining(","));
                        String roleNames = roles.stream().map(e -> e.getName()).collect(Collectors.joining(","));
                        osUser.setUserRoleId(roleIds);
                        osUser.setUserRoleName(roleNames);
                    }
//                    String jobId=osUser.getWxPosKey();
//                    if(jobId!=null&&!"".equals(jobId)){
//                        osUser.setDefaultRole(roleJobMap.get(jobId));
//                    }
                }
                page.setRecords(list);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        //}
        jsonPageResult.setResult(new JsonPage(page));
        return jsonPageResult;
    }
    /**
     * 平台管理-人员管理-人员信息列表
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/userListPingTai")
    @ApiOperation("人员信息列表")
    public JsonPageResult userListPingTai(@RequestBody @Validated QueryData queryData) {
        JsonPageResult jsonPageResult = JsonPageResult.getSuccess("");
        IPage<OsUser> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
        //log.info("人员信息列表-------------------------userList-queryData:" + JSON.toJSONString(queryData));
        try {
            Map<String, String> params = queryData.getParams();
            String wztUserId=params.get("wztUserId");
            String name = params.get("name");
            String userNo = params.get("userNo");
            String rankLevel = params.get("rankLevel");
            String group = params.get("group");
            String region = params.get("region");
            String city = params.get("city");
            String piazza = params.get("piazza");
            String userCategory = params.get("userCategory");//1万信用户,0平台用户
            String refGroup = params.get("refGroup");
            QueryWrapper<OsUser> queryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(name)) {
                queryWrapper.like("FULLNAME_", "%" + name + "%");
            }
            if (StringUtils.isNotBlank(refGroup)) {
                queryWrapper.eq("REF_GROUP_", refGroup);
            }
            if (StringUtils.isNotBlank(userNo)) {
                queryWrapper.eq("USER_NO_", userNo);
            }
            if (StringUtils.isNotBlank(rankLevel)) {
                queryWrapper.eq("RANK_LEVEL_", rankLevel);//层级
            }
            if (StringUtils.isNotBlank(userCategory)) {
                queryWrapper.eq("USER_CATEGORY_", userCategory);//1万信用户,0平台用户
            }
            //queryWrapper.eq("USER_CATEGORY_",1);//万信用户
            if (StringUtils.isNotEmpty(piazza)) {
                queryWrapper.eq("PIAZZA_", piazza);
            } else if (StringUtils.isNotEmpty(city)) {
                queryWrapper.eq("CITY_", city);
            } else if (StringUtils.isNotEmpty(region)) {
                queryWrapper.eq("REGION_", region);
            } else if (StringUtils.isNotEmpty(group)) {
                queryWrapper.eq("GROUP_ORG_", group);
            }
            //增加数据权限
            QueryWrapper<UserDataPower> queryUserDataPowerWrapper = new QueryWrapper<>();
            queryUserDataPowerWrapper.eq("USER_ID_", wztUserId);
            List<UserDataPower> userDataPowerList = userDataPowerService.getBaseMapper().selectList(queryUserDataPowerWrapper);
            if (userDataPowerList != null && userDataPowerList.size() > 0) {
                StringBuilder sql=new StringBuilder();
                sql.append(" (");
                for (int i = 0; i < userDataPowerList.size(); i++) {
                    UserDataPower userDataPower = userDataPowerList.get(i);
                    String level = userDataPower.getLevelAyer();
                    if (i == userDataPowerList.size() - 1) {
                        if ("1".equals(level)) {
                            sql.append(" PIAZZA_='").append(userDataPower.getBuildingId()).append("' ");
                        } else if ("2".equals(level)) {
                            sql.append(" CITY_='").append(userDataPower.getCity()).append("' ");
                        } else if ("3".equals(level)) {
                            sql.append(" REGION_='").append(userDataPower.getRegion()).append("' ");
                        } else if ("4".equals(level)) {
                            sql.append(" GROUP_ORG_='").append(userDataPower.getGroupOrg()).append("' ");
                        }
                    } else {
                        if ("1".equals(level)) {
                            sql.append(" PIAZZA_='").append(userDataPower.getBuildingId()).append("' or ");
                        } else if ("2".equals(level)) {
                            sql.append(" CITY_='").append(userDataPower.getCity()).append("' or ");
                        } else if ("3".equals(level)) {
                            sql.append(" REGION_='").append(userDataPower.getRegion()).append("' or ");
                        } else if ("4".equals(level)) {
                            sql.append(" GROUP_ORG_='").append(userDataPower.getGroupOrg()).append("' or ");
                        }
                    }
                }
                sql.append(") ");
                queryWrapper.apply(sql.toString());
            }else{
                log.info("改用户没有数据权限，请先授权！------------wztUserId="+wztUserId);
                page.setRecords(new ArrayList<>());
                jsonPageResult.setMessage("改用户没有数据权限，请先授权！");
                jsonPageResult.setResult(new JsonPage(page));
                return jsonPageResult;
            }
            queryWrapper.orderByDesc("CREATE_TIME_");

            page = osUserService.page(page, queryWrapper);
            List<OsUser> list = page.getRecords();
            if (list == null || list.size() <= 0) {
                page.setRecords(new ArrayList<>());
            } else {
                /*QueryWrapper queryRoleJobWrapper = new QueryWrapper<FireInfo>();
                queryRoleJobWrapper.eq("dict_code","ROLE_JOB");
                List<SysDictItem> sysDictItemList = sysDictItemService.list(queryRoleJobWrapper);
                Map<String, String> roleJobMap = sysDictItemList.stream().collect(Collectors.toMap(e -> e.getDictName(), e -> e.getDictDesc()));*/
                for (OsUser osUser : list) {
                    List<UserRoleResponse.DataDTO> roles = new ArrayList<>();
                    String userFw = osUser.getUserFw();
                    if ("3".equals(userFw)) {
                        roles = orgMiddleService.getRoleByAppIdAndWztUserId("SH", osUser.getWztUserId(), osUser.getTenantId());
                    } else {
                        roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", osUser.getWztUserId(), osUser.getTenantId());
                    }
                    String level = osUser.getLevelAyer();
                    if ("1".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName() + "/" + osUser.getCityName() + "/" + osUser.getPiazzaName());
                    } else if ("2".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName() + "/" + osUser.getCityName());
                    } else if ("3".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName() + "/" + osUser.getRegionName());
                    } else if ("4".equals(level)) {
                        osUser.setUnitName(osUser.getGroupOrgName());
                    }
                    if (roles != null && roles.size() > 0) {
                        String roleIds = roles.stream().map(e -> e.getGroupId()).collect(Collectors.joining(","));
                        String roleNames = roles.stream().map(e -> e.getName()).collect(Collectors.joining(","));
                        osUser.setUserRoleId(roleIds);
                        osUser.setUserRoleName(roleNames);
                    }
//                    String jobId=osUser.getWxPosKey();
//                    if(jobId!=null&&!"".equals(jobId)){
//                        osUser.setDefaultRole(roleJobMap.get(jobId));
//                    }
                }
                page.setRecords(list);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        //}
        jsonPageResult.setResult(new JsonPage(page));
        return jsonPageResult;
    }

    @Autowired
    private ConstructClient constructClient;
    /**
     * 平台管理-人员管理删除
     *
     * @param userId 用户id
     * @return
     */
    @RequestMapping(value = "/delRelateUser", method = RequestMethod.GET)
    @ResponseBody
    public JsonResult delRelateUser(HttpServletRequest request, @RequestParam("userId") String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new JsonResult(false, "");
        }

        String[] aryId = userId.split(",");
        List<String> list = Arrays.asList(aryId);
        for (String userIds : list) {
            OsUser osUser = osUserService.getById(userIds);
            if (osUser != null) {
                String wztUserId = osUser.getWztUserId();
                if (wztUserId != null && !"".equals(wztUserId)) {
                    List<UserRoleResponse.DataDTO> roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", wztUserId, osUser.getTenantId());
                    if (roles != null && roles.size() > 0) {
                        //List<String> userIdList = roles.stream().map(e->e.getGroupId()).collect(Collectors.toList());
                        String roleIds = roles.stream().map(e -> e.getGroupId()).collect(Collectors.joining(","));
                        Map<String, String> roleMap = new HashMap<>();
                        roleMap.put("roleIds", roleIds);
                        roleMap.put("key", userIds);
                        roleMap.put("tenantId", roles.get(0).getTenantId());
                        JsonResult jsonResult = jpaasUserClient.delUserRoleSh(roleMap);
                    }
                }
                String userName = osUser.getFullname();
                String result = "删除" + ":" + userName + osUser.getWxUserNo();
                boolean back = osUserService.removeById(userIds);
                if (back) {
                    //同步删除特殊授权
                    userDataPowerService.delUserDataPower(osUser.getWztUserId());//删除特殊授权
                    Journalizing journalizing = new Journalizing();
                    journalizing.setOperationContent(result);
                    journalizing.setOperationTypeCode(LogTypeEnums.PERSON_MANAGE.getType());
                    journalizingService.setLogInfo(request, journalizing);
                }
            } else {
                return new JsonResult(false, "未找到相应的人员信息");
            }

        }
        return new JsonResult(true, "");
    }

    /**
     * 导出excel
     *
     * @return
     */
    @MethodDefine(title = "导出EXCEL", path = "/export", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "修改数据", varName = "params")})
    @PostMapping("/export")
    public void exportUser(HttpServletRequest request, @RequestParam Map<String, String> params) throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        String wztUserIdParam = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserIdParam);
        String name = params.get("name");
        String userNo = params.get("userNo");
        String rankLevel = params.get("rankLevel");
        String userCategory = params.get("userCategory");
        QueryWrapper<OsUser> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like("FULLNAME_", "%" + name + "%");
        }
        if (StringUtils.isNotBlank(userNo)) {
            queryWrapper.eq("USER_NO_", userNo);
        }
        if (StringUtils.isNotBlank(rankLevel)) {
            queryWrapper.eq("RANK_LEVEL_", rankLevel);//层级
        }
        if (StringUtils.isNotBlank(userCategory)) {
            queryWrapper.eq("USER_CATEGORY_", userCategory);//1万信用户,0平台用户
        }
        //queryWrapper.orderByDesc("CREATE_TIME_");
        List<OsUser> userList = osUserService.list(queryWrapper);
        List<WdOrgUserExportVo> list = Lists.newArrayList();
        JSONObject map = new JSONObject();
        JsonResult jsonResult = orgMiddleService.getRoleList(map);
        Map<String, String> roleMap = new HashMap<>();
        if (jsonResult.getSuccess() && jsonResult.getData() != null) {
            UserRoleResponse userRoleResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserRoleResponse.class);
            if (userRoleResponse.getSuccess()) {
                List<UserRoleResponse.DataDTO> roleList = userRoleResponse.getData();
                roleMap = roleList.stream().collect(Collectors.toMap(e -> e.getKey(), e -> e.getName()));
            }
        }
        Map<String, String> finalRoleMap = roleMap;
        userList.forEach(userInfoVo -> {
            String wztUserId = userInfoVo.getWztUserId();
            WdOrgUserExportVo wdOrgUserExportVo = new WdOrgUserExportVo();
            wdOrgUserExportVo.setUserName(userInfoVo.getFullname());
            wdOrgUserExportVo.setWxNo(userInfoVo.getWxUserNo());
            wdOrgUserExportVo.setWxPos(userInfoVo.getWxPosValue());
            wdOrgUserExportVo.setRegisterTime(userInfoVo.getEntryTime());
            if ("1".equals(rankLevel)) {
                wdOrgUserExportVo.setOrgName(userInfoVo.getGroupOrgName());
            }
            if (StringUtils.isNotEmpty(wztUserId)) {
                if ("1".equals(userInfoVo.getLevelAyer())) {
                    wdOrgUserExportVo.setOrgName(userInfoVo.getGroupOrgName() + "-" + userInfoVo.getRegionName() + "-" + userInfoVo.getCityName() + "-" + userInfoVo.getPiazzaName());
                } else if ("2".equals(userInfoVo.getLevelAyer())) {
                    wdOrgUserExportVo.setOrgName(userInfoVo.getGroupOrgName() + "-" + userInfoVo.getRegionName() + "-" + userInfoVo.getCityName());
                } else if ("3".equals(userInfoVo.getLevelAyer())) {
                    wdOrgUserExportVo.setOrgName(userInfoVo.getGroupOrgName() + "-" + userInfoVo.getRegionName());
                }

                wdOrgUserExportVo.setUserType(userInfoVo.getWxPosValue());
/*                List<UserRoleResponse.DataDTO> roles = new ArrayList<>();
                String userFw = userInfoVo.getUserFw();
                if ("3".equals(userFw)) {
                    roles = orgMiddleService.getRoleByAppIdAndWztUserId("SH", userInfoVo.getWztUserId(), userInfoVo.getTenantId());
                } else {
                    roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", userInfoVo.getWztUserId(), userInfoVo.getTenantId());
                }
                if (roles != null && roles.size() > 0) {*/
                String roleName = "";
                String roleKeys = userInfoVo.getRoleKey();
                if (roleKeys != null && !"".equals(roleKeys)) {
                    String[] roleKeyArr = roleKeys.split(",");
                    for (String roleStr : roleKeyArr) {
                        if (finalRoleMap.containsKey(roleStr)) {
                            roleName = roleName + finalRoleMap.get(roleStr) + ",";
                        }
                    }
                    if (roleName.length() > 0) {
                        wdOrgUserExportVo.setUserRole(roleName.substring(0, roleName.length() - 1));
                    }
                }
                //}
            }

            list.add(wdOrgUserExportVo);
        });
        try {
            com.redxun.fire.core.utils.ExcelUtil.writeExcel(response, list, dataDto.getFullName() + "地方人员导出", "人员信息列表", new WdOrgUserExportVo());
        } catch (Exception e) {
            e.printStackTrace();
        }
        Journalizing journalizing = new Journalizing();
        journalizing.setOperationTypeCode(LogTypeEnums.PLATFORM_MANAGE.getType());
        journalizing.setOperationContent("导出地方人员列表数据");
        journalizingService.setLogInfo(request, journalizing);
    }

    /**
     * 通过用户Id查角色
     */
    @PostMapping("/selectRoleByWztUserId")
    public JsonResult selectRoleByWztUserId(HttpServletRequest request, @RequestBody JSONObject osUser) {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setSuccess(true);
        List<UserRoleResponse.DataDTO> roles = osUserService.selectRoleByWztUserId(request, osUser);
        jsonResult.setData(roles);
        return jsonResult;
    }

    /**
     * 平台管理-人员管理-新增或编辑
     */
    @PostMapping("/saveOrEdit")
    public JsonResult saveOrEdit(HttpServletRequest request, @RequestBody JSONObject osUser) {
        return osUserService.saveOrEdit(request, osUser);
    }

    @PostMapping("/findUserByUserName")
    public JsonResult findUserByUserName(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        params.put("tenantId", dataDto.getTenantId());
        return osUserService.findUserByUserName(params);
    }

    @PostMapping("/findUserByUserNameOrUserNo")
    public JsonResult findUserByUserNameOrUserNo(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        params.put("tenantId", dataDto.getTenantId());
        return osUserService.findUserByUserNameOrUserNo(params);
    }

    /**
     * 单点管理-人员管理-新增或编辑反显用户信息
     */
    @PostMapping("/findUserByMobile")
    public JsonResult findUserByMobile(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        QueryWrapper qw = new QueryWrapper();
        qw.eq("WZT_USER_ID_", wztUserId);
        OsUser osUser = osUserService.getBaseMapper().selectOne(qw);
        String tenantId = osUser.getTenantId();
        JsonResult jsonResult = new JsonResult(true, "");
        String userFw = params.get("userFw");
        String mobile = params.get("mobile");
        if ("1".equals(userFw)) {//通过手机号查询万达人员信息,手机号即万信号
            List<String> userNos = new ArrayList<>();
            userNos.add(mobile);
            List<UsersResponse.DataDTO> jPaasUser = orgMiddleService.findUsersByUserNos(userNos);
            if (jPaasUser != null && jPaasUser.size() > 0) {
                jsonResult.setData(jPaasUser.get(0));
            } else {
                jsonResult.setData(new UsersResponse.DataDTO());
            }
        } else if ("2".equals(userFw)) {
            List<UsersResponse.DataDTO> jPaasUser = orgMiddleService.findByMobile(mobile);
            if (jPaasUser != null && jPaasUser.size() > 0) {
                jsonResult.setData(jPaasUser.get(0));
            } else {
                jsonResult.setData(new UsersResponse.DataDTO());
            }
        } else {
            List<ShopUsersResponse.DataDTO> fwUser = orgMiddleService.findShopUserInfo(mobile, null, tenantId);
            if (fwUser != null && fwUser.size() > 0) {
                jsonResult.setData(fwUser.get(0));
            } else {
                jsonResult.setData(new ShopUsersResponse.DataDTO());
            }

        }
        //人员点同步时，同步手机号码
        if(!BeanUtil.isEmpty(jsonResult.getData())){
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(jsonResult.getData());
            String objectStr = jsonObject.getStr("mobile");
            String userId = jsonObject.getStr("userId");
            if(StrUtil.isNotBlank(objectStr)){
                OsUser selectOne = osUserService.getBaseMapper().selectOne(new LambdaQueryWrapper<OsUser>().eq(OsUser::getWztUserId, userId));
                if(ObjectUtil.isNotNull(selectOne)){
                    selectOne.setMobile(objectStr);
                    osUserService.updateById(selectOne);
                }
            }

        }

        return jsonResult;
    }

    /**
     * 单点管理-人员管理-新增或编辑--
     */
    @PostMapping("/saveOrEditSingleProject")
    public JsonResult saveOrEditSingleProject(HttpServletRequest request, @RequestBody OsUser osUser) {
        return osUserService.saveOrEditSingleProject(request, osUser);
    }

    /**
     * 单店管理-人员管理删除
     *
     * @param userId 用户id
     * @return
     */
    @GetMapping("/delSingleUser/{userId}")
    public JsonResult delSingleUser(HttpServletRequest request, @PathVariable("userId") String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new JsonResult(false, "");
        }
        String[] aryId = userId.split(",");
        List<String> list = Arrays.asList(aryId);
        for (String userIds : list) {
            OsUser osUser = osUserService.getById(userIds);
            String userFw = osUser.getUserFw();
            //需要修改逻辑，通过userId查所有角色
            List<UserRoleResponse.DataDTO> roles = new ArrayList<>();
            if("3".equals(userFw)){
                roles = orgMiddleService.getRoleByAppIdAndWztUserId("SH", osUser.getWztUserId(), osUser.getTenantId());
            }else{
                roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", osUser.getWztUserId(), osUser.getTenantId());
            }
            if (roles != null && roles.size() > 0) {//删除用户角色
                //List<String> userIdList = roles.stream().map(e->e.getGroupId()).collect(Collectors.toList());
                String roleIds = roles.stream().map(e -> e.getGroupId()).collect(Collectors.joining(","));
                Map<String, String> roleMap = new HashMap<>();
                roleMap.put("roleIds", roleIds);
                roleMap.put("userIds", osUser.getWztUserId());
                roleMap.put("tenantId", roles.get(0).getTenantId());
                if ("3".equals(userFw)) {
                    JsonResult jsonResult = jpaasUserClient.delNonUserRoleSh(roleMap);
                } else {
                    JsonResult jsonResult = jpaasUserClient.delUserRoleSh(roleMap);
                }
            }
            //同步删除中台用户
            boolean back = osUserService.removeById(userIds);
            if (back) {
                //同步删除特殊授权
                userDataPowerService.delUserDataPower(osUser.getWztUserId());//删除特殊授权
//                Journalizing journalizing = new Journalizing();
//                journalizing.setOperationContent(result);
//                journalizing.setOperationTypeCode(LogTypeEnums.PERSON_MANAGE.getType());
//                journalizingService.setLogInfo(request, journalizing);
            }

//            String userName = osUser.getFullname();
//            String result = "删除" + ":" + userName + osUser.getWxUserNo();
//            boolean back = osUserService.removeById(userIds);
//            if (back) {
//                Journalizing journalizing = new Journalizing();
//                journalizing.setOperationContent(result);
//                journalizing.setOperationTypeCode(LogTypeEnums.PERSON_MANAGE.getType());
//                journalizingService.setLogInfo(request, journalizing);
//            }
        }
        return new JsonResult(true, "");
    }

    /***
     * 根据等级获取架构
     * level 4 集团，3 大区，2 城市公司 1 广场=项目，0 部门
     * parentId  父级id
     * businessId   业务id
     * @return
     */
    @MethodDefine(title = "根据等级获取架构", path = "/queryOrgManageByLevel", method = HttpMethodConstants.POST)
    @ApiOperation(value = "根据等级获取架构", notes = "根据等级获取架构")
    @PostMapping({"/queryOrgManageByLevel"})
    public JsonResult queryOrgManageByLevel(@RequestBody JSONObject paramJson) {
        JsonResult jsonResult = new JsonResult();
        Integer level = paramJson.getInteger("level");
        String parentId = paramJson.getString("parentId");
        String businessId = paramJson.getString("businessId");
        if (1 == level.intValue()) {
            Map<String, String> params = new HashMap<>();
            params.put("level", level.toString());
            params.put("parentId", parentId);
            List<Map> buildingList = baseBuildingService.findPlazaList(params);
            jsonResult.setData(buildingList);
            return jsonResult;
        } else {
            OrgManageResponse orgManageResponse = orgMiddleService.queryOrgManageByLevel(level, parentId, businessId);
            if(4 == level.intValue()){
                if(orgManageResponse.getData() != null && orgManageResponse.getData().size() > 0){
                    List<OrgManageResponse.DataDTO> dataDTOList = orgManageResponse.getData().stream()
                            .filter(e -> e.getName().equals("酒店管理公司") || e.getName().equals("商管集团") ||e.getName().equals("物业公司") ||
                                    e.getName().equals("文旅集团") ).collect(Collectors.toList());
                    jsonResult.setData(dataDTOList);
                }else{
                    jsonResult.setData(new ArrayList<>());
                }
            }else{
                jsonResult.setData(orgManageResponse.getData());
            }
            jsonResult.setMessage(orgManageResponse.getMessage());
            return jsonResult;
        }

    }

    /***
     * 通过广场查部门
     * orgId   广场id
     * @return
     */
    @MethodDefine(title = "通过广场查部门", path = "/getDeptByOrgId", method = HttpMethodConstants.POST)
    @ApiOperation(value = "通过广场查部门", notes = "通过广场查部门")
    @PostMapping({"/getDeptByOrgId"})
    public Object getDeptByOrgId(@RequestBody JSONObject paramJson) {
        String orgId = paramJson.getString("orgId");
        String piazza = paramJson.getString("piazza");
        String userFw = paramJson.getString("userFw");
        if (!"2".equals(userFw)) {
            JsonResult jsonResult = orgMiddleService.getDeptByOrgId(orgId);
            return jsonResult;
        }
        return orgMiddleService.queryOrgManageByLevelNew(0, piazza, null);
    }

    /***
     * 根据部门查职务
     * tenantId   广场id
     * deptId
     * @return
     */
    @MethodDefine(title = "根据部门查职务", path = "/queryJob", method = HttpMethodConstants.POST)
    @ApiOperation(value = "根据部门查职务", notes = "根据部门查职务")
    @PostMapping({"/queryJob"})
    public JsonResult queryJob(@RequestBody Map<String, String> params) {
        JsonResult jsonResult = orgMiddleService.queryJob(params);
        return jsonResult;
    }



    //用万中台userId换取消防userId
    @PostMapping("/findUserIdByWztUserId")
    public JsonResult findUserIdByWztUserId(@RequestHeader("Wzt-Userid") String wztUserId, @RequestBody Map<String, String> params) {
        JsonResult jsonResult = new JsonResult();
        String userId = osUserService.findUserIdByWztUserId(wztUserId);
        jsonResult.setSuccess(true);
        jsonResult.setData(userId);
        return jsonResult;
    }

    //根据用户id 获取当前用户所属架构及所辖架构
    @PostMapping("/getOrgManageDownByUser")
    public JsonResult getOrgManageDownByUser(@RequestBody JSONObject params) {
        JsonResult jsonResult = orgMiddleService.getOrgManageDownByUser(params);
        return jsonResult;
    }

    //新增编辑用户的角色列表
    @PostMapping("/getRoleList")
    public JsonResult getRoleList(@RequestBody JSONObject map) {
        String status = map.getString("status");
        List result=new ArrayList();
        JsonResult jsonResult = orgMiddleService.getRoleList(map);
        if("1".equals(status)&&jsonResult.getSuccess()){//去除集团角色
            JSONArray arr=JSONArray.parseArray(JSON.toJSONString(jsonResult.getData()));
            if(arr!=null&&arr.size()>0){
                for(int i=0;i<arr.size();i++){
                    JSONObject ob=arr.getJSONObject(i);
                    if(!groupRoleKey.contains(ob.getString("key"))){
                        result.add(ob);
                    }
                }
                jsonResult.setData(result);
            }
        }
        return jsonResult;
    }


    /**
     * @param type=1 查询非商管外其他业态
     * @Description:查询业态
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "根据等级获取架构", notes = "根据等级获取架构")
    @PostMapping({"/queryOrgBusiness"})
    public JsonResult queryOrgBusiness(@RequestParam(required = false) Integer type) {
        JsonResult jsonResult = new JsonResult();
        OrgManageResponse orgManageResponse = orgMiddleService.queryOrgManageByLevel(4, "", null);
        if (type != null && 1 == type) {
            jsonResult.setData(orgManageResponse.getData().stream().filter(item -> !item.getShortName().equals("商管集团")).collect(Collectors.toList()));
        } else {
            jsonResult.setData(orgManageResponse.getData());
        }
        jsonResult.setMessage(orgManageResponse.getMessage());
        return jsonResult;
    }

    /**
     * @param params
     * @Description:查询业态下大区
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "根据等级获取架构", notes = "根据等级获取架构")
    @PostMapping({"/queryOrgRegion"})
    public JsonResult queryOrgRegion(@RequestParam Map<String, String> params, @RequestParam(required = false) String parentId) {
        JsonResult jsonResult = new JsonResult();

        //默认查询商管集团业态下大区的情况
        if (StringUtils.isNotBlank(parentId)) {
            OrgManageResponse orgManageResponse = orgMiddleService.queryOrgManageByLevel(3, parentId, null);
            jsonResult.setData(orgManageResponse.getData());
            return jsonResult;
        }

        if (params == null || StringUtils.isBlank(params.get("params")) || "{}".equals(params.get("params"))) {
            return JsonResult.Success();
        }
        JSONObject paramsJson = JSONObject.parseObject(params.get("params"));
        OrgManageResponse orgManageResponse = orgMiddleService.queryOrgManageByLevel(3, paramsJson.getString("parentId"), null);
        jsonResult.setData(orgManageResponse.getData());
        jsonResult.setMessage(orgManageResponse.getMessage());
        return jsonResult;
    }

    /**
     * @param params
     * @Description:查询大区下城市公司
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "根据等级获取架构", notes = "根据等级获取架构")
    @PostMapping({"/queryOrgCity"})
    public JsonResult queryOrgCity(@RequestParam Map<String, String> params) {
        JsonResult jsonResult = new JsonResult();
        if (params == null || StringUtils.isBlank(params.get("params")) || "{}".equals(params.get("params"))) {
            return JsonResult.Success();
        }
        JSONObject paramsJson = JSONObject.parseObject(params.get("params"));
        OrgManageResponse orgManageResponse = orgMiddleService.queryOrgManageByLevel(2, paramsJson.getString("parentId"), null);
        jsonResult.setData(orgManageResponse.getData());
        jsonResult.setMessage(orgManageResponse.getMessage());
        return jsonResult;
    }

    /**
     * @param params
     * @Description:查询城市公司下广场
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "根据等级获取架构", notes = "根据等级获取架构")
    @PostMapping({"/queryOrgBuilding"})
    public JsonResult queryOrgBuilding(@RequestParam Map<String, String> params) {
        JsonResult jsonResult = new JsonResult();

        JSONObject paramsJson = null;
        if (params != null || !StringUtils.isBlank(params.get("params"))) {
            paramsJson = JSONObject.parseObject(params.get("params"));
        }

        if (params == null || StringUtils.isBlank(params.get("params")) || "{}".equals(params.get("params")) || StringUtils.isBlank(paramsJson.getString("parentId"))) {
            List<Map> buildingList;
            Map<String, String> queryData = new HashMap<>();
            if (paramsJson != null) {
                queryData.put("orgId", paramsJson.getString("orgId"));
                queryData.put("regionId", paramsJson.getString("regionId"));
                buildingList = baseBuildingService.findPlazaList(queryData);
            } else {
                //如果查询全量，则增加缓存缓存
                String redisKey = "XF_ALL_BUILDING";
                String postStr = stringRedisTemplate.opsForValue().get(redisKey);
                if (StringUtils.isNotBlank(postStr)) {
                    buildingList = JSONObject.parseArray(postStr, Map.class);
                } else {
                    buildingList = baseBuildingService.findPlazaList(queryData);
                    stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(buildingList), 1, TimeUnit.HOURS);
                }
            }
            jsonResult.setData(buildingList);

            return jsonResult;
        } else {
            Map<String, String> queryData = new HashMap<>();
            queryData.put("level", "1");
            queryData.put("parentId", paramsJson.getString("parentId"));
            List<Map> buildingList = baseBuildingService.findPlazaList(queryData);
            jsonResult.setData(buildingList);
            return jsonResult;
        }
    }

    /**
     * @param params
     * @Description:查询非万职务
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "查询非万职务", notes = "查询非万职务")
    @PostMapping({"/fwPost"})
    public JsonResult fwPost(@RequestParam Map<String, String> params) {
        JsonResult jsonResult = orgMiddleService.fwPost();
        return jsonResult;
    }

    /**
     * 持证管理  获取当前组织下的用户
     *
     * @param buildingId 当前建筑的id
     * @return
     */
    @GetMapping("/getUsersByBuildId")
    public JsonResult getUsers(@RequestParam("buildingId") String buildingId) {
        JsonResult result = JsonResult.Success();
        if (StrUtil.isBlank(buildingId)) {
            return JsonResult.getFailResult("入参不规范");
        }
        final val byId = baseBuildingService.getById(buildingId);
        if (ObjectUtil.isEmpty(byId)) {
            return JsonResult.getFailResult("未查询到建筑信息");
        }
        List<OsUser> users = osUserService.list(new LambdaQueryWrapper<OsUser>().eq(OsUser::getPiazza, byId.getPiazza()));
        List<UserInfoVo> finalList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(users)) {
            for (OsUser user : users) {
                finalList.add(new UserInfoVo(user.getUserId(), user.getFullname(), user.getDepartmentIds(), user.getDepartmentName(), ""));
            }
        }
        result.setData(finalList);
        return result;
    }

    /**
     * 持证管理  获取当前组织下的用户
     *
     * @return
     */
    @PostMapping("/getWbUsers")
    public JsonResult getWbUsers(@RequestBody Map<String, String> params) {
        JsonResult result = JsonResult.Success();
        String buildingId = params.get("buildingId");
        String wbTeamId = params.get("wbTeamId");
        String userName = params.get("userName");

        if (StrUtil.isBlank(buildingId)) {
            return JsonResult.getFailResult("入参不规范");
        }
        final val byId = baseBuildingService.getById(buildingId);
        Map<String, String> teamMap = new HashMap<>();
        if (ObjectUtil.isEmpty(byId)) {
            return JsonResult.getFailResult("未查询到建筑信息");
        }
        final val queryWrapper = new LambdaQueryWrapper<OsUser>();
        if (StrUtil.isBlank(wbTeamId)) {
            List<OsGroupNew> groupList = osGroupMapper.selectList(new LambdaQueryWrapper<OsGroupNew>().eq(OsGroupNew::getBlock, "0")
                    .eq(OsGroupNew::getGroupType, "1").eq(OsGroupNew::getParentId, buildingId));
            if (CollectionUtil.isEmpty(groupList)) {
                return result;
            }
            final val collect = groupList.stream().map(OsGroupNew::getGroupId).collect(Collectors.toList());
            teamMap.putAll(groupList.stream().collect(Collectors.toMap(OsGroupNew::getGroupId, OsGroupNew::getName)));
            queryWrapper.in(OsUser::getRefGroup, collect);
        } else {
            queryWrapper.eq(OsUser::getRefGroup, wbTeamId);
        }
        if (StrUtil.isNotBlank(userName)) {
            queryWrapper.like(OsUser::getFullname, userName);
        }
        List<OsUser> users = osUserService.list(queryWrapper);
        List<UserInfoVo> finalList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(users)) {
            for (OsUser user : users) {
                final val userInfoVo = new UserInfoVo(user.getUserId(), user.getFullname(), user.getDepartmentIds(), user.getDepartmentName(), user.getMobile());
                userInfoVo.setOrgName(teamMap.get(user.getRefGroup()));
                finalList.add(userInfoVo);
            }
        }
        result.setData(finalList);
        return result;
    }

    @ApiOperation(value = "查询消防所有职务", notes = "查询消防所有职务")
    @GetMapping({"/findPost"})
    public JsonResult findPost() {
        String postStr = stringRedisTemplate.opsForValue().get("XF_POST");
        JsonResult jsonResult = new JsonResult();
        if(postStr!=null&&!"".equals(postStr)){
            if (StringUtils.isNotEmpty(postStr)) {
                JSONArray jsonArray = JSONObject.parseArray(postStr);
                jsonResult.setData(jsonArray);
            }
        }else{
            QueryWrapper qw=new QueryWrapper();
            qw.eq("DIM_ID_","3");
            List<OsGroup> osGroupList=osGroupService.getBaseMapper().selectList(qw);
            if(osGroupList!=null&&osGroupList.size()>0){
                jsonResult.setData(osGroupList);
                stringRedisTemplate.opsForValue().set("XF_POST",JSON.toJSONString(osGroupList));
            }
        }
        return jsonResult;
    }

    /**
     * 单店管理-人员管理人员导出excel
     *
     * @return
     */
    @MethodDefine(title = "单店管理-人员管理人员导出excel", path = "/exportSingle", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "单店管理-人员管理人员导出excel", varName = "params")})
    @GetMapping("/exportSingle")
    public void exportSingle(HttpServletRequest request, @RequestParam("unitId") String unitId) throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        String wztUserIdParam = request.getHeader("Wzt-Userid");//万中台userId
        try {
            QueryWrapper<OsUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("REF_GROUP_", unitId);
            UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserIdParam);
            List<OsUser> userList = osUserService.list(queryWrapper);
            List<WdOrgUserExportVo> list = Lists.newArrayList();
            userList.forEach(userInfoVo -> {
                String wztUserId = userInfoVo.getWztUserId();
                WdOrgUserExportVo wdOrgUserExportVo = new WdOrgUserExportVo();
                wdOrgUserExportVo.setUserName(userInfoVo.getFullname());
                wdOrgUserExportVo.setWxNo(userInfoVo.getWxUserNo());
                wdOrgUserExportVo.setWxPos(userInfoVo.getWxPosValue());
                wdOrgUserExportVo.setRegisterTime(userInfoVo.getEntryTime());
                if (StringUtils.isNotEmpty(wztUserId)) {
                    UsersResponse.DataDTO dto = orgMiddleService.findUserByUserId(wztUserId);
                    wdOrgUserExportVo.setOrgName(dto.getOrgName());

                    wdOrgUserExportVo.setUserType(dto.getPositionName());
                    List<UserRoleResponse.DataDTO> roles = new ArrayList<>();
                    String userFw = userInfoVo.getUserFw();
                    if ("3".equals(userFw)) {
                        roles = orgMiddleService.getRoleByAppIdAndWztUserId("SH", userInfoVo.getWztUserId(), userInfoVo.getTenantId());
                    } else {
                        roles = orgMiddleService.getRoleByAppIdAndUserNo("SH", userInfoVo.getWztUserId(), userInfoVo.getTenantId());
                    }
                    if (roles != null && roles.size() > 0) {
                        wdOrgUserExportVo.setUserRole(roles.get(0).getName());
                    }
                }

                list.add(wdOrgUserExportVo);
            });
            try {
                com.redxun.fire.core.utils.ExcelUtil.writeExcel(response, list, dataDto.getFullName() + "地方人员导出", "人员信息列表", new WdOrgUserExportVo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.PERSON_MANAGE.getType());
            journalizing.setOperationContent("导出地方人员列表数据");
            journalizingService.setLogInfo(request, journalizing);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    /**
     * @param params
     * @Description:查询固定分类商户
     * <AUTHOR>
     * @date 2024/4/26 9:05 AM
     */
    @ApiOperation(value = "查询固定分类商户", notes = "查询固定分类商户")
    @PostMapping({"/getMerchantListByType"})
    public JsonResult getMerchantListByType(@RequestBody Map<String, String> params) {
        JsonResult jsonResult = orgMiddleService.getMerchantListByType(params);
        return jsonResult;
    }
}
