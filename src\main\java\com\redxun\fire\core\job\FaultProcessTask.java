package com.redxun.fire.core.job;


import com.redxun.fire.core.service.alarm.IAppFaultInfoService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.HttpClientUtil;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FaultProcessTask extends IJobHandler {
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IAppFaultInfoService appFaultInfoService;

    @Autowired
    private IBaseBuildingService baseBuildingService;

    @Value("${faultPop.url}")
    private String faultUrl;

    private String url = "180.96.11.20:28800/faultFill/sendMsg";


    @Resource
    private HttpClientUtil httpClientUtil;

    /**
     * 每日晚八点清空状态
     */
    //@Scheduled(cron = "0 0 20 * * ? ")
    @XxlJob("faultProcessTask")
    public void execute() throws Exception{
    //public void initRedisForPop() {
        log.info("<<====初始化弹窗判断值====>>");
        List<String> buildingIdList = appFaultInfoService.getBuildingIdList();
        for (String id : buildingIdList) {
            redisUtils.set(id + "_fault_nope", false);
            redisUtils.set(id + "_fault_later", false);
            redisUtils.set(id + "_fault_report", false);
            //昨日缓存的故障列表清空
            // redisUtils.set(id + "_fault_list", new ArrayList<String>());
        }
        log.info("<<====初始化弹窗判断值 结束====>>");
    }




}
