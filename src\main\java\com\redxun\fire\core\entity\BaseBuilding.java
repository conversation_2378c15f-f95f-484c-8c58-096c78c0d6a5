package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redxun.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 建筑物信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BaseBuilding extends SuperEntity {

    private static final long serialVersionUID = 1L;


    @TableId(type = IdType.UUID)
    private String id;

    /**
     * 大区id
     */
    private String regional;

    /**
     * 大区名称
     */
    private String safetyBelt;

    /**
     * 中心id
     */
    private String center;
    /**
     * 中心名称
     */
    private String operatingCenter;

    /**
     * 区域id
     */
    private String jurisdiction;

    /**
     * 区域名称
     */
    private String jurisdictionVal;

    /**
     * 建筑物名称
     */
    private String buildingName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 街道
     */
    private String street;

    /**
     * 地址
     */
    private String wholeAddress;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 占地面积
     */
    private BigDecimal coverArea;

    /**
     * 建筑面积
     */
    private BigDecimal buildingArea;

    /**
     * 标准层面积
     */
    private BigDecimal standardFloorArea;

    /**
     * 建筑高度
     */
    private BigDecimal buildingHigh;

    /**
     * 结构类型
     */
    private String structureType;

    /**
     * 地上层数
     */
    private Integer upNum;

    /**
     * 地下层数
     */
    private Integer downNum;

    /**
     * 建筑状态
     */
    private String buildingStatus;

    /**
     * 是否认证
     */
    private String isApprove;

    /**
     * 消控室电话
     */
    private String ctrlPhone;

    /**
     * 值班中心电话
     */
    private String centerPhone;

    /**
     * 自持/合作物业
     */
    private String property;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 开业时间
     */
    private Date openDate;

    /**
     * 认证状态
     */
    private String verifyStatus;

    /**
     * 考勤范围
     */
    private String clockScope;

    /**
     * 使用性质
     */
    private String useNature;

    /**
     * 是否重点单位
     */
    private String isKey;

    /**
     * 是否危险源
     */
    private String isHazard;

    /**
     * 耐火等级
     */
    private String fireRating;

    /**
     * 火灾危险等级
     */
    private String fireLevel;

    /**
     * 建筑类型
     */
    private String buildingType;
    /**
     *建筑类型名称,对应万中台项目类型名称
     */
    private String buildingTypeName;

    /**
     * 投入使用日期
     */
    private Date serviceEntryDate;

    /**
     * 业态
     */
    private String format;

    /**
     * 远传设备品牌型号
     */
    private String remoteDevice;

    /**
     * 图片路径
     */
    private String picPath;

    /**
     * 所属组织部门
     */
    private String belongDep;

    /**
     * 建筑类型 0 广场 1 其他
     */
    private String buildType;
    /**
     * 注册时间
     */
    private String registerTime;

    /**
     * 单位类型
     */
    private String unitType;
    /**
     * 维保单位
     */
    private String mainUnit;
    /**
     * 管理单位
     */
    private String manageUnit;
    /**
     * 是否开通维保权限
     */
    private String mainFlag;
    /**
     * 建筑编号
     */
    private String buildCode;

    /**
     * 消防主机品牌型号
     */
    private String brand;
    /**
     * 消防主机品牌型号
     */
    private String brandId;

    /**
     运营中心
     */
//    private String operatingCenter;
    /**
     * 综合管理评分
     */
    private String managementScore;
    /**
     * 设备设施评分
     */
    private String devScore;


    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField(exist = false)
    private String buildTypes;

    /**
     * 其他类型的分类 0 酒店  1 影院
     */
    private String otherCat;


    /**
     * 流媒体播放地址
     * */
    private String mediaAddress;
    /**
     * 中心名称
     * */
    private String centerVal;

    private String groupOrg;
    private String region;
    private String cityCompany;
    private String piazza;
    private String levelAyer;
    private String middleId;
    private String projectId;
    private String projectType;

    @TableField("edge_check_config")
    private String edgeCheckConfig;

    @TableField("cloud_check_config")
    private String cloudCheckConfig;

    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
}
