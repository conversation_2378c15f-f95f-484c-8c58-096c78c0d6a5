package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.service.device.IPointSyncService;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2025/6/11
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/pointSync")
public class PointSyncController {

    @Autowired
    IPointSyncService pointSyncService;


    @Autowired
    RedisUtils redisUtils;

    @PostMapping("/syncPointAdjustScope")
    public JsonResult syncPointAdjustScope(@RequestBody Map<String, String> map) {
        final val adjustBuildingId = map.get("buildingId");
        String key = "syncPointAdjustScope-" + adjustBuildingId;
        if (redisUtils.tryLock(key)) {
            try {
                pointSyncService.syncAdjustMaintenancePlan(map);
            } catch (Exception e) {
                log.error("点位调改调整维保范围任务异常:", e);
                redisUtils.delete(key);
            }
            return JsonResult.getSuccessResult("维保范围调整任务开启，请稍等");
        } else {
            return JsonResult.getSuccessResult("点位调改调整维保范围任务正在执行中");
        }
    }
}
