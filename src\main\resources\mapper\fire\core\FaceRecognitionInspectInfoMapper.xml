<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.FaceRecognitionInspectInfoMapper">

    <resultMap id="BaseResultMap" type="com.redxun.fire.core.entity.dutymanage.FaceRecognitionInspectInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="squareId" column="square_id" jdbcType="VARCHAR"/>
            <result property="squareName" column="square_name" jdbcType="VARCHAR"/>
            <result property="result" column="result" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_id,square_id,
        square_name,result,create_time
    </sql>

    <select id="queryPage" resultType="com.redxun.fire.core.pojo.vo.FaceRecognitionInspectInfoVo">
        SELECT
            b.id,
            b.create_time,
            b.result,
            b.alarm_type,
            b.square_name,
            a.safety_belt AS center_val,
            a.jurisdiction_val
        FROM
            base_building a
        INNER JOIN face_recognition_inspect_info b ON a.id = b.square_id
        WHERE b.inspect_type = '1'
        <if test="param.cityCompany != null and param.cityCompany != ''">
            and a.city_company = #{param.cityCompany}
        </if>
        <if test="param.groupOrg != null and param.groupOrg != ''">
            and a.group_org = #{param.groupOrg}
        </if>
        <if test="param.piazza != null and param.piazza != ''">
            and a.piazza = #{param.piazza}
        </if>
        <if test="param.region != null and param.region != ''">
            and a.region = #{param.region}
        </if>
        <if test="param.alarmType != null and param.alarmType != ''">
            and b.alarm_type = #{param.alarmType}
        </if>
        <if test="param.beginTime != null and param.beginTime != ''">
            and a.create_time <![CDATA[>=]]> #{param.beginTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and a.create_time <![CDATA[<=]]> #{param.endTime}
        </if>
        order by b.create_time desc
    </select>
</mapper>
