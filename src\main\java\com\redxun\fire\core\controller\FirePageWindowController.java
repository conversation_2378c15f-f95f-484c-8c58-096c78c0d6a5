package com.redxun.fire.core.controller;

import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.alarm.IFirePageWindowService;
import com.redxun.fire.core.consts.Constant;
import com.redxun.fire.core.utils.ServletsUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 单店监控台弹窗控制台
 */
@RestController
@RequestMapping("/monitor-window")
public class FirePageWindowController {

    @Resource
    private IFirePageWindowService firePageWindowService;

    @Resource
    private IBaseDeviceTemporaryService baseDeviceTemporaryService;
//
//    @Resource
//    private SuspectedFireHandler suspectedFireHandler;

    @ApiOperation(value = "刷入redis", notes = "刷入redis")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "buildingId", paramType = "query", value = "建筑物id", required = true, dataType = "String"),
    })

    @RequestMapping(value = "/setPointRedis", method = RequestMethod.GET)
    public void setPointRedis(@RequestParam("buildingId") String buildingId) {
        baseDeviceTemporaryService.setPointRedis(buildingId);
    }

    /**
     * 1.4.2单店监控台-火警处理弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFireTreatmentWindow", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFireTreatmentWindow(HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        // 获取单个建筑物火警信息
        return ResultMsg.getResultMsg("获取单店报警信息成功", firePageWindowService.getFireWindowInfoList(param), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 1.4.2单店监控台-火警处理
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/updateFireTreatmentWindow", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg updateFireTreatmentWindow(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        try {
            return firePageWindowService.setFireTypeInfo(request, param);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.getResultMsg("处理信息失败", Constant.RESPONSE_STATUS_CODE_FAILED_COMMON);
        }
    }

    /**
     * 超时报备提交审批
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/paoDianOutTimeApprove", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg paoDianOutTimeApprove(@RequestBody Map<String, Object> param) {
        firePageWindowService.paoDianOutTimeApprove(param);
        // 获取单个建筑物火警信息
        return ResultMsg.getResultMsg("提交成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 火警填报自动获取信息接口
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFalsePositiveInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFalsePositiveInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("处理信息成功", firePageWindowService.getFalsePositiveInfo(request), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 1.3.2等待填报弹窗列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaitFillWindow", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaitFillWindow(HttpServletRequest request) {
        return ResultMsg.getResultMsg("处理信息成功", firePageWindowService.getWaiteFillWindow(request), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 1.9.1获取水泵异常信息列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPumpExpection", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPumpExpection(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水泵异常信息成功", firePageWindowService.getPumpExpInfo(request), 200);
    }

    /**
     * 1.9.1.5水泵异常信息列表导出
     *
     * @param request response
     * @return
     */
    @RequestMapping(value = "/exportPumpExpection", method = RequestMethod.GET)
    @ResponseBody
    public void exportPumpExpection(HttpServletRequest request, HttpServletResponse response) throws IOException {
        firePageWindowService.exportPumpExpInfo(request, response);
    }


    /**
     * 1.9.2获取水压异常信息列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaterExpection", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaterExpection(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水压异常信息成功", firePageWindowService.getWaterExpInfo(request), 200);
    }

    /**
     * 水压异常信息列表导出
     *
     * @param: request
     * @return: void
     */
    @RequestMapping(value = "/exportWaterExpection", method = RequestMethod.GET)
    @ResponseBody
    public void exportWaterExpection(HttpServletRequest request, HttpServletResponse response) throws IOException {
        firePageWindowService.exportWaterExpection(request, response);
    }

    /**
     * 1.5.2水压异常处理弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaterExpectionHandle", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaterExpectionHandle(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水压异常信息成功", firePageWindowService.getWaterExpHandleInfo(request), 200);
    }

    /**
     * 1.5.2水压异常信息统计
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaterStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaterStatistic(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水压异常信息成功", firePageWindowService.getWaterStatistic(request), 200);
    }

    /**
     * 1.6.2水泵异常处理弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPumpExpectionHandle", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPumpExpectionHandle(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水泵异常信息成功", firePageWindowService.getPumpExpHandleInfo(request), 200);
    }

    /**
     * 1.6.2水泵异常信息统计 5
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPumpStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPumpStatistic(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取水泵异常信息成功", firePageWindowService.getPumpStatistic(request), 200);
    }

    /**
     * 处理异常信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/updateExpectionHandle", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg updatePumpExpectionHandle(HttpServletRequest request, @RequestBody Map<String, Object> map) throws Exception {
        return firePageWindowService.updateExpHandleInfo(request, map);

    }

    /**
     * 暂不处理审批结束后调用
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/afterApprover", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg afterApprover(HttpServletRequest request) {
        return firePageWindowService.afterApprover(request);
    }

    /**
     * 火警误报统计折线图
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWBStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWBStatistic(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取误报统计信息成功", firePageWindowService.getWBStatistic(request), 200);
    }

    /**
     * 故障统计折线图
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFaultStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFaultStatistic(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取故障统计信息成功", firePageWindowService.getFaultStatistic(request), 200);
    }

    /**
     * 火警待填报列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaiteReported", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaiteReported(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取火警统计信息成功", firePageWindowService.getWaiteFillWindow(request), 200);
    }

    /**
     * 获取跑点人员名单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPaoDianName", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPaoDianName(HttpServletRequest request) {
        return firePageWindowService.getPaoDianName(request);
    }

//    /**
//     * 图片上传
//     *
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/uploadPic", method = RequestMethod.POST)
//    @ResponseBody
//    public ResultMsg uploadPic(MultipartFile uploadFile) throws IOException {
//        return ResultMsg.getResultMsg("操作成功", firePageWindowService.uploadPic(uploadFile), 200);
//    }

    /**
     * 误报火警提交
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/falsePositiveReported", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg falsePositiveReported(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        String ids = String.valueOf(param.get("id"));
        List<String> idList = getIdList(ids);
        for (String id : idList) {
            param.put("id", id);
            firePageWindowService.falsePositiveReported(request, param);
        }
        return ResultMsg.getResultMsg("操作成功", 200);
    }

    /**
     * 测试/确认火警提交
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/submitTestFireInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg submitTestFireInfo(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        String ids = String.valueOf(param.get("id"));
        List<String> idList = getIdList(ids);
        for (String id : idList) {
            param.put("id", id);
            firePageWindowService.testFireReported(request, param);
        }
        return ResultMsg.getResultMsg("操作成功", 200);
    }

    /**
     * 字符串id 切割获取列表
     *
     * @param id
     * @return
     */
    private List<String> getIdList(String id) {
        List<String> idList = new ArrayList<>();
        if (id.contains(",")) {
            String[] split = id.split(",");
            Collections.addAll(idList, split);
        } else {
            //单个id 批量
            idList.add(id);
        }
        return idList;
    }

    /**
     * 楼层平面图异常信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFloorPlanExpection", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFloorPlanExpection(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFloorPlanExpection(request), 200);
    }

    /**
     * 火警待处理，待填报，超时处理，超时填报条数统计
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFireStatisticInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFireStatisticInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFireStatisticInfo(request), 200);
    }

    /**
     * 超时处理，超时填报弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getOutTimeInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getOutTimeInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getOutTimeInfo(request), 200);
    }

    /**
     * 同一楼层异常数据列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFloorExpectionInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFloorExpectionInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFloorExpectionInfo(request), 200);
    }

    /**
     * 获取测试单位名称
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getTestUnitName", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getTestUnitName(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getTestUnitName(request), 200);
    }

    /**
     * 预警待处理查询弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaringInfoWindow", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaringInfoWindow(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getWaringInfoWindow(request), 200);
    }

    /**
     * 预警待处数据条数统计
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaringStatisticInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaringStatisticInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getWaringStatisticInfo(request), 200);
    }

    /**
     * 预警处理超时查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWaringOutTimeInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWaringOutTimeInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getWaringOutTimeInfo(request), 200);
    }

    /**
     * 故障待处理/处理中
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFaultInfoWindow", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFaultInfoWindow(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFaultInfoWindow(request), 200);
    }

    /**
     * 故障处理超时查询弹窗
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFaultOutTimeWindow", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFaultOutTimeWindow(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFaultOutTimeWindow(request), 200);
    }

    /**
     * 故障统计条数信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getFaultStatisticInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getFaultStatisticInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getFaultStatisticInfo(request), 200);
    }

    /**
     * 预警，故障指派他人
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/appointOthers", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg appointOthers(@RequestBody Map<String, Object> param, HttpServletRequest request) {
        return firePageWindowService.appointOthers(param, request);
    }

    /**
     * 当前建筑物维保人员查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getPersonnelInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getPersonnelInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getPersonnelInfo(request), 200);
    }

    /**
     * 获取当前建筑物维保单位信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getWbTeamInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getWbTeamInfo(HttpServletRequest request) {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.getWbTeamInfo(request), 200);
    }

    /**
     * 故障忽略处理
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/faultOverLook", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg faultOverLook(HttpServletRequest request) throws ParseException {
        return firePageWindowService.faultOverLook(request);
    }

    /**
     * 预警完成处理
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/warningFigureOut", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg warningFigureOut(HttpServletRequest request) {
        return firePageWindowService.warningFigureOut(request);
    }

    /**
     * 火警误报统计折线图导出
     *
     * @param: request, response
     * @return:
     */
    @RequestMapping(value = "/exportWBStatistic", method = RequestMethod.GET)
    @ResponseBody
    public void exportWBStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException {
        firePageWindowService.exportWBStatistic(request, response);
    }

    /**
     * 故障统计折线图导出
     *
     * @param: request, response
     * @return: void
     */
    @RequestMapping(value = "/exportFaultStatistic", method = RequestMethod.GET)
    @ResponseBody
    public void exportFaultStatistic(HttpServletRequest request, HttpServletResponse response) throws IOException {
        firePageWindowService.exportFaultStatistic(request, response);
    }

    /**
     * app首页水压水泵火警预警信息统计
     *
     * @param: request
     * @return: void
     */
    @RequestMapping(value = "/appFistPageStatistic", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg appFistPageStatistic(HttpServletRequest request) throws IOException {
        return ResultMsg.getResultMsg("操作成功", firePageWindowService.appFistPageStatistic(request), 200);
    }

    /**
     * app首页水压水泵火警预警建筑列表
     *
     * @param: request
     * @return: ResultMsg
     */
    @RequestMapping(value = "/appFistPageBuildingList", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg appFistPageBuildingList(HttpServletRequest request, @RequestBody Map<String, Object> param) throws IOException {
        //return ResultMsg.getResultMsg("操作成功", firePageWindowService.appFistPageBuildingList(request,param), 200);
        return firePageWindowService.appFistPageBuildingList(request, param);
    }

    /**
     * 获取单个跑点信息
     *
     * @param fireId
     * @return
     */
    @GetMapping("/getSingleInfo")
    public ResultMsg getSingleInfo(@RequestParam("id") String fireId) {
        return firePageWindowService.getSingleInfo(fireId);
    }

    /**
     * 获取未处理火警历史记录
     *
     * @return
     */
    @GetMapping("/getFireHistory")
    public ResultMsg getFireHistory(HttpServletRequest request) {
        return firePageWindowService.getFireHistory(request);
//        return ResultMsg.getResultMsg("操作成功",firePageWindowService.getFireHistory(request),200);
    }

}
