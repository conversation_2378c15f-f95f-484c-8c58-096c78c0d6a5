package com.redxun.fire.core.consts;


/**
 * @Description:缓存key定义
 * <AUTHOR>
 * @date 2024/8/5 1:50 PM
 */
public class RedisKeys {


    /**
     * 回路故障总表，记录所有key,避免模糊查询
     */
    public static final String XF_LOOP_FAULT_SET = "XF_LOOP_FAULT";


    /**
     * 回路故障
     */
    public static final String XF_JUDGE_SET = "judge:";

    public static final String LOCAL_HOST_INIT_BUILD_LIST = "LOCAL_HOST_INIT_BUILD_LIST";




}
