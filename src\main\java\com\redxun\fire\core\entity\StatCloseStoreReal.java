package com.redxun.fire.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 闭店监测设备广场统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatCloseStoreReal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 建筑物id
     */
    private String buildingId;

    /**
     * 运营中心编号
     */
    private String centerCode;

    /**
     * 运营中心名称
     */
    private String centerName;

    /**
     * 区域编号
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    private String regionalId;

    private String buildRegional;

    /**
     * 广场编号
     */
    private String buildingCode;

    /**
     * 广场名称
     */
    private String buildingName;

    /**
     * 设备数量
     */
    private Integer pointCount;

    /**
     * 商户数量
     */
    private Integer storeCount;

    /**
     * 闭店监测接入商户数量
     */
    private Integer closeStoreCount;

    /**
     * 故障点位数
     */
    private Integer faultPoint;

    /**
     * 离线点位数
     */
    private Integer offlinePoint;

    /**
     * 电量异常点位数
     */
    private Integer electricalAnomalyPoint;

    /**
     * 正常设备数
     */
    private Integer normalPoint;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME_",jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME_",jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer typeA;

    private Integer typeB;

    /**
     * A2型号
     */
    private Integer typeC;




    /**
     * 创建人ID
     */
    @TableField(exist = false)
    private Integer sumCount;
}
