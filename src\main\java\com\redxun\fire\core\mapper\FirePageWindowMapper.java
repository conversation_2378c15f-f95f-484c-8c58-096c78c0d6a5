package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.FaultDailyHistory;
import com.redxun.fire.core.entity.FaultInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FirePageWindowMapper extends BaseMapper<Map> {

    List<Map<String, Object>> getWindowInfoBeforeListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getFireCheckInfoList(@Param("fireId") String fireId);

    void updateFireCheck(@Param("param") Map param);

    Map<String, Object> selectFireRedordByFireId(@Param("param") Map param);

    List<Map<String, Object>> getPumpExpectionListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getWaterExpectionListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getWaterExpectionAllListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getWaterExpectionHandleListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getWaterExpectionHandleAllListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getPumpExpectionHandleListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getAllPumpExpectionHandleListByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> getWaterPushHandleInfo(@Param("param") Map param);

    List<Map<String, Object>> getPumpPushHandleInfo(@Param("param") Map param);

    int selectReusePointNum(@Param("param") Map param);

    int selectFaultPointNum(@Param("param") Map param);

//    List<StatWaterPressureEveryday> selectByBuildingIdForUpdate(@Param("buildingId") String buildingId,@Param("today") String today);

//    List<StatPumpEveryday> selectPumpByBuildingForUpdate(@Param("buildingId") String buildingId,@Param("today") String today);

    List<Map<String, Object>> selectWaterExpection(@Param("param") Map param);

    List<Map<String, Object>> selectAllWaterExpection(@Param("param") Map param);

    List<Map<String, Object>> selectPumpExpection(@Param("param") Map param);

    List<Map<String, Object>> selectAllPumpsExpection(@Param("param") Map param);

    List<Map<String, Object>> selectFaultProcessInfoByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectFaultProcessAllInfoByPage(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectTodayFaultInfo(@Param("param") Map param);

    List<Map<String, Object>> selectAllFaultInfo(@Param("param") Map param);

    List<Map<String, Object>> selectPersonnelInfo(@Param("param") Map param);

    List<Map<String, Object>> selectWbTeamInfo(@Param("param") Map param);

    List<Map<String, Object>> getWaterExpectionListWithoutPage(@Param("param") Map<String, Object> param);

    List<Map<String, Object>> getPumpExpectionListWithoutPage(@Param("param") Map<String, Object> param);

    int selectFireUndoneCount(String buildingId);

    int selectFireUndoneCountAllBuild(@Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDeps(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDepsWithFire(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDepsWithFault(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDepsWithWater(Page<?> page, @Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDepsWithPump(Page<?> page, @Param("param") Map param);

    @SqlParser(filter = true)
    int getWorngCount(@Param("pointId") String pointId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Map<String, Object>> getFireCheckInfoByIds(@Param("fireIds") List<String> fireIds);

    int selectWaterTotal(@Param("param") Map param);

    int selectPumpTotal(@Param("param") Map param);

    List<Map<String, Object>> selectBaseBuildingByBelongDepsWithCloseStore(Page<?> page, @Param("param") Map param);

    int selectCloseStoreTotal(@Param("param") Map param);

    List<FaultDailyHistory> selectFaultPointHistory(@Param("param") Map param);
}
