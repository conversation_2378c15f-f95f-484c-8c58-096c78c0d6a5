package com.redxun.fire.core.controller.dutymanage;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.dutymanage.DutyShiftInfo;
import com.redxun.fire.core.entity.dutymanage.FaceRecognitionAlarmInfo;
import com.redxun.fire.core.service.dutymanage.FaceRecognitionAlarmInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @createTime 2025/3/31
 * @description
 */
@RestController
@RequestMapping("faceRecognitionAlarmInfo")
@Slf4j
public class FaceRecognitionAlarmInfoController {

    @Autowired
    private FaceRecognitionAlarmInfoService faceRecognitionAlarmInfoService;

    @GetMapping(value = "queryAlarmData")
    public JsonResult queryAlarmData(@RequestParam String buildingId) {
        try {
            return faceRecognitionAlarmInfoService.queryAlarmData(buildingId);
        } catch (Exception e) {
            log.error("人脸识别报警查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }


    @PostMapping(value = "updateAlarmData")
    public JsonResult updateAlarmData(@RequestBody FaceRecognitionAlarmInfo faceRecognitionAlarmInfo, HttpServletRequest request) {
        try {
            return faceRecognitionAlarmInfoService.updateAlarmData(faceRecognitionAlarmInfo, request);
        } catch (Exception e) {
            log.error("更新人脸识别报警信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

}
