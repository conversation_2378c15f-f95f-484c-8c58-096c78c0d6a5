package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.entity.MisReportes;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 反复误报点位
 */
@Data
@NoArgsConstructor
public class AuditRepeatRecord implements Serializable {


    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditRecordId;

    /**
     * 建筑名称
     */
    private String buildingName;

    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 设备类型
     */
    private String devTypeName;

    /**
     * 点位描述
     */
    private String pointDesc;


    /**
     * 7天内误报次数
     */
    private String sevenDayCount;

    /**
     * 14天内误报次数
     */
    private String fourteenDayCount;

    /**
     * 最近一次误报时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Object reportTime;

    /**
     * 14天内误报次数
     */
    private String dateCount;


    public AuditRepeatRecord(MisReportes misReport) {
        this.pointNumber = misReport.getPointCode();
        this.devTypeName = misReport.getDevTypeName();
        this.pointDesc = misReport.getPointDesc();
        this.buildingName = misReport.getBuildingName();
        this.fourteenDayCount = misReport.getFourteenDayCount().toString();
        this.reportTime = misReport.getReportTime();
        this.dateCount = misReport.getDateCount().toString();
        this.sevenDayCount = String.valueOf(misReport.getSevenDayCount());
    }
}
