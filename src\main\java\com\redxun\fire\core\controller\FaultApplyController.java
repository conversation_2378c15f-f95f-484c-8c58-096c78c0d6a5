package com.redxun.fire.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.FaultApplyMapper;
import com.redxun.fire.core.mapper.FaultInfoMapper;
import com.redxun.fire.core.mapper.WWdFaultApplyMapper;
import com.redxun.fire.core.service.alarm.IFaultInfoService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <p>
 * 故障申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@RestController
@RequestMapping("/fault-apply")
public class FaultApplyController {

    @Resource
    private OrgManageClient orgManageClient;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Resource
    private IFaultInfoService faultInfoService;
    @Resource
    private FaultApplyMapper faultApplyMapper;
    @Resource
    RedisUtils redisUtils;
    @Resource
    FaultInfoMapper faultInfoMapper;
    @Resource
    BaseDevicePointMapper baseDevicePointMapper;
    @Resource
    private WWdFaultApplyMapper wWdFaultApplyMapper;
    Gson gson = new Gson();

    @ApiOperation(value = "故障申请获取审批人")
    @GetMapping({"/faultJobApprove"})
    public List<TaskExecutor> faultJobApprove(@RequestParam(value = "applyId") String applyId, @RequestParam(value = "jobId") String jobId, @RequestParam(value = "userId") String userId, @RequestParam(value = "level") String level) {
        log.info("自定义执行人[{}],[{}],[{}],[{}]", applyId, jobId, userId, level);
        List<TaskExecutor> userList = new ArrayList<>();
        UserOrgResponse userOrg = orgMiddleService.getOrgManageByUser(userId);
        if (userOrg.getSuccess()) {
            OrgManageParam orgParam = new OrgManageParam();
            orgParam.setBusinessId("1");
            if ("1".equals(level)) {
                orgParam.setPiazzaId(userOrg.getData().get(0).getPiazza());
            } else if ("2".equals(level)) {
                orgParam.setCityId(userOrg.getData().get(0).getCity());
            } else if ("3".equals(level)) {
                orgParam.setRegionId(userOrg.getData().get(0).getRegion());
            } else if ("4".equals(level)) {
                orgParam.setGroupId(userOrg.getData().get(0).getGroup());
            }
            orgParam.setJobId(jobId);
            orgParam.setTenantId(userOrg.getData().get(0).getTenantId());
            JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
            try {
                UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                if (data != null && data.size() > 0) {
                    for (UserOrgResponse.DataDTO dto : data) {
                        userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                    }
                }
            } catch (Exception e) {
                log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
            }
        }
        if(userList!=null&&userList.size()>15){
            log.info("---------------->>>>>>>>>>>>>查询执行人数量过多入参applyId:{},jobId:{},userId:{},level:{},数量:{}" ,applyId,jobId,userId,level, userList.size());
            return new ArrayList<TaskExecutor>();
        }
        return userList;
    }

    @ResponseBody
    @PostMapping(value = "/faultPassApply")
    @ApiOperation("故障申请审批通过")
    public JsonResult passApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("故障验证通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        WWdFaultApply faultApply = wWdFaultApplyMapper.selectOne(new LambdaQueryWrapper<WWdFaultApply>().eq(WWdFaultApply::getWId, data).orderByDesc(WWdFaultApply::getCreateTime).last(" limit 1"));
        if (faultApply == null || StringUtils.isEmpty(faultApply.getFaultidlist())) {
            log.info("故障验证通过，ID为：{}, 无对应故障申请信息", data);
            return new JsonResult(false).setMessage("无对应故障申请信息");
        }
        List<String> faultIdList = Arrays.asList(faultApply.getFaultidlist().split(","));
        List<FaultInfo> faultInfoList = faultInfoService.list(new LambdaQueryWrapper<FaultInfo>().in(FaultInfo::getId, faultIdList));
        List<String> pointIds = new ArrayList<>();
        faultInfoList.forEach(faultInfo -> {
            FarEastoneCache devicePoint = FastJSONUtils.toBean(redisUtils.get("point_status:" + faultInfo.getBuildingId() + "-" + faultInfo.getPointId()) + "", FarEastoneCache.class);

            if (devicePoint != null) {
                devicePoint.setFaultStatus("0");
                devicePoint.setFaultHandleStatus("1");
                redisUtils.set("point_status:" + faultInfo.getBuildingId() + "-" + faultInfo.getPointId(), JSON.toJSONString(devicePoint));
            }
            faultInfo.setFaultStatus("1");
            faultInfo.setFaultStatusStr("已处理");
            faultInfo.setRepairman("系统");
            faultInfo.setRepairmanId("1");
            faultInfo.setEndTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            faultInfo.setHandleStatus("已处理");
            faultInfo.setHandleUserId("1");
            faultInfo.setHandleUser("系统");
            faultInfo.setHandleTime(DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
            faultInfo.setRepairVerify("2");
            faultInfo.setApplyStatus(2);
            pointIds.add(faultInfo.getPointId());
        });
        faultInfoService.updateBatchById(faultInfoList);

        BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
        baseDevicePoint.setPointStatus("0");
        Integer num = baseDevicePointMapper.update(baseDevicePoint, new LambdaQueryWrapper<BaseDevicePoint>().in(BaseDevicePoint::getId, pointIds));
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/faultNoPassApply")
    @ApiOperation("故障申请审批未通过")
    public JsonResult noPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("故障验证被驳回，ID为：{}", JSON.toJSONString(jsonObject));
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        FaultApply faultApply = faultApplyMapper.selectOne(new LambdaQueryWrapper<FaultApply>().eq(FaultApply::getFId, data).orderByDesc(FaultApply::getCreateTime).last(" limit 1"));
        if (faultApply == null || StringUtils.isEmpty(faultApply.getFFaultidlist())) {
            log.info("故障验证通过，ID为：{}, 无对应故障申请信息", data);
            return new JsonResult(false).setMessage("无对应故障申请信息");
        }
        List<String> faultIdList = Arrays.asList(faultApply.getFFaultidlist().split(","));
        FaultInfo faultInfo = new FaultInfo();
        faultInfo.setApplyStatus(1);
        faultInfoMapper.update(faultInfo, new LambdaQueryWrapper<FaultInfo>().in(FaultInfo::getId, faultIdList));
        return jsonResult;
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return faultInfoService.flowEvent(dataJson);
    }

}
