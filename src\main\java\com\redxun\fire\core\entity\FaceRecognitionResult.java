package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@Builder
@TableName("face_recognition_result")
public class FaceRecognitionResult {

    @TableId(type = IdType.UUID)
    private String id;

    @TableField("id_number")
    private String idNumber;

    @TableField("project_id")
    private String projectId;

    @TableField("status")
    private String status;

    @TableField("recognition_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recognitionTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("recognition_picture")
    private String recognitionPicture;

    @TableField("alarm_type")
    private Integer alarmType;

    @TableField("alarm_remark")
    private String alarmRemark;

    @TableField("remark")
    private String remark;
}