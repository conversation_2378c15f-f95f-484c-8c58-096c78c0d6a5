package com.redxun.fire.core.service.device.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.feign.bpm.BpmClient;
import com.redxun.feign.bpm.entity.BpmInst;
import com.redxun.fire.core.consts.BPMConstants;
import com.redxun.fire.core.consts.Constant;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.enums.MethodEnum;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.pojo.base.StringRedisDto;
import com.redxun.fire.core.pojo.vo.DevicePointInfoVo;
import com.redxun.fire.core.pojo.vo.QuarterlyScope;
import com.redxun.fire.core.service.alarm.IFaultInfoService;
import com.redxun.fire.core.service.alarm.ISmokeAlarmSettingService;
import com.redxun.fire.core.service.alarm.IWdFireSysService;
import com.redxun.fire.core.service.bpm.IBpmForwardService;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.IAdjustptService;
import com.redxun.fire.core.service.device.*;
import com.redxun.fire.core.service.other.IAppointmentApScopeService;
import com.redxun.fire.core.service.other.IConfigRelationService;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.service.alarm.impl.WaterMonitorServiceImpl;
import com.redxun.fire.core.service.maintenance.MaintenancePlanService;
import com.redxun.fire.core.service.maintenance.MaintenanceScopeTestService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.fire.core.utils.UUIDUtils;
import com.redxun.fire.core.utils.*;
import com.redxun.fire.core.utils.ListUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-24
 */
@Slf4j
@Service
public class BaseDeviceTemporaryServiceImpl extends ServiceImpl<BaseDeviceTemporaryMapper, BaseDeviceTemporary> implements IBaseDeviceTemporaryService {
    @Resource
    BaseDeviceTemporaryMapper deviceTemporaryMapper;
    @Resource
    IBaseDevicePointDotService devicePointService;
    @Resource
    IBaseDeviceApplicationService iBaseDeviceApplicationService;
    @Resource
    private IBaseBuildingFloorService floorService;
    @Resource
    IBaseBuildingService iBaseBuildingService;
    @Resource
    RedisUtils redisUtils;
    @Resource
    IAdjustptService iAdjustptService;
    @Resource
    private BaseDevicePointMapper baseDevicePointMapper;
    @Autowired
    private WaterMonitorServiceImpl waterMonitorService;
    @Autowired
    private IJournalizingService journalizingServiceImpl;

    @Autowired
    IConfigRelationService configRelationService;

    @Resource
    private BpmClient bpmClient;
    @Resource
    private BaseLocalHostInfoMapper baseLocalHostInfoMapper;
    @Resource
    private BaseHostInfoMapper baseHostInfoMapper;
    @Resource
    private IWdFireSysService wdFireSysService;
    @Resource
    private IWdDevConfigService wdDevConfigService;
    @Resource
    private StatLocalHostExpectionMapper statLocalHostExpectionMapper;
    @Resource
    private MaintenancePlanMapper maintenancePlanMapper;
    @Resource
    private AppointmentApplyMapper appointmentApplyMapper;
    @Resource
    private MaintenanceConfigMapper maintenanceConfigMapper;
    @Resource
    private IAppointmentApScopeService appointmentApScopeService;
    @Resource
    private MaintenanceScopeTestMapper maintenanceScopeTestMapper;
    @Resource
    private MaintenanceScopeTestService scopeTestService;
    @Resource
    private MaintenancePlanService maintenancePlanService;
    @Resource
    private IPointBatchService pointBatchService;
    @Resource
    private PointBatchMapper pointBatchMapper;
    @Resource
    private IFaultInfoService faultInfoService;
    @Resource
    private IBasePointShieldLogService basePointShieldLogService;
    @Resource
    private IBpmForwardService bpmForwardService;
    @Resource
    private ISmokeAlarmSettingService smokeAlarmSettingService;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Resource
    private IOsUserService osUserService;

    @Autowired
    IBaseDevicePointService baseDevicePointService;

    @Autowired
    IBaseLocalHostInfoService baseLocalHostInfoService;

    // 点位新增和删改部分 由自身管理的线程池去实现
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(20, 100, 20000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private final ThreadPoolExecutor applyThreadPoolExecutor = new ThreadPoolExecutor(10, 50, 20000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    @Autowired
    private BaseBuildingMapper baseBuildingMapper;

    @Override
//    @GlobalTransactional
    @Transactional(rollbackFor = Exception.class)
    public JsonResult addApplyInfo(HttpServletRequest request, BaseDeviceTemporaryList baseDeviceTemporaryList) {

        JsonResult jsonResult = JsonResult.getSuccessResult("操作成功");

        List<BaseDeviceTemporary> baseDeviceTemporaries = baseDeviceTemporaryList.getList();
        String applyId = baseDeviceTemporaryList.getApplyId();
        String applyType = baseDeviceTemporaryList.getApplyType();
        BaseDeviceTemporary baseDeviceTemporary;
        Double precent = 0d;
        boolean generateFlag = false;

        Journalizing journalizing = new Journalizing();
        journalizing.setOperationTypeCode(LogTypeEnums.POINT_ADJUST.getType());

        Map<String, Object> map = new HashMap<>();
        // 先删除部分点位数据
        List<BaseDeviceTemporary> delList = baseDeviceTemporaryList.getDelList();
        // 删除部分不为空 那么先删除这些临时表数据
        log.info("发起点位调改申请，申请类型是:【{}】,申请id是:【{}】,需要删除的数据列表为:【{}】", applyType, applyId, JSONObject.toJSONString(delList));
        if (StringUtils.isNotBlank(applyId) && !CollectionUtils.isEmpty(delList)) {
            List<String> removeIds = delList.stream().map(BaseDeviceTemporary::getId).collect(Collectors.toList());
            Long s = System.currentTimeMillis();
            applyThreadPoolExecutor.execute(() ->  this.removeByIds(removeIds));
            Long e = System.currentTimeMillis();
            log.info("removeByIds接口时长为：{}", (e - s));
        }
        if (StringUtils.isBlank(applyId)) {
            applyId = UUIDUtils.getUUID();
            generateFlag = true;
        }
        if ("0".equals(applyType)) {
            // 新增点位申请
            List<BaseDeviceTemporary> tempBaseDeviceTemporaries = null;
            try {
                tempBaseDeviceTemporaries = getPointFileByApplyId(applyId);
            } catch (IOException e) {
                throw new RuntimeException("文件未找到！");
            }
            if (CollectionUtils.isEmpty(tempBaseDeviceTemporaries)) {
                throw new RuntimeException("申请id未传入");
            }
            //获取公共参数
            baseDeviceTemporary = tempBaseDeviceTemporaries.get(0);
            // 现有建筑的点位数
            int totalPointCount = devicePointService.count(new QueryWrapper<BaseDevicePoint>().eq("building_id", baseDeviceTemporary.getBuildingId()));
            // 新增的点位数量
            int newPointCount = tempBaseDeviceTemporaries.size();
            int allPointCount = newPointCount + totalPointCount;
            if (0 != allPointCount) {
                precent = (newPointCount * 1.0 / allPointCount) * 100;
                BigDecimal bg = new BigDecimal(precent);
                precent = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            BaseDeviceApplication baseDeviceApplication = new BaseDeviceApplication();
            baseDeviceApplication.setId(applyId);
            baseDeviceApplication.setModulationProportion(String.format("%.2f", precent));
            baseDeviceApplication.setBuildingId(baseDeviceTemporary.getBuildingId());
            baseDeviceApplication.setBuildingName(baseDeviceTemporary.getBuildName());
            baseDeviceApplication.setModulationType("0");
            baseDeviceApplication.setModulationQuantity(tempBaseDeviceTemporaries.size());
            baseDeviceApplication.setApplicantTime(DateUtil.getCurrentDatetime());
            baseDeviceApplication.setApprovOverTime(null);
            baseDeviceApplication.setApprovCondition("0");
            String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
            String userId = request.getHeader("xfUserId");//消防userId
            UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
            if (dataDto != null) {
                baseDeviceApplication.setCreateBy(userId);
                baseDeviceApplication.setProposer(dataDto.getFullName());
            }
            baseDeviceApplication.setCreateTime(new Date());

            // 新增或者修改该记录
            applyThreadPoolExecutor.execute(() -> {
                iBaseDeviceApplicationService.saveOrUpdate(baseDeviceApplication);
            });

            // 更新临时表点位状态 先修改相应的数据信息
            baseDeviceTemporaries.forEach(tem -> {
                tem.setOldPointCode(tem.getPointNumber());
                tem.setOldDescribe(tem.getPointDesc());
                tem.setOldDevTypeCode(tem.getDevTypeCode());
                tem.setOldDevTypeName(tem.getDevTypeName());
                tem.setOldZone(tem.getZoneName());
                tem.setOldFloor(String.valueOf(tem.getFloor()));
            });
            //更新文件内容
            List<String> ids = baseDeviceTemporaries.stream().map(BaseDeviceTemporary::getId).collect(Collectors.toList());
            List<BaseDeviceTemporary> collect = tempBaseDeviceTemporaries.stream().filter(baseDeviceTemporary1 -> !ids.contains(baseDeviceTemporary1.getId())).collect(Collectors.toList());
            collect.addAll(baseDeviceTemporaries);
            try {
                redisUtils.set(Constant.IMPORT_POINT_KEY + applyId, JSON.toJSON(collect));
            } catch (Exception e) {
                throw new RuntimeException("临时文件写入redis失败");
            }
            applyThreadPoolExecutor.execute(() -> {
                this.updateBatchById(baseDeviceTemporaries);
            });


            //日志插入
            journalizing.setOperationContent("申请增加" + baseDeviceTemporary.getBuildName() + baseDeviceTemporaries.size() + "个点位");
            journalizingServiceImpl.setLogInfo(request, journalizing);

            map.put("applyId", applyId);
            map.put("data", tempBaseDeviceTemporaries);
            map.put("adjustCount", newPointCount);
            map.put("precent", precent);
        } else if ("1".equals(applyType) || "2".equals(applyType) || "3".equals(applyType)) {
            //进入修改删除和特批点位申请等逻辑
            //获取公共参数
            baseDeviceTemporary = baseDeviceTemporaryList.getList().get(0);
            // 现有建筑的点位数\
            Long s = System.currentTimeMillis();
            int totalPointCount = devicePointService.count(new QueryWrapper<BaseDevicePoint>().eq("building_id", baseDeviceTemporary.getBuildingId()));
            Long e = System.currentTimeMillis();
            log.info("devicePointService.count接口时长为：{}", (e - s));
            // 新增的点位数量
            int newPointCount = baseDeviceTemporaryList.getList().size();
            int allPointCount = totalPointCount;
            // 非删除点位的时候 总数量为原点位加上新点位数据
            if (!"2".equals(applyType)) {
                allPointCount = newPointCount + totalPointCount;
            }
            if (0 != allPointCount) {
                precent = (newPointCount * 1.0 / allPointCount) * 100;
                BigDecimal bg = new BigDecimal(precent);
                precent = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            BaseDeviceApplication baseDeviceApplication = new BaseDeviceApplication();
            if (generateFlag) {
                baseDeviceApplication.setId(applyId);
                baseDeviceApplication.setCreateTime(new Date());
            } else {
                s = System.currentTimeMillis();
                baseDeviceApplication = iBaseDeviceApplicationService.getById(applyId);
                 e = System.currentTimeMillis();
                log.info("iBaseDeviceApplicationService.getById为：{}", (e - s));
                if (null == baseDeviceApplication) {
                    baseDeviceApplication = new BaseDeviceApplication();
                    baseDeviceApplication.setId(applyId);
                    baseDeviceApplication.setCreateTime(new Date());
                }
            }

            baseDeviceApplication.setId(applyId);
            baseDeviceApplication.setModulationProportion(String.format("%.2f", precent));
            baseDeviceApplication.setBuildingId(baseDeviceTemporary.getBuildingId());
            baseDeviceApplication.setBuildingName(baseDeviceTemporary.getBuildName());
            baseDeviceApplication.setModulationType(applyType);
            baseDeviceApplication.setModulationQuantity(newPointCount);
            baseDeviceApplication.setApplicantTime(DateUtil.getCurrentDatetime());
            baseDeviceApplication.setApprovCondition("0");
            String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
            String userId = request.getHeader("xfUserId");//消防userId
             s = System.currentTimeMillis();
            UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
             e = System.currentTimeMillis();
            log.info("orgMiddleService.findUserByUserId接口时长为：{}", (e - s));
            if (dataDto != null) {
                baseDeviceApplication.setCreateBy(userId);
                baseDeviceApplication.setProposer(dataDto.getFullName());
            }
            // 保存或修改相应记录
            BaseDeviceApplication finalBaseDeviceApplication = baseDeviceApplication;
             s = System.currentTimeMillis();
            applyThreadPoolExecutor.execute(() -> {
                iBaseDeviceApplicationService.saveOrUpdate(finalBaseDeviceApplication);
            });
             e = System.currentTimeMillis();
            log.info("iBaseDeviceApplicationService.saveOrUpdate接口时长为：{}", (e - s));
            //插入临时表
            s = System.currentTimeMillis();
            Map<String, Object> maps = this.addDevPoints(baseDeviceTemporaryList.getList(), precent, applyType, applyId);
            e = System.currentTimeMillis();
            log.info("this.addDevPoints接口时长为：{}", (e - s));
            List<BaseDeviceTemporary> deviceTemporaries = (List<BaseDeviceTemporary>) maps.get("list");
            applyId = (String) maps.get("applyId");
            String pointNumber = (String) maps.get("pointNumber");
            map.put("data", deviceTemporaries);
            map.put("applyId", applyId);
            map.put("adjustCount", newPointCount);
            map.put("precent", precent);
            if (StringUtils.isNotEmpty(pointNumber)) {
                //没有点位编号
                map.put("status", "3");
                jsonResult.setData(map);
                return jsonResult;
            }
        } else {
            throw new RuntimeException("未找到相应的申请类型 请查证后再次尝试");
        }
        // 发起审批流程

        //设置流程参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("checkType", BPMConstants.CHECK_TYPE_POINT);
        jsonObject.put("systemHand", true);
        JSONObject pointAdjustRecord = new JSONObject();
        JSONObject formJson = new JSONObject();

        pointAdjustRecord.put("adjust_record_id", applyId);
        pointAdjustRecord.put("building_id", baseDeviceTemporary.getBuildingId());
        if(baseDeviceTemporary.getBuildingId()!=null&&!"".equals(baseDeviceTemporary.getBuildingId())){
            BaseBuilding bb=baseBuildingMapper.selectById(baseDeviceTemporary.getBuildingId());
            pointAdjustRecord.put("building_name", bb.getBuildingName());
        }else{
            pointAdjustRecord.put("building_name", baseDeviceTemporary.getBuildName());
        }
        pointAdjustRecord.put("adjust_precent", precent);
        pointAdjustRecord.put("adjust_count", map.get("adjustCount"));
        //0新增   1修改   公用
        if ("0".equals(applyType) || "1".equals(applyType)) {

            //日志插入
            if ("1".equals(applyType)) {
                journalizing.setOperationContent("申请修改" + baseDeviceTemporary.getBuildName() + baseDeviceTemporaries.size() + "个点位");
                journalizingServiceImpl.setLogInfo(request, journalizing);
            }

            jsonObject.put("defKey", BPMConstants.DEF_ID_POINT);
            if ("0".equals(applyType)) {
                pointAdjustRecord.put("adjust_type", "0");
            } else {
                pointAdjustRecord.put("adjust_type", "1");
            }
        } else if ("2".equals(applyType)) {//删除
            pointAdjustRecord.put("adjust_type", "2");
            //日志插入
            journalizing.setOperationContent("申请删除" + baseDeviceTemporary.getBuildName() + baseDeviceTemporaries.size() + "个点位");
            journalizingServiceImpl.setLogInfo(request, journalizing);

            ConfigRelation configRelation = configRelationService.queryConfigByCodeOne("splc_4_dwjcbl");
            double configNum = Double.parseDouble(configRelation.getConfigStrVal());
            if (precent > configNum) {
                jsonObject.put("defKey", BPMConstants.S_DEF_ID_POINT);
            } else {
                jsonObject.put("defKey", BPMConstants.N_DEF_ID_POINT);
            }
        } else if ("3".equals(applyType)) {//特批

            pointAdjustRecord.put("adjust_type", "3");
            //日志插入
            journalizing.setOperationTypeCode(LogTypeEnums.SPECIAL_POINT.getType());
            journalizing.setOperationContent("申请" + baseDeviceTemporary.getBuildName() + baseDeviceTemporaries.size() + "个正常点位为特批点位");
            applyThreadPoolExecutor.execute(() -> journalizingServiceImpl.setLogInfo(request, journalizing));
            ConfigRelation configRelation = configRelationService.queryConfigByCodeOne("splc_5_dwjcbl");
            double configNum = Double.parseDouble(configRelation.getConfigStrVal());
            if (precent > configNum) {
                jsonObject.put("defKey", BPMConstants.SSP_DEF_ID_POINT);
            } else {
                jsonObject.put("defKey", BPMConstants.NSP_DEF_ID_POINT);
            }
        }
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        String userId = dataDto.getFullName();
        pointAdjustRecord.put("apply_user", userId);
        formJson.put("point_adjust_record", pointAdjustRecord);
        jsonObject.put("formJson", JSONObject.toJSONString(formJson));
        //启动审批流程
        String finalApplyId = applyId;
        Long s = System.currentTimeMillis();
        boolean processFlag = startPointAdjustProcess(finalApplyId, jsonObject, pointAdjustRecord.get("adjust_type").toString());
        Long e = System.currentTimeMillis();
        log.info("startPointAdjustProcess接口时长为：{}", (e - s));
        if (!processFlag) {
            throw new RuntimeException("流程申请未成功发起,请重试");
        }
        if (redisUtils.exists(Constant.IMPORT_POINT_KEY + applyId)) {
            redisUtils.set(Constant.IMPORT_POINT_KEY + applyId, redisUtils.get(Constant.IMPORT_POINT_KEY + applyId));
        }
        jsonResult.setData(map);
        return jsonResult;
    }


    /**
     * 点位调改申请流程启动
     *
     * @param applyId
     * @param jsonObject
     */
    private Boolean startPointAdjustProcess(String applyId, JSONObject jsonObject, String adjustType) {
        log.info("启动流程");
        JsonResult<BpmInst> bpmResult = bpmClient.startProcess(jsonObject);
        log.info("流程返回数据为：{}", JSONObject.toJSONString(bpmResult));
//        if (bpmResult.getCode() != 200) {
        if (!bpmResult.getSuccess()) {
            return Boolean.FALSE;
        }
        try {
            if ("3".equals(adjustType) || "2".equals(adjustType)) {
                //发起中台审批
//                bpmForwardService.sendMiddleApprove(bpmResult.getData().getInstId(), new PointAdjustDto(), null, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
//            throw new RuntimeException("发起中台审批失败");
        }
        return Boolean.TRUE;
    }


    /**
     * 添加点位信息
     *
     * @param allList
     * @param percent
     * @param applyId
     * @return
     */
    @Transactional
    @Override
    public Map<String, Object> addDevPoints(List<BaseDeviceTemporary> allList, double percent, String applyType, String applyId) {
        Map<String, Object> map = new HashMap<>();

        BaseDeviceTemporary deviceTemporary = allList.get(0);
        deviceTemporary.setApplyType(applyType);
        deviceTemporary.setApproveType("0");
        map.put("list", allList);
        map.put("applyId", applyId);
        /*int flag = deviceTemporaryMapper.existsNotApply(deviceTemporary);
        if (flag > 0) {
            map.put("flag", "1");
            return map;
        }*/
        // 判断是不是点位修改 点位修改的话 记录修改内容 新增 删除也要记录相应内容
        this.remove(new QueryWrapper<BaseDeviceTemporary>().eq("apply_id", applyId));
        List<List<BaseDeviceTemporary>> lists = ListUtil.averageBySize(allList, 1000);
        for (List<BaseDeviceTemporary> list : lists) {
            threadPoolExecutor.submit(() -> {
                List<BaseDeviceTemporary> devList = new ArrayList<>();
                List<String> pointIds = Lists.newArrayList();
                list.forEach(tem -> {
                    pointIds.add(tem.getId().replaceAll(applyId, "").trim());
                });
                // 查询所有的相关点位信息
                List<BaseDevicePoint> pointList = devicePointService.listByIds(pointIds);
                // 原点位信息部分修改 返回信息是点位列表 map的key是id 这个是为了记录原先的部分数据
                Map<String, List<BaseDevicePoint>> pointMap = pointList.stream().collect(Collectors.groupingBy(BaseDevicePoint::getId));
                for (BaseDeviceTemporary tem : list) {
                    if (StringUtils.isEmpty(tem.getPointNumber())) {
//                        map.put("pointNumber", "1");
//                        return map;
                    }
                    if (!tem.getId().contains(applyId)) {
                        // 这个之前是点位id 防止id重复
                        tem.setId(applyId + tem.getId());
                    }
                    // 获取原点位信息
                    List<BaseDevicePoint> points = pointMap.get(tem.getId().replaceAll(applyId, "").trim());
                    if (!CollectionUtils.isEmpty(points)) {
                        BaseDevicePoint prePoint = points.get(0);
                        tem.setOldPointCode(prePoint.getPointNumber());
                        tem.setOldDescribe(prePoint.getPointDesc());
                        tem.setOldDevTypeCode(prePoint.getDevTypeCode());
                        tem.setOldDevTypeName(prePoint.getDevTypeName());
                        tem.setOldZone(prePoint.getZoneName());
                        tem.setOldFloor(String.valueOf(prePoint.getFloor()));
                        // 设置点位修改内容 这个是点位修改的时候会记录下来 记录变更内容
                        if ("1".equals(applyType)) {
                            List<String> changes = Lists.newArrayList();
                            if (StringUtils.isNotBlank(prePoint.getZoneName()) && !prePoint.getZoneName().equals(tem.getZoneName())) {
                                changes.add("防火分区从" + prePoint.getZoneName() + "变成" + tem.getZoneName());
                            }
                            if (StringUtils.isNotBlank(prePoint.getPointDesc()) && !prePoint.getPointDesc().equals(tem.getPointDesc())) {
                                changes.add("点位描述从" + prePoint.getPointDesc() + "变成" + tem.getPointDesc());
                            }
                            if (null != prePoint.getFloor() && !String.valueOf(prePoint.getFloor()).equals(tem.getFloor())) {
                                changes.add("楼层从" + prePoint.getFloor() + "变成" + tem.getFloor());
                            }
                            if (StringUtils.isNotBlank(prePoint.getDevTypeCode()) && !prePoint.getDevTypeCode().equals(tem.getDevTypeCode())) {
                                changes.add("设备类型从" + prePoint.getDevTypeName() + "变成" + tem.getDevTypeName());
                            }
                            tem.setOldContent(StringUtils.join(changes, ","));
                        }
                    }
                    tem.setApplyId(applyId);
                    tem.setApplyType(applyType);
                    tem.setApproveType("0");
                    tem.setCreateTime(null);
                    tem.setUpdateTime(null);
                    devList.add(tem);
                }
                // 保存点位申请记录表
//        先根据applyId删除之前记录 后续的再批量插入
                if (devList.size() > 0) {
                    this.saveBatch(devList);
                }
            });
        }
        return map;
    }

    /**
     * 添加点位信息
     *
     * @param list
     * @return
     */
    @Transactional
    @Override
    public Map<String, Object> addNewDevPoints(List<BaseDeviceTemporary> list) {
        Map<String, Object> map = new HashMap<>();
        List<BaseDeviceTemporary> devList = new ArrayList<>();
        BaseDeviceTemporary deviceTemporary = list.get(0);
        deviceTemporary.setApplyType("0");
        deviceTemporary.setApproveType("0");
        String applyId = "";
        map.put("list", devList);
        applyId = UUIDUtils.getUUID();
        map.put("applyId", applyId);
        //为0时 新增
        for (BaseDeviceTemporary tem : list) {
            // 插入部分数据供列表展示
            tem.setOldPointCode(tem.getPointNumber());
            tem.setOldDescribe(tem.getPointDesc());
            tem.setOldDevTypeCode(tem.getDevTypeCode());
            tem.setOldDevTypeName(tem.getDevTypeName());
            tem.setOldZone(tem.getZoneName());
            tem.setOldFloor(String.valueOf(tem.getFloor()));
            tem.setApplyId(applyId);
            tem.setApplyType("0");
            tem.setApproveType("0");
            tem.setCreateTime(null);
            tem.setUpdateTime(null);
            devList.add(tem);
        }
        long start = System.currentTimeMillis();
        log.info("开始插入数据库：" + start);
        if (devList.size() > 0) {
            List<List<BaseDeviceTemporary>> lists = ListUtil.averageBySize(devList, 999);
            lists.forEach(list1 -> {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        deviceTemporaryMapper.insertList(list1);
                    }
                }).start();
            });
        }
        long end = System.currentTimeMillis();
        log.info("插入数据库成功：" + (end - start));
        return map;
    }


    /**
     * 添加点位信息直接导入
     *
     * @param baseDeviceTemporaryList
     * @return
     */
    @Transactional
    @Override
    public Map<String, Object> addDevPointsT(HttpServletRequest request, BaseDeviceTemporaryList baseDeviceTemporaryList, double percent) {
        List<BaseDeviceTemporary> list = baseDeviceTemporaryList.getList();
        Map<String, Object> map = new HashMap<>();
        List<BaseDeviceTemporary> deviceTemporaries = new ArrayList<>();
        List<BaseDeviceTemporary> devList = new ArrayList<>();
        BaseDeviceTemporary deviceTemporary = list.get(0);
        String applyId = "";
        map.put("list", deviceTemporaries);
        if (StringUtils.isNotEmpty(deviceTemporary.getApplyId())) {
            if (addSpeciallyDotApply(list)) {
                map.put("list", deviceTemporaries);
                map.put("applyId", applyId);
                return map;
            }
            return map;
        }
        applyId = UUIDUtils.getUUID();
        map.put("applyId", applyId);
//        int flag = deviceTemporaryMapper.existsNotApply(deviceTemporary);
//        if (flag > 0) {
//            return map;
//        }

        //为0时 新增
        if ("0".equals(deviceTemporary.getApplyType())) {
            for (BaseDeviceTemporary tem : list) {
                tem.setApplyId(applyId);
//                int num = deviceTemporaryMapper.existsPoint(tem);
//                if (num > 0) {
//                    deviceTemporaries.add(tem);
//                } else {
                devList.add(tem);
//                }
            }
        } else {
            for (BaseDeviceTemporary tem : list) {
                tem.setApplyId(applyId);
//                int num = deviceTemporaryMapper.existsNotApprPoint(tem);
//                if (num > 0) {
//                    deviceTemporaries.add(tem);
//                } else {
                devList.add(tem);
//                }
            }
        }

        // 保存点位申请记录表
        BaseDeviceApplication baseDeviceApplication = new BaseDeviceApplication();
        baseDeviceApplication.setId(applyId);
        baseDeviceApplication.setBuildingId(deviceTemporary.getBuildingId());
        baseDeviceApplication.setModulationProportion(String.format("%.2f", percent));
//            baseDeviceApplication.setBuildingName(deviceTemporary.get)
        baseDeviceApplication.setModulationType(baseDeviceTemporaryList.getApplyType());
        baseDeviceApplication.setModulationQuantity(devList.size());
        baseDeviceApplication.setApplicantTime(DateUtil.getCurrentDatetime());
        baseDeviceApplication.setApprovCondition("0");
        baseDeviceApplication.setModulationType("");
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        if (dataDto != null) {
            baseDeviceApplication.setCreateBy(userId);
            baseDeviceApplication.setCreateDepId(dataDto.getUnitId());
            baseDeviceApplication.setProposer(dataDto.getFullName());
        }
        baseDeviceApplication.setCreateTime(new Date());
        boolean flag1 = iBaseDeviceApplicationService.saveBaseDeviceApplication(baseDeviceApplication);

        if (devList.size() > 0) {
            boolean fl = super.saveBatch(devList);
        }
        return map;
    }


    /**
     * 根据申请ID获取信息
     *
     * @param baseDeviceTemporary
     * @return
     */
    @Override
    public List<BaseDeviceTemporary> selectDevPointsByApplyId(BaseDeviceTemporary baseDeviceTemporary) {
        return deviceTemporaryMapper.queryBaseDeviceTemporaryByApplyId(baseDeviceTemporary);
    }

    /**
     * 新增点位
     *
     * @param deviceTemporary
     * @param approveTime
     * @return
     */
    @Override
    @Transactional
    public boolean addApplyPoint(BaseDeviceTemporary deviceTemporary, Date approveTime) {
        List<BaseDeviceTemporary> deviceTemporaries = null;
        try {
            String applyId = deviceTemporary.getApplyId();
            deviceTemporaries = getPointFileByApplyId(applyId);
        } catch (IOException e) {
            throw new RuntimeException("文件未找到！");
        }
//        List<BaseDeviceTemporary> deviceTemporaries = deviceTemporaryMapper.queryBaseDeviceTemporaryByApplyId(deviceTemporary);
        if (CollectionUtils.isEmpty(deviceTemporaries)) {
            log.info("点位调改-新增点位申请通过，进入相应的流程,暂无数据");
            return false;
        }

        log.info("点位调改-新增点位申请通过，进入相应的修改流程,新增的建筑物id为:【{}】,申请的id为:【{}】", deviceTemporary.getBuildingId(), deviceTemporaries.get(0).getApplyId());
        log.info("点位调改-新增点位导入的点位数量：" + deviceTemporaries.size());
        QueryWrapper<BaseBuildingFloor> queryWrapper = new QueryWrapper<>();
        String buildId = deviceTemporaries.get(0).getBuildingId();
        queryWrapper.eq("building_id", buildId);
        log.info("点位调改-新增点位导入的建筑id：" + buildId);
        List<BaseBuildingFloor> buildingFloors = floorService.list(queryWrapper);
        Map<String, BaseBuildingFloor> floorMap = buildingFloors.stream().collect(Collectors.toMap(BaseBuildingFloor::getFloorName, Function.identity(), (v1, v2) -> v1));
        List<BaseLocalHostInfo> baseLocalHostInfos = baseLocalHostInfoMapper.selectByBuildId(buildId);
        Map<String, BaseLocalHostInfo> devNoMap = baseLocalHostInfos.stream().collect(Collectors.toMap(baseLocalHostInfo -> baseLocalHostInfo.getHid() + baseLocalHostInfo.getDevNo(), Function.identity(), (v1, v2) -> v1));
        List<BaseHostInfo> hostInfos = baseHostInfoMapper.selectByMap(new HashMap<>());
        Map<String, BaseHostInfo> transmissionCodeMap = hostInfos.stream().collect(Collectors.toMap(BaseHostInfo::getTransmissionCode, Function.identity(), (v1, v2) -> v1));
        List<BaseDevicePoint> devicePoints = new ArrayList<>();
        try {
            Map<String, String> hostInfoMap = deviceTemporaries.stream().collect(Collectors.toMap(BaseDeviceTemporary::getTransmissionNumber, BaseDeviceTemporary::getMethodStr, (key1, key2) -> key2));
            log.info("点位调改-新增点位申请通过,修改主机方法:" + hostInfoMap);
            hostInfoMap.forEach((TransmissionNumber, MethodStr) -> {
                if (MethodStr != null && TransmissionNumber != null) {
                    BaseHostInfo baseHostInfo = new BaseHostInfo();
                    baseHostInfo.setTransmissionCode(TransmissionNumber);
                    baseHostInfo.setMethodStr(MethodStr);
                    baseHostInfo.setMethod(Integer.parseInt(MethodEnum.getEnumByName(MethodStr).getNum()));
                    baseHostInfoMapper.update(baseHostInfo, new LambdaQueryWrapper<BaseHostInfo>().eq(BaseHostInfo::getTransmissionCode, TransmissionNumber));
                }
            });
        } catch (Exception e) {
            log.info("点位调改-新增点位申请通过,修改主机的方法异常");
            e.printStackTrace();
        }

        for (BaseDeviceTemporary tem : deviceTemporaries) {
            BaseDevicePoint point = new BaseDevicePoint();
            point.setId(UUIDUtils.getUUID());

            point.setApplyUser(tem.getApplyUser());
            //建筑id
            point.setBuildingId(tem.getBuildingId());
            //建筑名称
            point.setBuildName(tem.getBuildName());
            //主机号
            point.setHostNum(tem.getHostNum());
            //回路号
            point.setLoopCode(tem.getLoopCode());
            //点位号
            point.setPointCode(tem.getPointCode());
            //向前端展示点位号
            point.setPointNumber(tem.getHostNum() + "-" + tem.getLoopCode() + "-" + tem.getPointCode());
            //设备号
            point.setHostId(tem.getHostId());
            //did
            point.setDid(tem.getDid());
            //pid
            point.setPid(point.getHostId() + point.getDid());

            point.setDevTypeCat(tem.getDevTypeCat());
            // 正常点位
            point.setPointType(tem.getPointType());
            //本地实际主机号
            point.setLocalHostNum(tem.getLocalHostNum());
            //新增本地实际主机号
            if (devNoMap.get(tem.getHostId() + tem.getLocalHostNum()) == null && !"0".equals(tem.getLocalHostNum())) {

                try {
                    log.info("点位调改-新增点位插入本地实际主机号：" + tem.getLocalHostNum());
                    BaseLocalHostInfo baseLocalHostInfo = new BaseLocalHostInfo();
                    baseLocalHostInfo.setBuildingId(buildId);
                    baseLocalHostInfo.setBuildingName(tem.getBuildName());
                    baseLocalHostInfo.setDevNo(tem.getLocalHostNum());

                    if (transmissionCodeMap.get(tem.getTransmissionNumber()) != null) {
                        BaseHostInfo baseHostInfo = transmissionCodeMap.get(tem.getTransmissionNumber());
                        baseLocalHostInfo.setHid(baseHostInfo.getHid());
                        baseLocalHostInfo.setHostModelName(baseHostInfo.getHostModelName());
                        baseLocalHostInfo.setTerminalBrandName(baseHostInfo.getTerminalBrandName());
                        baseLocalHostInfo.setTransmissionCode(tem.getTransmissionNumber());
                        baseLocalHostInfoMapper.insert(baseLocalHostInfo);
                        devNoMap.put(tem.getHostId() + tem.getLocalHostNum(), baseLocalHostInfo);
                    }
                } catch (Exception e) {
                    log.error("点位调改-新增点位新增本地实际主机失败！失败原因是：【】", e.getMessage());
                    e.printStackTrace();
                }
            }
            // 本地实际回路号
            point.setLocalLoopCode(tem.getLocalLoopCode());
            //用户传输装置编号
            point.setTransmissionNumber(tem.getTransmissionNumber());
            //点位号 前端展示 点位编号
            point.setPointNumber(tem.getPointNumber());
            // 设置消防系统的编码和名称 设备类型和类型名称
            point.setFasCode(tem.getFasCode());
            point.setFasName(tem.getFasName());
            point.setDevTypeCode(tem.getDevTypeCode());
            point.setDevTypeName(tem.getDevTypeName());
            //点位描述
            point.setPointDesc(tem.getPointDesc());
            //防火分区
            point.setZoneName(tem.getZoneName());
            //楼层
            point.setFloorName(tem.getFloorName());
            point.setSuperType("0");
            point.setZoneId(tem.getZoneId());
            point.setCreateTime(new Date());
            BaseBuildingFloor baseBuildingFloor = floorMap.get(point.getFloorName());
            if (ObjectUtils.isNotNull(tem.getPointY()) && ObjectUtils.isNotNull(tem.getPointX())) {
                point.setHasTracing("1");
                point.setTall(tem.getPointY().floatValue());
                point.setWide(tem.getPointX().floatValue());
            } else {
                point.setHasTracing("0");
            }
            if (null != baseBuildingFloor) {
                point.setFloorId(baseBuildingFloor.getId());
                point.setFloor(baseBuildingFloor.getFloor());
            }
            point.setUpdateTime(new Date());
            devicePoints.add(point);
        }
        log.info("点位调改-新增点位新增点位开始插入数据库：" + devicePoints.size());
        boolean flag = devicePointService.mergeIntoDb(devicePoints, 900);
        log.info("点位调改-新增点位插入成功！：" + flag);
        smokeAlarmSettingService.batchAddSmokeAlarmSetting(devicePoints);
        //插入成功时插入redis
        if (flag) {
            setPointToRedis(devicePoints);
        }
        try {
            if (deviceTemporaries.size() > 0) {
                //修改点位调改记录状态
                BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
                log.info("点位调改-新增点位 开始插入 数据中心  点位调整记录   新增记录");
                BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(baseDeviceTemporary.getApplyId());
                if (null == byId) {
                    log.info("点位调改-新增点位 无相应数据审批记录");
                    return flag;
                }
                BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
                if (null == baseBuilding) {
                    log.info("点位调改-新增点位 无相应建筑");
                    return flag;
                }
                DecimalFormat decimalFormat = new DecimalFormat("0.00");
                Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
                // 申请人
                String fullName = byId.getProposer();
                iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                        baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                        "0", "增加", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", fullName,
                        byId.getApplicantTime(), DateUtil.formatDate(approveTime, "yyyy-MM-dd HH:mm:ss"), "审批通过"));
                //相应维保计划和预约等调整
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildId, devicePoints, true));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("点位调改-新增点位新增报错：【{}】", e.getMessage());
        }
        return flag;
    }

    @Override
    public void adjustMaintenancePlan(String buildingId, List<BaseDevicePoint> devicePoints, Boolean flag, String effectiveTime) {
        //涉及到设备类型的点位，需要重新分配
        try {
            log.info("开始进入维保调整，建筑id为：{},维保月份为{}", buildingId, effectiveTime);
            deviceTypeRedistribution(buildingId, devicePoints, flag, effectiveTime);
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            // 将日期设置为本月的第一天
            LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);
            // 结合时间为0时0分0秒，构建LocalDateTime对象
            LocalDateTime firstDayFirstSecond = LocalDateTime.of(firstDayOfMonth, LocalTime.MIDNIGHT);
            // 定义日期时间格式化器，例如按照"yyyy-MM-dd HH:mm:ss"格式进行格式化
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = firstDayFirstSecond.format(formatter);
            List<AppointmentApply> appointmentApplies = appointmentApplyMapper.selectList(new LambdaQueryWrapper<AppointmentApply>()
                    .eq(AppointmentApply::getBuildingId, buildingId)
                    .eq(AppointmentApply::getApplicationStatus, "2")
                    .ne(AppointmentApply::getMaintenanceStart, "4")
                    .gt(AppointmentApply::getStartTime, formattedDateTime));
            if (appointmentApplies.size() > 0) {
                Map<String, List<String>> pointIdCache = new HashMap<>();
                for (AppointmentApply appointmentApply : appointmentApplies) {
                    Map<String, Boolean> baseDevicePointMap = new HashMap<>();
                    //  key预约范围id    map key 点位id  val: 是否维保
                    Map<String, Map<String, Boolean>> scopeDevicePointMap = new HashMap<>();
                    // 根据预约申请id获取预约信息
                    if ("2".equals(appointmentApply.getPlanType()) || "3".equals(appointmentApply.getPlanType())) {
                        String fasCodes = appointmentApply.getFasCodes();
                        String[] fasCodesArray = fasCodes.split(",");
                        List<String> fasCodeList = Arrays.asList(fasCodesArray);
                        if (fasCodeList.size() > 0) {
                            Set<String> deviceCodes = new HashSet<>();
                            List<MaintenanceConfig> maintenanceConfigs = maintenanceConfigMapper.selectList(new LambdaQueryWrapper<MaintenanceConfig>().in(MaintenanceConfig::getFireproofSysId, fasCodeList));
                            for (MaintenanceConfig maintenanceConfig : maintenanceConfigs) {
                                deviceCodes.add(maintenanceConfig.getDeviceCode());
                            }
                            Map<String, Object> param = new HashMap<>(8);
                            param.put("buildingId", appointmentApply.getBuildingId());
                            param.put("deviceCodes", deviceCodes);
                            String cacheKey = appointmentApply.getBuildingId() + "&" + fasCodes;
//                        List<BaseDevicePoint> list1 = baseDevicePointMapper.selectByParam(param);
                            List<String> list1 = new ArrayList<>();
                            if (pointIdCache.containsKey(cacheKey)) {
                                list1 = pointIdCache.get(cacheKey);
                            } else {
                                list1 = baseDevicePointMapper.selectIdByParam(param);
                                pointIdCache.put(cacheKey, list1);
                            }
                            for (String pointId : list1) {
                                baseDevicePointMap.put(pointId, false);
                            }
                        }
                    } else {
                        // 根据预约申请id查询维保范围
                        log.info("维保点位调改：根据预约申请id查询维保范围");
                        QueryWrapper<AppointmentApScope> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("appointment_apply_id", appointmentApply.getId());
                        List<AppointmentApScope> list = appointmentApScopeService.list(queryWrapper);
                        for (int i = 0; i < list.size(); i++) {
                            AppointmentApScope appointmentApScope = list.get(i);
                            // 根据维保范围id查询
                            log.info("维保点位调改：根据维保范围id查询");
                            MaintenanceScopeTest maintenanceScopeTest = maintenanceScopeTestMapper.selectById(appointmentApScope.getMaintenanceScopeTestId());
                            if (maintenanceScopeTest != null) {
                                // 根据维保项id查询生成规则（楼层、回路、分区、批次）
                                String generationRule = maintenanceScopeTestMapper.getMaintenanceConfigByScopeTestId(maintenanceScopeTest.getId());
                                String batchCode = null;
                                if (StringUtils.isNotBlank(generationRule)) {
                                    switch (generationRule) {
                                        case "1":
                                            batchCode = "floor_id";
                                            break;
                                        case "2":
                                            batchCode = "loop_code";
                                            break;
                                        case "3":
                                            batchCode = "zone_name";
                                            break;
                                    }
                                    List<String> scopeTestIdList = new ArrayList<>();
                                    scopeTestIdList.add(maintenanceScopeTest.getId());
                                    // 根据维保范围查询点位
//                                List<DevicePointInfoVo> devicePointInfoVoList = maintenanceScopeTestMapper.getWebDevicePointInfoByScopeTestId(scopeTestIdList, batchCode);
                                    List<String> devicePointInfoVoList = maintenanceScopeTestMapper.getWebDevicePointIdByScopeTestId(scopeTestIdList, batchCode);

                                    Map<String, Boolean> map = new HashMap<>();
                                    devicePointInfoVoList.forEach(pointId -> {
                                        map.put(pointId, false);
                                        // 下面map是之前使用的 后面可以删除
                                        baseDevicePointMap.put(pointId, false);
                                    });
                                    scopeDevicePointMap.put(maintenanceScopeTest.getId(), map);
                                }
                            }

                        }
                    }
                    //更新redis数据
                    Object o = redisUtils.get("weibao-" + buildingId);
                    if (o != null) {
                        WeibaoDto weibaoDto = FastJSONUtils.toBean((String) o, WeibaoDto.class);
                        Map<String, Boolean> oldBaseDevicePointMap = weibaoDto.getBaseDevicePointMap();
                        Map<String, Map<String, Boolean>> oldScopeDevicePointMap = weibaoDto.getScopeDevicePointMap();
                        for (Map.Entry<String, Boolean> entry : baseDevicePointMap.entrySet()) {
                            if (oldBaseDevicePointMap.get(entry.getKey()) != null) {
                                entry.setValue(oldBaseDevicePointMap.get(entry.getKey()));
                            }
                        }
                        for (Map.Entry<String, Map<String, Boolean>> entry : scopeDevicePointMap.entrySet()) {
                            for (Map.Entry<String, Boolean> entry1 : entry.getValue().entrySet()) {
                                if (oldScopeDevicePointMap.get(entry.getKey()) != null && oldScopeDevicePointMap.get(entry.getKey()).get(entry1.getKey()) != null) {
                                    entry1.setValue(oldScopeDevicePointMap.get(entry.getKey()).get(entry1.getKey()));
                                }
                            }
                        }
                        weibaoDto.setBaseDevicePointMap(baseDevicePointMap);
                        weibaoDto.setScopeDevicePointMap(scopeDevicePointMap);
//                    redisUtils.set("weibao-" + buildingId, JSON.toJSONString(weibaoDto));
                        redisUtils.updateWbData("weibao-" + buildingId, weibaoDto);
                    }
                }
            }

            LambdaQueryWrapper<MaintenancePlan> queryWrapper = new LambdaQueryWrapper<MaintenancePlan>()
                    .eq(MaintenancePlan::getBuildingId, buildingId)
                    .eq(MaintenancePlan::getScheduling, "0");
            if (StrUtil.isNotBlank(effectiveTime)) {
                queryWrapper.eq(MaintenancePlan::getEffectiveTime, effectiveTime);
            } else {
                queryWrapper.ge(MaintenancePlan::getEffectiveTime, DateUtil.formatDate(new Date(), "yyyy-MM"));
            }
            List<MaintenancePlan> maintenancePlanList = maintenancePlanMapper.selectList(queryWrapper);
            long countSum;
            long countMainSum;
            long countMain;
            List<MaintenancePlan> maintenancePlanList1 = new ArrayList<>();
            log.info("-----------建筑{}开始查询维保计划，调整维保范围数量-------------", buildingId);
            for (MaintenancePlan maintenancePlan : maintenancePlanList) {
                countMainSum = 0L;
                countSum = 0L;
                List<MaintenanceScopeTest> maintenanceScopeTests = new ArrayList<>();
                List<MaintenanceScopeTest> maintenanceScopeTestList = maintenanceScopeTestMapper.getScopeTestByPlanId(maintenancePlan.getId(), null);
                for (MaintenanceScopeTest maintenanceScopeTest : maintenanceScopeTestList) {
                    List<DevicePointInfoVo> webScopeTestPoint = scopeTestService.getWebScopeTestPointByEffTime(maintenanceScopeTest.getId(), maintenancePlan.getBuildingId(), maintenancePlan.getEffectiveTime(), null);
                    log.info("维保范围查询点位list返回为：{}，维保范围id为{}", JSONObject.toJSONString(webScopeTestPoint), maintenanceScopeTest.getId());
                    int pointNum = StrUtil.isBlank(maintenanceScopeTest.getPointNum()) ? 0 : Integer.parseInt(maintenanceScopeTest.getPointNum());
                    int maintenancePoint =  StrUtil.isBlank(maintenanceScopeTest.getMaintenancePoint()) ? 0 : Integer.parseInt(maintenanceScopeTest.getMaintenancePoint());

                    countMain = webScopeTestPoint.stream().filter(devicePointInfoVo -> devicePointInfoVo.getMaintenanceStatus() != null && devicePointInfoVo.getMaintenanceStatus() == 1).count();
                    countSum += webScopeTestPoint.size();
                    countMainSum += countMain;
                    if (webScopeTestPoint.size() == pointNum && maintenancePoint == countMain) {
                        log.info("维保范围计算查出的点位数量一致,pointNum:{},maintenancePoint:{}，不更新维保范围数量，维保范围id为{}", webScopeTestPoint.size(), countMain, maintenanceScopeTest.getId());
                        continue;
                    }
                    maintenanceScopeTest.setPointNum(webScopeTestPoint.size() + "");
                    maintenanceScopeTest.setMaintenancePoint(countMain + "");
                    maintenanceScopeTests.add(maintenanceScopeTest);
                }
                List<String> idList = maintenanceScopeTestList.stream().map(MaintenanceScopeTest::getId).collect(Collectors.toList());
                log.info("建筑{}开始更新维保范围清单，维保范围id为{}", buildingId, JSONObject.toJSONString(idList));
                scopeTestService.updateBatchById(maintenanceScopeTests);

                int pointNum = StrUtil.isBlank(maintenancePlan.getPointNum()) ? 0 : Integer.parseInt(maintenancePlan.getPointNum());
                int maintenancePoint =  StrUtil.isBlank(maintenancePlan.getMaintenancePoint()) ? 0 : Integer.parseInt(maintenancePlan.getMaintenancePoint());
                if (pointNum == countSum && maintenancePoint == countMainSum) {
                    log.info("维保计划计算查出的点位数量一致,pointNum:{},maintenancePoint:{}，不更新维保范围数量，维保计划id为{}", countSum, countMainSum, maintenancePlan.getId());
                    continue;
                }
                maintenancePlan.setPointNum(countSum + "");
                maintenancePlan.setMaintenancePoint(countMainSum + "");
                maintenancePlan.setPercentageMon(countSum != 0L ? String.format("%.3f", (double) countMainSum / (double) countSum) : "0");

                maintenancePlanList1.add(maintenancePlan);
            }
            maintenancePlanService.updateBatchById(maintenancePlanList1);
            log.info("建筑{}维保点位调改结束：更新的维保计划清单->{}", buildingId, FastJSONUtils.toJSONString(maintenancePlanList1));
        } catch (Exception e) {
            log.info("调整维保范围异常,建筑id为{}", buildingId);
            log.error("调整维保范围异常：", e);
        }
    }

    @Override
    public int deletePoint(String buildingId, String flag) {
        List<BaseDevicePoint> baseDevicePoints = this.baseMapper.getRepeatPoint(buildingId);
        //重复点位
        if (baseDevicePoints == null || baseDevicePoints.size() == 0) {
            return 1;
        }
        if (flag != null && "1".equals(flag)) {
            log.info("删除重复点位：" + baseDevicePoints.size());
            List<String> ids = baseDevicePoints.stream().map(BaseDevicePoint::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)) {
                // 因为有两次重复删除的申请审批 所以先确定点位存在再进行删除操作
                ListUtil.averageBySize(ids, 1000).forEach(avePids -> {
                    // 多线程去进行删除的操作
                    threadPoolExecutor.submit(() -> {
                        log.info("删除重复点位数量为：" + avePids.size());
                        devicePointService.remove(new QueryWrapper<BaseDevicePoint>().in("id", avePids));
                    });
                });
            }
        }
        try {
            List<String> buildingIdList = baseDevicePoints.stream().map(BaseDevicePoint::getBuildingId).collect(Collectors.toList());

            if (buildingIdList != null && buildingIdList.size() > 0) {
                buildingIdList = new ArrayList<>();
                buildingIdList.add(buildingId);
            }
            buildingIdList.forEach(buildingId1 -> {
                log.info("删除预约之后重置维保相关：" + buildingId1);
//                Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, buildingId1));
//                baseLocalHostInfoMapper.initLocalHostInfo(buildingId1);
//                statLocalHostExpectionMapper.delete(new LambdaQueryWrapper<StatLocalHostExpection>().eq(StatLocalHostExpection::getBuildingId, buildingId1));
                //相应维保计划和预约等调整
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId1, null, false));
            });
        } catch (Exception e) {
            log.error("1失败原因是：【】", e.getMessage());
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public void restPlan(String buildingId) {
        if (buildingId == null) {
            List<BaseBuilding> baseBuildingList = iBaseBuildingService.list();
            baseBuildingList.forEach(baseBuilding -> {
                String finishKey = "weibao-finish-" + baseBuilding.getId() + "=" + DateUtil.formateDateTimeYYYY_MM(DateUtil.formatDatetime(System.currentTimeMillis()));
                redisUtils.remove(finishKey);
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(baseBuilding.getId(), null, false));

            });
        } else {
            String finishKey = "weibao-finish-" + buildingId + "=" + DateUtil.formateDateTimeYYYY_MM(DateUtil.formatDatetime(System.currentTimeMillis()));
            redisUtils.remove(finishKey);
            Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId, null, false));

        }
    }

    @Override
    public void restPlanByPoint(String buildingId, List<BaseDevicePoint> baseBuildingList) {
        String finishKey = "weibao-finish-" + buildingId + "=" + DateUtil.formateDateTimeYYYY_MM(DateUtil.formatDatetime(System.currentTimeMillis()));
        redisUtils.remove(finishKey);
        Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId, baseBuildingList, true));
    }

    @Override
    public String transferStringQingniao(String localHost, String localLoop, String pointCode) {
        return flip(String.format("%08X", Long.parseLong(localHost + String.format("%03d", Integer.parseInt(localLoop)) + String.format("%03d", Integer.parseInt(pointCode)))));
    }

    @Override
    public String transferHaiWanMethodSeven(String parseInt) {
        String hexByDecimal = HaiWanSevenUtil.getHexByDecimal("0" + parseInt);
        String hex = HaiWanSevenUtil.getHex(hexByDecimal);
        return hex;
    }


    private String flip(String did) {
        String result = "";
        for (int i = did.length(); i > 0; i = i - 2) {
            result = result + did.substring(i - 2, i);
        }
        return result;
    }

    class AdjustMaintenanceThread implements Callable<Object> {
        private String buildingId;

        private List<BaseDevicePoint> devicePoints;

        private Boolean flag;

        public AdjustMaintenanceThread(String buildingId, List<BaseDevicePoint> devicePoints, Boolean flag) {
            this.buildingId = buildingId;
            this.devicePoints = devicePoints;
            this.flag = flag;
        }

        @Override
        public Object call() {
            try {
                adjustMaintenancePlan(buildingId, devicePoints, flag, "");
            } catch (Exception e) {
                e.printStackTrace();
                return "fail:" + e.getMessage();
            }
            return "success";
        }

        public String getBuildingId() {
            return buildingId;
        }

        public void setBuildingId(String buildingId) {
            this.buildingId = buildingId;
        }

        public List<BaseDevicePoint> getDevicePoints() {
            return devicePoints;
        }

        public void setDevicePoints(List<BaseDevicePoint> devicePoints) {
            this.devicePoints = devicePoints;
        }
    }

    /**
     * 根据主机号、点位号、回路号、建筑id删除重复点位
     *
     * @return
     */
    @Transactional
    public boolean deletePoint(List<BaseDevicePoint> devicePoints) {
        LambdaQueryWrapper<BaseDevicePoint> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (BaseDevicePoint bdp : devicePoints) {
            lambdaQueryWrapper.or(wrapper -> wrapper.eq(BaseDevicePoint::getBuildingId, bdp.getBuildingId()).eq(BaseDevicePoint::getPointCode, bdp.getPointCode())
                    .eq(BaseDevicePoint::getHostNum, bdp.getHostNum()).eq(BaseDevicePoint::getLoopCode, bdp.getLoopCode()));
        }
        if (devicePoints.size() > 0) {
            return devicePointService.remove(lambdaQueryWrapper);
        }
        return false;
    }

    /**
     * 编辑点位
     *
     * @param deviceTemporaries 修改的点位列表
     * @param approveTime
     */
    @Override
    public void editPointInfo(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime) {

        //增加系统类型判断
        QueryWrapper<WdFireSys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sys_category", "1");
        List<WdFireSys> wdFireSysList = wdFireSysService.list(queryWrapper);
        // 消防系统Map
        Map<String, WdFireSys> sysMap = wdFireSysList.stream().collect(Collectors.toMap(WdFireSys::getFireSysCode, Function.identity(), (v1, v2) -> v1));
        List<WdDevConfig> devConfigs = wdDevConfigService.list();
        // 设备类型Map
        Map<String, WdDevConfig> devMap = devConfigs.stream().collect(Collectors.toMap(WdDevConfig::getDevName, Function.identity(), (v1, v2) -> v1));
        // 修改的部分修改的
        List<BaseDeviceTemporary> finalChangeList = deviceTemporaries.stream().filter(tem -> StringUtils.isNotBlank(tem.getOldContent())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalChangeList)) {
            return;
        }
        List<String> pids = Lists.newArrayList();
        Map<String, BaseDeviceTemporary> pidMaps = Maps.newHashMap();
        finalChangeList.forEach(tem -> {
            String pid = tem.getHostId() + tem.getDid();
            pids.add(pid);
            pidMaps.put(pid, tem);
        });
        if (CollectionUtils.isEmpty(finalChangeList)) {
            log.error("点位调改-修改点位,本次申请暂无相关修改，请查看相关修改数据,修改的建筑物id为:【{}】,申请的id为:【{}】", deviceTemporaries.get(0).getBuildingId(), deviceTemporaries.get(0).getApplyId());
            return;
        }
        String buildingId = finalChangeList.get(0).getBuildingId();
        log.info("点位调改-修改点位申请通过，进入相应的修改流程,修改的建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
        List<BaseDevicePoint> list = Lists.newArrayList();
        List<List<String>> lists = ListUtil.averageBySize(pids, 800);
        lists.forEach(pid -> {
            list.addAll(devicePointService.list(new QueryWrapper<BaseDevicePoint>().eq("building_id", buildingId).in("pid", pid)));
        });
        log.info("点位调改-修改点位申请通过，进入相应的修改流程,修改的建筑物id为:【{}】,申请的id为:【{}】,修改的数据量为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId(), list.size());
        list.forEach(baseDevicePoint -> {
            String pid = baseDevicePoint.getPid();
            BaseDeviceTemporary tem = pidMaps.get(pid);
            if (null != tem) {
                baseDevicePoint.setZoneId(tem.getZoneId());
                baseDevicePoint.setZoneName(tem.getZoneName());
                baseDevicePoint.setPointDesc(tem.getPointDesc());
                if (StringUtils.isNotBlank(tem.getFloor())) {
                    baseDevicePoint.setFloor(Integer.valueOf(tem.getFloor()));
                }
                baseDevicePoint.setFloorId(tem.getFloorId());
                baseDevicePoint.setFloorName(tem.getFloorName());
                baseDevicePoint.setDevTypeCat(tem.getDevTypeCat());
                baseDevicePoint.setDevTypeCode(tem.getDevTypeCode());
                baseDevicePoint.setDevTypeName(tem.getDevTypeName());
                WdDevConfig wdDevConfig = devMap.get(tem.getDevTypeName());
                if (wdDevConfig != null) {
                    if ("1".equals(wdDevConfig.getDevCategory())) {
                        // 1 消防主机
                        baseDevicePoint.setDevTypeCat("0");
                    } else if ("2".equals(wdDevConfig.getDevCategory())) {
                        // 2 非消防主机
                        baseDevicePoint.setDevTypeCat("1");
                    }
                    WdFireSys wdFireSys = sysMap.get(wdDevConfig.getFireSysCode());
                    if (wdFireSys != null) {
                        baseDevicePoint.setFasCode(wdFireSys.getFireSysCode());
                        baseDevicePoint.setFasName(wdFireSys.getFireSysName());
                    }
                }
            }
        });
        devicePointService.updateBatchById(list);
        log.info("更新redis数据");
        smokeAlarmSettingService.batchAddSmokeAlarmSetting(list);
        setPointToRedis(list);
        try {
            log.info("点位调改-修改点位申请通过，删除本地实际主机，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
            if (buildingId != null) {
                Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, buildingId));
                log.info("点位调改-修改点位申请通过，删除本地实际主机成功,建筑物id为:【{}】,申请的id为:【{}】,删除数量为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId(), num);
                log.info("点位调改-修改点位申请通过，初始化本地主机数据，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
//                baseLocalHostInfoMapper.initLocalHostInfo(buildingId);
                baseLocalHostInfoService.initLocalHostInfoByBuildingId(buildingId);
                statLocalHostExpectionMapper.delete(new LambdaQueryWrapper<StatLocalHostExpection>().eq(StatLocalHostExpection::getBuildingId, buildingId));
                log.info("点位调改-修改点位，初始化本地主机数据成功，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
                //相应维保计划和预约等调整
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId, list, true));
            }
        } catch (Exception e) {
            log.error("点位调改-修改点位申请通过，执行本地实际主机初始化失败！失败原因是：【】", e.getMessage());
            e.printStackTrace();
        }
        BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
        BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(baseDeviceTemporary.getApplyId());
        log.info("开始插入 数据中心  点位调整记录   修改记录，申请的id为【{}】", baseDeviceTemporary.getApplyId());
        BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
        String fullName = byId.getProposer();
        iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                "1", "修改", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", fullName,
                byId.getApplicantTime(), DateUtil.formatDate(approveTime, "yyyy-MM-dd HH:mm:ss"), "审批通过"));
        log.info("点位调改-修改点位申请通过，进入相应的修改流程,修改的建筑物id为:【{}】,申请的id为:【{}】,修改的数据量为:【{}】,修改成功", buildingId, deviceTemporaries.get(0).getApplyId(), list.size());
    }

    @Override
    public void addSpecialPoint(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime) {
        List<String> pids = Lists.newArrayList();
        String buildingId = deviceTemporaries.get(0).getBuildingId();
        String applyId = deviceTemporaries.get(0).getApplyId();
        deviceTemporaries.forEach(tem -> {
            String pid = tem.getHostId() + tem.getDid();
            pids.add(pid);
        });
        log.info("点位调改-特批点位申请通过，进入相应的修改流程,修改的建筑物id为:【{}】,申请的id为:【{}】", buildingId, applyId);
        // 找到要修改的特批点位 修改特批点位状态
        List<BaseDevicePoint> list = Lists.newArrayList();
        List<List<String>> lists = ListUtil.averageBySize(pids, 800);
        lists.forEach(pid -> {
            list.addAll(devicePointService.list(new QueryWrapper<BaseDevicePoint>().eq("building_id", buildingId).in("pid", pid)));
        });
//        List<BaseDevicePoint> list = devicePointService.list(new QueryWrapper<BaseDevicePoint>().eq("building_id", buildingId).in("pid", pids));
        log.info("点位调改-特批点位申请通过，进入相应的修改流程,修改的数量为:【{}】", list.size());
        list.forEach(point -> point.setPointType("1"));
        devicePointService.updateBatchById(list);
        log.info("点位调改-特批点位申请通过，进入相应的修改流程,修改的数量为:【{}】,修改为特批点位成功,放入相应调整记录", list.size());
        BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(applyId);
        BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
        iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                byId.getModulationType(), "特批", String.valueOf(byId.getModulationQuantity()), byId.getModulationProportion() + "%", byId.getProposer(),
                byId.getApplicantTime(), DateUtil.formatDate(approveTime, "yyyy-MM-dd HH:mm:ss"), "审批通过"));
        setPointToRedis(list);
        //特批点位申请通过 修改故障和屏蔽 数据为已修复
        FaultInfo faultInfo = new FaultInfo();
        faultInfo.setFaultStatus("1");
        faultInfoService.update(faultInfo,
                new LambdaQueryWrapper<FaultInfo>()
                        .in(FaultInfo::getPointCode, list.stream().map(BaseDevicePoint::getPointNumber).collect(Collectors.toList()))
                        .in(FaultInfo::getBuildingId, baseBuilding.getId())
                        .in(FaultInfo::getFaultStatus, "2", "0"));

        BasePointShieldLog basePointShieldLog = new BasePointShieldLog();
        basePointShieldLog.setStatus("0");
        basePointShieldLogService.update(basePointShieldLog,
                new LambdaQueryWrapper<BasePointShieldLog>()
                        .in(BasePointShieldLog::getDevNum, list.stream().map(BaseDevicePoint::getPointNumber).collect(Collectors.toList()))
                        .in(BasePointShieldLog::getBuildingId, baseBuilding.getId())
                        .eq(BasePointShieldLog::getStatus, "1"));
        try {
            log.info("点位调改-特批点位申请通过，删除本地实际主机，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
            if (buildingId != null) {
                Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, buildingId));
                log.info("点位调改-特批点位申请通过，删除本地实际主机成功,建筑物id为:【{}】,申请的id为:【{}】,删除数量为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId(), num);
                log.info("点位调改-特批点位申请通过，初始化本地主机数据，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
//                baseLocalHostInfoMapper.initLocalHostInfo(buildingId);
                baseLocalHostInfoService.initLocalHostInfoByBuildingId(buildingId);
                statLocalHostExpectionMapper.delete(new LambdaQueryWrapper<StatLocalHostExpection>().eq(StatLocalHostExpection::getBuildingId, buildingId));
                log.info("点位调改-特批点位申请通过，初始化本地主机数据成功，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
                //相应维保计划和预约等调整
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId, list, false));
            }
        } catch (Exception e) {
            log.error("点位调改-特批点位申请通过,执行本地实际主机初始化失败！失败原因是：【】", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 编辑点位
     *
     * @param deviceTemporary
     * @return
     */
    @Transactional
    @Override
    public boolean editApplyPoint(BaseDeviceTemporary deviceTemporary) {
        //增加系统类型判断
        QueryWrapper<WdFireSys> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sys_category", "1");
        List<WdFireSys> list = wdFireSysService.list(queryWrapper);
        // 消防系统Map
        Map<String, WdFireSys> sysMap = list.stream().collect(Collectors.toMap(WdFireSys::getFireSysCode, Function.identity(), (v1, v2) -> v1));
        List<WdDevConfig> devConfigs = wdDevConfigService.list();
        // 设备类型Map
        Map<String, WdDevConfig> devMap = devConfigs.stream().collect(Collectors.toMap(WdDevConfig::getDevName, Function.identity(), (v1, v2) -> v1));

        List<BaseDeviceTemporary> deviceTemporaries = deviceTemporaryMapper.queryBaseDeviceTemporaryByApplyId(deviceTemporary);
        List<BaseDevicePoint> devicePoints = new ArrayList<>();
        for (BaseDeviceTemporary tem : deviceTemporaries) {
            // 获取修改的部分数据 修改的只是部分
            BaseDevicePoint oldPoint = getPoint(tem);
            //原点位号
            tem.setOldPointCode(oldPoint.getPointCode());
            //原点位描述
            tem.setOldDescribe(oldPoint.getPointDesc());
            //原点位内容
            tem.setOldContent(oldPoint.toString());
            BaseDevicePoint point = new BaseDevicePoint();
            point.setPointCode(tem.getPointCode());
            point.setDevTypeCode(tem.getDevTypeCode());
            point.setHostNum(tem.getHostNum());
            point.setLoopCode(tem.getLoopCode());
            point.setDevTypeCat(tem.getDevTypeCat());
            point.setDevTypeName(tem.getDevTypeName());
            point.setPointDesc(tem.getPointDesc());
            point.setZoneId(tem.getZoneId());
            point.setZoneName(tem.getZoneName());
            point.setFloorAddr(tem.getFloorAddr());
            point.setFloorId(tem.getFloorId());
            point.setHostId(tem.getHostId());
            point.setBuildingId(tem.getBuildingId());
            point.setCreateTime(new Date());
            point.setFloorName(tem.getFloorName());
            point.setFasCode(tem.getFasCode());
            point.setFasName(tem.getFasName());
            WdDevConfig wdDevConfig = devMap.get(tem.getDevTypeName());
            if (wdDevConfig != null) {
                WdFireSys wdFireSys = sysMap.get(wdDevConfig.getFireSysCode());
                if (wdFireSys != null) {
                    point.setFasCode(wdFireSys.getFireSysCode());
                    point.setFasName(wdFireSys.getFireSysName());
                }
            }
            //did
            point.setDid(tem.getDid());
            point.setPointNumber(tem.getPointNumber());
            devicePoints.add(point);
        }
        int num = devicePointService.updatePointByBuildId(devicePoints);
        if (deviceTemporaries.size() > 0) {
            //更新原点位内容
            boolean oldFalse = super.updateBatchById(deviceTemporaries);
            boolean status = this.updateByApplyId(deviceTemporary);
            BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
            //修改点位调改记录状态
            iBaseDeviceApplicationService.updateBaseDeviceApplication(baseDeviceTemporary.getApplyId(), "1");
            log.info("开始插入 数据中心  点位调整记录   修改记录");
            BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(baseDeviceTemporary.getApplyId());
            BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
            iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                    baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                    "1", "修改", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", byId.getProposer(),
                    byId.getApplicantTime(), byId.getApprovOverTime(), "已审批"));
        }
        if (num > 0) {
            return true;
        }
        return false;
    }

    private boolean updateByApplyId(BaseDeviceTemporary tem) {
        UpdateWrapper<BaseDeviceTemporary> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("approve_type", tem.getApproveType());
        updateWrapper.eq("apply_id", tem.getApplyId());
        boolean flag = super.update(updateWrapper);
        return flag;
    }

    /**
     * 根据建筑ID和主机号获取点位
     *
     * @param tem
     * @return
     */
    private BaseDevicePoint getPoint(BaseDeviceTemporary tem) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("building_id", tem.getBuildingId());
        queryWrapper.eq("pid", tem.getHostId() + tem.getDid());
        return devicePointService.getOne(queryWrapper);
    }


    /**
     * 删除点位信息
     *
     * @param deviceTemporaries 涉及点位列表
     * @param approveTime
     */
    @Override
//    @Transactional(propagation = Propagation.REQUIRED,isolation = Isolation.SERIALIZABLE)
    public void deletePointInfo(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime) {
        String buildingId = deviceTemporaries.get(0).getBuildingId();
        List<String> pids = Lists.newArrayList();
        deviceTemporaries.forEach(tem -> {
            pids.add(tem.getHostId() + tem.getDid());
        });
        if (!CollectionUtils.isEmpty(pids)) {
            log.info("点位调整记录   开始进行删除记录操作，申请的id为【{}】", deviceTemporaries.get(0).getApplyId());
            // 因为有两次重复删除的申请审批 所以先确定点位存在再进行删除操作
            ListUtil.averageBySize(pids, 1000).forEach(avePids -> {
                // 多线程去进行删除的操作
                threadPoolExecutor.submit(() -> {
                    devicePointService.remove(new QueryWrapper<BaseDevicePoint>().in("pid", avePids));
                });
            });
        }
        smokeAlarmSettingService.deleteSmokeAlarmSettingByPids(pids);
        BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
        //修改点位调改记录状态
//        iBaseDeviceApplicationService.updateBaseDeviceApplication(baseDeviceTemporary.getApplyId(), "1");
        BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(baseDeviceTemporary.getApplyId());
        log.info("开始插入 数据中心  点位调整记录   删除记录，申请的id为【{}】", baseDeviceTemporary.getApplyId());
        BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
        String fullName = byId.getProposer();
        iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                "2", "删除", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", fullName,
                byId.getApplicantTime(), DateUtil.formatDate(approveTime, "yyyy-MM-dd HH:mm:ss"), "审批通过"));
//        iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
//                baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
//                "2", "删除", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", fullName,
//                byId.getApplicantTime(), byId.getApprovOverTime(), "审批通过"));
        log.info("插入成功 数据中心  点位调整记录   删除记录，申请的id为【{}】", baseDeviceTemporary.getApplyId());

        try {
            log.info("点位调改-删除点位申请通过，删除本地实际主机，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
            if (buildingId != null) {
                Integer num = baseLocalHostInfoMapper.delete(new LambdaQueryWrapper<BaseLocalHostInfo>().eq(BaseLocalHostInfo::getBuildingId, buildingId));
                log.info("点位调改-删除点位申请通过，删除本地实际主机成功,建筑物id为:【{}】,申请的id为:【{}】,删除数量为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId(), num);
                log.info("点位调改-删除点位申请通过，初始化本地主机数据，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
//                baseLocalHostInfoMapper.initLocalHostInfo(buildingId);
                baseLocalHostInfoService.initLocalHostInfoByBuildingId(buildingId);
                statLocalHostExpectionMapper.delete(new LambdaQueryWrapper<StatLocalHostExpection>().eq(StatLocalHostExpection::getBuildingId, buildingId));
                log.info("点位调改-删除点位申请通过，初始化本地主机数据成功，建筑物id为:【{}】,申请的id为:【{}】", buildingId, deviceTemporaries.get(0).getApplyId());
                //相应维保计划和预约等调整
                Future<Object> result = threadPoolExecutor.submit(new AdjustMaintenanceThread(buildingId, null, false));
            }
        } catch (Exception e) {
            log.error("点位调改-删除点位申请通过,执行本地实际主机初始化失败！失败原因是：【】", e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void deleteDeviceTemporarysByTime(Date time) throws Exception {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.le("CREATE_TIME_", DateUtil.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
        deviceTemporaryMapper.delete(queryWrapper);
    }


    /**
     * 删除点位
     *
     * @param deviceTemporary
     * @return
     */
    @Override
    @Transactional
    public boolean deleteApplyPoint(BaseDeviceTemporary deviceTemporary) {
        List<BaseDeviceTemporary> deviceTemporaries = deviceTemporaryMapper.queryBaseDeviceTemporaryByApplyId(deviceTemporary);
        List<BaseDevicePoint> devicePoints = new ArrayList<>();
        String userId = deviceTemporary.getApplyId();//消防userId
        OsUser dataDto = osUserService.getById(userId);
        for (BaseDeviceTemporary tem :
                deviceTemporaries) {
            BaseDevicePoint point = new BaseDevicePoint();
            point.setPointCode(tem.getPointCode());
            point.setDevTypeCode(tem.getDevTypeCode());
            point.setHostNum(tem.getHostNum());
            point.setLoopCode(tem.getLoopCode());
            point.setDevTypeCat(tem.getDevTypeCat());
            point.setDevTypeName(tem.getDevTypeName());
            point.setPointDesc(tem.getPointDesc());
            point.setZoneId(tem.getZoneId());
            point.setZoneName(tem.getZoneName());
            point.setFloorAddr(tem.getFloorAddr());
            point.setFloorId(tem.getFloorId());
            point.setBuildingId(tem.getBuildingId());
            point.setPointNumber(tem.getPointNumber());
            point.setHostId(tem.getHostId());
            point.setCreateTime(new Date());
            point.setFloorName(tem.getFloorName());
            point.setFasCode(tem.getFasCode());
            point.setFasName(tem.getFasName());
            devicePoints.add(point);
        }//删除点位
        int num = devicePointService.deletePoint(devicePoints);
        deviceTemporaryMapper.updateStatusByApplyId(deviceTemporary);
        if (deviceTemporaries.size() > 0) {
            BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
            //修改点位调改记录状态
            iBaseDeviceApplicationService.updateBaseDeviceApplication(baseDeviceTemporary.getApplyId(), "1");

            log.info("开始插入 数据中心  点位调整记录   删除记录");
            BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(baseDeviceTemporary.getApplyId());
            BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
            String fullName = dataDto == null ? "" : dataDto.getFullname();
            iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                    baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                    "2", "删除", String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", fullName,
                    byId.getApplicantTime(), byId.getApprovOverTime(), "已审批"));
        }
        if (num > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean updateStatusByApplyId(BaseDeviceTemporary deviceTemporary) {
        int num = deviceTemporaryMapper.updateStatusByApplyId(deviceTemporary);
        String userId = deviceTemporary.getApplyId();
        OsUser osUser = osUserService.getById(userId);
        if (deviceTemporary != null) {
            //修改点位调改记录状态
//            iBaseDeviceApplicationService.updateBaseDeviceApplication(deviceTemporary.getApplyId(), deviceTemporary.getApproveType());

            log.info("开始插入 数据中心  点位调整记录   未通过记录");
            BaseDeviceApplication byId = iBaseDeviceApplicationService.getById(deviceTemporary.getApplyId());
            BaseBuilding baseBuilding = iBaseBuildingService.getById(byId.getBuildingId());
            String adjustTypeStr = "";
            if ("0".equals(byId.getModulationType())) {
                adjustTypeStr = "新增";
            } else if ("1".equals(byId.getModulationType())) {
                adjustTypeStr = "修改";
            } else if ("2".equals(byId.getModulationType())) {
                adjustTypeStr = "删除";
            } else if ("3".equals(byId.getModulationType())) {
                adjustTypeStr = "特批";
            }
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            Double modulationProportion = byId.getModulationProportion() == null || "".equals(byId.getModulationProportion()) ? 0 : Double.parseDouble(byId.getModulationProportion());
            iAdjustptService.save(iAdjustptService.setAdjustpt(byId.getBuildingId(), baseBuilding.getBuildingName(),
                    baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                    byId.getModulationType(), adjustTypeStr, String.valueOf(byId.getModulationQuantity()), decimalFormat.format(modulationProportion) + "%", osUser.getFullname(),
                    byId.getApplicantTime(), byId.getApprovOverTime(), "审批通过"));
        }

        return num > 0;
    }

    /**
     * 更新点位状态
     *
     * @param baseDeviceTemporary
     * @return
     */
    @Override
    @Transactional
    public boolean updateDotStatus(BaseDeviceTemporary baseDeviceTemporary) {
        List<BaseDeviceTemporary> list = deviceTemporaryMapper.queryBaseDeviceTemporaryByApplyId(baseDeviceTemporary);
        int num = deviceTemporaryMapper.updateDotStatus(list);
        if (num > 0) {
            return true;
        }
        return false;
    }

    /**
     * 根据建筑ID获取点位总数
     *
     * @param buildingId 建筑物id
     * @return
     */
    @Override
    public int getPointCountByBuildId(String buildingId) {
        QueryWrapper<BaseDevicePoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("building_id", buildingId);
        return devicePointService.count(queryWrapper);
    }

    /**
     * 根据apply 查询
     *
     * @param applyId
     * @return
     */
    @Override
    public List<BaseDeviceTemporary> getPointByApplyId(String applyId) {
        return this.list(new QueryWrapper<BaseDeviceTemporary>().eq("apply_id", applyId));
    }

    @Override
    public List<BaseDeviceTemporary> getPointFileByApplyId(String applyId) throws IOException {
//        List<BaseDeviceTemporary> devList = new ArrayList<>();
//        ParseLocalFile parseLocalFile = new ParseLocalFile("/data/tmp/" + applyId);
//        BufferedReader bufferedReader = parseLocalFile.getBufferedReader();
//        String line;
//        while ((line = bufferedReader.readLine()) != null) {
//            BaseDeviceTemporary baseDeviceTemporary = FastJSONUtils.toBean(line, BaseDeviceTemporary.class);
//            devList.add(baseDeviceTemporary);
//        }
//        return devList;
        if (redisUtils.exists(Constant.IMPORT_POINT_KEY + applyId)) {
            List<BaseDeviceTemporary> baseDeviceTemporaryList = JSONObject.parseArray(redisUtils.get(Constant.IMPORT_POINT_KEY + applyId).toString(), BaseDeviceTemporary.class);
            return baseDeviceTemporaryList;
        } else {
            throw new RuntimeException("文件未找到！");
        }
    }

    /**
     * 保存特批点位信息
     *
     * @param list
     * @return
     */

    private boolean addSpeciallyDotApply(List<BaseDeviceTemporary> list) {
        BaseDeviceTemporary deviceTemporary = list.get(0);
        int num = deviceTemporaryMapper.deleteInfoByApplyId(deviceTemporary);
        if (num > 0) {
            super.saveBatch(list);
            return true;
        }
        return false;
    }


    /**
     * 将点位信息存入redis
     */
    public void setPointToRedis(List<BaseDevicePoint> devicePoints) {
        log.info("点位调改-新增点位 插入redis条数：" + devicePoints.size());
        List<StringRedisDto> redisDtoList = Lists.newArrayList();
        for (BaseDevicePoint baseDevicePoint : devicePoints) {
            StringRedisDto stringRedisDto = new StringRedisDto();
            stringRedisDto.setKey("equipment:" + baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid());
            stringRedisDto.setValue(JSONObject.toJSONString(new PointCache(baseDevicePoint.getBuildingId(), baseDevicePoint.getId(), baseDevicePoint.getFasCode(), baseDevicePoint.getFasName(), baseDevicePoint.getDevTypeCode(), baseDevicePoint.getDevTypeName())));
            redisDtoList.add(stringRedisDto);
        }
        long re = System.currentTimeMillis();
        redisUtils.batchSet(redisDtoList);
//        devicePoints.forEach(baseDevicePoint -> {
////            log.info("key:" + "equipment:" + baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid());
//            redisUtils.set("equipment:" + baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid(), JSONObject.toJSONString(new PointCache(baseDevicePoint.getBuildingId(), baseDevicePoint.getId(), baseDevicePoint.getFasCode(), baseDevicePoint.getFasName(), baseDevicePoint.getDevTypeCode(), baseDevicePoint.getDevTypeName())));
//        });
        log.info("点位调改-新增点位 插入redis成功！" + devicePoints.size());
    }

//    public static void main(String[] args) {
//
//        System.out.println(ByteArrayUtil.hex10To16Desc(Integer.parseInt("66301"), 12));
//        String hostId = "6";
//        String loop = "4";
//        String pointCode = "187";
//        System.out.println("方法0：" + new BaseDeviceTemporaryServiceImpl().transferDidMethodZero(hostId, loop, pointCode));
//        System.out.println("进入方法1：" + new BaseDeviceTemporaryServiceImpl().transferDidMethodOne(hostId, loop, pointCode));
//        System.out.println("进入方法2：" + new BaseDeviceTemporaryServiceImpl().transferDidMethodTwo(hostId, loop, pointCode));
//        System.out.println("进入方法3和方法4和方法6：" + new BaseDeviceTemporaryServiceImpl().transferDidXimenzi(hostId, loop, pointCode));
//        System.out.println("进入方法5：" + new BaseDeviceTemporaryServiceImpl().transferDidMethodFive(hostId, loop, pointCode));
//        System.out.println("进入赋安5116：" + new BaseDeviceTemporaryServiceImpl().transferDidFuan(hostId, loop, pointCode));
//        System.out.println("青鸟：" + new BaseDeviceTemporaryServiceImpl().transferStringQingniao(hostId, loop, pointCode));
//
//    }

    /**
     * 数字转换 did hid 等相应转换
     *
     * @param source 源字符串
     * @param length 最大长度
     * @return
     */
    @Override
    public String transferString(String source, Integer length) {
        if (StringUtils.isBlank(source)) {
            return String.format("%0" + length + "d", 0);
        }
        char[] array = source.toCharArray();
        StringBuilder reverse = new StringBuilder();
        for (int i = array.length - 1; i >= 0; i--) {
            reverse.append(array[i]).append("0");
        }
        if (reverse.length() > length) {
            return reverse.reverse().substring(0, length - 1);
        } else if (reverse.length() < length) {
            // 补0
            return String.format("%0" + (length - reverse.length()) + "d", 0) + reverse.reverse();
        } else {
            return reverse.reverse().toString();
        }
    }

    /**
     * 方法0
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    @Override
    public String transferDidMethodZero(String localHost, String localLoop, String pointCode) {
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(localLoop), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(pointCode), 4);
        return s1 + s;
    }

    /**
     * 方法0
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    @Override
    public String transferDidMethodSeven(Integer localHost, Integer localLoop, Integer pointCode) {
        //补0 ---
        String str = String.format("%02d", localHost) + String.format("%03d", localLoop) + String.format("%03d", pointCode);
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(str.substring(0, 4)), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(str.substring(4, 8)), 4);
        return s1 + s;
    }

    /**
     * 方法1
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    @Override
    public String transferDidMethodOne(String localHost, String localLoop, String pointCode) {
        int length = localLoop.length();
        if (length >= 3) {
            localLoop = localLoop.substring(0, 3);
        } else {
            localLoop = String.format("%0" + (3 - length) + "d", 0) + localLoop;
        }
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(localHost + localLoop), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(pointCode), 4);
        return s1 + s;
    }

    /**
     * 方法二
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    @Override
    public String transferDidMethodTwo(String localHost, String localLoop, String pointCode) {
        int length = localLoop.length();
        if (length >= 2) {
            localLoop = localLoop.substring(0, 2);
        } else {
            localLoop = String.format("%0" + (2 - length) + "d", 0) + localLoop;
        }
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(localHost + localLoop), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(pointCode), 4);
        return s1 + s;

    }

    /**
     * 方法5
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    @Override
    public String transferDidMethodFive(String localHost, String localLoop, String pointCode) {
        int length = localLoop.length();
        if (length >= 3) {
            localLoop = localLoop.substring(0, 3);
        } else {
            localLoop = String.format("%0" + (3 - length) + "d", 0) + localLoop;
        }
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(localHost + localLoop), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(pointCode), 4);
        return s1 + s;
    }

    /**
     * 转换fuan
     *
     * @param localHostNum
     * @param localLoopCode
     * @param pointNumber
     * @return
     */
    @Override
    public String transferDidFuan(String localHostNum, String localLoopCode, String pointNumber) {
        int length = localLoopCode.length();
        if (length >= 3) {
            localLoopCode = localLoopCode.substring(0, 3);
        } else {
            localLoopCode = String.format("%0" + (3 - length) + "d", 0) + localLoopCode;
        }
        String s = ByteArrayUtil.hex10To16Desc(Integer.parseInt(localHostNum + localLoopCode), 4);
        String s1 = ByteArrayUtil.hex10To16Desc(Integer.parseInt(pointNumber), 4);
        return s1 + s;
    }

    /**
     * 转换直接16进制
     *
     * @param localHostNum
     * @param localLoopCode
     * @param pointNumber
     * @return
     */
    @Override
    public String transferDidXimenzi(String localHostNum, String localLoopCode, String pointNumber) {
        StringBuilder result = new StringBuilder();
        for (int i = pointNumber.length(); i > 0; i = i - 2) {
            if (i < 2) {
                result.append(pointNumber, 0, i);
            } else {
                result.append(pointNumber, i - 2, i);
            }
        }
        if (result.length() < 8) {
            result.append(String.format("%0" + (8 - result.length()) + "d", 0));
        }
        return result.toString();
    }

    @Override
    public void setPointRedis(String id) {
        List<BaseDevicePoint> firePointList = baseDevicePointMapper.selectList(new QueryWrapper<BaseDevicePoint>().eq("building_id", id).eq("super_type", "0"));
        if (firePointList != null && firePointList.size() > 0) {
            setPointToRedis(firePointList);
        }

        List<BaseDevicePoint> PointLists = baseDevicePointMapper.selectList(new QueryWrapper<BaseDevicePoint>().eq("building_id", id).in("super_type", "1", "2", "3"));
        if (PointLists != null && PointLists.size() > 0) {
            for (BaseDevicePoint PointList : PointLists) {
                waterMonitorService.setPointInfoToRedis(PointList);
            }
        }
    }

    @Override
    public void setWaterPump() {
        List<BaseDevicePoint> PointLists = baseDevicePointMapper.selectList(new QueryWrapper<BaseDevicePoint>().in("super_type", "1", "2", "3"));
        if (PointLists != null && PointLists.size() > 0) {
            for (BaseDevicePoint PointList : PointLists) {
                waterMonitorService.setPointInfoToRedis(PointList);
            }
        }
    }

    /**
     * 新增设备类型分配
     *
     * @param buildingId
     * @param devicePoints
     * @param flag
     */
    private void deviceTypeRedistribution(String buildingId, List<BaseDevicePoint> devicePoints, Boolean flag, String effectiveTime) {

        if (flag && devicePoints != null && devicePoints.size() > 0) {
            log.info("维保点位调改：{}，需要调整的点位清单为-> {}", buildingId, FastJSONUtils.toJSONString(devicePoints));
            Map<String, Object> param = new HashMap<>(8);
            param.put("buildingId", buildingId);
            if (StrUtil.isNotBlank(effectiveTime)) {
                param.put("effectiveTime", effectiveTime);
            }
            //1.获得建筑的当月和之后的维保计划最小范围信息
            List<MaintenanceScopeTest> maintenanceScopeTests = maintenanceScopeTestMapper.selectDeviceTypeScopeTestByParam(param);
            log.info("维保点位调改：{}，相应的最小范围清单->{}", buildingId, FastJSONUtils.toJSONString(maintenanceScopeTests));
            //2.筛选设备类型下匹配的点位
            Map<String, List<BaseDevicePoint>> result = new HashMap<>(64);
            maintenanceScopeTests.forEach(maintenanceScopeTest -> {
                List<PointBatch> pointBatches = pointBatchMapper.selectList(new LambdaQueryWrapper<PointBatch>().eq(PointBatch::getBatchId, maintenanceScopeTest.getBatchId()));
                //删除批次点位中点位修改后的不匹配的点位批次
                List<String> pointBatchList = new ArrayList<>();
                List<BaseDevicePoint> baseDevicePoints = new ArrayList<>();
                Set<String> existingPoint = new HashSet<>();
                devicePoints.forEach(baseDevicePoint -> {
                    for (PointBatch pointBatch : pointBatches) {
                        if (pointBatch.getEquipmentiId().equals(baseDevicePoint.getId())) {
                            if (!maintenanceScopeTest.getDeviceCode().equals(baseDevicePoint.getDevTypeCode())) {
                                pointBatchList.add(pointBatch.getId());
                            } else {
                                return;
                            }
                        }
                    }
                    if (maintenanceScopeTest.getDeviceCode().equals(baseDevicePoint.getDevTypeCode())) {
                        baseDevicePoints.add(baseDevicePoint);
                    }
                });
                if (baseDevicePoints != null && baseDevicePoints.size() > 0) {
                    result.put(maintenanceScopeTest.getDeviceCode(), baseDevicePoints);
                }
                if (pointBatchList != null && pointBatchList.size() > 0) {
                    pointBatchService.removeByIds(pointBatchList);
                    log.info("维保点位调改：{}，删除的点位批次->{}", buildingId, FastJSONUtils.toJSONString(pointBatches));
                }
            });
            //分配点位
            if (result.size() > 0) {
                //求 季度 以及季度对应的最小范围
                List<QuarterlyScope> quarterlyScopesList = maintenanceScopeTestMapper.selectDeviceTypeScopeTestQuarterlyByParam(param);
                log.info("维保点位调改：{}，需要调改的点位清单->{}", buildingId, FastJSONUtils.toJSONString(result));
                List<PointBatch> pointBatches = new ArrayList<>();
                for (Map.Entry<String, List<BaseDevicePoint>> entry : result.entrySet()) {
                    quarterlyScopesList.forEach(quarterlyScope -> {
                        log.info("维保点位调改：{}，开始统计第{}季度数据，", buildingId, quarterlyScope.getQuarterly());
                        List<MaintenanceScopeTest> maintenanceScopeTestList = new ArrayList<>();
                        List<MaintenanceScopeTest> maintenanceScopeTestListCheckAll = new ArrayList<>();
                        for (MaintenanceScopeTest maintenanceScopeTest : quarterlyScope.getScopeTestVoList()) {
                            if (maintenanceScopeTest.getDeviceCode().equals(entry.getKey())) {
                                String[] batchIdArray = maintenanceScopeTest.getBatchId().split(",");
                                for (int i = 0; i < batchIdArray.length; i++) {
                                    MaintenanceScopeTest tmp = new MaintenanceScopeTest();
                                    tmp.setBatchId(batchIdArray[i]);
                                    if (maintenanceScopeTest.getIsCheckAll() == 1) {
                                        maintenanceScopeTestListCheckAll.add(tmp);
                                    } else {
                                        maintenanceScopeTestList.add(tmp);
                                    }
                                }
                            }
                        }
                        List<BaseDevicePoint> baseDevicePoints = entry.getValue();
                        if (maintenanceScopeTestListCheckAll.size() > 0) {
                            for (int i = 0; i < baseDevicePoints.size(); i++) {
                                for (int j = 0; j < maintenanceScopeTestListCheckAll.size(); j++) {
                                    PointBatch pointBatch = new PointBatch();
                                    pointBatch.setBatchId(maintenanceScopeTestListCheckAll.get(j).getBatchId());
                                    pointBatch.setEquipmentiId(baseDevicePoints.get(i).getId());
                                    pointBatches.add(pointBatch);
                                }
                            }
                        }
                        if (maintenanceScopeTestList.size() > 0) {
                            int count;
                            for (int i = 0; i < baseDevicePoints.size(); i++) {
                                count = i % maintenanceScopeTestList.size();
                                MaintenanceScopeTest maintenanceScopeTest = maintenanceScopeTestList.get(count);
                                PointBatch pointBatch = new PointBatch();
                                pointBatch.setBatchId(maintenanceScopeTest.getBatchId());
                                pointBatch.setEquipmentiId(baseDevicePoints.get(i).getId());
                                pointBatches.add(pointBatch);
                            }
                        }
                    });
                }
                log.info("维保点位调改：{}，开始点位去重校验", buildingId);
                List<PointBatch> finalPointBatchList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(pointBatches)) {
                    Map<String, PointBatch> pointBatchHashMap = pointBatches.stream().collect(Collectors.toMap(e -> (e.getEquipmentiId() + "&" + e.getBatchId()), e -> e, (a, b) -> a));
                    Set<String> existingSet = new HashSet<>();
                    maintenanceScopeTests.forEach(maintenanceScopeTest -> {
                        List<PointBatch> pointBatchList = pointBatchMapper.selectList(new LambdaQueryWrapper<PointBatch>().eq(PointBatch::getBatchId, maintenanceScopeTest.getBatchId()));
                        if (CollectionUtil.isNotEmpty(pointBatchList)) {
                            for (PointBatch pointBatch : pointBatchList) {
                                String key = pointBatch.getEquipmentiId() + "&" + pointBatch.getBatchId();
                                    if (pointBatchHashMap.containsKey(key)) {
                                        existingSet.add(key);
                                    }
                                }
                            }
                    });
                    if (CollectionUtil.isEmpty(existingSet)) {
                        finalPointBatchList.addAll(pointBatches);
                    } else {
                        for (PointBatch pointBatch : pointBatches) {
                            String key = pointBatch.getEquipmentiId() + "&" + pointBatch.getBatchId();
                            if (!existingSet.contains(key)) {
                                finalPointBatchList.add(pointBatch);
                            }
                        }
                    }
                    log.info("维保点位调改：{}，最终要保存得数据为:{}", buildingId, Arrays.toString(finalPointBatchList.toArray()));
                    pointBatchService.saveBatch(finalPointBatchList);
                }
//                pointBatchService.saveBatch(pointBatches);
                log.info("维保点位调改：{}，新增的点位批次->{}", buildingId, FastJSONUtils.toJSONString(finalPointBatchList));
            }
        }
    }
}
