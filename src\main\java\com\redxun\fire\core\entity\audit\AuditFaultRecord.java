package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.entity.FaultInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 检查记录故障点位
 */
@Data
@NoArgsConstructor
public class AuditFaultRecord implements Serializable {


    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditRecordId;

    /**
     * 建筑名称
     */
    private String buildingName;

    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 设备类型名称
     */
    private String devTypeName;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 防火分区
     */
    private String zoneName;

    /**
     * 上报时间
     */
    private Date reportedTime;

    /**
     * 派发人
     */
    private String dispatchUser;

    /**
     * 处理人
     */
    private String disposeUser;


    public AuditFaultRecord(FaultInfo faultInfo) {
        this.buildingName = faultInfo.getBuildingName();
        this.pointNumber = faultInfo.getPointCode();
        this.devTypeName = faultInfo.getDevTypeName();
        this.pointDesc = faultInfo.getPointDesc();
        this.zoneName = faultInfo.getZoneName();
        this.reportedTime = faultInfo.getReportedTime();
        this.dispatchUser = faultInfo.getRepairman();
        this.disposeUser = faultInfo.getHandleUser();
    }
}
