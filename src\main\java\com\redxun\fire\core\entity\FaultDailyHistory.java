package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 每日故障存量(FaultDailyHistory)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:40:09
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class FaultDailyHistory extends Model<FaultDailyHistory> {
    //主键
    @TableId(type = IdType.UUID)
    private String id;
    //建筑物id
    @TableField(value = "building_id")
    private String buildingId;
    //点位id
    @TableField(value = "point_id")
    private String pointId;

    //历史故障状态（0 未处理 1 已处理 2 处理中）
    @TableField(value = "history_fault_status_str")
    private String historyFaultStatusStr;

    //原始故障表记录id
    @TableField(value = "fault_info_id")
    private String faultInfoId;

    @TableField(value = "today_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date todayDate;

    //点位号
    @TableField(value = "point_code")
    private String pointCode;

    //点位描述
    @TableField(value = "point_desc")
    private String pointDesc;
    //设备类型名称
    @TableField(value = "dev_type_name")
    private String devTypeName;
    //上报时间
    @TableField(value = "reported_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date reportedTime;
    //派发人
    @TableField(value = "send_user")
    private String sendUser;
    //处理人
    @TableField(value = "handle_user")
    private String handleUser;
}

