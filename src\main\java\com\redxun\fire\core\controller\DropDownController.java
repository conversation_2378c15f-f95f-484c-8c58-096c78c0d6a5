
package com.redxun.fire.core.controller;

import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.mapper.ConfigRelationMapper;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/fire/core/dropDown")
@Api(tags = "数据字典")
@ClassDefine(title = "数据字典", alias = "DropDownController", path = "/fire/core/dropDown", packages = "core", packageName = "子系统名称")
public class DropDownController {

    @Autowired
    DropDownServiceImpl dropDownService;
    @Resource
    private ConfigRelationMapper configRelationMapper;

    /**
     * 根据分类id获取所有设备类型
     * @return
     */
    @GetMapping("queryDropDown/{treeId}")
    public JsonResult queryDropDown(@PathVariable("treeId") String treeId) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryNameByTreeId(treeId));
        return jsonResult;
    }

    /**
     * 获取所有设备类型
     *
     * @return
     */
    @GetMapping("queryDevDropDown")
    public JsonResult queryDevDropDown() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryDevDropDown());
        return jsonResult;
    }

    /**
     * 获取所有消防系统
     *
     * @return
     */
    @GetMapping("queryFireDropDown")
    public JsonResult queryFireDropDown() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryFireDropDown());
        return jsonResult;
    }

    /**
     * 获取建筑所有楼层
     *
     * @param buildingId
     * @return
     */
    @GetMapping("queryFloorDropDown/{buildingId}")
    public JsonResult queryFloorDropDown(@PathVariable("buildingId") String buildingId) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryFloorDropDown(buildingId));
        return jsonResult;
    }

    /**
     * 查询误报原因
     *
     * @param code
     * @return
     */
    @RequestMapping("queryCase/{code}")
    public JsonResult queryCase(@PathVariable("code") String code) {
        JsonResult jsonResult = JsonResult.Success();
//        jsonResult.setData(configRelationMapper.queryConfigByCode(code));
        jsonResult.setData(dropDownService.queryConfigByCode(code));
        return jsonResult;
    }

    /**
     * 获取防火分区下拉列表
     *
     * @return
     */
    @GetMapping("queryFireAreaDropDown")
    public JsonResult queryFireAreaDropDown() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryFireAreaDropDown());
        return jsonResult;
    }

    @GetMapping("queryConfigByCode")
    public JsonResult queryConfigByCode(@RequestParam String code) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryConfigByCode(code));
        return jsonResult;
    }

    @GetMapping("queryMapConfigByCode")
    public JsonResult queryMapConfigByCode(@RequestParam String code) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(dropDownService.queryConfigMapByCode(code));
        return jsonResult;
    }

}

