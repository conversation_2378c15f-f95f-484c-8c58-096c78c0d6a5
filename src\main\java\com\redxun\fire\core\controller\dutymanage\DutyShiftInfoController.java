package com.redxun.fire.core.controller.dutymanage;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.OsUser;
import com.redxun.fire.core.entity.dutymanage.DutyShiftInfo;
import com.redxun.fire.core.job.AutoExecuteCloudCheckJob;
import com.redxun.fire.core.service.dutymanage.DutyShiftInfoService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2025/2/8
 * @description
 */
@RestController
@RequestMapping("/dutyShiftInfo")
@Slf4j
public class DutyShiftInfoController {

    @Autowired
    DutyShiftInfoService dutyShiftInfoService;

    @Autowired
    IOsUserService osUserService;

    @PostMapping(value = "queryPage")
    public JsonResult queryPage(@RequestBody QueryData queryData, HttpServletRequest request) {
        try {
            return dutyShiftInfoService.queryPage(queryData, request);
        } catch (Exception e) {
            log.error("值班班次分页异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "saveOrUpdate")
    public JsonResult saveOrUpdate(@RequestBody DutyShiftInfo dutyShiftInfo, HttpServletRequest request) {
        try {
            return dutyShiftInfoService.saveOrUpdate(dutyShiftInfo, request);
        } catch (Exception e) {
            log.error("保存值班班次信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "delete")
    public JsonResult delete(@RequestBody DutyShiftInfo dutyShiftInfo) {
        try {
            return dutyShiftInfoService.delete(dutyShiftInfo);
        } catch (Exception e) {
            log.error("删除值班班次信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return dutyShiftInfoService.flowEvent(dataJson);
    }

    @PostMapping("/approvePass")
    public JsonResult approvePass(@RequestBody JSONObject jsonObject) {
        try {
            return dutyShiftInfoService.approvePass(jsonObject);
        } catch (Exception e) {
            log.error("值班班次审批通过异常：", e);
            return JsonResult.getFailResult(e.getMessage());
        }
    }

    @GetMapping(value = "downloadTemplate")
    public void downloadTemplate(@RequestParam String id, HttpServletResponse response) {
        try {
             dutyShiftInfoService.downloadTemplate(id, response);
        } catch (Exception e) {
            log.error("下载值班班次模板信息异常：", e);
        }
    }

    /**
     */
    @GetMapping("/getDutyShiftDetails")
    public JsonResult getDutyShiftDetails(@RequestParam String detailsId) {
        return dutyShiftInfoService.getDutyShiftDetails(detailsId);
    }

    @GetMapping({"/getApproveUser"})
    public List<TaskExecutor> deviceDelayJobApprove(@RequestParam(value = "busKey") String busKey) {
        List<TaskExecutor> userList = new ArrayList<>();
        final val byId = dutyShiftInfoService.getById(busKey);
        if (ObjectUtil.isNotEmpty(byId)) {
            final val approveUserId = byId.getApproveUserId();
            final val one = osUserService.getOne(new LambdaQueryWrapper<OsUser>()
                    .eq(OsUser::getWztUserId, approveUserId));
            if (ObjectUtil.isNotEmpty(one)) {
                userList.add(new TaskExecutor("user", one.getWztUserId(), one.getFullname(), one.getUserNo()));
            }
        }
        return userList;
    }

    @GetMapping("/getRedisDataByKey")
    public JsonResult getRedisDataByKey(@RequestParam String redisKey) {
        return dutyShiftInfoService.getRedisDataByKey(redisKey);
    }

    @PostMapping("/setRedisDataByKey")
    public JsonResult setRedisDataByKey(@RequestBody Map<String, String> jsonObject) {
        return dutyShiftInfoService.setRedisDataByKey(jsonObject);
    }

    @Autowired
    AutoExecuteCloudCheckJob cloudCheckJob;

    @GetMapping("/test")
    public void test() throws Exception {
        cloudCheckJob.execute();
    }
}
