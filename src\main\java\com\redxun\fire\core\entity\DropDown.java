
/**
 * <pre>
 *
 * 描述：数据字典实体类定义
 * 表:drop_down
 * 作者：gl
 * 邮箱:
 * 日期:2024-02-29 20:46:16
 * 版权：万达
 * </pre>
 */
package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.redxun.common.base.entity.BaseExtEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


@Setter
@Getter
@Accessors(chain = true)
@TableName(value = "drop_down")
public class DropDown {

    @JsonCreator
    public DropDown() {
    }

    //主键
    @TableId(value = "DIC_ID_", type = IdType.INPUT)
    private String dicId;

    //描述
    @TableField(value = "DESCP_")
    private String descp;
    //项名
    @TableField(value = "NAME_")
    private String name;
    //父ID
    @TableField(value = "PARENT_ID_")
    private String parentId;
    //路径
    @TableField(value = "PATH_")
    private String path;
    //序号
    @TableField(value = "SN_")
    private Integer sn;
    //分类Id
    @TableField(value = "TREE_ID_")
    private String treeId;
    //项值
    @TableField(value = "VALUE_")
    private String value;


//    @Override
//    public String getPkId() {
//        return dicId;
//    }
//
//    @Override
//    public void setPkId(String pkId) {
//        this.dicId = pkId;
//    }


    /**
     生成子表属性的Array List
     */

}



