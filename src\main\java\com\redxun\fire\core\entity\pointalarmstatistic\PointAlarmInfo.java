package com.redxun.fire.core.entity.pointalarmstatistic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 点位年度报警统计
 * @TableName point_alarm_info
 */
@TableName(value ="point_alarm_info")
@Data
public class PointAlarmInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 点位id
     */
    @TableField(value = "point_id")
    private String pointId;

    /**
     * 万中台隔间ID
     */
    @TableField(value = "wzt_compartment_id")
    private String wztCompartmentId;

    /**
     * 报警类型
     */
    @TableField(value = "alarm_type")
    private String alarmType;

    /**
     * 报警时间
     */
    @TableField(value = "alarm_time")
    private Date alarmTime;

    /**
     * 报警月份
     */
    @TableField(value = "alarm_month")
    private String alarmMonth;

    /**
     * 报警年
     */
    @TableField(value = "alarm_year")
    private String alarmYear;

    @TableField(value = "building_id")
    private String buildingId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}