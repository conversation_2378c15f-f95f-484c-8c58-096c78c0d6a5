package com.redxun.fire.core.cassandra.dao;

import com.redxun.fire.core.cassandra.entity.CloseData;
import com.redxun.fire.core.consts.CassandraConstants;
import com.redxun.fire.core.pojo.dto.CassandraQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.cassandra.core.CassandraAdminTemplate;
//import org.springframework.data.cassandra.core.CassandraTemplate;
//import org.springframework.data.cassandra.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

//import static org.springframework.data.cassandra.core.query.Criteria.where;

/**
 * <AUTHOR>
 */
@Service
public class CloseDataDao {

//    @Resource
//    private CassandraTemplate cassandraTemplate;

    @Autowired(required = false)
    CassandraHttpService cassandraHttpService;


    /**
     * @param entity
     * @return
     */
    public CloseData save(CloseData entity) {
//        return cassandraTemplate.insert(entity);
        return null;
    }

    public CloseData update(CloseData entity) {
//        return cassandraTemplate.update(entity);
        return null;
    }

//    public CloseData saveCloseStoreData(CloseData entity) {
//        return cassandraTemplate.insert(entity);
//    }

//    public List<CloseData> findDeviceByTime(String buildingId, String pointId, Date startTime, Date endTime) {
//        Select query = QueryBuilder.select().from("close_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.gte("ts", startTime))
//                .and(QueryBuilder.lte("ts", endTime))
////                .and(QueryBuilder.eq("status", "1"))
//                .allowFiltering();
//        return cassandraTemplate.select(query, CloseData.class);
//    }

    public List<CloseData> findCloseStoreDataByTime(String buildingId, String pointId, Date startTime, Date endTime,
                                                    String outageStatus, String deviceStatus, String electricalStatus, String faultStatus) {
//        Select query = QueryBuilder.select().from("close_data");
//        Select.Where where = query.where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.gte("ts", startTime))
//                .and(QueryBuilder.lte("ts", endTime));
//        if (StringUtils.isNotEmpty(outageStatus)) {
//            where = where.and(QueryBuilder.eq("outage_status", outageStatus));
//        }
//        if (StringUtils.isNotEmpty(deviceStatus)) {
//            where = where.and(QueryBuilder.eq("device_status", deviceStatus));
//        }
//        if (StringUtils.isNotEmpty(electricalStatus)) {
//            where = where.and(QueryBuilder.eq("electrical_status", electricalStatus));
//        }
//        if (StringUtils.isNotEmpty(faultStatus)) {
//            where = where.and(QueryBuilder.eq("fault_status", faultStatus));
//        }
//        return cassandraTemplate.select(where.allowFiltering(), CloseData.class);
        CassandraQueryDTO dto = new CassandraQueryDTO( buildingId,  pointId,  startTime,  endTime,
                 outageStatus,  deviceStatus,  electricalStatus,  faultStatus);
        List<CloseData> data = cassandraHttpService.select(dto, CassandraConstants.CLOSESTORE_DATA,CloseData.class);
        return data;
    }

//    public List<CloseData> findOneByStatus(String buildingId, String pointId, String status) {
//        Select query = QueryBuilder.select().from("close_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.eq("status", status))
//                .limit(1);
//        return cassandraTemplate.select(query, CloseData.class);
//    }

//        public List<CloseData> findDeviceByTime(String buildingId, String pointId) {
//
//            Query query = Query.query(where("buildingId").is(buildingId),where("pointId").is(pointId)).limit(10);
//            return cassandraTemplate.select(query, CloseData.class);
//    }
}
