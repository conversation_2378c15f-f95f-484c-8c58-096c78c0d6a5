package com.redxun.fire.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 用户数据权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserDataPower implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("ID_")
    private String id;

    /**
     * 万中台用户ID
     */
    @TableField("USER_ID_")
    private String userId;

    /**
     * 广场id
     */
    @TableField("BUILDING_ID_")
    private String buildingId;

    /**
     * 操作类型
     */
    @TableField("OPERATE_TYPE_")
    private String operateType;
    /**
     * 组织层级
     */
    @TableField("LEVEL_AYER_")
    private String levelAyer;
    /**
     * 集团
     */
    @TableField("GROUP_ORG_")
    private String groupOrg;
    /**
     * 大区
     */
    @TableField("REGION_")
    private String region;
    /**
     * 城市公司
     */
    @TableField("CITY_")
    private String city;


    /**
     * 逻辑删除 0 正常 1 删除
     */
    @TableField("DELETED_")
    private Integer deleted;

    /**
     * 公司ID
     */
    @TableField("COMPANY_ID_")
    private String companyId;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 组织名称
     */
    @TableField("ORG_NAME_")
    private String orgName;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty(value = "申请人姓名")
    @TableField(exist = false)
    private String userName;
    @ApiModelProperty(value = "申请人姓名")
    @TableField(exist = false)
    private String buildingName;
    @TableField(exist = false)
    private String userNo;

}
