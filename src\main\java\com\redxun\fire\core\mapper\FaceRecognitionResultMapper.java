package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.FaceRecognitionResult;
import com.redxun.fire.core.pojo.dto.FaceRecognitionResultQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project hdwa-xf-fire
 * @description 人脸识别结果记录表 mapper接口
 * @date 2024/10/15 10:00:10
 */
@Mapper
public interface FaceRecognitionResultMapper extends BaseMapper<FaceRecognitionResult> {
    /**
     * 获取人脸识别结果记录表
     */
    List<FaceRecognitionResultQueryDto> findUserIds(@Param("projectId") String projectId, @Param("timeDiff") int timeDiff);

    Date selectMaxRecognitionTime(@Param("idNumber") String idNumber, @Param("projectId") String projectId);

}
