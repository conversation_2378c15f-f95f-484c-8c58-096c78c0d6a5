package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.danger.DangerStandardConfiguration;
import com.redxun.fire.core.pojo.vo.DangerDataInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface DangerStandardConfigurationMapper extends BaseMapper<DangerStandardConfiguration> {


    /**
     *
     * @return
     */
    List<DangerDataInfo> getExceptionInfo(@Param("buildingIds") List<String>buildingIds,
                                          @Param("dangerEquimentExceptionType") List<String> dangerEquimentExceptionType,
                                          @Param("dangerEquimentDevType") List<String> dangerEquimentDevType,
                                          @Param("time") Date time,
                                          @Param("startTime") Date startTime,
                                          @Param("exceptionEquimentNum") String exceptionEquimentNum);


    /**
     * 根据隐患ID，查询异常状态
     * @param infoId
     * @return
     */
    int checkDangerStatus(@Param("infoId") Long infoId);


    List<String> getDeviceType();


    List<String> getExceptionType();
}
