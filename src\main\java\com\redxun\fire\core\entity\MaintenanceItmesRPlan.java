package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.redxun.common.model.SuperEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 维保项和维保计划关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaintenanceItmesRPlan extends SuperEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 维保计划id
     */
    private String maintenancePlanId;

    /**
     * 消防系统id
     */
    @TableField(exist = false)
    private String fireproofSysId;

    /**
     * 维保项id
     */
    private String maintenanceConfigId;

    /**
     * 是否合格
     */
    private String qualifiedNo;

    /**
     * 是否完成
     */
    private String completeNo;

    /**
     * 复核结果 0不合格 1合格
     */
    @ApiModelProperty(value = "复核结果" )
    private String checkResult;

    /**
     * 复核人ID
     */
    @ApiModelProperty(value = "复核人ID" )
    private String checkBy;

    /**
     * 复核人姓名
     */
    @ApiModelProperty(value = "复核人姓名" )
    private String checkName;

    /**
     * 复核时间
     */
    @ApiModelProperty(value = "复核时间" )
    private LocalDateTime checkTime;


    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
}
