package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.FaceUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @project hdwa-xf-fire
 * @description 人脸识别用户表 mpper接口
 * @date 2024/10/15 09:58:53
 */
@Mapper
public interface FaceUserMapper extends BaseMapper<FaceUser> {

    FaceUser selectUserByProjectIdAndIdNumber(@Param("projectId") String projectId, @Param("idNumber") String idNumber);


}
