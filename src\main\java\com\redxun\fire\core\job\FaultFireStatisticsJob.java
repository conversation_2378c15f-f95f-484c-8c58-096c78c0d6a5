package com.redxun.fire.core.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.FaultFireStatisticsMapper;
import com.redxun.fire.core.mapper.FaultInfoMapper;
import com.redxun.fire.core.mapper.FireInfoMapper;
import com.redxun.fire.core.mapper.FirePageWindowMapper;
import com.redxun.fire.core.service.alarm.IRepeatedlyMisinformationService;
import com.redxun.fire.core.service.fault.FaultDailyHistoryService;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.DateUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @author: 程国栋
 * @since: 2024/4/26 13:45
 * @description: 单店监控台故障率误报率统计
 * @cron: 0 59 23 * * ?
 */
@Slf4j
@Service
public class FaultFireStatisticsJob extends IJobHandler {

    @Resource
    private BaseBuildingMapper baseBuildingMapper;

    @Resource
    private FireInfoMapper fireInfoMapper;

    @Resource
    private BaseDevicePointMapper baseDevicePointMapper;

    @Resource
    private IRepeatedlyMisinformationService repeatedlyMisinformationService;

    @Resource
    private FaultFireStatisticsMapper faultFireStatisticsMapper;

    @Resource
    private FaultInfoMapper faultInfoMapper;

    @Resource
    private FirePageWindowMapper firePageWindowMapper;

    @Resource
    private FaultDailyHistoryService faultDailyHistoryService;

    @Resource(name = "jobTaskAsyncExecutor")
    private Executor executor;

    @Override
    @XxlJob("FaultFireStatisticsJob")
    public void execute() throws Exception {
        //已调试完成
        StopWatch watch = new StopWatch();
        Date nowDate = new Date();
        watch.start();
        log.info("单店监控台故障率误报率统计 开始时间：" + DateUtil.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss"));
        //查询建筑表
        int i = 0;
        List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(new QueryWrapper<BaseBuilding>());
        if (!CollectionUtils.isEmpty(baseBuildings)) {
            log.info("单店监控台故障率误报率统计 baseBuildings建筑数量" + baseBuildings.size());
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (BaseBuilding baseBuilding : baseBuildings) {
            CompletableFuture.runAsync(()->{
                faultFireStatistics(baseBuilding, nowDate);
            }).exceptionally(ex -> {
                log.error("单店监控台故障率误报率失败",ex);
                return null;
            });
            i++;
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        watch.stop();
        log.info("单店监控台故障率误报率统计 用时:" + (watch.getTotalTimeSeconds()) + "秒；共处理门店：" + i + "家");
    }

    public void faultFireStatistics(BaseBuilding baseBuilding, Date nowDate) {
        String todayData = DateUtil.formatDate(nowDate, "yyyy-MM-dd");

        //***计算误报率***
        log.info("单店监控台故障率误报率统计 计算误报率，广场id：" + baseBuilding.getId());
        //查询火警总点位数量
        int num = baseDevicePointMapper.selectCount(new QueryWrapper<BaseDevicePoint>().eq("super_type", "0").eq("building_id", baseBuilding.getId()));

        //查询当日报警总数
        int alarmNum = fireInfoMapper.selectCount(new QueryWrapper<FireInfo>().eq("fire_type", "0").eq("building_id", baseBuilding.getId())
                .ge("last_time", todayData + " 00:00:00").le("last_time", todayData + " 23:59:59"));

        Date date = DateUtil.parseDate(todayData, "yyyy-MM-dd");
        AtomicReference<Integer> num1 = new AtomicReference<>(0);
        List<MisReportes> misReportesList = repeatedlyMisinformationService.queryMisReportesListJob(baseBuilding.getId(), date);
        misReportesList.forEach(misReportes -> {
            if (misReportes.getFlag().equals(1)) {
                num1.getAndSet(num1.get() + 1);
            }
        });
        Integer reusePointNum = num1.get();
        String alarmRate = "0";
        if (reusePointNum != 0 && num != 0) {
            //计算结果乘100，四舍五入保留10位小数
            alarmRate = new BigDecimal(reusePointNum + "").divide(new BigDecimal(num + ""), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100 + "")) + "";
        }

        List<FaultFireStatistics> ffs = faultFireStatisticsMapper.selectList(new QueryWrapper<FaultFireStatistics>()
                .eq("building_id", baseBuilding.getId()).eq("types", "2").eq("today_date", todayData));
        if (ffs != null && ffs.size() > 0) {
            ffs.get(0).setBuildingId(baseBuilding.getId());
            ffs.get(0).setBuildingName(baseBuilding.getBuildingName());
            ffs.get(0).setAlarmNum(alarmNum + "");
            ffs.get(0).setReusePointNum(reusePointNum + "");
            ffs.get(0).setAlarmRate(alarmRate);
            ffs.get(0).setUpdateTime(nowDate);
            faultFireStatisticsMapper.updateById(ffs.get(0));
        } else {
            FaultFireStatistics faultFireStatistics = new FaultFireStatistics();
            faultFireStatistics.setBuildingId(baseBuilding.getId());
            faultFireStatistics.setBuildingName(baseBuilding.getBuildingName());
            faultFireStatistics.setAlarmNum(alarmNum + "");
            faultFireStatistics.setReusePointNum(reusePointNum + "");
            faultFireStatistics.setAlarmRate(alarmRate);
            faultFireStatistics.setTodayDate(todayData);
            faultFireStatistics.setTypes("2");
            faultFireStatistics.setUpdateTime(nowDate);
            faultFireStatisticsMapper.insert(faultFireStatistics);
        }

        //***计算故障率***
        log.info("单店监控台故障率误报率统计 计算故障率，广场id：" + baseBuilding.getId());
        int faultNum = faultInfoMapper.selectCount(new QueryWrapper<FaultInfo>()
                .eq("building_id", baseBuilding.getId()).ge("last_time", todayData + " 00:00:00")
                .le("last_time", todayData + " 23:59:59"));
        //查询历史未处、处理中理故障
        Map<String, Object> param = new HashMap<>();
        LocalDateTime endTime = nowDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().minusDays(1);
        String endTimeStr= endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        param.put("buildingId", baseBuilding.getId());
        param.put("endTime", endTimeStr);
        int faultPointNum = firePageWindowMapper.selectFaultPointNum(param);

        //故障率
        String faultRate = "0";
        if (faultPointNum != 0 && num != 0) {
            //计算结果乘100，四舍五入保留10位小数
            faultRate = new BigDecimal(faultPointNum + "").divide(new BigDecimal(num + ""), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100)) + "";
        }
        List<FaultFireStatistics> ffsTwo = faultFireStatisticsMapper.selectList(new QueryWrapper<FaultFireStatistics>()
                .eq("building_id", baseBuilding.getId()).eq("types", "1").eq("today_date", todayData));
        if (ffsTwo != null && ffsTwo.size() > 0) {
            ffsTwo.get(0).setBuildingId(baseBuilding.getId());
            ffsTwo.get(0).setBuildingName(baseBuilding.getBuildingName());
            ffsTwo.get(0).setFaultNum(faultNum + "");
            ffsTwo.get(0).setFaultPointNum(faultPointNum + "");
            ffsTwo.get(0).setFaultRate(faultRate);
            ffsTwo.get(0).setUpdateTime(nowDate);
            faultFireStatisticsMapper.updateById(ffsTwo.get(0));
        } else {
            FaultFireStatistics faultFireStatistics = new FaultFireStatistics();
            faultFireStatistics.setBuildingId(baseBuilding.getId());
            faultFireStatistics.setBuildingName(baseBuilding.getBuildingName());
            faultFireStatistics.setFaultNum(faultNum + "");
            faultFireStatistics.setFaultPointNum(faultPointNum + "");
            faultFireStatistics.setFaultRate(faultRate);
            faultFireStatistics.setTodayDate(todayData);
            faultFireStatistics.setTypes("1");
            faultFireStatistics.setUpdateTime(nowDate);
            faultFireStatisticsMapper.insert(faultFireStatistics);
        }
        log.info("----每日剩余故障统计数据留痕--------------");
        if (faultPointNum > 0) {
            List<FaultDailyHistory> faultInfoList = firePageWindowMapper.selectFaultPointHistory(param);
            List<FaultDailyHistory> dailyHistories=new ArrayList<>();
            faultInfoList.forEach(faultDailyHistory -> {
                faultDailyHistory.setTodayDate(DateUtil.parseDate(todayData,"yyyy-MM-dd"));
                dailyHistories.add((faultDailyHistory));
            });
            faultDailyHistoryService.batChSaveHistory(dailyHistories);
        }
    }
}
