package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


import java.util.Date;

@Data
@TableName("face_user")
public class FaceUser {

    @TableId(type = IdType.UUID)
    private String id;

    @TableField("project_id")
    private String projectId;

    @TableField("username")
    private String username;

    @TableField("id_number")
    private String idNumber;

    @TableField("gender")
    private String gender;

    @TableField("user_category")
    private String userCategory;

    @TableField("phone")
    private String phone;

    @TableField("mobile")
    private String mobile;

    @TableField("email")
    private String email;

    @TableField("birthday")
    private Date birthday;

    @TableField("head_portrait")
    private String headPortrait;

    @TableField("position")
    private String position;

    @TableField("grade")
    private String grade;

    @TableField("user_status")
    private String userStatus;
}