package com.redxun.fire.core.controller.dutymanage;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.dutymanage.FaceRecognitionInspectPersonInfo;
import com.redxun.fire.core.entity.dutymanage.FaceRecognitionInspectPersonReviewInfo;
import com.redxun.fire.core.entity.dutymanage.InspectCloudConfig;
import com.redxun.fire.core.entity.dutymanage.PersonDutyInfo;
import com.redxun.fire.core.service.dutymanage.FaceRecognitionInfoService;
import com.redxun.fire.core.service.dutymanage.InspectCloudConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2025/2/13
 * @description
 */
@RestController
@RequestMapping("/inspectCloudConfig")
@Slf4j
public class InspectCloudConfigController {

    @Autowired
    InspectCloudConfigService configService;

    @Autowired
    FaceRecognitionInfoService faceRecognitionInfoService;

    @PostMapping(value = "queryPage")
    public JsonResult queryPage(@RequestBody QueryData queryData, HttpServletRequest request) {
        try {
            return configService.queryPage(queryData, request);
        } catch (Exception e) {
            log.error("值班查岗设置分页异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "saveCloudConfig")
    public JsonResult saveCloudConfig(@RequestBody List<InspectCloudConfig> cloudConfigList, HttpServletRequest request) {
        try {
            return configService.saveOrUpdateConfig(cloudConfigList, request);
        } catch (Exception e) {
            log.error("保存云端查岗设置信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "updateBuildingConfigById")
    public JsonResult updateBuildingConfigById(@RequestBody BaseBuilding building, HttpServletRequest request) {
        try {
            return configService.updateBuildingConfigById(building, request);
        } catch (Exception e) {
            log.error("广场查岗设置保存异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @GetMapping(value = "getCloudConfigByBuildingId")
    public JsonResult getCloudConfigByBuildingId(@RequestParam String buildingId) {
        try {
            return configService.getCloudConfigByBuildingId(buildingId);
        } catch (Exception e) {
            log.error("值班查岗设置信息查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @GetMapping(value = "getAlarmDetailById")
    public JsonResult getAlarmDetailById(@RequestParam String alarmId) {
        try {
            return configService.getAlarmDetailById(alarmId);
        } catch (Exception e) {
            log.error("报警详情查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }


    @PostMapping(value = "reviewDutyPerson")
    public JsonResult reviewDutyPerson(@RequestBody List<FaceRecognitionInspectPersonReviewInfo> personInfoList, HttpServletRequest request) {
        try {
            return configService.reviewDutyPerson(personInfoList, request);
        } catch (Exception e) {
            log.error("复核值班人员异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "alarmQueryPage")
    public JsonResult alarmQueryPage(@RequestBody QueryData queryData, HttpServletRequest request) {
        try {
            return faceRecognitionInfoService.queryPage(queryData, request);
        } catch (Exception e) {
            log.error("接警中心漏岗页面分页查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }


}
