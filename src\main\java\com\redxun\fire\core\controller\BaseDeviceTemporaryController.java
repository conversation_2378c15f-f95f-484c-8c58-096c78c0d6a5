package com.redxun.fire.core.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.feign.bpm.BpmClient;
import com.redxun.fire.core.consts.BPMConstants;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.service.building.impl.BaseBuildingServiceImpl;
import com.redxun.fire.core.service.common.IAdjustptService;
import com.redxun.fire.core.service.device.IBaseDeviceApplicationService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.other.IConfigRelationService;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-24
 */
@Slf4j
@RestController
@RequestMapping("/base-device-temporary")
public class BaseDeviceTemporaryController {

    @Autowired
    IBaseDeviceTemporaryService deviceTemporaryService;
    @Autowired
    IBaseDeviceApplicationService deviceApplicationService;
    @Autowired
    private IBaseDevicePointService baseDevicePointService;

    @Autowired
    IAdjustptService iAdjustptService;
    @Autowired
    IBaseDeviceApplicationService applicationService;
    @Autowired
    IConfigRelationService configRelationService;
    @Resource
    BpmClient bpmClient;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Autowired
    private BaseBuildingServiceImpl baseBuildingServiceImpl;

    @RequestMapping(value = "/addApplyDevPoints")
    public JsonResult addApplyDevPoints(HttpServletRequest request, @RequestBody @Validated(Update.class) BaseDeviceTemporaryList baseDeviceTemporaryList) {
        JsonResult jsonResult = deviceTemporaryService.addApplyInfo(request, baseDeviceTemporaryList);
        return jsonResult;
    }

    /**
     * 点位申请 发起申请流程
     * 传入临时表数据
     *
     * @param baseDeviceTemporaryList
     * @return
     */
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/addApplyDevPoints111")
    public JsonResult addApplyDevPoints111(HttpServletRequest request, @RequestBody @Validated(Update.class) BaseDeviceTemporaryList baseDeviceTemporaryList) {
        JsonResult jsonResult = JsonResult.Success();
        List<BaseDeviceTemporary> baseDeviceTemporaries = baseDeviceTemporaryList.getList();
        String applyId = baseDeviceTemporaryList.getApplyId();
        String applyType = baseDeviceTemporaryList.getApplyType();
        BaseDeviceTemporary baseDeviceTemporary = null;
        double precent = 0;
        int count = 0;

        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);


        int importNum;
        Map<String, Object> map = new HashMap<>();
        //正常申请时
        if (baseDeviceTemporaries != null && baseDeviceTemporaries.size() > 0 && StringUtils.isEmpty(applyId)) {
            //新增更新Temporary表
            if ("0".equals(applyType)) {

                // 获取临时表数据
                List<BaseDeviceTemporary> baseDeviceTemporaries1 = null;
                try {
                    baseDeviceTemporaries1 = deviceTemporaryService.getPointFileByApplyId(applyId);
                } catch (IOException e) {
                    throw new RuntimeException("文件未找到！");
                }
                importNum = baseDeviceTemporaries1.size();
                //获取公共参数
                baseDeviceTemporary = baseDeviceTemporaryList.getList().get(0);
                // 现有建筑的点位数
                int totalPointCount = baseDevicePointService.count(new QueryWrapper<BaseDevicePoint>().eq("building_id", baseDeviceTemporary.getBuildingId()));

                /*BaseDeviceApplication baseDeviceApplication = new BaseDeviceApplication();
                baseDeviceApplication.setId(applyId);
                baseDeviceApplication.setModulationProportion(String.format("%.2f", percent));
                baseDeviceApplication.setBuildingId(baseDeviceTemporary.getBuildingId());
                baseDeviceApplication.setBuildingName(baseDeviceTemporary.getBuildName());
                baseDeviceApplication.setModulationType("0");
                baseDeviceApplication.setModulationQuantity(baseDeviceTemporaries1.size());
                baseDeviceApplication.setApplicantTime(DateUtil.getCurrentDatetime());
                baseDeviceApplication.setApprovCondition("0");
                IUser currentUser = ContextUtil.getCurrentUser();
                if (currentUser != null) {
                    baseDeviceApplication.setCreateBy(currentUser.getUserId());
                    baseDeviceApplication.setProposer(currentUser.getFullName());
                }
                baseDeviceApplication.setCreateTime(DateUtil.getCurrentDatetime());
                applicationService.saveBaseDeviceApplication(baseDeviceApplication);*/
                precent = 100;
                if (baseDeviceTemporaries1.size() > 0) {
                    //获取点位总数
                    count = deviceTemporaryService.getPointCountByBuildId(baseDeviceTemporaries1.get(0).getBuildingId());
                }
                //跟新临时表点位状态
                deviceTemporaryService.updateBatchById(baseDeviceTemporaries);


                map.put("applyId", applyId);
                map.put("data", baseDeviceTemporaries1);
            } else {
                //进入修改删除等逻辑
                //获取公共参数
                baseDeviceTemporary = baseDeviceTemporaryList.getList().get(0);
                String buildingId = baseDeviceTemporary.getBuildingId();
                //获取点位总数
                count = deviceTemporaryService.getPointCountByBuildId(buildingId);
                //计算点位调整比例
                precent = baseDeviceTemporaryList.getList().size() * 1.0 / count * 100;
                BigDecimal bg = new BigDecimal(precent);
                double f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                //插入临时表
                Map<String, Object> maps = deviceTemporaryService.addDevPoints(baseDeviceTemporaryList.getList(), f1, applyType, applyId);
                List<BaseDeviceTemporary> deviceTemporaries = (List<BaseDeviceTemporary>) maps.get("list");
                importNum = deviceTemporaries.size();
                applyId = (String) maps.get("applyId");
                String flag = (String) maps.get("flag");
                String pointNumber = (String) maps.get("pointNumber");
                map.put("data", deviceTemporaries);
                map.put("applyId", applyId);
                if (StringUtils.isNotEmpty(flag)) {
                    //有未审批的
                    map.put("status", "2");
                    jsonResult.setData(map);
                    return jsonResult;
                } else if (StringUtils.isNotEmpty(pointNumber)) {
                    //没有点位编号
                    map.put("status", "3");
                    jsonResult.setData(map);
                    return jsonResult;
                }
            }
        } else if (baseDeviceTemporaries != null && baseDeviceTemporaries.size() > 0 && StringUtils.isNotEmpty(applyId)) {
            //退回重新申请时
            LambdaQueryWrapper<BaseDeviceTemporary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BaseDeviceTemporary::getApplyId, applyId);
            //根据申请id删除临时表点位
            deviceTemporaryService.remove(lambdaQueryWrapper);
            LambdaQueryWrapper<BaseDeviceApplication> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BaseDeviceApplication::getId, applyId);
            deviceApplicationService.remove(wrapper);
            String buildingId = baseDeviceTemporary.getBuildingId();
            //根据建筑id获取总点位
            count = deviceTemporaryService.getPointCountByBuildId(buildingId);
            //计算点位调整比例
            precent = baseDeviceTemporaries.size() * 1.0 / count * 100;
            BigDecimal bg = new BigDecimal(precent);
            double f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            //插入临时表
            Map<String, Object> maps = deviceTemporaryService.addDevPoints(baseDeviceTemporaries, f1, applyType, applyId);
            List<BaseDeviceTemporary> deviceTemporaries = (List<BaseDeviceTemporary>) maps.get("list");
            importNum = deviceTemporaries.size();
            applyId = (String) maps.get("applyId");
            String flag = (String) maps.get("flag");
            String pointNumber = (String) maps.get("pointNumber");
            map.put("data", deviceTemporaries);
            map.put("applyId", applyId);
            if (StringUtils.isNotEmpty(flag)) {
                //存在未审批点位
                map.put("status", "2");
                jsonResult.setData(map);
                return jsonResult;
            } else if (StringUtils.isNotEmpty(pointNumber)) {
                //没有点位编号
                map.put("status", "3");
                jsonResult.setData(map);
                return jsonResult;
            }
        } else {
            List<BaseDeviceTemporary> baseDeviceTemporaries1 = deviceTemporaryService.getPointByApplyId(applyId);
            precent = 100;
            importNum = baseDeviceTemporaries1.size();
            baseDeviceTemporary = baseDeviceTemporaries1.get(0);
            map.put("applyId", applyId);
            map.put("data", baseDeviceTemporaries1);
        }
        //设置流程参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("checkType", BPMConstants.CHECK_TYPE_POINT);
        jsonObject.put("systemHand", true);
        JSONObject pointAdjustRecord = new JSONObject();
        JSONObject formJson = new JSONObject();

        pointAdjustRecord.put("adjust_record_id", applyId);
        pointAdjustRecord.put("building_id", baseDeviceTemporary.getBuildingId());
        if(baseDeviceTemporary.getBuildingId()!=null&&!"".equals(baseDeviceTemporary.getBuildingId())){
            BaseBuilding bb=baseBuildingServiceImpl.getById(baseDeviceTemporary.getBuildingId());
            pointAdjustRecord.put("building_name", bb.getBuildingName());
        }else{
            pointAdjustRecord.put("building_name", baseDeviceTemporary.getBuildName());
        }
        pointAdjustRecord.put("building_name", baseDeviceTemporary.getBuildName());
        BigDecimal bg = new BigDecimal(precent);
        double f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        pointAdjustRecord.put("adjust_precent", f1);
        //0新增   1修改   公用
        if ("0".equals(applyType) || "1".equals(applyType)) {
            jsonObject.put("defId", BPMConstants.DEF_ID_POINT);
        } else if ("2".equals(applyType)) {//删除
            ConfigRelation configRelation = configRelationService.queryConfigByCodeOne("splc_4_dwjcbl");
            double configNum = Double.parseDouble(configRelation.getConfigStrVal());
            if (f1 > configNum) {
                jsonObject.put("defId", BPMConstants.S_DEF_ID_POINT);
            } else {
                jsonObject.put("defId", BPMConstants.N_DEF_ID_POINT);
            }
        } else if ("3".equals(applyType)) {//特批
            ConfigRelation configRelation = configRelationService.queryConfigByCodeOne("splc_5_dwjcbl");
            double configNum = Double.parseDouble(configRelation.getConfigStrVal());
            if (f1 > configNum) {
                jsonObject.put("defId", BPMConstants.SSP_DEF_ID_POINT);
            } else {
                jsonObject.put("defId", BPMConstants.NSP_DEF_ID_POINT);
            }
        }
        String userId = dataDto.getFullName();
        pointAdjustRecord.put("apply_user", userId);
        pointAdjustRecord.put("adjust_type", baseDeviceTemporary.getApplyType());
        pointAdjustRecord.put("adjust_count", count);
        formJson.put("point_adjust_record", pointAdjustRecord);
        jsonObject.put("formJson", JSONObject.toJSONString(formJson));
        //启动审批流程
        JsonResult bpmResult = bpmClient.startProcess(jsonObject);
        log.info("[流程]流程启动返回结果为{}", JSONObject.toJSONString(bpmResult));
//        System.out.println(bpmResult.getCode());
//        if (bpmResult.getCode() == 200) {
        if (bpmResult.getSuccess()) {
            map.put("status", 1);
        } else {
            log.info("申请流程失败，直接按审批失败处理，走审批失败逻辑。");
            map.put("status", 4);
            UpdateWrapper<BaseDeviceTemporary> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("approve_type", "2");
            updateWrapper.eq("apply_id", map.get("applyId").toString());
            boolean flag = deviceTemporaryService.update(updateWrapper);
            UpdateWrapper<BaseDeviceApplication> Wrapper = new UpdateWrapper<>();
            Wrapper.set("approv_condition", "2");
            Wrapper.eq("id", map.get("applyId").toString());
            applicationService.update(Wrapper);
        }
        jsonResult.setData(map);

        return jsonResult;
    }

    /**
     * 点位申请直接导入点位
     *
     * @param baseDeviceTemporaryList
     * @return
     */
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/addApplyDevPointsT")
    public JsonResult addApplyDevPointsT(HttpServletRequest request, @RequestBody @Validated(Update.class) BaseDeviceTemporaryList baseDeviceTemporaryList) {
        JsonResult jsonResult = JsonResult.Success();
        Map<String, Object> map = new HashMap<>();
        BaseDeviceTemporary baseDeviceTemporary = baseDeviceTemporaryList.getList().get(0);
        //获取总点位数
        int count = deviceTemporaryService.getPointCountByBuildId(baseDeviceTemporary.getBuildingId());
        double precent = baseDeviceTemporaryList.getList().size() * 1.0 / count * 100;
        //插入临时表
        Map<String, Object> maps = deviceTemporaryService.addDevPointsT(request, baseDeviceTemporaryList, precent);
        List<BaseDeviceTemporary> deviceTemporaries = (List<BaseDeviceTemporary>) maps.get("list");
        String applyId = (String) maps.get("applyId");
        map.put("data", deviceTemporaries);
        map.put("applyId", applyId);
        map.put("status", deviceTemporaries.size() > 0 ? (deviceTemporaries.size() == baseDeviceTemporaryList.getList().size() ? 2 : 1) : deviceTemporaries.size());
        jsonResult.setData(map);
//        BaseDeviceTemporary baseDeviceTemporary = baseDeviceTemporaryList.getList().get(0);
        String applyType = baseDeviceTemporary.getApplyType();
        baseDeviceTemporary.setApproveType("1");
        baseDeviceTemporary.setApplyId(applyId);
        if ("0".equals(applyType)) {
            //新增并且审批通过
            deviceTemporaryService.addApplyPoint(baseDeviceTemporary, new Date());
        } else if ("1".equals(applyType)) {
            //编辑并且审批通过
            deviceTemporaryService.editApplyPoint(baseDeviceTemporary);
        } else if ("2".equals(applyType)) {
            //删除并且审批通过
            deviceTemporaryService.deleteApplyPoint(baseDeviceTemporary);
        } else {
            deviceTemporaryService.updateStatusByApplyId(baseDeviceTemporary);
        }
        return jsonResult;
    }


    /**
     * 申请审批（同上）
     *
     * @param baseDeviceTemporary
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/approvePoint")
    @Transactional
    public JsonResult approvePoint(@RequestBody @Validated(Update.class) BaseDeviceTemporary baseDeviceTemporary) {
        JsonResult jsonResult = JsonResult.Success();
        String applyType = baseDeviceTemporary.getApplyType();
        String approveType = baseDeviceTemporary.getApproveType();

        if ("0".equals(applyType) && "1".equals(approveType)) {
            //新增并且审批通过
            jsonResult.setData(deviceTemporaryService.addApplyPoint(baseDeviceTemporary, new Date()));
        } else if ("1".equals(applyType) && "1".equals(approveType)) {
            //编辑并且审批通过
            jsonResult.setData(deviceTemporaryService.editApplyPoint(baseDeviceTemporary));
        } else if ("2".equals(applyType) && "1".equals(approveType)) {
            //删除并且审批通过
            jsonResult.setData(deviceTemporaryService.deleteApplyPoint(baseDeviceTemporary));
        } else {
            jsonResult.setData(deviceTemporaryService.updateStatusByApplyId(baseDeviceTemporary));
        }

        return jsonResult;
    }


    /**
     * 根据审批ID获取点位信息
     *
     * @param baseDeviceTemporary
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getApplyDotInfoById")
    public JsonResult getApplyDotInfoById(@RequestBody @Validated(Select.class) BaseDeviceTemporary baseDeviceTemporary) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(deviceTemporaryService.selectDevPointsByApplyId(baseDeviceTemporary));
        return jsonResult;
    }

    /**
     * 特批审批
     *
     * @param baseDeviceTemporary
     * @return
     */
    @Transactional
    @ResponseBody
    @RequestMapping(value = "/speciallyApprovePoint")
    public JsonResult speciallyApprovePoint(@RequestBody @Validated(Update.class) BaseDeviceTemporary baseDeviceTemporary) {
        JsonResult jsonResult = JsonResult.Success();
        String applyType = baseDeviceTemporary.getApplyType();
        String approveType = baseDeviceTemporary.getApproveType();
        //更新临时表申请状态
        if ("1".equals(applyType) && "1".equals(approveType)) {//新增并且审批通过
            jsonResult.setData(deviceTemporaryService.updateDotStatus(baseDeviceTemporary));
        }
        //审批拒绝时
        jsonResult.setData(deviceTemporaryService.updateStatusByApplyId(baseDeviceTemporary));
        return jsonResult;
    }

    /**
     * 根据申请ID查询申请点位信息
     *
     * @param baseDeviceTemporary
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getAppltDotInfoByApplyId")
    public JsonResult getAppltDotInfoByApplyId(@RequestBody @Validated(Update.class) BaseDeviceTemporary baseDeviceTemporary) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(deviceTemporaryService.selectDevPointsByApplyId(baseDeviceTemporary));
        return jsonResult;
    }


}
