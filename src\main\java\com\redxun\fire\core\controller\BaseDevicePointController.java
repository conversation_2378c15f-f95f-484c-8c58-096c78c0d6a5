package com.redxun.fire.core.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.api.feign.ConstructClient;
import com.redxun.common.base.entity.IUser;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.tool.StringUtils;
import com.redxun.common.utils.ContextUtil;
import com.redxun.fire.core.dto.common.PageRequestParam;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.pojo.dto.AddSelectedPointsDto;
import com.redxun.fire.core.pojo.dto.BaseListDto;
import com.redxun.fire.core.pojo.dto.FloorPointInfoSearchDto;
import com.redxun.fire.core.pojo.vo.DevicePointInfoVo;
import com.redxun.fire.core.pojo.vo.FloorPointInfoVo;
import com.redxun.fire.core.service.alarm.IWdFireSysService;
import com.redxun.fire.core.service.device.IWdDevConfigService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 建筑物设备资产信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Slf4j
@RestController
@RequestMapping("/base-device-point")
public class BaseDevicePointController {

    private final IBaseDevicePointService baseDevicePointService;

    public BaseDevicePointController(IBaseDevicePointService baseDevicePointService) {
        this.baseDevicePointService = baseDevicePointService;
    }

    @Resource
    public IBaseBuildingFloorService baseBuildingFloorService;

    /**
     * 通过楼层来进行所有已标点点位的获取
     *
     * @param id 楼层的id
     * @return {@link JsonResult}
     */
    @GetMapping("/getAllSelectedFloorPoints/{id}")
    @SuppressWarnings("unchecked")
    public JsonResult listAllSelectedFloorPoints(@PathVariable("id") String id) {
        JsonResult result = JsonResult.Success();
        List<FloorPointInfoVo> list = baseDevicePointService.listSelectedFloorPoints(id);
        result.setData(list);
        return result;
    }


    /**
     * 通过楼层来进行所有点位的获取 分页获取
     *
     * @param pageRequestParam 分页查询参数
     * @return {@link JsonResult}
     */
    @PostMapping("/getAllFloorPoints")
    @SuppressWarnings("unchecked")
    public JsonResult<IPage<DevicePointInfoVo>> listAllFloorPoints(@RequestBody @Validated PageRequestParam<FloorPointInfoSearchDto> pageRequestParam, BindingResult validResult) {
        if (validResult.hasErrors()) {
            JsonResult result = JsonResult.Fail("查询点位信息失败");
            List<ObjectError> allErrors = validResult.getAllErrors();
            StringBuilder sb = new StringBuilder();
            for (ObjectError error : allErrors) {
                if (error instanceof FieldError) {
                    sb.append("[" + ((FieldError) error).getField() + "]");
                }
                sb.append(error.getDefaultMessage());
                sb.append("\r\n");
            }
            result.setData(sb.toString());
            return result;
        }
        JsonResult result = JsonResult.Success();
        IPage<FloorPointInfoVo> devicePointInfoPage = baseDevicePointService.listAllFloorPoints(pageRequestParam);
        result.setData(devicePointInfoPage);
        return result;
    }


    /**
     * 已标记点位的删除 标记点位变成未标记点位
     *
     * @param id 点位的id
     * @return {@link JsonResult}
     */
    @GetMapping("/removeSelectedFloorPoints/{id}")
    @SuppressWarnings("unchecked")
    public JsonResult removeSelectedFloorPoints(HttpServletRequest request, @PathVariable("id") String id) {
        JsonResult result = JsonResult.Success();
        baseDevicePointService.removeSelectedFloorPoints(request, id);
        result.setData("点位移除成功");
        return result;
    }


    /**
     * 新增相应点位列表 描点相关
     *
     * @param addSelectedPoints {@link AddSelectedPointsDto} 点位信息列表
     * @return {@link JsonResult}
     */
    @PostMapping("/addFloorSelectedPoints")
    @SuppressWarnings("unchecked")
    public JsonResult<String> addSelectedFloorPoints(HttpServletRequest request, @RequestBody @Validated(Select.class) AddSelectedPointsDto addSelectedPoints) {
        JsonResult result = JsonResult.Success();
        baseDevicePointService.addSelectedPoints(request, addSelectedPoints);
        result.setData("标点信息成功");
        return result;
    }


    /**
     * 单店管理-点位管理-消防主机点位-恢复正常和批量恢复
     *
     * @param idList id列表
     * @return {@link JsonResult}
     */
    @PostMapping("batchRecover")
    @SuppressWarnings("unchecked")
    public JsonResult<String> batchRecover(HttpServletRequest request, @RequestBody @Validated(Update.class) BaseListDto idList) {
        try {
            String res = baseDevicePointService.batchRecover(request, idList);
            if (StrUtil.isBlank(res)) {
                JsonResult result = JsonResult.Success();
                result.setData("恢复正常成功");
                return result;
            } else {
                return JsonResult.Fail(res);
            }
        } catch (Exception e) {
            log.error("消防主机点位恢复正常功能异常：", e);
            return JsonResult.Fail(e.getMessage());
        }
//        return JsonResult.Fail("请求异常");
    }


    /**
     * 消防主机点位-正常点位列表
     *
     * @param queryData 查询数据
     * @return JsonPageResult
     */
    @PostMapping("/normalPointList")
    public JsonPageResult normalPointList(HttpServletRequest request, @RequestBody QueryData queryData) {
        String buildingId = request.getParameter("building_id");
        if (StringUtils.isBlank(buildingId)) {
            throw new RuntimeException("建筑物id必传");
        }
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageNo() < 1) {
            queryData.setPageSize(10);
        }
        return baseDevicePointService.pointList(buildingId, queryData);
    }

    /**
     * 消防主机点位-特殊点位列表
     *
     * @param queryData 查询数据
     * @return JsonPageResult
     */
    @PostMapping("/specialPointList")
    public JsonPageResult specialPointList(HttpServletRequest request, @RequestBody QueryData queryData) {
        String buildingId = request.getParameter("building_id");
        if (StringUtils.isBlank(buildingId)) {
            throw new RuntimeException("建筑物id必传");
        }
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageNo() < 1) {
            queryData.setPageSize(10);
        }
        return baseDevicePointService.pointList(buildingId, queryData);
    }

    /**
     * 消防主机点位-特殊点位列表
     *
     * @param queryData 查询数据
     * @return JsonPageResult
     */
    @PostMapping("/exportSpecialPointList")
    public void exportSpecialPointList(HttpServletRequest request, HttpServletResponse response, @RequestBody QueryData queryData) {
        String buildingId = request.getParameter("building_id");
        if (StringUtils.isBlank(buildingId)) {
            throw new RuntimeException("建筑物id必传");
        }
        if (null == queryData.getPageNo() || queryData.getPageNo() < 1) {
            queryData.setPageNo(1);
        }
        if (null == queryData.getPageSize() || queryData.getPageNo() < 1) {
            queryData.setPageSize(10);
        }
        try {
            baseDevicePointService.exportSpecialPointList(buildingId, queryData, response);
        } catch (Exception e) {
            log.error("导出异常：", e);
        }
    }

    /**
     * 数据导出-非维保点位-待补全点位
     *
     * @param queryData 查询数据
     * @return JsonPageResult
     */
    @PostMapping("/selectList")
    public JsonPageResult selectList(HttpServletRequest request, @RequestBody QueryData queryData) {
        return baseDevicePointService.selectList(request, queryData);
    }

    /**
     * 根据ID获取点位信息
     *
     * @param id 点位id
     * @return JsonResult
     */
    @GetMapping("/getPointById/{id}")
    public JsonResult<BaseDevicePoint> getPointById(@PathVariable String id) {
        BaseDevicePoint baseDevicePoint = baseDevicePointService.getById(id);
        if (null == baseDevicePoint.getFloorId()) {
            BaseBuildingFloor baseBuildingFloor = baseBuildingFloorService.getOne(new LambdaQueryWrapper<BaseBuildingFloor>()
                    .eq(BaseBuildingFloor::getBuildingId, baseDevicePoint.getBuildingId())
                    .eq(BaseBuildingFloor::getFloor, baseDevicePoint.getFloor())
                    .eq(BaseBuildingFloor::getFloorNum, "0")
                    .eq(BaseBuildingFloor::getEnabled, 0).last(" limit 1"));
            if (null != baseBuildingFloor) {
                baseDevicePoint.setFloorId(baseBuildingFloor.getId());
            }
        }
        return JsonResult.Success().setData(baseDevicePoint);
    }

    @PostMapping("/updateById")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult<String> updateById(@RequestBody BaseDevicePoint baseDevicePoint, HttpServletRequest request) {
        try {
            return baseDevicePointService.updatePointDataById(baseDevicePoint, request);
        } catch (Exception e) {
            log.error("修改点位异常：", e);
            return JsonResult.getFailResult(e.getMessage());
        }
    }

    /**
     * 获取设备列表
     *
     * @param baseDevicePoint
     * @return
     */
    @PostMapping(value = "/selectDevicePointList")
    @ApiOperation("获取设备列表信息")
    public JsonResult<BaseDevicePoint> selectDevicePointList(@RequestBody BaseDevicePointExtend baseDevicePoint) {
        JsonResult result = JsonResult.Success();
        List<BaseDevicePoint> list = baseDevicePointService.selectDevicePointList(baseDevicePoint);
        result.setData(list);
        return result;
    }

    @GetMapping(value = "/syncPoint")
    public JsonResult syncPoint(@RequestParam String buildingId) {
        return baseDevicePointService.syncPoint(buildingId);
    }

    @GetMapping(value = "/getSelectByType")
    public JsonResult getSelectByType(@RequestParam String type, @RequestParam String buildingId, @RequestParam(required = false) String parentId) {
        return baseDevicePointService.getSelectByType(type, buildingId, parentId);
    }

    @GetMapping(value = "/getCameraDomain")
    public JsonResult getCameraDomain(@RequestParam String ipAddress){
        return baseDevicePointService.getCameraDomain(ipAddress);
    }

}
