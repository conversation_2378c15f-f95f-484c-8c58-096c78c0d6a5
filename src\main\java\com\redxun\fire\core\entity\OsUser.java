package com.redxun.fire.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OsUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId("USER_ID_")
    private String userId;

    /**
     * 姓名
     */
    @TableField("FULLNAME_")
    private String fullname;

    /**
     * 用户编号
     */
    @TableField("USER_NO_")
    private String userNo;

    /**
     * 密码
     */
    @TableField("PWD_")
    private String pwd;

    /**
     * 入职时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "ENTRY_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryTime;

    /**
     * 离职时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "QUIT_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date quitTime;
    /**
     * 用户类型
     */
    @TableField("USER_TYPE_")
    private String userType;

    /**
     * 来源	            system,系统添加,import,导入,grade,分级添加的
     */
    @TableField("FROM_")
    private String from;
    /**
     * 部门名称
     */
    @TableField("DEPARTMENT_NAME_")
    private String departmentName;

    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "BIRTHDAY_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthday;
    /**
     * 姓别
     */
    @TableField("SEX_")
    private String sex;

    /**
     * 手机
     */
    @TableField("MOBILE_")
    private String mobile;

    /**
     * 邮件
     */
    @TableField("EMAIL_")
    private String email;

    /**
     * 地址
     */
    @TableField("ADDRESS_")
    private String address;

    /**
     * 紧急联系人
     */
    @TableField("URGENT_")
    private String urgent;

    /**
     * 是否同步到微信
     */
    @TableField("SYNC_WX_")
    private String syncWx;

    /**
     * 紧急联系人手机
     */
    @TableField("URGENT_MOBILE_")
    private String urgentMobile;

    /**
     * QQ号
     */
    @TableField("QQ_")
    private String qq;

    /**
     * 照片
     */
    @TableField("PHOTO_")
    private String photo;

    /**
     * OPEN_ID
     */
    @TableField("OPEN_ID_")
    private String openId;

    /**
     * 企业微信OPENID
     */
    @TableField("WX_OPEN_ID_")
    private String wxOpenId;

    /**
     * 用户在当前机构的状态	            IN_JOB=在职	            OUT_JOB=离职
     */
    @TableField("STATUS_")
    private String status;

    /**
     * 租用用户Id
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "UPDATE_TIME_", fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 钉钉ID
     */
    @TableField("DD_ID_")
    private String ddId;

    /**
     * 用户分类 1 万信用户   0 平台用户
     */
    @TableField("USER_CATEGORY_")
    private String userCategory;

    /**
     * 指向的组织机构 这个是如果该用户是维保或者物业的用户的话 是和该组织直接进行绑定 这个地址指向物业或者维保的地址
     */
    @TableField("REF_GROUP_")
    private String refGroup;

    /**
     * 相关单位用户的权限 是否是管理员 0 普通用户  1 管理员
     */
    @TableField("RELATE_ROLE_")
    private String relateRole;

    /**
     * 万信号
     */
    @TableField("WX_USER_NO_")
    private String wxUserNo;

    /**
     * 万信职务key
     */
    @TableField("WX_POS_KEY_")
    private String wxPosKey;

    /**
     * 万信职务value
     */
    @TableField("WX_POS_VALUE_")
    private String wxPosValue;

    /**
     * 用于推送的手机cid
     */
    @TableField("REGISTRATION_ID")
    private String registrationId;

    /**
     * 层级 1总部,2地方
     */
    @TableField("RANK_LEVEL_")
    private String rankLevel;

    /**
     * 万中台UserId
     */
    @TableField("WZT_USER_ID_")
    private String wztUserId;
    /**
     * 角色code
     */
    @TableField("ROLE_KEY_")
    private String roleKey;

    /**
     * 集团
     */
    @TableField("GROUP_ORG_")
    private String groupOrg;
    /**
     * 大区
     */
    @TableField("REGION_")
    private String region;
    /**
     * 城市公司
     */
    @TableField("CITY_")
    private String city;
    /**
     * 广场
     */
    @TableField("PIAZZA_")
    private String piazza;
    /**
     * 集团
     */
    @TableField("GROUP_ORG_NAME_")
    private String groupOrgName;
    /**
     * 大区
     */
    @TableField("REGION_NAME_")
    private String regionName;
    /**
     * 城市公司
     */
    @TableField("CITY_NAME_")
    private String cityName;
    /**
     * 广场
     */
    @TableField("PIAZZA_NAME_")
    private String piazzaName;
    /**
     * 万信层级
     */
    @TableField("LEVEL_AYER_")
    private String levelAyer;
    /**
     * 非商管部门ID(逗号分隔)
     */
    @TableField("DEPARTMENT_IDS_")
    private String departmentIds;
    /**
     * 是否非万(1:万达人员,2:非商管,3:非万人员)
     */
    @TableField("USER_FW_")
    private String userFw;
    //关系
    @TableField(exist = false)
    private List<Map<String,String>> roleList;
    /**
     * 角色id
     */
    @TableField(exist = false)
    private String userRoleId;
    /**
     * 角色key
     */
    @TableField(exist = false)
    private String userRoleKey;

    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String userRoleName;
    /**
     * 广场id
     */
    @TableField(exist = false)
    private String projectId;
    /**
     * 单位
     */
    @TableField(exist = false)
    private String unitName;
    /**
     * 默认角色
     */
    @TableField(exist = false)
    private String defaultRole;
    /**
     *
     */
    @TableField("WX_POSITION_NAME_")
    private String positionName;

    /**
     * 万中台商户Id
     */
    @TableField("WZT_MERCHANT_ID")
    private String wztMerchantId;
    /**
     * 万中台商户名称
     */
    @TableField("WZT_MERCHANT_NAME")
    private String wztMerchantName;
}
