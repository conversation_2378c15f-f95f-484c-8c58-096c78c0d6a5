package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户组(OsGroup)实体类
 *
 * <AUTHOR>
 * @since 2024-04-12 10:10:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("os_group")
@Accessors(chain = true)
public class OsGroupNew {
    /**
     * 来自用户类型
     */
    public static String FROM_USER_TYPE="_USER_TYPE";

    /**
     * 是否权限分级
     */
    public static final String IS_GRADE="isGrade";

    private static final long serialVersionUID = 4497149010220586111L;

    @TableId(value = "GROUP_ID_",type = IdType.INPUT)
    private String groupId;

    @TableField(value = "DIM_ID_")
    private String dimId;
    @TableField(value = "NAME_")
    private String name;
    @TableField(value = "KEY_")
    private String key;
    @TableField(value = "RANK_LEVEL_")
    private Integer rankLevel;
    @TableField(value = "STATUS_")
    private String status;
    @TableField(value = "DESCP_")
    private String descp;
    @TableField(value = "SN_")
    private Integer sn;
    @TableField(value = "PARENT_ID_")
    private String parentId;
    @TableField(value = "PATH_")
    private String path;
    @TableField(value = "AREA_CODE_")
    private String areaCode;
    @TableField(value = "FORM_")
    private String form;
    @TableField(value = "SYNC_WX_")
    private Integer syncWx;
    @TableField(value = "WX_PARENT_PID_")
    private Integer wxParentPid;
    @TableField(value = "WX_PID_")
    private Integer wxPid;
    @TableField(value = "IS_DEFAULT_")
    private String isDefault;
    @TableField(value = "IS_LEAF_")
    private Short isLeaf;
    @TableField(value = "DD_PARENT_ID_")
    private String ddParentId;
    @TableField(value = "DD_ID_")
    private String ddId;
    @TableField(value = "WZT_MERCHANT_ID")
    private String wztMerchantId;
    /**
     * 是否在pc端进行展示   0 展示  1 不展示 不展示的就是那些不在页面上显示的那些团队 包括维保团队或者商管团队 这边不展示的还多个部分团队 为了把总部团队进行区分(广场的一部分团队 其他的一部分团队)
     */
    @TableField(value = "PC_SHOW_")
    private String pcShow;

    /**
     * 黑名单 默认是0 正常   1是黑名单
     */
    @TableField(value = "BLOCK_")
    private String block;

    /**
     * 组织机构类型  0 默认 1 维保  2 物业
     */
    @TableField(value = "GROUP_TYPE_")
    private String groupType;

    /**
     * 工商注册号
     */
    @TableField(value = "REGISTER_NO_")
    private String registerNo;

    /**
     * 组织机构代码
     */
    @TableField(value = "ORGAN_CODE_")
    private String organCode;

    /**
     * 公司类型 1 有限责任公司  2 股份有限公司
     */
    @TableField(value = "ORGAN_TYPE_")
    private String organType;

    /**
     * 注册地址
     */
    @TableField(value = "REGISTER_ADDRESS_")
    private String registerAddress;


    /**
     * 行业
     */
    @TableField(value = "INDUSTRY_")
    private String industry;

    /**
     * 登记机关
     */
    @TableField(value = "REGISTRATION_AUTHORITY_")
    private String registrationAuthority;

    /**
     * 营业开始时间
     */
    @TableField(value = "START_TIME_")
    private Date startTime;

    /**
     * 营业结束时间
     */
    @TableField(value = "END_TIME_")
    private Date endTime;

    /**
     * 组织的类型 1 广场 0 其他 给个default 1
     */
    @TableField(value = "FORMAT_")
    private String format;

    @TableField(value = "BUILDING_ID_")
    private String buildingId;

}

