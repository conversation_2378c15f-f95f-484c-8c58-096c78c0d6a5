package com.redxun.fire.core.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.pointalarmstatistic.PointAlarmInfo;
import com.redxun.fire.core.mapper.PointAlarmInfoMapper;
import com.redxun.fire.core.pojo.vo.FireInfoSyncVo;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.pointalarmstatistic.PointAlarmInfoService;
import com.redxun.fire.core.service.pointalarmstatistic.PointYearlyAlarmStatisticService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2025/6/12
 * @description
 */
@Slf4j
@Service
public class FireInfoSyncJob extends IJobHandler {

    @Resource
    PointAlarmInfoMapper pointAlarmInfoMapper;

    @Autowired
    PointAlarmInfoService pointAlarmInfoService;

    @Autowired
    PointYearlyAlarmStatisticService statisticService;

    @Autowired
    IBaseBuildingService baseBuildingService;

    @Override
    @XxlJob("FireInfoSyncJob")
    public void execute() throws Exception {
        try {
            log.info("-----------------火警数据同步开始----------------------");
            final val jobParam = XxlJobHelper.getJobParam();
            log.info("FireInfoSyncJob execute, jobParam:{}", jobParam);

            final val list = baseBuildingService.list();
            final val syncTable = pointAlarmInfoMapper.getSyncTable();
            log.info("---------------查询到得需要同步得火警数据表为{}-------------------------------------", JSONObject.toJSONString(syncTable));
            if (CollUtil.isNotEmpty(syncTable)) {
                for (String tableName : syncTable) {
                    long s = System.currentTimeMillis();
                    log.info("-----火警数据表{}开始同步-------", tableName);
                    if (CollUtil.isNotEmpty(list)) {
                        for (BaseBuilding building : list) {
                            long ss = System.currentTimeMillis();
                            final val fireInfoSyncVos = pointAlarmInfoMapper.selectSyncDataByTableAndBuildingId(tableName, building.getId());
                            log.info("开始同步建筑{}的火警数据, 表名为{},查询到得火警数据个数为{}", building.getId(), tableName, fireInfoSyncVos.size());
                            if (CollUtil.isNotEmpty(fireInfoSyncVos)) {
                                Map<String, Long> countMap = new HashMap<>();
                                List<PointAlarmInfo> pointAlarmInfoList = new ArrayList<>();
                                for (FireInfoSyncVo fireInfoSyncVo : fireInfoSyncVos) {
                                    String lastTime = fireInfoSyncVo.getLastTime();
                                    PointAlarmInfo pointAlarmInfo = new PointAlarmInfo();
                                    pointAlarmInfo.setPointId(fireInfoSyncVo.getPointId());
                                    pointAlarmInfo.setId(fireInfoSyncVo.getId());
                                    pointAlarmInfo.setBuildingId(building.getId());
                                    pointAlarmInfo.setAlarmType("正常".equals(fireInfoSyncVo.getBuildingStatusStr()) ? "1":"2");
                                    if (StrUtil.isNotBlank(lastTime)) {
                                        pointAlarmInfo.setAlarmTime(DateUtil.parseDateTime(lastTime));
                                        pointAlarmInfo.setAlarmYear(lastTime.substring(0, 4));
                                        pointAlarmInfo.setAlarmMonth(lastTime.substring(0, 7));
                                    }
                                    pointAlarmInfoList.add(pointAlarmInfo);
                                    String key = fireInfoSyncVo.getPointId() + "&" + pointAlarmInfo.getAlarmYear() + "&" + pointAlarmInfo.getAlarmType();
                                    countMap.put(key, countMap.getOrDefault(key, 0L) + 1);
                                    if (pointAlarmInfoList.size() >= 200) {
                                        pointAlarmInfoService.saveOrUpdateBatch(pointAlarmInfoList);
                                        pointAlarmInfoList.clear();
                                    }
                                }
//                                log.info("-----------------开始刷新点位统计数据，建筑id为{}----------------------", building.getId());
//                                if (CollUtil.isNotEmpty(countMap)) {
//                                    Set<String> pointSet = new HashSet<>();
//                                    for (Map.Entry<String, Long> stringLongEntry : countMap.entrySet()) {
//                                        String[] split = stringLongEntry.getKey().split("&");
//                                        if (!pointSet.add(split[0])) {
//                                            continue;
//                                        }
//                                        final val one = statisticService.getOne(new LambdaQueryWrapper<PointYearlyAlarmStatistic>()
//                                                .eq(PointYearlyAlarmStatistic::getPointId, split[0])
//                                                .eq(PointYearlyAlarmStatistic::getYear, split[1]));
//                                        long alarmNum = 0L;
//                                        long testNum = 0L;
//                                        String key = split[0] + "&" + split[1] + "&";
//                                        alarmNum += countMap.getOrDefault(key + "1", 0L);
//                                        testNum += countMap.getOrDefault(key + "2", 0L);
//                                        if (ObjectUtil.isNotEmpty(one)) {
//                                            alarmNum += one.getAlarmNum();
//                                            testNum += one.getTestNum();
//                                            one.setAlarmNum(alarmNum);
//                                            one.setTestNum(testNum);
//                                            if (testNum > 0) {
//                                                one.setPointState("green");
//                                            } else {
//                                                int i = Integer.parseInt(split[1]);
//                                                i--;
//                                                final val lastOne = statisticService.getOne(new LambdaQueryWrapper<PointYearlyAlarmStatistic>()
//                                                        .eq(PointYearlyAlarmStatistic::getPointId, split[0])
//                                                        .eq(PointYearlyAlarmStatistic::getYear, i + ""));
//                                                if (ObjectUtil.isNotEmpty(lastOne)) {
//                                                    final val testNum1 = lastOne.getTestNum();
//                                                    final val alarmNum1 = lastOne.getAlarmNum();
//                                                    if (testNum1 > 0) {
//                                                        one.setPointState("yellow");
//                                                    } else {
//                                                        if (alarmNum1 < 0) {
//                                                            one.setPointState("grey");
//                                                        } else {
//                                                            one.setPointState("red");
//                                                        }
//                                                    }
//                                                } else {
//                                                    one.setPointState("grey");
//                                                }
//                                            }
//                                            statisticService.updateById(one);
//                                        } else {
//                                            PointYearlyAlarmStatistic pointYearlyAlarmStatistic = new PointYearlyAlarmStatistic();
//                                            pointYearlyAlarmStatistic.setPointId(split[0]);
//                                            pointYearlyAlarmStatistic.setYear(split[1]);
//                                            pointYearlyAlarmStatistic.setBuildingId(building.getId());
//                                            pointYearlyAlarmStatistic.setAlarmNum(alarmNum);
//                                            pointYearlyAlarmStatistic.setTestNum(testNum);
//                                            if (testNum > 0) {
//                                                pointYearlyAlarmStatistic.setPointState("green");
//                                            } else {
//                                                int i = Integer.parseInt(split[1]);
//                                                i--;
//                                                final val lastOne = statisticService.getOne(new LambdaQueryWrapper<PointYearlyAlarmStatistic>()
//                                                        .eq(PointYearlyAlarmStatistic::getPointId, split[0])
//                                                        .eq(PointYearlyAlarmStatistic::getYear, i + ""));
//                                                if (ObjectUtil.isNotEmpty(lastOne)) {
//                                                    final val testNum1 = lastOne.getTestNum();
//                                                    final val alarmNum1 = lastOne.getAlarmNum();
//                                                    if (testNum1 > 0) {
//                                                        pointYearlyAlarmStatistic.setPointState("yellow");
//                                                    } else {
//                                                        if (alarmNum1 < 0) {
//                                                            pointYearlyAlarmStatistic.setPointState("grey");
//                                                        } else {
//                                                            pointYearlyAlarmStatistic.setPointState("red");
//                                                        }
//                                                    }
//                                                } else {
//                                                    pointYearlyAlarmStatistic.setPointState("grey");
//                                                }
//                                            }
//                                            statisticService.save(pointYearlyAlarmStatistic);
//                                        }
//                                    }
//                                }
                            }
                            long ee = System.currentTimeMillis();
                            log.info("同步建筑{}火警数据结束, 表名为{}，时间为{}", building.getId(), tableName, (ee -ss));
                        }
                        long e = System.currentTimeMillis();
                        log.info("-----火警数据表{}同步结束，时间为{}-------", tableName, (e -s));
                    }
                    pointAlarmInfoMapper.updateSyncTableState(tableName);
                }
            }
        } catch (Exception e) {
            log.error("FireInfoSyncJob execute error:", e);
        }
    }
}
