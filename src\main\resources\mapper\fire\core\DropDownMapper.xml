<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.DropDownMapper">

    <resultMap id="DropDown" type="com.redxun.fire.core.entity.DropDown">
                <id property="dicId" column="DIC_ID_" jdbcType="VARCHAR"/>
                <result property="createBy" column="CREATE_BY_" jdbcType="VARCHAR"/>
                <result property="createDepId" column="CREATE_DEP_ID_" jdbcType="VARCHAR"/>
                <result property="createTime" column="CREATE_TIME_" jdbcType="TIMESTAMP"/>
                <result property="descp" column="DESCP_" jdbcType="VARCHAR"/>
                <result property="name" column="NAME_" jdbcType="VARCHAR"/>
                <result property="parentId" column="PARENT_ID_" jdbcType="VARCHAR"/>
                <result property="path" column="PATH_" jdbcType="VARCHAR"/>
                <result property="sn" column="SN_" jdbcType="NUMERIC"/>
                <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR"/>
                <result property="treeId" column="TREE_ID_" jdbcType="VARCHAR"/>
                <result property="updateBy" column="UPDATE_BY_" jdbcType="VARCHAR"/>
                <result property="updateTime" column="UPDATE_TIME_" jdbcType="TIMESTAMP"/>
                <result property="value" column="VALUE_" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="query" resultType="com.redxun.fire.core.entity.DropDown" parameterType="java.util.Map">
        select CREATE_BY_,CREATE_DEP_ID_,CREATE_TIME_,DESCP_,DIC_ID_,NAME_,PARENT_ID_,PATH_,SN_,TENANT_ID_,TREE_ID_,UPDATE_BY_,UPDATE_TIME_,VALUE_ from drop_down
        <where>
            <if test="@rx.Ognl@isNotEmpty(w.whereSql)">
                ${w.whereSql}
            </if>
        </where>
        <if test="@rx.Ognl@isNotEmpty(w.orderBySql)">
            ORDER BY ${w.orderBySql}
        </if>
        <if test="@rx.Ognl@isEmpty(w.orderBySql)">
            ORDER BY  DIC_ID_ DESC
        </if>
    </select>

    <!--根据treeId查询Name-->
    <select id="queryNameByTreeId" parameterType="string" resultType="com.redxun.fire.core.entity.DropDown">
        SELECT * FROM drop_down WHERE `TREE_ID_` = #{treeId}
    </select>

    <select id="queryDictVoByTreeId" resultType="com.redxun.fire.core.pojo.vo.DropDownDictVo">
        SELECT NAME_, VALUE_ FROM drop_down WHERE `TREE_ID_` = #{treeId}
    </select>

    <!--根据buildingId查询Name-->
    <select id="queryFloorDropDown" parameterType="string" resultType="com.redxun.fire.core.pojo.vo.DropDownVo">
        SELECT id,floor_name showName FROM base_building_floor WHERE building_id = #{buildingId}
    </select>
    <!--查询设备类型Name-->
    <select id="queryDevDropDown" resultType="com.redxun.fire.core.pojo.vo.DropDownVo">
        SELECT dev_code id,dev_name showName FROM wd_dev_config
    </select>
    <!--查询消防Name-->
    <select id="queryFireDropDown" resultType="com.redxun.fire.core.pojo.vo.DropDownVo">
        SELECT  distinct fire_sys_name as showName, id FROM wd_fire_sys
    </select>
    <!--查询防火分区-->
    <select id="queryFireAreaDropDown" resultType="com.redxun.fire.core.pojo.vo.DropDownVo">
        SELECT distinct zone_id id, zone_name showName FROM base_device_point where zone_id is not null and zone_name is not null
    </select>

</mapper>


