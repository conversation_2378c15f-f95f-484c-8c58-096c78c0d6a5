
package com.redxun.fire.core.kitchen.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.redxun.api.feign.AlarmPointClient;
import com.redxun.api.feign.ConstructClient;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.FireInfo;
import com.redxun.fire.core.enums.KitchenDeviceTypeEnum;
import com.redxun.fire.core.influxdb.dao.InfluxDataDao;
import com.redxun.fire.core.influxdb.entity.CloseInfluxData;
import com.redxun.fire.core.kitchen.dto.KitchenDeviceBasicDto;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceBasic;
import com.redxun.fire.core.kitchen.entity.KitchenEquipStat;
import com.redxun.fire.core.kitchen.entity.KitchenSignA;
import com.redxun.fire.core.kitchen.entity.KitchenSignB;
import com.redxun.fire.core.kitchen.fvo.*;
import com.redxun.fire.core.kitchen.mapper.KitchenDeviceBasicMapper;
import com.redxun.fire.core.kitchen.mapper.KitchenMonitorMapper;
import com.redxun.fire.core.mapper.BaseBuildingFloorMapper;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.FireInfoMapper;
import com.redxun.fire.core.service.kitchen.KitchenDeviceRedisService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * [厨房设备检测数据统计表]业务服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KitchenMonitorServiceImpl {

    @Resource
    private OrgMiddleServiceImpl orgMiddleService;

    @Resource
    OrgManageClient orgManageClient;

    @Resource
    KitchenMonitorMapper kitchenMonitorMapper;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    KitchenSignAServiceImpl kitchenSignAService;

    @Resource
    KitchenSignBServiceImpl kitchenSignBService;

    @Resource
    InfluxDataDao influxDataDao;

    @Autowired
    KitchenDeviceRedisService kitchenDeviceRedisService;

    @Resource
    KitchenDeviceBasicMapper kitchenDeviceBasicMapper;

    @Resource
    KitchenEquipStatServiceImpl kitchenEquipStatService;

    @Resource
    BaseDevicePointMapper baseDevicePointMapper;

    @Resource
    BaseBuildingFloorMapper baseBuildingFloorMapper;

    @Resource
    FireInfoMapper fireInfoMapper;

    @Autowired
    ConstructClient constructClient;

    /**
     * 获取厨房检测点位数量
     *
     * @param param
     * @return
     */
    public JsonResult getPointCountInfo(PointReqFvo param) {
        String buildingId = param.getBuildingId();
        if (StringUtils.isBlank(buildingId)) {
            return JsonResult.getFailResult("建筑id不能为空");
        }
        LambdaQueryWrapper<KitchenEquipStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenEquipStat::getBuildingId, buildingId);
        queryWrapper.eq(KitchenEquipStat::getDeleted, "0");
        queryWrapper.orderByDesc(KitchenEquipStat::getCreateTime);
        queryWrapper.last("LIMIT 1");
        KitchenEquipStat kitchenEquipStat = kitchenEquipStatService.getOne(queryWrapper);
        JSONObject object = new JSONObject();
        if (kitchenEquipStat != null) {
            object.put("id", kitchenEquipStat.getId());
            object.put("buildingId", kitchenEquipStat.getBuildingId());
            object.put("pointCount", kitchenEquipStat.getPointCount());
            object.put("storeCount", kitchenEquipStat.getEquipStoreCount());
            object.put("normalPointCount", kitchenEquipStat.getNormalPoint());
            object.put("exceptionPointCount", kitchenEquipStat.getOutagePoint() + kitchenEquipStat.getFaultPoint() + kitchenEquipStat.getOfflinePoint() + kitchenEquipStat.getElectricalAnomalyPoint());
        }
        //获取原消防模块监测数量
        object.put("oldPointCount", getOldPointCount(buildingId));
        return JsonResult.getSuccessResult(object);
    }

    @Autowired
    AlarmPointClient alarmPointClient;

    private Integer getOldPointCount(String buildingId) {
        PointRequest param = new PointRequest();
        param.setBuildIngId(buildingId);
        List<BaseDevicePointFvo> pageList = kitchenMonitorMapper.selectOldPointList(param, null, null);
        if(pageList != null) {
            return pageList.size();
        }
        return 0;
    }

    /**
     * 所属楼座枚举查询
     *
     * @param param
     * @return
     */
    public JsonResult getWztBuildingInfo(PointReqFvo param) {
        String buildingId = param.getBuildingId();
        if (StringUtils.isBlank(buildingId)) {
            return JsonResult.getFailResult("建筑id不能为空");
        }
        /*LambdaQueryWrapper<BaseDevicePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseDevicePoint::getBuildingId, buildingId);
        queryWrapper.select(BaseDevicePoint::getId, BaseDevicePoint::getWztBuilding, BaseDevicePoint::getWztBuildingName);
        List<BaseDevicePoint> baseDevicePoints = baseDevicePointMapper.selectList(queryWrapper);*/
        LambdaQueryWrapper<BaseBuildingFloor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseBuildingFloor::getWztConstructId, BaseBuildingFloor::getWztConstructName);
        queryWrapper.eq(BaseBuildingFloor::getBuildingId, buildingId);
        queryWrapper.eq(BaseBuildingFloor::getEnabled, "0");
        List<BaseBuildingFloor> baseBuildingFloors = baseBuildingFloorMapper.selectList(queryWrapper);
        Map<String, String> wztBuildIngMap = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(baseBuildingFloors)) {
            for (BaseBuildingFloor dto : baseBuildingFloors) {
                if(dto != null) {
                    String wztBuilding = dto.getWztConstructId();
                    String wztBuildingName = dto.getWztConstructName();
                    if (StringUtils.isNotBlank(wztBuilding) && StringUtils.isNotBlank(wztBuildingName)) {
                        wztBuildIngMap.put(wztBuilding, wztBuildingName);
                    }
                }
            }
        }
        return JsonResult.getSuccessResult(wztBuildIngMap);
    }

    /**
     * 获取所有商户
     * @param param
     * @return
     */
    public JsonResult getMerchantByBuildIng(PointReqFvo param) {
        String buildingId = param.getBuildingId();
        if (StringUtils.isBlank(buildingId)) {
            return JsonResult.getFailResult("建筑id不能为空");
        }

//        List<BaseMerchantFvo> list = kitchenMonitorMapper.getMerchantList(buildingId);
        List<BaseMerchantFvo> list = kitchenMonitorMapper.getMerchantListNew(param);

        return JsonResult.getSuccessResult(list);
    }

    /**
     * 查询原消防模块
     * @param param
     * @return
     */
    public JsonResult getOldPointList(PointRequest param) {
        //建筑物id
        String buildingId = param.getBuildIngId();
        if (StringUtils.isEmpty(buildingId)) {
            return JsonResult.getFailResult("未获取到建筑物id信息");
        }
        JSONObject object = new JSONObject();
        object.put("pageNumber", param.getPageNumber());
        object.put("pageSize", param.getPageSize());

        List<BaseDevicePointFvo> pointList = new ArrayList<>();
        List<BaseDevicePointFvo> pageList = kitchenMonitorMapper.selectOldPointList(param, null, null);
        List<BaseDevicePointFvo> resList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.selectOldPointList(param, (param.getPageNumber() - 1) * param.getPageSize(), param.getPageSize());
            List<String> pointIdList = pointList.stream()
                    .map(BaseDevicePointFvo::getId)
                    .collect(Collectors.toList());
            LambdaQueryWrapper<FireInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(FireInfo::getPointId, pointIdList);
            queryWrapper.in(FireInfo::getFireStatus, "0", "2");
            List<FireInfo> fireInfos = fireInfoMapper.selectList(queryWrapper);
            Map<String, String> map = new HashMap<>();
            if(CollectionUtils.isNotEmpty(fireInfos)) {
                for (FireInfo fireDto : fireInfos) {
                    String pointId = fireDto.getPointId();
                    String fireStatus = fireDto.getFireStatus();
                    map.put(pointId, fireStatus);
                }
            }
            for (BaseDevicePointFvo pointDto : pointList) {
                String id = pointDto.getId();
                int alarmStatus = 0;
                if(map.containsKey(id)) {
                    alarmStatus = 1;
                }
                pointDto.setAlarmStatus(alarmStatus);
                resList.add(pointDto);
            }
        }
        object.put("total", pageList.size());
        object.put("list", resList);
        return JsonResult.getSuccessResult(object);
    }

    /**
     * 获取厨房监测点位列表
     *
     * @param param
     * @return
     */
    public JsonResult getPointList(PointRequest param) {
        //建筑物id
        String buildingId = param.getBuildIngId();
        if (StringUtils.isEmpty(buildingId)) {
            return JsonResult.getFailResult("未获取到建筑物id信息");
        }
        JSONObject object = new JSONObject();
        object.put("pageNumber", param.getPageNumber());
        object.put("pageSize", param.getPageSize());

        List<BaseDevicePointFvo> pointList = new ArrayList<>();
        List<BaseDevicePointFvo> pageList = kitchenMonitorMapper.selectPointList(param, null, null);
        if (CollectionUtil.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.selectPointList(param, (param.getPageNumber() - 1) * param.getPageSize(), param.getPageSize());
        }
        object.put("total", pageList.size());
        object.put("list", pointList);
        return JsonResult.getSuccessResult(object);
    }


    /**
     * 点击单个点位查看历史上传记录状态
     *
     * @param param
     * @return
     */
    public JsonResult getPointStatusHistory(JSONObject param) {
        //点位id
        String pointId = param.getString("id");
        //建筑id
        String buildingId = param.getString("buildingId");
        String beginTime = param.getString("beginTime");
        String endTime = param.getString("endTime");
        /**
         * 断电状态，0-断电，1-上电
         */
        String outageStatus = param.getString("outageStatus");
        /**
         * 设备状态 0-正常，1-离线
         */
        String deviceStatus = param.getString("deviceStatus");
        /**
         * 电量状态 0-正常，1-低电量
         */
        String electricalStatus = param.getString("electricalStatus");
        /**
         * 故障状态 0-正常，1-故障
         */
        String faultStatus = param.getString("faultStatus");
        Date beginDate;
        Date endDate;
        if (StringUtils.isNotEmpty(beginTime) || StringUtils.isNotEmpty(endTime)) {
            if (StringUtils.isEmpty(beginTime)) {
                return JsonResult.getFailResult("开始时间不能为空");
            }
            if (StringUtils.isEmpty(endTime)) {
                return JsonResult.getFailResult("结束时间不能为空");
            }
            try {
                beginDate = DateUtils.parseDatetime(beginTime);
                endDate = DateUtils.parseDatetime(endTime);
            } catch (ParseException e) {
                return JsonResult.getFailResult("解析日期时间失败{}", e.getMessage());
            }
        } else {
            try {
                beginDate = DateUtils.getBeforeWeekDate();
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RuntimeException("解析日期时间失败");
            }
            endDate = new Date();
        }
        //todo 待修改
        List<CloseInfluxData> dataList = influxDataDao.findCloseStoreDataByTime(buildingId, pointId, beginDate, endDate, outageStatus,
                deviceStatus, electricalStatus, faultStatus);
        return JsonResult.getSuccessResult(dataList);
    }

    /**
     * 厨房检测设备正常数量查看数据
     *
     * @param param
     * @return
     */
    public JsonResult getAllNormalPointData(PointReqFvo param) {
        //建筑id
        String buildingId = param.getBuildingId();
        if (StringUtils.isBlank(buildingId)) {
            return JsonResult.getFailResult("建筑id不能为空");
        }
        JSONObject object = new JSONObject();
        Integer pageNumber = param.getPageNumber();
        Integer pageSize = param.getPageSize();
        object.put("pageNumber", pageNumber);
        object.put("pageSize", pageSize);
        int total = 0;
        List<PointNormalRespFvo> pageList = kitchenMonitorMapper.getNormalPoint(param, null, null);
        List<PointNormalRespFvo> pointList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.getNormalPoint(param, (pageNumber - 1) * pageSize, pageSize);
            total = pointList.size();
            List<String> hostIdList = pointList.stream()
                    .map(PointNormalRespFvo::getHostId)
                    .filter(hostId -> hostId != null)
                    .collect(Collectors.toList());
            //根据主机id查询所有B版通道信息
            Map<String, List<KitchenDeviceBasicDto>> hostMap = getPointBMap(buildingId, hostIdList);
            for (PointNormalRespFvo respFvo : pointList) {
                String hostId = respFvo.getHostId();
                if (hostMap != null && hostMap.containsKey(hostId)) {
                    List<KitchenDeviceBasicDto> dtoList = hostMap.get(hostId);
                    buildPointBInfo(respFvo, dtoList);
                }
                respFvo.setStatus("在线");
            }
        }
        object.put("total", total);
        object.put("list", pointList);
        return JsonResult.getSuccessResult(object);
    }

    private void buildPointBInfo(PointNormalRespFvo respFvo, List<KitchenDeviceBasicDto> dtoList) {
        if (CollectionUtils.isNotEmpty(dtoList)) {
            String hostTypeB = "";
            String state = "";

            for (KitchenDeviceBasicDto dto : dtoList) {
                char hostType = dto.getHostType();
                String hostTypeStr = "B" + hostType;

                if (StringUtils.isBlank(hostTypeB)) {
                    hostTypeB = hostTypeStr;
                } else if (StringUtils.isNotBlank(hostTypeB)) {
                    hostTypeB = hostTypeB + "," + hostTypeStr;
                }

                if (dto.getOnlineState() == 2) {
                    if (StringUtils.isNotBlank(state)) {
                        state = state + "," + "离线";
                    } else {
                        state = "离线";
                    }
                }

                if (dto.getFaultState() == 30) {
                    if (StringUtils.isNotBlank(state)) {
                        state = state + "," + "故障";
                    } else {
                        state = "故障";
                    }
                }

                if (dto.getPowerState() == 50) {
                    if (StringUtils.isNotBlank(state)) {
                        state = state + "," + "断电";
                    } else {
                        state = "断电";
                    }
                }
            }
            respFvo.setHostTypeB(hostTypeB);
            respFvo.setStatus(state);
        }
    }

    private Map<String, List<KitchenDeviceBasicDto>> getPointBMap(String buildingId, List<String> hostIdList) {
        if (CollectionUtils.isNotEmpty(hostIdList)) {
            List<KitchenDeviceBasicDto> list = kitchenDeviceBasicMapper.getByHostIdList(buildingId, hostIdList);
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream()
                        .filter(device -> device.getHostId() != null)
                        .collect(Collectors.groupingBy(KitchenDeviceBasicDto::getHostId));

            }
        }
        return null;
    }

    /**
     * 点击厨房检测设备异常数量查看数据
     *
     * @param param
     * @return
     */
    public JsonResult getAllExceptionPointData(PointReqFvo param) {
        //建筑id
        String buildingId = param.getBuildingId();
        if (StringUtils.isBlank(buildingId)) {
            return JsonResult.getFailResult("建筑id不能为空");
        }
        JSONObject object = new JSONObject();
        Integer pageNumber = param.getPageNumber();
        Integer pageSize = param.getPageSize();
        object.put("pageNumber", pageNumber);
        object.put("pageSize", pageSize);
        int total = 0;
        List<PointNormalRespFvo> pageList = kitchenMonitorMapper.getExceptionPoint(param, null, null);
        List<PointNormalRespFvo> pointList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.getExceptionPoint(param, (pageNumber - 1) * pageSize, pageSize);
            total = pointList.size();
            List<String> hostIdList = pointList.stream()
                    .map(PointNormalRespFvo::getHostId)
                    .filter(hostId -> hostId != null)
                    .collect(Collectors.toList());
            //根据主机id查询所有B版通道信息
            Map<String, List<KitchenDeviceBasicDto>> hostMap = getPointBMap(buildingId, hostIdList);
            for (PointNormalRespFvo respFvo : pointList) {
                String hostId = respFvo.getHostId();
                if (hostMap != null && hostMap.containsKey(hostId)) {
                    List<KitchenDeviceBasicDto> dtoList = hostMap.get(hostId);
                    buildPointBInfo(respFvo, dtoList);
                }
            }
        }
        object.put("total", total);
        object.put("list", pointList);
        return JsonResult.getSuccessResult(object);
    }


    /**
     * 删除点位
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delPoint(PointReqFvo param) {
        //1、首先删除kitchen_device_basic_info表的数据：根据主键id删除
        //2、删除base_device_point表中的数据，根据buildingId, hostId, did、super_type状态删除30
        //3、更新统计表w_kitchen_equip_stat表数据，数量加减1；
        //4、删除A版和B通道信息
        //5、删除缓存内容
        String id = param.getId();
        if (StringUtils.isBlank(id)) {
            return JsonResult.getFailResult("id不能为空");
        }
        LambdaQueryWrapper<KitchenDeviceBasic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KitchenDeviceBasic::getId, id);
        queryWrapper.last("LIMIT 1");
        KitchenDeviceBasic kitchenDeviceBasic = kitchenDeviceBasicMapper.selectOne(queryWrapper);
        if (kitchenDeviceBasic == null) {
            return JsonResult.getFailResult("未获取到厨房监测设备基础信息");
        }
        String buildingId = kitchenDeviceBasic.getBuildingId();
        String hostId = kitchenDeviceBasic.getHostId();
        String hostType = kitchenDeviceBasic.getHostType();
        //删除kitchen_device_basic_info表的数据（A版数据）
        kitchenDeviceBasicMapper.deleteById(id);
        //删除kitchen_device_basic_info表的数据（B版数据）
        delPointBInfo(buildingId, hostId);
        //删除base_device_point表中的数据
        delBaseDevicePoint(buildingId, hostId);
        // 删除注册A版表数据
        kitchenSignAService.remove(new LambdaUpdateWrapper<KitchenSignA>().eq(KitchenSignA::getHostId, hostId));
        kitchenSignBService.remove(new LambdaUpdateWrapper<KitchenSignB>().eq(KitchenSignB::getHostId, hostId));
//        kitchenSignAService.delInfo(hostId);
//        // 删除通道B表数据
//        kitchenSignBService.delInfo(hostId);
        //更新统计表中的数据信息
        kitchenEquipStatService.updateKitchenEquipStat(kitchenDeviceBasic, buildingId);

        for (int i = 1; i < 5; i++) {
            //删缓存
            String stateKey = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(hostId, i + "");
            String key = kitchenDeviceRedisService.getEquipmentRegisterKey(hostId);
            String pointStateKey = kitchenDeviceRedisService.getEquipmentStateKey(hostId, i + "");
            log.info("-------开始删除厨房设备redis数据，redis-key为{}；{}；{}--------------", stateKey, key, pointStateKey);
            redisUtils.delete(stateKey);
            redisUtils.delete(key);
            redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXCZM.getTypeCode());
            redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXRQBJ.getTypeCode());
            redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXDHLR.getTypeCode());
        }

//        //删缓存
//        String stateKey = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(hostId, hostType);
//        String key = kitchenDeviceRedisService.getEquipmentRegisterKey(hostId);
//        String pointStateKey = kitchenDeviceRedisService.getEquipmentStateKey(hostId, hostType);
//        log.info("-------开始删除厨房设备redis数据，redis-key为{}；{}；{}--------------", stateKey, key, pointStateKey);
//        redisUtils.delete(stateKey);
//        redisUtils.delete(key);
//        redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXCZM.getTypeCode());
//        redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXRQBJ.getTypeCode());
//        redisUtils.delete(pointStateKey + "-" + KitchenDeviceTypeEnum.WXDHLR.getTypeCode());
        return JsonResult.getSuccessResult("删除成功");
    }

    private void delBaseDevicePoint(String buildingId, String hostId) {
        LambdaQueryWrapper<BaseDevicePoint> qw = new LambdaQueryWrapper<>();
        qw.select(BaseDevicePoint::getId, BaseDevicePoint::getWztPointId);
        qw.eq(BaseDevicePoint::getBuildingId, buildingId);
        qw.eq(BaseDevicePoint::getHostId, hostId);
        qw.eq(BaseDevicePoint::getSuperType, "30");//代表厨房监测设备
        List<BaseDevicePoint> baseDevicePointList = baseDevicePointMapper.selectList(qw);
        if (CollectionUtils.isNotEmpty(baseDevicePointList)) {
            List<String> idList = new ArrayList<>();
            for (BaseDevicePoint baseDevicePoint : baseDevicePointList) {
                baseDevicePointMapper.deleteById(baseDevicePoint.getId());
                idList.add(baseDevicePoint.getWztPointId());
            }
            final val jsonResult = constructClient.delAlarmPoint(idList);
            log.info("----------删除中台厨房模块报警点位，返回值为{},入参为{}------------",
                    JSONObject.toJSONString(jsonResult), JSONObject.toJSONString(idList));
            if (ObjectUtil.isNotEmpty(jsonResult) && !jsonResult.getSuccess()) {
                throw new RuntimeException("删除中台厨房模块报警点位失败");
            }
        }

    }

    private void delPointBInfo(String buildingId, String hostId) {
        LambdaQueryWrapper<KitchenDeviceBasic> queryWrapperPointB = new LambdaQueryWrapper<>();
        queryWrapperPointB.eq(KitchenDeviceBasic::getBuildingId, buildingId);
        queryWrapperPointB.eq(KitchenDeviceBasic::getHostId, hostId);
//        queryWrapperPointB.in(KitchenDeviceBasic::getHostType, "1","2","3", "4");
        queryWrapperPointB.in(KitchenDeviceBasic::getHostType, "4","2","3");
        List<KitchenDeviceBasic> kitchenDeviceBasicList = kitchenDeviceBasicMapper.selectList(queryWrapperPointB);
        if (CollectionUtils.isNotEmpty(kitchenDeviceBasicList)) {
            for (KitchenDeviceBasic dto : kitchenDeviceBasicList) {
                kitchenDeviceBasicMapper.deleteById(dto.getId());
            }
        }
    }

    /**
     * 查看运行记录
     * @param param
     * @return
     */
    public JsonResult getDetailInfo(PointReqFvo param) {
        String id = param.getId();
        if (StringUtils.isBlank(id)) {
            return JsonResult.getFailResult("id不能为空");
        }


        return JsonResult.getSuccessResult("");
    }

    /**
     * 根据建筑id查询报警设备信息
     * @param param
     * @return
     */
    public JsonResult queryAlarmPointList(PointRequest param) {
        String buildingId = param.getBuildIngId();
        if (StringUtils.isEmpty(buildingId)) {
            return JsonResult.getFailResult("未获取到建筑物id信息");
        }
        JSONObject object = new JSONObject();
        object.put("pageNumber", param.getPageNumber());
        object.put("pageSize", param.getPageSize());
        List<BaseDevicePointFvo> pointList = new ArrayList<>();
        List<BaseDevicePointFvo> pageList = kitchenMonitorMapper.queryAlarmPointList(param, null, null);
        if (CollectionUtil.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.queryAlarmPointList(param, (param.getPageNumber() - 1) * param.getPageSize(), param.getPageSize());
        }
        List<BaseDevicePointFvo> resList = new ArrayList<>();
        try {
            for (BaseDevicePointFvo pointFvo : pointList) {
                String pointId = pointFvo.getId();
                String equipmentTestStateKey = kitchenDeviceRedisService.getEquipmentTestStateKey(pointId);
                if(StringUtils.isNotBlank(equipmentTestStateKey)) {
                    pointFvo.setTestButton(equipmentTestStateKey);
                } else {
                    pointFvo.setTestButton("1");
                }
                resList.add(pointFvo);
            }
            object.put("list", resList);
        } catch (Exception e) {
            log.error("[KitchenMonitorServiceImpl]获取测试按钮状态识别{}", e.getMessage());
            object.put("list", pointList);
        }

        object.put("total", pageList.size());
        return JsonResult.getSuccessResult(object);
    }

    /**
     * 根据建筑id查询异常设备信息
     * @param param
     * @return
     */
    public JsonResult queryExceptionPointList(PointRequest param) {

        String buildingId = param.getBuildIngId();
        if (StringUtils.isEmpty(buildingId)) {
            return JsonResult.getFailResult("未获取到建筑物id信息");
        }
        JSONObject object = new JSONObject();
        object.put("pageNumber", param.getPageNumber());
        object.put("pageSize", param.getPageSize());
        List<BaseDevicePointFvo> pointList = new ArrayList<>();
        List<BaseDevicePointFvo> pageList = kitchenMonitorMapper.queryExceptionPointList(param, null, null);
        if (CollectionUtil.isNotEmpty(pageList)) {
            pointList = kitchenMonitorMapper.queryExceptionPointList(param, (param.getPageNumber() - 1) * param.getPageSize(), param.getPageSize());
        }
        object.put("total", pageList.size());
        object.put("list", pointList);
        return JsonResult.getSuccessResult(object);
    }


}
