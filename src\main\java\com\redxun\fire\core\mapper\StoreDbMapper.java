package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.StoreDb;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 门店调试表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-15
 */
public interface StoreDbMapper extends BaseMapper<StoreDb> {

    /**
     * 切换门店调试状态
     *
     * @param storeDb
     * @return
     */
    public int changeStoreState(StoreDb storeDb);

    /**
     * 获取门店
     *
     * @param storeDb
     * @return
     */
    public StoreDb getStore(StoreDb storeDb);


    /**
     * 用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectStoreDbToExport(@Param("param") Map param);
    /**
     * 恢复调试状态并且清空调试时间
     * */
    void recoveryTimeById(@Param("ids") List<String> ids);


    /**
     * 根据建筑ID查询当前建筑的状态
     * @param buildingId
     * @return
     */
    String getBuildState(@Param("buildingId") String buildingId);

    List<String> findDubegBuild();
}
