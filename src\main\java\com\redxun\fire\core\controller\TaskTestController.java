package com.redxun.fire.core.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.redxun.fire.core.mapper.PointSyncLogMapper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2024/8/13
 * @description
 */
@RestController
@RequestMapping("/tasktest")
@Slf4j
public class TaskTestController {

    @Resource
    PointSyncLogMapper pointSyncLogMapper;

    @PostMapping("/deletePointSyncLog")
    public void test1(@RequestBody Map<String, String> map) {
        try {
            log.info("-------------手动删除点位调改同步记录开始-----------------");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, -7);
            String paramsTime = map.get("time");
            if (StrUtil.isNotBlank(paramsTime)) {
                log.info("---------------手动删除点位调改同步记录任务,手动执行调整时间--------------------------");
                final val parse = DateUtil.parse(paramsTime, "yyyy-MM-dd");
                calendar.setTime(parse);
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            String time = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
            int deleteNum = 10000;
            int count = 0;
            while (deleteNum > 0 && count < 10) {
                deleteNum = pointSyncLogMapper.deleteByTime(time);
                count++;
            }
            log.info("-------------手动删除点位调改同步记录结束-----------------");
        } catch (Exception e) {
            log.error("手动删除点位调改同步记录异常:", e);
        }
    }
}
