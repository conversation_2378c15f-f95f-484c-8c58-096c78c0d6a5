package com.redxun.fire.core.consts;

/**
 * <AUTHOR>
 * Created on 2020/10/31 0031.
 */
public class WbConstants {

    /**
     * 测试
     */
    public static final String PLAN_TEST = "0";
    /**
     * 检查
     */
    public static final String PLAN_CHECK = "1";
    /**
     * 联动
     */
    public static final String PLAN_LINK = "2";
    /**
     * 临时
     */
    public static final String PLAN_TEMP = "3";


    /**预约状态常亮
     /* *
     * 默认（未开始）
     */
    public static final String MAINTENANCE_START_DEFAULT = "1";
    /**
     * 进行中
     */
    public static final String MAINTENANCE_START_PROCESSING = "2";
    /**
     * 暂停
     */
    public static final String MAINTENANCE_START_TIMEOUT = "3";
    /**
     * 结束
     */
    public static final String MAINTENANCE_START_END = "4";


    /**
     * 检查小组GROUP_ID ---> 城市公司检查小组/安全主管
     */
//    public static final String CHECK_GROUP = "1485798376962269185";
        //安全主管
//    public static final String CHECK_GROUP = "1710683422565666818";
            //城市公司检查小组
    public static final String CHECK_GROUP = "1753626476514463746";
    //安全主管
    public static final String CHECK_GROUP1 = "1710683422565666818";


    /**
     *城市安全总监及以上安监人员：
     *城市公司安全总监
     *大区城市公司分管人
     *大区安监中心总经理
     *安监中心城市公司业务指导人
     *总部中心部门总经理
     *总部中心总经理
     * */

    /**
     * 城市公司安全总监 -------> 城市公司安全总监
     */
//    public static final String AREA_SAFETY_MANAGER_GROUP = "1303535643724025858";
    public static final String AREA_SAFETY_MANAGER_GROUP = "50fc9a02-e3cd-11ed-adff-0242ac110002";

    /**
     * 大区城市公司分管人   -------> 大区公司安监中心业务总监
     *                              大区公司安监中心副总经理
     *                              大区公司安监中心经理
     */
//    public static final String REGION_CITY_MANAGE_GROUP = "1355091037613207554";
    public static final String REGION_CITY_MANAGE_GROUP = "1748741515902185473";
    public static final String REGION_CITY_MANAGE_GROUP1 = "1710814628120694785";
    public static final String REGION_CITY_MANAGE_GROUP2 = "1710814692796862465";

    /**
     * 大区安监中心总经理  -----------> 大区公司安监中心总经理
     *
     */
//    public static final String CENTER_PRE_MANAGER_GROUP = "1303534696776011778";
    public static final String CENTER_PRE_MANAGER_GROUP = "1710814571464036354";

    /**
     * 安监中心城市公司业务指导人    ----------------------->
     * 商管集团安全监督管理中心检查考核部副总经理
     * 商管集团安全监督管理中心智慧消防部副总经理
     * 商管集团安全监督管理中心制度培训部副总经理
     * 商管集团安全监督管理中心综合管理部副总经理
     * 商管集团安全监督管理中心检查考核部经理
     * 商管集团安全监督管理中心智慧消防部经理
     * 商管集团安全监督管理中心制度培训部经理
     * 商管集团安全监督管理中心综合管理部经理
     */
//    public static final String SAFETY_SHARD_LEADER_GROUP = "1303532830365257730";
    public static final String SAFETY_SHARD_LEADER_GROUP1 = "1710885178033639425";
    public static final String SAFETY_SHARD_LEADER_GROUP2 = "1710885221482434561";
    public static final String SAFETY_SHARD_LEADER_GROUP3 = "1710885268500582401";
    public static final String SAFETY_SHARD_LEADER_GROUP4 = "1710885311970349057";
    public static final String SAFETY_SHARD_LEADER_GROUP5 = "1710885351912706050";
    public static final String SAFETY_SHARD_LEADER_GROUP6 = "1710885395155980290";
    public static final String SAFETY_SHARD_LEADER_GROUP7 = "1710885428001574914";
    public static final String SAFETY_SHARD_LEADER_GROUP8 = "1710885494477099009";
//    public static final String SAFETY_SHARD_LEADER_GROUP9 = "1859049995157635074";

    /**
     * 总部中心部门总经理  ----->
     * 商管集团安全监督管理中心检查考核部总经理
     * 商管集团安全监督管理中心智慧消防部总经理
     * 商管集团安全监督管理中心制度培训部总经理
     * 商管集团安全监督管理中心综合管理部总经理
     */
//    public static final String GROUP_MANAGER_GROUP = "1303531444575604739";
    public static final String GROUP_MANAGER_GROUP1 = "1710885033799913474";
    public static final String GROUP_MANAGER_GROUP2 = "1710885062220517378";
    public static final String GROUP_MANAGER_GROUP3 = "1710885100636147714";
    public static final String GROUP_MANAGER_GROUP4 = "1710885140549144578";

    /**
     * 总部中心总经理   ------------> 商管集团安全监督管理中心总经理
     *
     */
//    public static final String HEAD_CENTER_MANAGER_GROUP = "1355091857943572482";
    public static final String HEAD_CENTER_MANAGER_GROUP = "1710884950136131585";

    /**
     * 新增职务 总部临时安全检查岗
     */
    public static final String HEAD_TEMP_CHECK_GROUP = "1775058061407920130";


}
