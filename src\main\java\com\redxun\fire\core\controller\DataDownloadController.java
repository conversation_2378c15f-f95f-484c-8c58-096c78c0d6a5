package com.redxun.fire.core.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.commons.io.Charsets;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.redxun.api.feign.SystemClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.DataDownload;
import com.redxun.fire.core.entity.Journalizing;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.dto.common.PageRequestParam;
import com.redxun.fire.core.utils.POIExcelTool;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.other.IDataDownloadService;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.utils.ResponseEntityUtils;
import com.redxun.fire.core.utils.ZipUtils;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-25
 */
@Slf4j
@RestController
@RequestMapping("/data")
public class DataDownloadController {
    @Resource
    IDataDownloadService iDataDownloadService;
    @Resource
    IJournalizingService journalizingService;
    @Resource
    IBaseBuildingService baseBuildingService;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    POIExcelTool poiExcelTool;

    /**
     * 查询所有资料
     *
     * @return
     */
    @RequestMapping("queryAll")
    public JsonResult queryAll() {
        JsonResult jsonResult = JsonResult.Success();
        List<DataDownload> list = iDataDownloadService.list();
        jsonResult.setData(list);
        return jsonResult;
    }

    /**
     * 根据文件名查询
     *
     * @return
     */
    @RequestMapping("queryByDataName/{dataName}")
    public JsonResult queryByDataName(@PathVariable("dataName") String dataName) {
        log.info("根据文件名模糊查询  文件名为--->" + dataName);
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.queryByDataName(dataName));
        return jsonResult;
    }

    /**
     * 根据Id查询
     *
     * @param id
     * @return
     */
    @RequestMapping("queryByid/{id}")
    public JsonResult queryByid(HttpServletRequest request, @PathVariable("id") String id) {
        log.info("根据id查询  id名为--->" + id);
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.queryByid(request, id));
        return jsonResult;
    }


    /**
     * 修改
     *
     * @param dataDownload
     * @return
     */
    @RequestMapping("update")
    public JsonResult update(HttpServletRequest request, @RequestBody @Validated(Update.class) DataDownload dataDownload) {
        log.info("修改资料信息  要修改的参数对象为--->" + JSON.toJSONString(dataDownload));
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.update(request, dataDownload));
        return jsonResult;
    }


    /**
     * 删除
     *
     * @param dataDownload
     * @return
     */
    @RequestMapping("remove")
    public JsonResult remove(HttpServletRequest request, @RequestBody @Validated(Update.class) DataDownload dataDownload) {
        log.info("根据id删除资料  id为--->" + dataDownload.getId());
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.remove(request, dataDownload.getId()));
        return jsonResult;
    }

    /**
     * 下载
     *
     * @param request
     * @param httpServletResponse
     * @throws FileNotFoundException
     */
    @RequestMapping("download/public/{id}")
    public void download(HttpServletRequest request, HttpServletResponse httpServletResponse, @PathVariable("id") String id) {
        try {
            log.info("根据id下载资料   id为--->" + id);
            DataDownload dataDownload = iDataDownloadService.queryByid(request, id);
            JSONArray jsonArray = JSONArray.parseArray(dataDownload.getDataUrlNew());
            if (jsonArray.size() == 0) {
                return;
            }
            String fileId = jsonArray.getJSONObject(0).getString("fileId");
            httpServletResponse.setContentType("application/x-download;charset=" + Charsets.UTF_8.displayName());
            Response response = systemClient.download(fileId);
            Response.Body body = response.body();
            OutputStream outputStream = httpServletResponse.getOutputStream();
            InputStream inputStream = body.asInputStream();
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.info("下载文件异常");
            e.printStackTrace();
        }
    }

    /**
     * 下载
     *
     * @param request
     * @param response
     * @throws FileNotFoundException
     */
    @GetMapping("/downloadFile")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response) throws FileNotFoundException {
        String url = request.getParameter("url");
        String buildingId = request.getParameter("buildingId");
        File file = new File(url);
        if (!file.exists()) {
            try {
                throw new FileNotFoundException();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
        // 下载本地文件
        // 文件的默认保存名
        poiExcelTool.downloadLocal(response, url);

        String fileName = file.getName();
        String buildingName = "";
        BaseBuilding building = baseBuildingService.getBuildingInfoById(buildingId);

        if (Objects.nonNull(building)) {
            buildingName = building.getBuildingName();
        }

        //日志插入
        Journalizing journalizing = new Journalizing();
        journalizing.setOperationTypeCode(LogTypeEnums.POINT_ADJUST.getType());
        journalizing.setOperationContent("下载" + buildingName + "的" + fileName + "点位模板");
        journalizingService.setLogInfo(request, journalizing);

    }

    /**
     * 文件下载
     *
     * @param type 模板类型：1：排班表，2:水压监测-下载导入模板 3：摄像头导入模版
     */
    @GetMapping("/downloadTemplate")
    public JsonResult downloadTemplate(@RequestParam String type) throws IOException {
        if (StrUtil.isBlank(type)) {
            JsonResult.getFailResult("入参不规范");
        }
        JsonResult result = JsonResult.Success();
        result.setData(iDataDownloadService.downloadTemplate(type));
        return result;
    }

    /**
     * 批量下载
     *
     * @param request
     * @param response
     * @throws FileNotFoundException
     */
    @RequestMapping("downloadBatch")
    public ResponseEntity<byte[]> downloadBatch(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<String> ids = new ArrayList<>(Arrays.asList(request.getParameter("ids").split(",")));
        log.info("根据id下载资料   id为--->" + JSON.toJSONString(ids));
        List<DataDownload> dataDownloads = iDataDownloadService.listByIds(ids);
        log.info("将要下载的数据为--->" + JSON.toJSONString(dataDownloads));
        Map<String, byte[]> byteFileMap = new HashMap<>();
        for (DataDownload dataDownload : dataDownloads) {
            if (StringUtils.isNotEmpty(dataDownload.getDataUrlNew())) {
                JSONArray jsonArray = JSONArray.parseArray(dataDownload.getDataUrlNew());
                String fileId = jsonArray.getJSONObject(0).getString("fileId");
                Response fileResponse = systemClient.download(fileId);
                InputStream inputStream = fileResponse.body().asInputStream();
                byte[] bytes = StreamUtils.copyToByteArray(inputStream);
                inputStream.read(bytes);
                byteFileMap.put(dataDownload.getDataName(), bytes);
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ZipUtils.batchFileToZIP(byteFileMap, byteArrayOutputStream);
        return ResponseEntityUtils.buildByteFileResponse("导出文件.zip", byteArrayOutputStream.toByteArray(), response);
    }

    @PostMapping("addData")
    //上传本地
    public JsonResult addData(HttpServletRequest request, @RequestParam("file") MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        List<DataDownload> list = iDataDownloadService.queryByDataNameAll(fileName);
        if (!CollectionUtils.isEmpty(list)) {
            return JsonResult.Fail("该文件名已存在！");
        }
        log.info("准备开始文件上传");
        JsonResult jsonResult = systemClient.uploadFileByOpenFeign(multipartFile, "ZHXF", "65ml54fa76wx8178gsp2q9r0i34hdck3");
        if (!jsonResult.isSuccess()) {
            return jsonResult;
        }

        log.info("文件上传完毕 准备入库");
        JSONObject jsonObject = JSONUtil.parseArray(jsonResult.getData()).getJSONObject(0);
        String userId = request.getParameter("userId");
        DataDownload dataDownload = new DataDownload();
        dataDownload.setDataName(fileName);
        dataDownload.setUpdateTime(jsonObject.getStr("createTime"));
        dataDownload.setCreateDepId(jsonObject.getStr("createDepId"));
        dataDownload.setOperator(jsonObject.getStr("createUser"));
        dataDownload.setDataUrl(jsonObject.getStr("path"));
        dataDownload.setDataUrlNew(JSON.toJSONString(jsonObject));
        dataDownload.setCreateBy(userId);
        dataDownload.setUpdateBy(userId);
        log.info("入库对象参数为--->" + JSON.toJSONString(dataDownload));
        return JsonResult.Success().setData(iDataDownloadService.addData(request, dataDownload));
    }

    /**
     * 查询所有
     *
     * @param pageRequestParam
     * @return
     */
    @PostMapping("queryPage")
    public JsonResult queryPage(@RequestBody @Validated(Select.class) PageRequestParam<DataDownload> pageRequestParam) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.queryPage(pageRequestParam));
        return jsonResult;
    }

    /**
     * 查询误报原因
     * 95528  监控台火警误报原因
     *
     * @return
     */
    @GetMapping("queryDropDown/{treeId}")
    public JsonResult queryDropDown(@PathVariable("treeId") String treeId) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDataDownloadService.queryNameByTreeId(treeId));
        return jsonResult;
    }
}
