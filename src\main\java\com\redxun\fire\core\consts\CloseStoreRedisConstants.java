package com.redxun.fire.core.consts;

import com.redxun.fire.core.utils.DateUtil;

/**
 * 闭店监测redis key入口
 */
public class CloseStoreRedisConstants {

    /**
     * 延时列队key
     */
    public static  String DELAY_QUEUE_PREFIX = "close_store_timer:delay_queue_";

    public static String EQUIPMENT_PREFIX ="close_store_equipment:";

    public static String REDIS_KEY_CLOSE_STORE ="close_store:";

    public static String CLOSE_STORE_HOST_BUILDING ="close_store_host_building:";

    public static String CLOSE_STORE_DEVICE_TIME ="close_store_device_time:";


    public static String getEquipmentKey(String address, Integer did) {
        String key = EQUIPMENT_PREFIX+address+"-" + did+"-16";
        return key;
    }

    /**
     * 设备状态缓存
     * @param buildingId
     * @param pointId
     * @return
     */
    public static String getPointInfoKey(String buildingId, String pointId) {
        String key = REDIS_KEY_CLOSE_STORE + buildingId+"_"+pointId;
        return key;
    }

    /**
     * 获取指定日期的延时队列key
     * 按日期分布
     * @param i
     */
    public static String getDelayQueueKey(int i) {
        String date = DateUtil.getTodayDateAfter(i);
        String key = DELAY_QUEUE_PREFIX+date;
        return key;
    }

    /**
     * 设备对应建筑信息
     * @param hostId 10进制8位设备号
     * @return
     */
    public static String getHostBuildingKey(String hostId) {
        String key = CLOSE_STORE_HOST_BUILDING + hostId;
        return key;
    }

    /**
     * 设备开闭店时间规则key
     * @param pointId
     * @return
     */
    public static String getDeviceTimeKey(String pointId) {
        String key = CLOSE_STORE_DEVICE_TIME  + pointId;
        return key;
    }
}
