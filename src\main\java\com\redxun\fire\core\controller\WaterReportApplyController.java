package com.redxun.fire.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSON;
import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.exception.BusinessException;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.BpmDefNodeReference;
import com.redxun.fire.core.entity.BpmDefReference;
import com.redxun.fire.core.entity.WaterAbnormalReportInfo;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.mapper.WaterAbnormalReportInfoMapper;
import com.redxun.fire.core.service.alarm.WaterAbnormalReportInfoService;
import com.redxun.fire.core.service.bpm.IBpmDefNodeReferenceService;
import com.redxun.fire.core.service.bpm.IBpmDefReferenceService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.IAdjustptService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.WdMessageComponent;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <p>
 * 异常报备 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@RestController
@RequestMapping("/waterReportApply")
public class WaterReportApplyController {

    @Resource
    private OrgManageClient orgManageClient;

    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Resource
    private WaterAbnormalReportInfoMapper waterAbnormalReportInfoMapper;
    @Resource
    WaterAbnormalReportInfoService waterAbnormalReportInfoService;
    @Autowired
    IAdjustptService iAdjustptService;
    @Autowired
    IBpmDefNodeReferenceService bpmDefNodeReferenceService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Autowired
    private IBpmDefReferenceService bpmDefReferenceService;
    @Resource
    private WdMessageComponent wdMessageComponent;
    Gson gson = new Gson();

    @ApiOperation(value = "异常报备获取审批人")
    @GetMapping({"/waterReportApplyJobApprove"})
    public List<TaskExecutor> waterReportApplyJobApprove(@RequestParam(value = "nodeId") String nodeId, @RequestParam(value = "userId") String userId, @RequestParam(value = "bpmDefId") String bpmDefId,@RequestParam(value = "buildingId") String buildingId) {
        List<TaskExecutor> userList = new ArrayList<>();
        log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人入参:nodeId：" + nodeId + ",userId:" + userId + ",bpmDefId:" + bpmDefId);

        //UserOrgResponse userOrg = orgMiddleService.getOrgManageByUser(userId);
        BaseBuilding baseBuilding=baseBuildingService.getBaseMapper().selectById(buildingId);
        if (baseBuilding!=null) {
            QueryWrapper qw = new QueryWrapper();
            qw.eq("node_id", nodeId);
            qw.eq("bpm_def_id", bpmDefId);
            qw.eq("node_enable", 1);
            List<BpmDefNodeReference> bpmDefNodeReferenceList = bpmDefNodeReferenceService.getBaseMapper().selectList(qw);


            if (bpmDefNodeReferenceList != null && bpmDefNodeReferenceList.size() > 0) {
                String[] jobIds = bpmDefNodeReferenceList.get(0).getJobId().split(",");
                if (jobIds != null && jobIds.length > 0) {
                    for (String jobId : jobIds) {
                        OrgManageParam orgParam = new OrgManageParam();
                        orgParam.setBusinessId(baseBuilding.getProjectType());
                        String level = bpmDefNodeReferenceList.get(0).getLevelAyer();
                        if ("1".equals(level)) {
                            orgParam.setPiazzaId(baseBuilding.getPiazza());
                        } else if ("2".equals(level)) {
                            orgParam.setCityId(baseBuilding.getCityCompany());
                        } else if ("3".equals(level)) {
                            orgParam.setRegionId(baseBuilding.getRegion());
                        } else if ("4".equals(level)) {
                            orgParam.setGroupId(baseBuilding.getGroupOrg());
                        }
                        orgParam.setJobId(jobId);
                        orgParam.setTenantId(baseBuilding.getTenantId());
                        JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
                        try {
                            UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                            List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                            if (data != null && data.size() > 0) {
                                for (UserOrgResponse.DataDTO dto : data) {
                                    userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                                }
                            }
                        } catch (Exception e) {
                            log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
                        }
                    }
                }
            }

        }
        if(userList!=null&&userList.size()>15){
            log.info("---------------->>>>>>>>>>>>>查询执行人数量过多入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId, userList.size());
            return new ArrayList<TaskExecutor>();
        }
        log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人:" + JSON.toJSONString(userList));
        return userList;
    }


    @ResponseBody
    @PostMapping(value = "/waterReportApplyPassApply")
    @ApiOperation("异常报备审批通过")
    public JsonResult waterReportApplyPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("异常报备通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        Date approveTime = new Date();
        WaterAbnormalReportInfo info = waterAbnormalReportInfoMapper.selectById(data);
        if (info != null) {
            //当前流程处理状态  0待审批  1 已审批  2 回退
            info.setApproveStatus("1");
            info.setProcessTime(approveTime);
            info.setProcessResult("已审批");
            waterAbnormalReportInfoMapper.updateById(info);
            log.info("水压/水泵/检线异常报备流程申请 正常结束处理，ID为：{}", data);
        } else {
            log.info("水压/水泵/检线异常报备流程申请 未获取到对应业务数据，ID为：{}", data);
            throw new BusinessException("异常报备流程申请,准备重试 id为:" + data);
        }
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/waterReportApplyNoPassApply")
    @ApiOperation("异常报备审批未通过")
    public JsonResult waterReportApplyNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("异常报备未通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        Date approveTime = new Date();
        WaterAbnormalReportInfo info = waterAbnormalReportInfoMapper.selectById(data);
        if (info != null) {
            //当前流程处理状态  0待审批  1 已审批  2 已退回
            info.setApproveStatus("2");
            info.setProcessTime(approveTime);
            info.setProcessResult("已退回");
            waterAbnormalReportInfoMapper.updateById(info);
            log.info("水压/水泵/检线异常报备流程申请 正常结束处理，ID为：{}", data);
        } else {
            log.info("水压水泵异常报备流程申请 失败结束处理 未获取到对应业务数据，ID为：{}", data);
            throw new BusinessException("异常报备流程申请,准备重试 id为:" + data);
        }
        return jsonResult;
    }

    @ApiOperation(value = "获取审批人多个职务审批")
    @GetMapping({"/waterReportApplyChooseJobApprove"})
    public List<TaskExecutor> waterReportApplyChooseJobApprove(@RequestParam(value = "applyId") String applyId,
                                                               @RequestParam(value = "jobId") String jobId,
                                                               @RequestParam(value = "userId") String userId) {
        log.info("异常报备自定义执行人[{}]", userId);
        List<TaskExecutor> userList = new ArrayList<>();
        UserOrgResponse userOrg = orgMiddleService.getOrgManageByUser(userId);
        if (userOrg.getSuccess() && userOrg.getData() != null && userOrg.getData().size() > 0) {
            String[] jobArr = jobId.split(",");
            if (jobArr != null && jobArr.length > 0) {
                for (String job : jobArr) {
                    OrgManageParam orgParam = new OrgManageParam();
                    orgParam.setBusinessId("1");
                    orgParam.setGroupId(userOrg.getData().get(0).getGroup());
                    orgParam.setJobId(job);
                    orgParam.setTenantId(userOrg.getData().get(0).getTenantId());
                    JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
                    try {
                        UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                        List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                        if (data != null && data.size() > 0) {
                            for (UserOrgResponse.DataDTO dto : data) {
                                userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                            }
                        }
                    } catch (Exception e) {
                        log.info("---------------->>>>>>>>>>>>>公告自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
                    }
                }
            }
        }
        return userList;
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return waterAbnormalReportInfoService.flowEvent(dataJson);
    }
    @MethodDefine(title = "网关条件匹配", path = "/setCondition", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "网关条件匹配", varName = "dataJson")})
    @AuditLog(operation = "网关条件匹配")
    @IdempotenceRequired
    @PostMapping("/setCondition")
    public JsonResult setCondition(@RequestBody JSONObject dataJson) {
        return waterAbnormalReportInfoService.setCondition(dataJson);
    }
    @MethodDefine(title = "检线异常报备网关条件匹配", path = "/setExceptionCondition", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "检线异常报备网关条件匹配", varName = "dataJson")})
    @AuditLog(operation = "检线异常报备网关条件匹配")
    @IdempotenceRequired
    @PostMapping("/setExceptionCondition")
    public JsonResult setExceptionCondition(@RequestBody JSONObject dataJson) {
        return waterAbnormalReportInfoService.setExceptionCondition(dataJson);
    }

}
