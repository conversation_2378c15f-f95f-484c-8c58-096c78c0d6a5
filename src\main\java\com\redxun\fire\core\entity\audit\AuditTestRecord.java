package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.entity.FireInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 测试点位
 */
@Data
@NoArgsConstructor
public class AuditTestRecord implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditRecordId;

    /**
     * 建筑名称
     */
    private String buildingName;

    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 设备类型
     */
    private String devTypeName;


    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 防火分区
     */
    private String zoneName;

    /**
     * 设施系统
     */
    private String fasName;

    /**
     * 楼层
     */
    private String floorName;


    /**
     * 检查时间
     */
    private String fillInTime;


    public AuditTestRecord(FireInfo fireInfo) {
        this.buildingName = fireInfo.getBuildingName();
        this.pointNumber = fireInfo.getPointCode();
        this.devTypeName = fireInfo.getDevName();
        this.pointDesc = fireInfo.getPointDesc();
        this.zoneName = fireInfo.getZoneName();
        this.fillInTime = fireInfo.getLastTime();
        this.fasName = fireInfo.getFasName();
    }
}
