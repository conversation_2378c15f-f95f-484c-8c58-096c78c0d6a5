package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.service.maintenance.IAuditRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 检查计划
 */
@Slf4j
@RestController
@RequestMapping("/auditRecord")
public class AuditRecordController {

    @Autowired
    private IAuditRecordService auditRecordService;


    /**
     * 生成安全检查记录
     */
    @PostMapping("/createAuditRecord/{buildingId}/{applyId}")
    public JsonResult createAuditRecord(@PathVariable String buildingId, @PathVariable String applyId) {
        return auditRecordService.createAuditRecord(buildingId, applyId);
    }


    /**
     * 获取待补全点位
     */
    @PostMapping("/getWaitCompletePoint/{auditRecordId}")
    public JsonPageResult getWaitCompletePoint(@PathVariable Long auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getWaitCompletePoint(auditRecordId,queryData);
    }

    /**
     * 获取故障点位
     */
    @PostMapping("/getFaultPoint/{auditRecordId}")
    public JsonPageResult getFaultPoint(@PathVariable Long auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getFaultPoint(auditRecordId,queryData);
    }

    /**
     * 获取重复点位
     */
    @PostMapping("/getRepeatPoint/{auditRecordId}")
    public JsonPageResult getRepeatPoint(@PathVariable Long auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getRepeatPoint(auditRecordId,queryData);
    }

    /**
     * 获取测试火警信息
     */
    @PostMapping("/getFire/{auditRecordId}")
    public JsonPageResult getFire(@PathVariable Long auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getFire(auditRecordId,queryData);
    }

    /**
     * 获取测试点位
     */
    @PostMapping("/getCheckPoint/{auditRecordId}")
    public JsonPageResult getCheckPoint(@PathVariable String auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getCheckPoint(auditRecordId,queryData);
    }

    /**
     * 获取跑点复核
     */
    @PostMapping("/getCheck/{auditRecordId}")
    public JsonPageResult getCheck(@PathVariable String auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getCheckFire(auditRecordId,queryData);
    }

    /**
     * 维保计划审核
     */
    @PostMapping("/updatePlanResult/{detailId}/{checkResult}")
    public JsonResult updatePlanCheckResult(HttpServletRequest request,
                                            @PathVariable("detailId") String detailId,
                                            @PathVariable("checkResult") String checkResult) {
        return auditRecordService.updateCheckResult(request,  detailId, checkResult);
    }

    /**
     * 误报火警审核
     */
    @PostMapping("/updateFireResult/{fireInfoId}/{checkResult}")
    public JsonResult updatePlanCheckResultFire(HttpServletRequest request,
                                                @PathVariable("fireInfoId") String fireInfoId,
                                                @PathVariable("checkResult") String checkResult){
        return auditRecordService.updateFireInfoResult(request, fireInfoId, checkResult);
    }

    /**
     * 获取维保计划
     */
    @PostMapping(value = "getCheckPlan/{auditRecordId}")
    public JsonPageResult getCheckPlan(@PathVariable String auditRecordId, @RequestBody QueryData queryData) {
        return auditRecordService.getCheckPlan(auditRecordId,queryData);
    }

    /**
     * 查询当前项目最新状态
     */
    @PostMapping("/getCurrentStatus/{buildingId}")
    public JsonResult getCurrentStatus(@PathVariable String buildingId) {
        return auditRecordService.getCurrentStatus(buildingId);
    }
}
