package com.redxun.fire.core.kitchen.mapper;

import com.redxun.common.base.db.BaseDao;
import com.redxun.fire.core.kitchen.dto.KitchenEquipStatDto;
import com.redxun.fire.core.kitchen.dto.SyncKitchenEquipDTO;
import com.redxun.fire.core.kitchen.entity.KitchenEquipStat;
import com.redxun.fire.core.kitchen.fvo.KitchenEquipStatFvo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 厨房设备检测数据统计表数据库访问层
*/
@Mapper
public interface KitchenEquipStatMapper extends BaseDao<KitchenEquipStat> {

    /**
     * 查询厨房设备检测信息
     * @param param
     * @param pageNumber
     * @param pageSize
     * @return
     */
    List<KitchenEquipStatDto> queryKitchenEquipStatInfo(@Param("param") KitchenEquipStatFvo param, @Param("pageNumber") Integer pageNumber, @Param("pageSize") Integer pageSize);

    /**
     * 查询数量
     * @param param
     * @return
     */
    List<KitchenEquipStatDto> queryKitchenCountInfo(@Param("param") KitchenEquipStatFvo param);


    List<SyncKitchenEquipDTO> syncKitchenEquipStat(@Param("id") String id);

    List<Map<String, Object>> getKitchenRankList(@Param("params") Map<String, Object> paramsMap);
}
