package com.redxun.fire.core.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.SelectByBuildId;
import com.redxun.fire.core.utils.validated.SelectById;
import com.redxun.fire.core.utils.validated.SelectByJJType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 事件统计表 为了配合Jpaas来实现
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_event_statistics")
public class BaseEventStatistics implements Serializable {

    private List<String> ids;

    /**
     * 点位受理的id
     */
    private String receivingId;
    private String receiver;
    private static final long serialVersionUID = 1L;

    /**
     * 疑似火警点位类型
     */
    private String bp;
    /**
     * 疑似火警点位描述
     */
    private String pointDescribe;
    private String pointCount;
    /**
     * 事件总数
     */
    private String eventCount;
    /**
     * 分页对象
     */
    private Page page;
    /**
     * 主键id
     */
    @NotBlank(message = "主键 id 不能为空 ", groups = Select.class)
    @NotBlank(message = "主键 id 不能为空 ", groups = SelectById.class)
    private String id;
    /**
     * 建筑物id
     */
    @NotBlank(message = "建筑物id buildId 不能为空 ", groups = SelectByBuildId.class)
    private String buildId;

    /**
     * 建筑物名称
     */
    private String buildName;

    /**
     * 事件类型  暂定字符串类型 目前有6种  确认火警 疑似火警 自动火警 漏岗 水泵异常 水压异常 7传输异常 8 重大隐患 9 较大隐患
     */
    private String eventType;

    /**
     * 事件类型文字
     */
    private String eventTypeStr;

    /**
     * 关联的事件id 可能是火警 可能是水系统 可能是查岗
     */
    private String eventId;

    /**
     * 事件上报时间
     */
    private Object reportTime;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 点位编号
     */
    private String pointCode;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 点位类型 这个暂时不确定几类 后期修改
     */
    private String pointType;

    /**
     * 点位类型文字
     */
    private String pointTypeStr;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 处理结果文字
     */
    private String processResultStr;

    /**
     * 附件 图片信息地址 中间用,隔开
     */
    private String attach;

    /**
     * 受理情况
     */
    private String situation;

    /**
     * 消防控制室电话
     */
    private String tel;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField("CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME_")
    private Object createTime;

    /**
     * 更新人ID
     */
    @TableField("UPDATE_BY_")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME_")
    private Object updateTime;

    /**
     * 类型  0 为待受理 1为待核实  2为已受理
     */
    private String type;

    /**
     * 业态
     */
    @NotBlank(message = "业态 jjType 不能为空 1为广场 0为其他", groups = SelectByBuildId.class)
    @NotBlank(message = "业态 jjType 不能为空 1为广场 0为其他", groups = SelectByJJType.class)
    private String jjType;

    /**
     * 文件路径
     */
    @TableField("filePath")
    private String filePath;

    /**
     * 受理类型
     */
    @NotBlank(message = "受理类型 acceptType 不能为空 ", groups = Select.class)
    private String acceptType;

    /**
     * 受理类型名
     */
    private String acceptName;

    /**
     * 误报原因
     */
    private String causeType;

    /**
     * 误报原因名
     */
    private String causeName;
    /**
     * 防火分区名
     */
    private String zoneName;
    /**
     * 防火分区id
     */
    private String zone_id;

    /**
     * 所属部门的组织机构id
     */
    @TableField(value = "belong_dep")
    private String belongDep;

    @TableField(value = "valid_time")
    private String validTime;

    /**
     * 文件路径
     */
    @TableField("face_inspect_id")
    private String faceInspectId;

    /**
     * 文件路径
     */
    @TableField("face_inspect_type")
    private String faceInspectType;

    @TableField(exist = false)
    private String reportTimeStr;


}
