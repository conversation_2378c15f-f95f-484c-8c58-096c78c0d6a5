package com.redxun.fire.core.entity.dutymanage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员排班表
 * @TableName person_duty_info
 */
@TableName(value ="person_duty_info")
@Data
@Accessors(chain = true)
public class PersonDutyInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 建筑物
     */
    @TableField(value = "build_id")
    private String buildId;

    /**
     * 员工id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 员工姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 员工机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 员工机构
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 员工职位
     */
    @TableField(value = "user_position")
    private String userPosition;

    /**
     * 值班年
     */
    @TableField(value = "duty_year")
    private String dutyYear;

    /**
     * 值班月份
     */
    @TableField(value = "duty_month")
    private String dutyMonth;

    /**
     * 值班日期
     */
    @TableField(value = "duty_date")
    private String dutyDate;

    /**
     * 值班时间
     */
    @TableField(value = "duty_time")
    private String dutyTime;

    /**
     * 值班班次
     */
    @TableField(value = "duty_type")
    private String dutyType;

    /**
     * 值班班次
     */
    @TableField(value = "duty_type_name")
    private String dutyTypeName;

    /**
     * 
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField(value = "CREATE_DEP_ID_")
    private String createDepId;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_BY_")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME_")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_BY_")
    private String updateBy;


    /**
     */
    @TableField(value = "shift_id")
    private String shiftId;

    /**
     */
    @TableField(value = "person_duty_state")
    private String personDutyState;

    /**
     */
    @TableField(value = "import_state")
    private String importState;

    /**
     */
    @TableField(value = "approve_user_id")
    private String approveUserId;


    /**
     */
    @TableField(value = "import_process_id")
    private String importProcessId;

    @TableField(value = "review_result")
    private String reviewResult;

    @TableField(value = "off_duty_duration")
    private String offDutyDuration;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME_")
    private Date updateTime;

    @TableField(exist = false)
    private String adjustFlag;

    @TableField(value = "begin_time")
    private Date beginTime;

    @TableField(value = "end_time")
    private Date endTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}