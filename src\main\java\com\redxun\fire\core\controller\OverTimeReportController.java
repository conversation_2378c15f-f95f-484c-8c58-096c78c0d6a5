package com.redxun.fire.core.controller;

import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.FireInfo;
import com.redxun.fire.core.mapper.FireInfoMapper;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.RedisUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 * 超时报备 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@RestController
@RequestMapping("/overTimeReport")
public class OverTimeReportController {

    @Resource
    private OrgManageClient orgManageClient;

    @Resource
    private OrgMiddleServiceImpl orgMiddleService;

    @Autowired
    private RedisUtils redisUtils;
    @Resource
    private FireInfoMapper fireInfoMapper;
    Gson gson = new Gson();

    @ResponseBody
    @PostMapping(value = "/overTimeReportPassApply")
    @ApiOperation("超时报备审批通过")
    public JsonResult overTimeReportPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("超时报备通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        FireInfo fireInfo = fireInfoMapper.selectInfoById(data);
        fireInfo.setOverTimeStatus("1");
        fireInfoMapper.updateById(fireInfo);
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/overTimeReportNoPassApply")
    @ApiOperation("超时报备审批未通过")
    public JsonResult overTimeReportNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("超时报备未通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");

        return jsonResult;
    }
}
