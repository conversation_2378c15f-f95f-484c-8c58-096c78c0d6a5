package com.redxun.fire.core.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.utils.ExceptionUtil;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.WbCheckApply;
import com.redxun.fire.core.pojo.vo.WbCheckApplyBuildingVo;
import com.redxun.fire.core.service.maintenance.IWbCheckApplyService;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2022-01-20
 */
@Slf4j
@RestController
@RequestMapping("/wbCheckApply")
public class WbCheckApplyController extends IJobHandler {

    @Resource
    IWbCheckApplyService wbCheckApplyService;

    /**
     * APP获取测试|联动计划列表(分页)
     */
    @PostMapping("getWbCheckApplyPage")
    public JsonResult getWbCheckApplyPage(HttpServletRequest request, @RequestBody QueryData queryData) {

        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        try {
            IPage<WbCheckApplyBuildingVo> page = wbCheckApplyService.getWbCheckApplyPage(request, queryData);
            jsonResult.setPageData(page);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    /**
     * 录入预约
     */
    @PostMapping("apply")
    public JsonResult apply(HttpServletRequest request, @RequestBody WbCheckApply wbCheckApply) {

        JsonResult jsonResult = new JsonResult();
        try {
            jsonResult = wbCheckApplyService.apply(request, wbCheckApply);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    /**
     * 修改状态
     */
    @PostMapping("updateStatus")
    public JsonResult updateStatus(@RequestBody WbCheckApply wbCheckApply) {

        JsonResult jsonResult = new JsonResult();
        try {
            jsonResult = wbCheckApplyService.updateStatus(wbCheckApply);
        } catch (Exception ex) {
            jsonResult.setSuccess(false);
            log.error(ExceptionUtil.getExceptionMessage(ex));
            jsonResult.setMessage(ExceptionUtil.getExceptionMessage(ex));
        }
        return jsonResult;
    }

    /**
     * 自动结束过期的定时任务
     * @throws Exception
     */
    @Override
    @XxlJob("autoEndWbCheckJob")
    public void execute() throws Exception {
        wbCheckApplyService.autoEndWbCheck();
    }

    /**
     * 获取当前预约测试点位总数
     */
    @PostMapping("getPointNumById/{id}")
    public JsonResult getPointNumById(@PathVariable("id") String id) {
        return JsonResult.Success("返回数据成功!").setData(wbCheckApplyService.getPointNumById(id));
    }

    /**
     * 获取当前预约测试系统
     */
    @PostMapping("getFireproofSysNameById/{id}")
    public JsonResult getFireproofSysNameById(@PathVariable("id") String id) {
        return JsonResult.Success("返回数据成功!").setData(wbCheckApplyService.getFireproofSysName(id));
    }

    /**
     * 获取当前预约测试系统下对应的点位
     */
    @PostMapping("getDeviceInfoById/{id}")
    public JsonResult updateStatus(@PathVariable("id") String id, String fireproofSysName) {
        return JsonResult.Success("返回数据成功!").setData(wbCheckApplyService.getTestDeviceInfo(id, fireproofSysName));
    }

    @ApiOperation(value = "获取审批人")
    @GetMapping({"/checkApplyjobApprove"})
    public List<TaskExecutor> checkApplyjobApprove(@RequestParam(value = "applyId") String applyId,
                                                   @RequestParam(value = "nodeId") String nodeId,
                                                   @RequestParam(value = "buildingId") String buildingId,
                                                   @RequestParam(value = "userId") String userId,
                                                   @RequestParam(value = "bpmDefId") String bpmDefId) {
        return wbCheckApplyService.checkApplyjobApprove(applyId, nodeId, buildingId, userId, bpmDefId);
    }
    @ApiOperation(value = "获取审批人")
    @GetMapping({"/checkApplyjobApproveNoNode"})
    public List<TaskExecutor> checkApplyjobApproveNoNode(@RequestParam(value = "applyId") String applyId,
                                                   @RequestParam(value = "jobId") String jobId,
                                                   @RequestParam(value = "buildingId") String buildingId,
                                                   @RequestParam(value = "userId") String userId,
                                                   @RequestParam(value = "level") String level) {
        return wbCheckApplyService.checkApplyjobApproveNoNode(applyId, jobId, buildingId, userId, level);
    }
    @ApiOperation(value = "是否有执行人")
    @GetMapping({"/isNoUser"})
    public JsonResult isNoUser(@RequestParam(value = "applyId") String applyId,
                                                   @RequestParam(value = "nodeId") String nodeId,
                                                   @RequestParam(value = "buildingId") String buildingId,
                                                   @RequestParam(value = "userId") String userId,
                                                   @RequestParam(value = "bpmDefId") String bpmDefId) {
        return wbCheckApplyService.isNoUser(applyId, nodeId, buildingId, userId, bpmDefId);
    }
    @ApiOperation(value = "获取审批人多个职务审批")
    @GetMapping({"/chooseJobApprove"})
    public List<TaskExecutor> chooseJobApprove(@RequestParam(value = "applyId") String applyId,
                                               @RequestParam(value = "nodeId") String nodeId,
                                               @RequestParam(value = "buildingId") String buildingId,
                                               @RequestParam(value = "userId") String userId,
                                               @RequestParam(value = "bpmDefId") String bpmDefId) {
        return wbCheckApplyService.chooseJobApprove(applyId, nodeId, buildingId, userId, bpmDefId);
    }

    /**
     * 获取当前用户是否有其他开启广场
     */
    @GetMapping("getOpenBuilding/{buildingId}")
    public JsonResult getOpenBuilding(HttpServletRequest request, @PathVariable("buildingId") String buildingId) {
        return JsonResult.Success(wbCheckApplyService.getOpenBuilding(request, buildingId));
    }

    @ResponseBody
    @PostMapping(value = "/wbCheckPassApply")
    @ApiOperation("检查模式申请审批通过")
    public JsonResult wbCheckPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        return wbCheckApplyService.wbCheckPassApply(jsonObject);
    }

    @ResponseBody
    @PostMapping(value = "/wbCheckNoPassApply")
    @ApiOperation("检查模式申请审批未通过")
    public JsonResult wbCheckNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        return wbCheckApplyService.wbCheckNoPassApply(jsonObject);
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return wbCheckApplyService.flowEvent(dataJson);
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/wbCheckApplyFlowEvent")
    public Object wbCheckApplyFlowEvent(@RequestBody JSONObject dataJson) {
        return wbCheckApplyService.wbCheckApplyFlowEvent(dataJson);
    }

}
