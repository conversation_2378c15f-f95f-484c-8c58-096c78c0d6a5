package com.redxun.fire.core.service.alarm.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.consts.ConstantUtil;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.influxdb.dao.InfluxDataDao;
import com.redxun.fire.core.influxdb.entity.LiquidInfluxData;
import com.redxun.fire.core.influxdb.entity.PreInfluxData;
import com.redxun.fire.core.influxdb.entity.PumpInfluxData;
import com.redxun.fire.core.kitchen.entity.KitchenEquipStat;
import com.redxun.fire.core.kitchen.mapper.KitchenEquipStatMapper;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.pojo.vo.BaseFloorInfoVo;
import com.redxun.fire.core.pojo.vo.FloorInfoReturnVo;
import com.redxun.fire.core.service.alarm.IStatWaterPressRealService;
import com.redxun.fire.core.service.alarm.WaterMonitorService;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.device.IWdDevConfigService;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.service.monitor.IMonitorConsoleService;
import com.redxun.fire.core.service.other.IStatPumpRealService;
import com.redxun.fire.core.utils.POIExcelUtil;
import com.redxun.fire.core.utils.*;
import dm.jdbc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 水系统监测（水泵、水压）
 * @since 20200921
 */
@Slf4j
@Service
public class WaterMonitorServiceImpl extends ServiceImpl<WaterMonitorMapper, Map> implements WaterMonitorService {

    @Resource
    private IBaseDevicePointService iBaseDevicePointService;
    @Resource
    private IBaseBuildingService baseBuildingService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private InfluxDataDao influxDataDao;
    @Resource
    private IBaseBuildingFloorService baseBuildingFloorService;
    @Resource
    private ConfigRelationMapper configRelationMapper;
    @Resource
    private IStatWaterPressRealService iStatWaterPressRealService;
    @Resource
    private IStatPumpRealService statPumpRealService;
    @Resource
    private IStatPumpRealService iStatPumpRealService;
    @Resource
    private StatWaterExpectionMapper statWaterExpectionMapper;
    @Resource
    private StatPumpExpectionMapper statPumpExpectionMapper;
    @Resource
    private StatWaterPressRealMapper statWaterPressRealMapper;
    @Resource
    private StatPumpRealMapper statPumpRealMapper;
    @Resource
    private BaseDevicePointMapper baseDevicePointMapper;
    @Resource
    private IMonitorConsoleService monitorConsoleService;
    @Resource
    private IJournalizingService journalizingService;
    @Resource
    private IBaseBuildingFloorService iBaseBuildingFloorService;
    @Resource
    private WaterMonitorService waterMonitorService;
    @Resource
    private IWdDevConfigService wdDevConfigService;
    @Resource
    IBaseDeviceTemporaryService baseDeviceTemporaryService;
    @Resource
    StatCloseStoreRealMapper statCloseStoreRealMapper;
    @Autowired
    DropDownServiceImpl dropDownService;

    @Resource
    private KitchenEquipStatMapper kitchenEquipStatMapper;


    @Override
    public Map<String, Object> getWaterPressureDeviceData(HttpServletRequest request, Map<String, Object> paramsMap) {
        List<String> userBuildingList = monitorConsoleService.getUserBuildingList(request, paramsMap);
        paramsMap.put("buildList", userBuildingList);
        //获取点位总数
        Integer deviceTotalNum = queryWaterPressureAllPointNum(paramsMap);
        //获取异常设备总数
        Integer abnormalTotalNum = queryWaterPressureAbnormalNum(paramsMap);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("deviceTotalNum", deviceTotalNum);
        returnMap.put("normalTotalNum", (deviceTotalNum - abnormalTotalNum) < 0 ? 0 : (deviceTotalNum - abnormalTotalNum));
        returnMap.put("abnormalTotalNum", abnormalTotalNum);
        return returnMap;
    }

    @Override
    public Map<String, Object> getWaterPressureAbnormalRankList(HttpServletRequest request, Map<String, Object> paramsMap) {
        List<String> buildingList = monitorConsoleService.getUserBuildingList(request, paramsMap);
        paramsMap.put("buildList", buildingList);
        List<Map<String, Object>> dataList = this.baseMapper.getWaterPressureAbnormalList(paramsMap);
        String type = (String) paramsMap.get("type");
        //1-压力异常, 2-设备异常
        List<Map<String, Object>> resultList = analyseDataList(dataList, "1".equals(type) ? 1 : 2);
        Map<String, Object> resultMap = new HashMap<>();
        filterList(resultList);
        resultMap.put("abnormalList", resultList);
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult pressureDeviceRegister(HttpServletRequest request, Map<String, Object> paramsMap) {
        JsonResult jsonResult = new JsonResult(true);
        String userId = request.getHeader("xfUserId");//消防设施userId
        //建筑物id
        String buildId = (String) paramsMap.get("buildId");
        //建筑物名称
        String buildName = (String) paramsMap.get("buildName");
        //RTU编号
        String hostId = (String) paramsMap.get("hostId");
        //从地址号
        String pointCode = (String) paramsMap.get("pointCode");

        String devTypeCode = (String) paramsMap.get("devTypeCode");
        //设备类型
        String devTypeName = (String) paramsMap.get("devTypeName");
        String floorId = (String) paramsMap.get("floorId");
        //物理楼层
        String floor = (String) paramsMap.get("floor");
        //楼层名称
        String floorName = (String) paramsMap.get("floorName");
        //最大阈值
        String maxval = (String) paramsMap.get("maxval");
        //最小阈值
        String minval = (String) paramsMap.get("minval");
        //点位描述
        String pointDesc = (String) paramsMap.get("pointDesc");
        //所属报警阀
        String alarmValve = (String) paramsMap.get("alarmValve");
        //传输设备卡号
        String simNum = (String) paramsMap.get("simNum");
        String errorMsg = "";
        if (StringUtils.isEmpty(buildId)) {
            errorMsg = "建筑物编号不能为空";
        } else if (StringUtils.isEmpty(hostId)) {
            errorMsg = "RTU编号不能为空";
        } else if (StringUtils.isEmpty(pointCode)) {
            errorMsg = "从地址号不能为空";
        } else if (StringUtils.isEmpty(devTypeName)) {
            errorMsg = "设备类型不能为空";
        } else if (StringUtils.isEmpty(floorId)) {
            errorMsg = "楼层不能为空";
        } else if (StringUtils.isEmpty(maxval)) {
            errorMsg = "最大阈值不能为空";
        } else if (StringUtils.isEmpty(minval)) {
            //errorMsg = "最小阈值不能为空";
        } else if (StringUtils.isEmpty(pointDesc)) {
            errorMsg = "位置描述不能为空";
        } else if (!"YLX".equals(devTypeCode) && !"FQYL".equals(devTypeCode) && !"SPYG".equals(devTypeCode)) {
            if (StringUtils.isEmpty(alarmValve)) {
                errorMsg = "所属报警阀不能为空";
            }
        } else if (StringUtils.isEmpty(simNum)) {
            errorMsg = "传输设备卡号不能为空";
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            jsonResult.setMessage(errorMsg);
            //throw new RuntimeException(errorMsg);
        }
        //点位号
        String pointId = hostId + "-" + pointCode;
        QueryWrapper<BaseDevicePoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("point_number", pointId);
        queryWrapper.eq("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
//        queryWrapper.eq("building_id", buildId);
        //queryMap.put("building_id", buildId);
        List<BaseDevicePoint> resultList = iBaseDevicePointService.list(queryWrapper);
        if (null != resultList && resultList.size() > 0) {
            jsonResult.setMessage("点位号（RTU编号+从地址号）已存在");
            //throw new RuntimeException("点位号（RTU编号+从地址号）已存在");
        }
        BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
        baseDevicePoint.setBuildingId(buildId);
        baseDevicePoint.setBuildName(buildName);
        baseDevicePoint.setAlarmValve(alarmValve);
        baseDevicePoint.setDevTypeCode(devTypeCode);
        baseDevicePoint.setDevTypeName(devTypeName);
        baseDevicePoint.setPointNumber(pointId);
        baseDevicePoint.setPid(hostId + pointCode);
        baseDevicePoint.setHostId(hostId);
        baseDevicePoint.setDid(pointCode);
        baseDevicePoint.setPointCode(pointCode);
        baseDevicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        baseDevicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
        baseDevicePoint.setMaxval(new BigDecimal(maxval));
        if (StringUtils.isNotEmpty(minval)) {
            baseDevicePoint.setMinval(new BigDecimal(minval));
        }
        baseDevicePoint.setFloorId(floorId);
        if (StringUtils.isNotEmpty(floor)) {
            baseDevicePoint.setFloor(Integer.valueOf(floor));
        }
        if (StringUtils.isNotEmpty(floorName)) {
            baseDevicePoint.setFloorName(floorName);
        }
        baseDevicePoint.setPointDesc(pointDesc);
        baseDevicePoint.setBrandName((String) paramsMap.get("brandName"));
        baseDevicePoint.setSimNum(simNum);
        baseDevicePoint.setCreateBy(userId);
        baseDevicePoint.setUpdateBy(userId);
        baseDevicePoint.setCreateTime(new Date());
        baseDevicePoint.setUpdateTime(new Date());
        Boolean flag = false;
        try {
            flag = iBaseDevicePointService.savePointInfo(baseDevicePoint);
        } catch (Exception e) {
            jsonResult.setMessage("点位重复无法新增！");
            //throw new RuntimeException("点位重复无法新增！");
        }
        //水泵数据发生改变后刷新维保相关数据
        List<BaseDevicePoint> baseDevicePointList = new ArrayList<>();
        baseDevicePointList.add(baseDevicePoint);
        baseDeviceTemporaryService.restPlanByPoint(baseDevicePoint.getBuildingId(), baseDevicePointList);
        if (flag) {
            setPointInfoToRedis(baseDevicePoint);
            editStatWaterPressureTotal(buildId, 1);

            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationContent("新增" + baseDevicePoint.getBuildName() + "的压力设备" + hostId + "+" + pointCode);
            journalizing.setOperationTypeCode(LogTypeEnums.WATER_MONITOR.getType());
            journalizingService.setLogInfo(request, journalizing);
            return jsonResult;
        }
        return jsonResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean liquidDeviceRegister(HttpServletRequest request, Map<String, Object> paramsMap) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        //建筑物id
        String buildId = (String) paramsMap.get("buildId");
        //RTU编号
        String hostId = (String) paramsMap.get("hostId");
        //从地址号
        String pointCode = (String) paramsMap.get("pointCode");
        //设备类型
        String devTypeName = (String) paramsMap.get("devTypeName");
        String floorId = (String) paramsMap.get("floorId");
        //物理楼层
        String floor = (String) paramsMap.get("floor");
        //楼层名称
        String floorName = (String) paramsMap.get("floorName");
        //最大阈值
        String maxval = (String) paramsMap.get("maxval");
        //最小阈值
        String minval = (String) paramsMap.get("minval");
        //点位描述
        String pointDesc = (String) paramsMap.get("pointDesc");
        //传输设备卡号
        String simNum = (String) paramsMap.get("simNum");
        String errorMsg = "";
        if (StringUtils.isEmpty(buildId)) {
            errorMsg = "建筑物编号不能为空";
        } else if (StringUtils.isEmpty(hostId)) {
            errorMsg = "RTU编号不能为空";
        } else if (StringUtils.isEmpty(pointCode)) {
            errorMsg = "从地址号不能为空";
        } else if (StringUtils.isEmpty(devTypeName)) {
            errorMsg = "设备类型不能为空";
        } else if (StringUtils.isEmpty(floorId)) {
            errorMsg = "楼层不能为空";
        } else if (StringUtils.isEmpty(maxval)) {
            errorMsg = "最大阈值不能为空";
        } else if (StringUtils.isEmpty(minval)) {
            //errorMsg = "最小阈值不能为空";
        } else if (StringUtils.isEmpty(pointDesc)) {
            errorMsg = "位置描述不能为空";
        } else if (StringUtils.isEmpty(simNum)) {
            errorMsg = "传输设备卡号不能为空";
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            throw new RuntimeException(errorMsg);
        }
        //点位号
        String pointId = hostId + "-" + pointCode;
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("point_code", pointId);
        queryMap.put("building_id", buildId);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_LIQUID_GAGE);
        List<BaseDevicePoint> resultList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null != resultList && resultList.size() > 0) {
            throw new RuntimeException("点位号（RTU编号+从地址号）已存在");
        }
        BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
        baseDevicePoint.setBuildingId(buildId);
        baseDevicePoint.setBuildName((String) paramsMap.get("buildName"));
        baseDevicePoint.setDevTypeCode((String) paramsMap.get("devTypeCode"));
        baseDevicePoint.setDevTypeName(devTypeName);
        baseDevicePoint.setPointNumber(pointId);
        baseDevicePoint.setPid(hostId + pointCode);
        baseDevicePoint.setHostId(hostId);
        baseDevicePoint.setDid(pointCode);
        baseDevicePoint.setPointCode(pointCode);
        baseDevicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_LIQUID_GAGE);
        baseDevicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
        baseDevicePoint.setMaxval(new BigDecimal(maxval));
        if (StringUtils.isNotEmpty(minval)) {
            baseDevicePoint.setMinval(new BigDecimal(minval));
        }
        baseDevicePoint.setFloorId(floorId);
        if (StringUtils.isNotEmpty(floor)) {
            baseDevicePoint.setFloor(Integer.valueOf(floor));
        }
        baseDevicePoint.setFloorName(floorName);
        baseDevicePoint.setPointDesc(pointDesc);
        baseDevicePoint.setBrandName((String) paramsMap.get("brandName"));
        baseDevicePoint.setSimNum(simNum);
        baseDevicePoint.setCreateBy(userId);
        baseDevicePoint.setUpdateBy(userId);
        baseDevicePoint.setCreateTime(new Date());
        baseDevicePoint.setUpdateTime(new Date());
        //水泵数据发生改变后刷新维保相关数据
        List<BaseDevicePoint> baseDevicePointList = new ArrayList<>();
        baseDevicePointList.add(baseDevicePoint);
        baseDeviceTemporaryService.restPlanByPoint(baseDevicePoint.getBuildingId(), baseDevicePointList);
        if (iBaseDevicePointService.savePointInfo(baseDevicePoint)) {
            setPointInfoToRedis(baseDevicePoint);
            editStatWaterPressureTotal(buildId, 1);
            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationContent("新增" + baseDevicePoint.getBuildName() + "的液位设备" + hostId + "+" + pointCode);
            journalizing.setOperationTypeCode(LogTypeEnums.WATER_MONITOR.getType());
            journalizingService.setLogInfo(request, journalizing);

            return true;
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> queryPressureMonitorData(Map<String, Object> paramsMap) {
        //建筑物id
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物id不能为空");
        }
        if ("1".equals(paramsMap.get("type"))) {
            //压力监测
            return queryPointInfo(buildId, ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE, ConstantUtil.REDIS_KEY_WATER_PRESSURE,
                    (String) paramsMap.get("abnormalType"));
        } else {
            //液位监测
            return queryPointInfo(buildId, ConstantUtil.DEVICE_TYPE_LIQUID_GAGE, ConstantUtil.REDIS_KEY_WATER_LIQUID,
                    (String) paramsMap.get("abnormalType"));
        }
    }

    @Override
    @Transactional
    public boolean deletePointInfo(HttpServletRequest request, Map<String, Object> paramsMap) {
        if (StringUtils.isEmpty((String) paramsMap.get("id"))) {
            throw new RuntimeException("点位id不能为空");
        }
        BaseDevicePoint baseDevicePoint = iBaseDevicePointService.getDevicePointById((String) paramsMap.get("id"));
        if (null == baseDevicePoint) {
            throw new RuntimeException("该点位不存在");
        }

        if (iBaseDevicePointService.deletePointInfo(baseDevicePoint.getId())) {
            String result = "删除" + baseDevicePoint.getBuildName();

            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.WATER_MONITOR.getType());

            String key = null;
            if (ConstantUtil.DEVICE_TYPE_PUMP.equals(baseDevicePoint.getSuperType())) {
                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_PUMP + baseDevicePoint.getId();
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("point_id", baseDevicePoint.getId());
                statPumpExpectionMapper.deleteByMap(deleteMap);
                editStatPumpPressureTotal(key, baseDevicePoint.getBuildingId());

                result += ("的⽔泵设备设备编号" + baseDevicePoint.getDeviceNo());
                journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());

            } else if (ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE.equals(baseDevicePoint.getSuperType())) {

                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_WATER_PRESSURE + baseDevicePoint.getId();
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("point_id", baseDevicePoint.getId());
                //删除异常表数据
                statWaterExpectionMapper.deleteByMap(deleteMap);
                editStatWaterPressureTotal(key, baseDevicePoint.getBuildingId());

                result += ("的压力设备" + baseDevicePoint.getHostId() + "+" + baseDevicePoint.getPointCode());
//                editStatWaterPressureTotal(baseDevicePoint.getBuildingId(), -1);
            } else if (ConstantUtil.DEVICE_TYPE_LIQUID_GAGE.equals(baseDevicePoint.getSuperType())) {
                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_WATER_LIQUID + baseDevicePoint.getId();
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("point_id", baseDevicePoint.getId());
                //删除异常表数据
                statWaterExpectionMapper.deleteByMap(deleteMap);
                editStatWaterLiquidTotal(key, baseDevicePoint.getBuildingId());

                result += ("的液位设备" + baseDevicePoint.getHostId() + "+" + baseDevicePoint.getPointCode());
//                editStatWaterPressureTotal(baseDevicePoint.getBuildingId(), -1);
            }
            if (StringUtils.isNotEmpty(key)) {
                redisUtils.remove(key);
            }

            journalizing.setOperationContent(result);
            journalizingService.setLogInfo(request, journalizing);
            return true;
        }
        return false;
    }

    private void editStatWaterLiquidTotal(String key, String buildingId) {
        //更新水压redis
        Object o = redisUtils.get(key);
        if (o != null) {
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(o), FarEastoneCache.class);
            String highStatus = farEastoneCache.getHighStatus();
            String lowStatus = farEastoneCache.getLowStatus();
            String deviceStatus = farEastoneCache.getDeviceStatus();
            List<StatWaterPressReal> list = iStatWaterPressRealService.list(new QueryWrapper<StatWaterPressReal>().eq("build_id", buildingId));
            if (list != null && list.size() > 0) {
                StatWaterPressReal statWaterPressReal = list.get(0);
                if ("1".equals(highStatus)) {
                    Integer liquidOnPoint = statWaterPressReal.getLiquidOnPoint();
                    statWaterPressReal.setLiquidOnPoint(liquidOnPoint - 1);
                }
                if ("1".equals(lowStatus)) {
                    Integer liquidDownPoint = statWaterPressReal.getLiquidDownPoint();
                    statWaterPressReal.setLiquidDownPoint(liquidDownPoint - 1);
                }
                if ("1".equals(deviceStatus)) {
                    Integer waterPointOff = statWaterPressReal.getWaterPointOff();
                    statWaterPressReal.setWaterPointOff(waterPointOff - 1);
                }
                Integer current = statWaterPressReal.getWaterPressPoint();
                statWaterPressReal.setWaterPressPoint(current - 1);
                iStatWaterPressRealService.updateById(statWaterPressReal);
            }
        }
    }

    private void editStatPumpPressureTotal(String key, String buildingId) {
        Object o = redisUtils.get(key);
        if (o != null) {
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(o), FarEastoneCache.class);
            //运行状态，0-停止，1-运行（水泵） 异常状态：运行
            String operationStatus = farEastoneCache.getOperationStatus();
            //手动状态，0-手动，1-自动（水泵） 异常状态：手动
            String manualStatus = farEastoneCache.getManualStatus();
            //断电状态，0-断电，1-已上电（水泵） 异常状态：断电
            String outageStatus = farEastoneCache.getOutageStatus();
            //故障
            String faultStatus = farEastoneCache.getFaultStatus();
            //离线状态，0-正常，1-离线（水泵） 异常状态：离线
            String deviceStatus = farEastoneCache.getDeviceStatus();
            List<StatPumpReal> list = statPumpRealService.list(new QueryWrapper<StatPumpReal>().eq("build_id", buildingId));
            boolean flag = true;
            if (null != list && list.size() > 0 && null != list.get(0)) {
                StatPumpReal statPumpReal = list.get(0);
                if ("1".equals(operationStatus)) {
                    Integer current = statPumpReal.getRunPumps();
                    statPumpReal.setRunPumps(current - 1);
                    flag = false;
                }
                if ("0".equals(manualStatus)) {
                    Integer current = statPumpReal.getHandsPumps();
                    statPumpReal.setHandsPumps(current - 1);
                    flag = false;
                }
                if ("0".equals(outageStatus)) {
                    Integer current = statPumpReal.getOutagePumps();
                    statPumpReal.setOutagePumps(current - 1);
                    flag = false;
                }
                if ("1".equals(faultStatus)) {
                    Integer current = statPumpReal.getFaultPumps();
                    statPumpReal.setFaultPumps(current - 1);
                    flag = false;
                }
                if ("1".equals(deviceStatus)) {
                    Integer current = statPumpReal.getOffPumps();
                    statPumpReal.setOffPumps(current - 1);
                    flag = false;
                }

                if (flag) {
                    Integer current = statPumpReal.getNormalPumps();
                    statPumpReal.setNormalPumps(current - 1);
                    Integer pumpCount = statPumpReal.getPumpCount();
                    statPumpReal.setPumpCount(pumpCount - 1);
                    statPumpRealService.updateById(statPumpReal);
                } else {
                    Integer pumpCount = statPumpReal.getPumpCount();
                    statPumpReal.setPumpCount(pumpCount - 1);
                    statPumpRealService.updateById(statPumpReal);
                }
            }
        }
    }

    @Override
    public boolean editPressurePointInfoById(HttpServletRequest request, Map<String, Object> paramsMap) {
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        if (StringUtils.isEmpty((String) paramsMap.get("id"))) {
            throw new RuntimeException("点位id不能为空");
        }
        BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
        baseDevicePoint.setId((String) paramsMap.get("id"));
        if (StringUtils.isNotEmpty((String) paramsMap.get("devTypeCode"))) {
            baseDevicePoint.setDevTypeCode((String) paramsMap.get("devTypeCode"));
        } else {
            throw new RuntimeException("设备类型不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("hostId"))) {
            baseDevicePoint.setHostId((String) paramsMap.get("hostId"));
        } else {
            throw new RuntimeException("RTU编号不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("pointCode"))) {
            baseDevicePoint.setPointCode((String) paramsMap.get("pointCode"));
            baseDevicePoint.setDid((String) paramsMap.get("pointCode"));
            baseDevicePoint.setPointNumber(baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid());
        } else {
            throw new RuntimeException("从地址号不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("devTypeName"))) {
            baseDevicePoint.setDevTypeName((String) paramsMap.get("devTypeName"));
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("maxval"))) {
            baseDevicePoint.setMaxval(new BigDecimal((String) paramsMap.get("maxval")));
        } else {
            throw new RuntimeException("最大阈值不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("minval"))) {
            baseDevicePoint.setMinval(new BigDecimal((String) paramsMap.get("minval")));
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floorId"))) {
            baseDevicePoint.setFloorId((String) paramsMap.get("floorId"));
        } else {
            throw new RuntimeException("楼层id不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floor"))) {
            baseDevicePoint.setFloor(Integer.valueOf((String) paramsMap.get("floor")));
        } else {
            throw new RuntimeException("楼层不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floorName"))) {
            baseDevicePoint.setFloorName((String) paramsMap.get("floorName"));
        } else {
            throw new RuntimeException("楼层名称不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("pointDesc"))) {
            baseDevicePoint.setPointDesc((String) paramsMap.get("pointDesc"));
        } else {
            throw new RuntimeException("点位描述不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("alarmValve"))) {
            baseDevicePoint.setAlarmValve((String) paramsMap.get("alarmValve"));
        } else {
            throw new RuntimeException("所属报警阀不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("simNum"))) {
            baseDevicePoint.setSimNum((String) paramsMap.get("simNum"));
        }
        BaseDevicePoint oldPoint = baseDevicePointMapper.selectById((String) paramsMap.get("id"));
        if (!oldPoint.getPointNumber().equals(baseDevicePoint.getPointNumber())) {
            QueryWrapper<BaseDevicePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("did", baseDevicePoint.getDid());
            queryWrapper.eq("host_id", baseDevicePoint.getHostId());
            queryWrapper.eq("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
//            queryWrapper.eq("building_id", oldPoint.getBuildingId());
            //queryMap.put("building_id", buildId);
            List<BaseDevicePoint> resultList = iBaseDevicePointService.list(queryWrapper);
            if (null != resultList && resultList.size() > 0) {
                throw new RuntimeException("点位号（RTU编号+从地址号）已存在");
            }
        }

        baseDevicePoint.setBrandName((String) paramsMap.get("brandName"));
        baseDevicePoint.setUpdateBy(userId);
        baseDevicePoint.setUpdateTime(new Date());
        //更新redis
        String oldDeviceNo = oldPoint.getHostId();
        String oldAddressNo = oldPoint.getDid();
        updateRedis(oldPoint.getBuildingId(), oldDeviceNo, oldAddressNo, baseDevicePoint, oldPoint.getSuperType());


        //日志插入
        String result = "编辑" + paramsMap.get("buildName") + "液位设备" + oldPoint.getHostId() + oldPoint.getPointCode() + ":";
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getHostId(), oldPoint.getHostId())) {
            result += ("RTU编号" + oldPoint.getHostId() + "为" + baseDevicePoint.getHostId() + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getDevTypeName(), oldPoint.getDevTypeName())) {
            result += ("设备类型" + oldPoint.getDevTypeName() + "为" + baseDevicePoint.getDevTypeName() + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getPointCode(), oldPoint.getPointCode())) {
            result += ("从地址号" + oldPoint.getPointCode() + "为" + baseDevicePoint.getPointCode() + ",");
        }
        if (Objects.nonNull(baseDevicePoint.getSimNum()) && !baseDevicePoint.getSimNum().equals(oldPoint.getSimNum())) {
            result += ("传输设备卡号" + oldPoint.getSimNum() + "为" + baseDevicePoint.getSimNum() + ",");
        }
        if (Objects.nonNull(baseDevicePoint.getBrandName()) && !baseDevicePoint.getBrandName().equals(oldPoint.getBrandName())) {
            result += ("品牌名称" + oldPoint.getBrandName() + "为" + baseDevicePoint.getBrandName() + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getFloorName(), oldPoint.getFloorName())) {
            result += ("楼层" + oldPoint.getFloorName() + "为" + baseDevicePoint.getFloorName() + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getPointDesc(), oldPoint.getPointDesc())) {
            result += ("位置描述" + oldPoint.getPointDesc() + "为" + baseDevicePoint.getPointDesc() + ",");
        }
        if (Objects.nonNull(baseDevicePoint.getMaxval()) && baseDevicePoint.getMaxval().compareTo(oldPoint.getMaxval()) != 0) {
            result += ("最大阈值" + oldPoint.getMaxval().toString() + "为" + baseDevicePoint.getMaxval().toString() + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(baseDevicePoint.getAlarmValve(), oldPoint.getAlarmValve())) {
            result += ("所属报警阀" + oldPoint.getAlarmValve() + "为" + baseDevicePoint.getAlarmValve() + ",");
        }


        if (!("编辑" + paramsMap.get("buildName") + "压力设备" + oldPoint.getHostId() + "+" + oldPoint.getPointCode()).equals(result)) {
            result = result.substring(0, result.length() - 1);
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.WATER_MONITOR.getType());
            journalizing.setOperationContent(result);
            journalizingService.setLogInfo(request, journalizing);
        }

        return iBaseDevicePointService.editPressurePointInfoById(baseDevicePoint);
    }

    @Override
    public boolean editPumpPointInfoById(HttpServletRequest request, Map<String, Object> paramsMap) {
        if (StringUtils.isEmpty((String) paramsMap.get("id"))) {
            throw new RuntimeException("点位id不能为空");
        }
        String id = (String) paramsMap.get("id");
        String buildingId = paramsMap.get("buildingId").toString();
        if (StringUtils.isEmpty(buildingId)) {
            throw new RuntimeException("建筑物id为空");
        }
        String buildName = paramsMap.get("buildName").toString();
        //设备厂商编号
        String venderCode = paramsMap.get("venderCode").toString();
        //设备厂商名称
        String venderName = paramsMap.get("venderName").toString();
        //设备编号
        String deviceNo = paramsMap.get("deviceNo").toString();
        if (StringUtils.isEmpty(deviceNo)) {
            throw new RuntimeException("设备编号为空");
        }
        //设备类型编号
        String devTypeCode = paramsMap.get("devTypeCode").toString();
        //设备类型名称
        String devTypeName = paramsMap.get("devTypeName").toString();
        if (StringUtils.isEmpty(devTypeCode)) {
            throw new RuntimeException("设备类型为空");
        }
        //判断设备编号与设备类型唯一
//        Map<String, Object> queryMap1 = new HashMap<>();
//        queryMap1.put("building_id", buildingId);
//        queryMap1.put("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
//        queryMap1.put("dev_type_code", devTypeCode);
//        queryMap1.put("device_no", deviceNo);
//        List<BaseDevicePoint> pointList1 = iBaseDevicePointService.listSelectedPoints(queryMap1);
//        if (null != pointList1 && pointList1.size() > 0 && !pointList1.get(0).getId().equals(id)) {
//            throw new RuntimeException("设备编号&设备类型:" + deviceNo + "_" + devTypeName + "已存在，不能重复添加");
//        }
        //DTU
        String dtuNo = paramsMap.get("dtuNo").toString();
        if (StringUtils.isEmpty(dtuNo)) {
            throw new RuntimeException("DTU编号为空");
        }
        //接入点编号
        String joinCode = paramsMap.get("joinCode").toString();
        //接入点名称
        String joinName = paramsMap.get("joinName").toString();
        //检查接入点唯一
//        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("building_id", buildingId);
//        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
//        queryMap.put("pid", joinCode);
//        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
//        if (null != pointList && pointList.size() > 0 && !pointList.get(0).getId().equals(id)) {
//            throw new RuntimeException("DTU编号&接入点:" + joinCode + "已存在，不能重复添加");
//        }
//
        //安装位置
        String pointDesc = paramsMap.get("pointDesc").toString();
        if (StringUtils.isEmpty(pointDesc)) {
            throw new RuntimeException("安装位置为空");
        }
        //楼层id
        String floorId = paramsMap.get("floorId").toString();
        //楼层名称
        String floorName = paramsMap.get("floorName").toString();
        if (StringUtils.isEmpty(floorId)) {
            throw new RuntimeException("楼层为空");
        }
        devTypeCode = "SBJC";
        //流量卡号
        String simNum = paramsMap.get("simNum").toString();
        BaseDevicePoint devicePoint = new BaseDevicePoint();
        devicePoint.setId(id);
        devicePoint.setBuildingId(buildingId);
        devicePoint.setBuildName(buildName);
        devicePoint.setVenderCode(venderCode);
        devicePoint.setVenderName(venderName);
        devicePoint.setDeviceNo(deviceNo);
        if (StringUtils.isNotEmpty(joinCode)) {
            devicePoint.setJoinCode(joinCode);
        }
        devicePoint.setDeviceName(devTypeName + deviceNo);
        if (StringUtils.isNotEmpty(joinName)) {
            devicePoint.setJoinName(joinName);
        }
        devicePoint.setDevTypeCode(devTypeCode);
        devicePoint.setDevTypeName(devTypeName);
        devicePoint.setPointDesc(pointDesc);
        devicePoint.setFloorId(floorId);
        if (null != paramsMap.get("floor")) {
            devicePoint.setFloor(Integer.parseInt(paramsMap.get("floor").toString()));
        }
        if (StringUtils.isNotEmpty(floorName)) {
            devicePoint.setFloorName(floorName);
        }
        devicePoint.setSimNum(simNum);
        devicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_PUMP);
        devicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
        devicePoint.setDtuNo(dtuNo);
        devicePoint.setUpdateTime(new Date());
        String[] split = joinCode.split("_");
        devicePoint.setHostId(dtuNo);
        devicePoint.setDid(split[1]);
        devicePoint.setPid(joinCode);
        devicePoint.setPointNumber(joinCode);
        BaseDevicePoint oldPoint = baseDevicePointMapper.selectById((String) paramsMap.get("id"));
        if (!oldPoint.getJoinCode().equals(joinCode)) {
            QueryWrapper<BaseDevicePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pid", joinCode);
            queryWrapper.eq("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
//            queryWrapper.eq("building_id", oldPoint.getBuildingId());
            //queryMap.put("building_id", buildId);
            List<BaseDevicePoint> resultList = iBaseDevicePointService.list(queryWrapper);
            if (null != resultList && resultList.size() > 0) {
                throw new RuntimeException("点位号（RTU编号+从地址号）已存在");
            }
        }

        //更新redis
        String oldDeviceNo = oldPoint.getDtuNo();
        String oldAddressNo = oldPoint.getDeviceNo();
        updateRedis(oldPoint.getBuildingId(), oldDeviceNo, oldAddressNo, devicePoint, oldPoint.getSuperType());

        Journalizing journalizing = new Journalizing();
        String result = "编辑" + buildName + "的水泵设备设备编号" + deviceNo + ":";
        if (!org.apache.commons.lang3.StringUtils.equals(dtuNo, oldPoint.getHostId())) {
            result += "DTU编号" + paramsMap.get("hostId") + ",";
        }
        if (!org.apache.commons.lang3.StringUtils.equals(joinCode, oldPoint.getJoinCode())) {
            result += ("接入点" + oldPoint.getJoinCode() + "为" + joinCode + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(devTypeName, oldPoint.getDevTypeName())) {
            result += ("设备类型为" + devTypeName + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(deviceNo, oldPoint.getDeviceNo())) {
            result += ("设备编号为" + oldPoint.getDeviceNo() + "为" + deviceNo + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(floorName, oldPoint.getFloorName())) {
            result += ("楼层" + oldPoint.getFloorName() + "为" + floorName + ",");
        }
        if (Objects.nonNull(simNum) && !simNum.equals(oldPoint.getSimNum())) {
            result += ("流量卡号" + oldPoint.getSimNum() + "为" + simNum + ",");
        }
        if (!org.apache.commons.lang3.StringUtils.equals(pointDesc, oldPoint.getPointDesc())) {
            result += ("安装位置" + oldPoint.getPointDesc() + pointDesc);
        }
        if (!org.apache.commons.lang3.StringUtils.equals(venderName, oldPoint.getVenderName())) {
            result += ("设备厂商" + oldPoint.getVenderName() + "为" + venderName + ",");
        }
        if (!("编辑" + paramsMap.get("buildName") + "的水泵设备设备编号" + paramsMap.get("deviceNo") + ":").equals(result)) {
            result = result.substring(0, result.length() - 1);
            journalizing.setOperationContent(result);
            journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());
            journalizingService.setLogInfo(request, journalizing);
        }


        return iBaseDevicePointService.editPressurePointInfoById(devicePoint);
    }

    public void updateRedis(String buildingId, String oldDeviceNo, String oldAddressNo, BaseDevicePoint baseDevicePoint, String superType) {
        String oldKey = ConstantUtil.REDIS_DEVICE_KEY_EQUIPMENT + oldDeviceNo + "-" + oldAddressNo + "-" + superType;
        String newKey = ConstantUtil.REDIS_DEVICE_KEY_EQUIPMENT + baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid() + "-" + superType;
        String value = buildingId + "-" + baseDevicePoint.getId();
        //System.out.println("=======================================equipment key:" + key);
        //System.out.println("=======================================equipment value:" + value);
        redisUtils.remove(oldKey);
        redisUtils.set(newKey, value);
    }


    @Override
    public boolean editLiquidPointInfoById(HttpServletRequest request, Map<String, Object> paramsMap) {

        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        if (StringUtils.isEmpty((String) paramsMap.get("id"))) {
            throw new RuntimeException("点位id不能为空");
        }

        BaseDevicePoint oldPoint = baseDevicePointMapper.selectById((Serializable) paramsMap.get("id"));
        BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
        baseDevicePoint.setId((String) paramsMap.get("id"));
        if (StringUtils.isNotEmpty((String) paramsMap.get("devTypeCode"))) {
            baseDevicePoint.setDevTypeCode((String) paramsMap.get("devTypeCode"));
        } else {
            throw new RuntimeException("设备类型不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("hostId"))) {
            baseDevicePoint.setHostId((String) paramsMap.get("hostId"));
        } else {
            throw new RuntimeException("RTU编号不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("pointCode"))) {
            baseDevicePoint.setPointCode((String) paramsMap.get("pointCode"));
            baseDevicePoint.setDid((String) paramsMap.get("pointCode"));
        } else {
            throw new RuntimeException("从地址号不能为空");
        }
        baseDevicePoint.setPointNumber(baseDevicePoint.getHostId() + "-" + baseDevicePoint.getDid());
        if (StringUtils.isNotEmpty((String) paramsMap.get("devTypeName"))) {
            baseDevicePoint.setDevTypeName((String) paramsMap.get("devTypeName"));
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("maxval"))) {
            baseDevicePoint.setMaxval(new BigDecimal((String) paramsMap.get("maxval")));
        } else {
            throw new RuntimeException("最大阈值不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("minval"))) {
            baseDevicePoint.setMinval(new BigDecimal((String) paramsMap.get("minval")));
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floorId"))) {
            baseDevicePoint.setFloorId((String) paramsMap.get("floorId"));
        } else {
            throw new RuntimeException("楼层id不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floor"))) {
            baseDevicePoint.setFloor(Integer.valueOf((String) paramsMap.get("floor")));
        } else {
            throw new RuntimeException("楼层不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("floorName"))) {
            baseDevicePoint.setFloorName((String) paramsMap.get("floorName"));
        } else {
            throw new RuntimeException("楼层名称不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("pointDesc"))) {
            baseDevicePoint.setPointDesc((String) paramsMap.get("pointDesc"));
        } else {
            throw new RuntimeException("点位描述不能为空");
        }
        if (StringUtils.isNotEmpty((String) paramsMap.get("simNum"))) {
            baseDevicePoint.setSimNum((String) paramsMap.get("simNum"));
        }

        baseDevicePoint.setBrandName((String) paramsMap.get("brandName"));
        baseDevicePoint.setUpdateBy(userId);
        baseDevicePoint.setUpdateTime(new Date());
        //更新redis
        String oldDeviceNo = oldPoint.getHostId();
        String oldAddressNo = oldPoint.getPointCode();
        updateRedis(oldPoint.getBuildingId(), oldDeviceNo, oldAddressNo, baseDevicePoint, oldPoint.getSuperType());
        //日志插入
        String result = "编辑" + paramsMap.get("buildName") + "液位设备" + oldPoint.getHostId() + "+" + oldPoint.getPointCode() + ":";
        if (!baseDevicePoint.getHostId().equals(oldPoint.getHostId())) {
            result += ("RTU编号" + oldPoint.getHostId() + "为" + baseDevicePoint.getHostId() + ",");
        }
        if (!baseDevicePoint.getDevTypeName().equals(oldPoint.getDevTypeName())) {
            result += ("设备类型" + oldPoint.getDevTypeName() + "为" + baseDevicePoint.getDevTypeName() + ",");
        }
        if (!baseDevicePoint.getPointCode().equals(oldPoint.getPointCode())) {
            result += ("从地址号" + oldPoint.getPointCode() + "为" + baseDevicePoint.getPointCode() + ",");
        }
        if (!baseDevicePoint.getFloorName().equals(oldPoint.getFloorName())) {
            result += ("楼层" + oldPoint.getFloorName() + "为" + baseDevicePoint.getFloorName() + ",");
        }
        if (!baseDevicePoint.getPointDesc().equals(oldPoint.getPointDesc())) {
            result += ("位置描述" + oldPoint.getPointDesc() + "为" + baseDevicePoint.getPointDesc() + ",");
        }
        if (baseDevicePoint.getMaxval().compareTo(oldPoint.getMaxval()) != 0) {
            result += ("最大阈值" + oldPoint.getMaxval() + "为" + baseDevicePoint.getMaxval() + ",");
        }
        if (Objects.nonNull(baseDevicePoint.getSimNum()) && !baseDevicePoint.getSimNum().equals(oldPoint.getSimNum())) {
            result += ("传输设备卡号" + oldPoint.getSimNum() + "为" + baseDevicePoint.getSimNum() + ",");
        }
        if (Objects.nonNull(baseDevicePoint.getBrandName()) && !baseDevicePoint.getBrandName().equals(oldPoint.getBrandName())) {
            result += ("品牌名称" + oldPoint.getBrandName() + "为" + baseDevicePoint.getBrandName() + ",");
        }

        if (!("编辑" + paramsMap.get("buildName") + "液位设备" + oldPoint.getHostId() + oldPoint.getPointCode()).equals(result)) {
            result = result.substring(0, result.length() - 1);
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.WATER_MONITOR.getType());
            journalizing.setOperationContent(result);
            journalizingService.setLogInfo(request, journalizing);
        }


        return iBaseDevicePointService.editPressurePointInfoById(baseDevicePoint);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult batchImportPointInfo(HttpServletRequest request, MultipartFile multipartFile, String buildingId) {
        JsonResult jsonResult = new JsonResult(true);
        String fileName = multipartFile.getOriginalFilename();
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            jsonResult.setMessage("文件格式不正确");
            //throw new RuntimeException("文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        InputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            try {
                fileInputStream = multipartFile.getInputStream();
                if (isExcel2003) {
                    workbook = new HSSFWorkbook(fileInputStream);
                } else {
                    workbook = new XSSFWorkbook(fileInputStream);
                }
            } catch (IOException e) {
                jsonResult.setMessage("解析文件失败");
                e.printStackTrace();
                //throw new RuntimeException("解析文件失败");
            }
            BaseBuilding baseBuilding = baseBuildingService.getById(buildingId);
            // 获取建筑下所有楼层
            QueryWrapper<BaseBuildingFloor> floorQueryWrapper = new QueryWrapper<>();
            floorQueryWrapper.eq("building_id", buildingId);
            List<BaseBuildingFloor> list1 = iBaseBuildingFloorService.list(floorQueryWrapper);
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("building_id", buildingId);
            //queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
            queryMap.put("dev_type_code", ConstantUtil.DEVICE_TYPE_YK);
            List<BaseDevicePoint> xhfPointList = iBaseDevicePointService.listSelectedPoints(queryMap);
            //报警阀点位
            List<String> pointNumbers = xhfPointList.stream().map(BaseDevicePoint::getPointNumber).collect(Collectors.toList());
            //楼层分组 key = floor/floorName
            Map<String, List<BaseBuildingFloor>> floorMap = groupBillingDataByFooor(list1);
            //设备类型
            List<WdDevConfig> wdDevConfigs = wdDevConfigService.list(new QueryWrapper<WdDevConfig>().eq("fire_sys_code", "5"));
            List<String> devTypeCodeList = wdDevConfigs.stream().map(WdDevConfig::getDevCode).collect(Collectors.toList());

            Sheet sheet = workbook.getSheetAt(0);
            List<BaseDevicePoint> pointList = new LinkedList<>();
            Map<String, Object> numMap = new HashMap<>();
            for (Row row : sheet) {
                //如果当前行的行号（从0开始）未达到2（第三行）则从新循环
                if (row.getRowNum() < 1) {
                    continue;
                }
                //RTU编号
                String hostId = POIExcelUtil.getCellValueByCell(row.getCell(0));
                //从地址号
                String addressCode = POIExcelUtil.getCellValueByCell(row.getCell(1));
                //设备类型code
                String devTypeCode = POIExcelUtil.getCellValueByCell(row.getCell(2));
                //设备类型
                String devTypeName = POIExcelUtil.getCellValueByCell(row.getCell(3));
                //传输设备卡号
                String simNum = POIExcelUtil.getCellValueByCell(row.getCell(4));
                //最大阈值
                String maxval = POIExcelUtil.getCellValueByCell(row.getCell(5));
                //楼层
                String floorInfo = POIExcelUtil.getCellValueByCell(row.getCell(6));
                //点位描述
                String pointDesc = POIExcelUtil.getCellValueByCell(row.getCell(7));
                //类型
                String superType = POIExcelUtil.getCellValueByCell(row.getCell(8));
                //所属报警阀
                String alarmValve = POIExcelUtil.getCellValueByCell(row.getCell(9));
                //设备品牌
                String brandName = POIExcelUtil.getCellValueByCell(row.getCell(10));
                //建筑名称
                String buildName = baseBuilding.getBuildingName();

                String errorMsg = "";
                if (StringUtils.isEmpty(buildingId)) {
                    errorMsg = "建筑物编号不能为空";
                } else if (StringUtils.isEmpty(hostId)) {
                    errorMsg = "RTU编号不能为空";
                } else if (StringUtils.isEmpty(addressCode)) {
                    errorMsg = "从地址号不能为空";
                } else if (StringUtils.isEmpty(devTypeCode)) {
                    errorMsg = "设备类型编号不能为空";
                } else if (StringUtils.isEmpty(devTypeName)) {
                    errorMsg = "设备类型不能为空";
                } else if (StringUtils.isEmpty(floorInfo)) {
                    errorMsg = "楼层不能为空";
                } else if (StringUtils.isEmpty(maxval)) {
                    errorMsg = "最大阈值不能为空";
                } else if (StringUtils.isEmpty(pointDesc)) {
                    errorMsg = "位置描述不能为空";
                } else if (StringUtils.isEmpty(simNum)) {
                    errorMsg = "传输设备卡号不能为空";
                } else if (StringUtils.isEmpty(superType)
                        || (!ConstantUtil.IMPORT_DEVICE_TYPE_PRESSURE_GAGE.equals(superType)
                        && !ConstantUtil.IMPORT_DEVICE_TYPE_LIQUID_GAGE.equals(superType))) {
                    errorMsg = "类型不匹配";
                }
                if (!devTypeCodeList.contains(devTypeCode)) {
                    jsonResult.setMessage("无此设备code[" + devTypeCode + "]");
                    //throw new RuntimeException("无此设备code[" + devTypeCode + "]");
                }
                //所属报警阀(液位仪无报警阀)
                if (ConstantUtil.IMPORT_DEVICE_TYPE_PRESSURE_GAGE.equals(superType)) {
                    if (!"YLX".equals(devTypeCode) && !"FQYL".equals(devTypeCode) && !"SPYG".equals(devTypeCode)) {
                        if (StringUtils.isEmpty(alarmValve)) {
                            jsonResult.setMessage("所属压力开关不能为空 [建筑名:" + buildName + ",RTU编号:" + hostId + ",从地址号:" + addressCode + "]");
                            //throw new RuntimeException("所属压力开关不能为空 [建筑名:" + buildName + ",RTU编号:" + hostId + ",从地址号:" + addressCode + "]");
                        }
                    }
                }
                if (ConstantUtil.IMPORT_DEVICE_TYPE_PRESSURE_GAGE.equals(superType) && !pointNumbers.contains(alarmValve)) {
                    jsonResult.setMessage("此建筑无此压力开关！[建筑名:" + buildName + ",RTU编号:" + hostId + ",从地址号:" + addressCode + "报警阀:" + alarmValve + "]");
                    //throw new RuntimeException("此建筑无此压力开关！[建筑名:" + buildName + ",RTU编号:" + hostId + ",从地址号:" + addressCode + "报警阀:" + alarmValve + "]");
                }
                if (StringUtils.isNotEmpty(errorMsg)) {
                    jsonResult.setMessage(errorMsg);
                }
                //点位号
                String pointId = hostId + "-" + addressCode;
                QueryWrapper<BaseDevicePoint> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("point_number", pointId);
                queryWrapper.eq("super_type", superType);
                queryWrapper.eq("building_id", buildingId);
                List<BaseDevicePoint> resultList = iBaseDevicePointService.list(queryWrapper);
                if (null != resultList && resultList.size() > 0) {
                    jsonResult.setMessage("点位号（RTU编号+从地址号）已存在");
                    //throw new RuntimeException("点位号（RTU编号+从地址号）已存在");
                }
                String pointCode = hostId + "-" + addressCode;
                BaseDevicePoint baseDevicePoint = new BaseDevicePoint();
                baseDevicePoint.setBuildingId(buildingId);
                baseDevicePoint.setBuildName(buildName);
                baseDevicePoint.setPointNumber(pointCode);
                baseDevicePoint.setPid(pointCode);
                baseDevicePoint.setHostId(hostId);
                baseDevicePoint.setDid(addressCode);
                baseDevicePoint.setPointCode(addressCode);
                if (StringUtils.isNotEmpty(brandName)) {
                    baseDevicePoint.setBrandName(brandName);
                }
                baseDevicePoint.setDevTypeName(devTypeName);
                baseDevicePoint.setDevTypeCode(devTypeCode);
                baseDevicePoint.setSimNum(simNum);
                baseDevicePoint.setMaxval(new BigDecimal(maxval));
                //根据设备类型查询最小值
                String minval = waterMonitorService.getMinValByDevTypeCode(devTypeCode);
                baseDevicePoint.setMinval(new BigDecimal(minval));
                //楼层信息
                if (StringUtils.isNotEmpty(floorInfo) && floorInfo.contains("/")) {
                    String[] split = floorInfo.split("/");
                    baseDevicePoint.setFloor(Integer.parseInt(split[0]));
                    baseDevicePoint.setFloorName(split[1]);
                    if (floorMap != null && floorMap.size() > 0 && floorMap.get(floorInfo) != null && floorMap.get(floorInfo).size() > 0) {
                        List<BaseBuildingFloor> baseBuildingFloors1 = floorMap.get(floorInfo);
                        baseDevicePoint.setFloorId(baseBuildingFloors1.get(0).getId());
                    }
                } else {
                    baseDevicePoint.setFloor(null);
                    baseDevicePoint.setFloorName(null);
                    baseDevicePoint.setFloorId(null);
                }
                baseDevicePoint.setPointDesc(pointDesc);
                baseDevicePoint.setAlarmValve(alarmValve);
                baseDevicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
                baseDevicePoint.setCreateTime(new Date());
                baseDevicePoint.setUpdateTime(new Date());

                baseDevicePoint.setSuperType(superType);
                Integer num = (Integer) numMap.get(buildingId);
                if (null == num) {
                    numMap.put(buildingId, 1);
                } else {
                    numMap.put(buildingId, num + 1);
                }
                pointList.add(baseDevicePoint);
            }
            if (pointList.size() > 0) {
                Boolean flag = false;
                try {
                    flag = iBaseDevicePointService.batchInsertPointInfo(pointList);
                } catch (Exception e) {
                    jsonResult.setMessage("点位重复无法新增！");
                    //throw new RuntimeException("点位重复无法新增！");
                }

                //刷新维保数据
                baseDeviceTemporaryService.restPlanByPoint(pointList.get(0).getBuildingId(), pointList);

                if (flag) {
                    for (BaseDevicePoint baseDevicePoint : pointList) {
                        setPointInfoToRedis(baseDevicePoint);
                    }
                    editStatWaterPressureTotal(numMap);
                    //日志插入
                    Journalizing journalizing = new Journalizing();
                    journalizing.setOperationContent("批量导入水压设备点位" + pointList.size() + "个");
                    journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());
                    journalizingService.setLogInfo(request, journalizing);
                    return jsonResult;
                }
                return jsonResult;
            } else {
                jsonResult.setMessage("导入文件内容为空");
                //throw new RuntimeException("导入文件内容为空");
            }
        } finally {
            try {
                workbook.close();
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return jsonResult;
    }

    /**
     * 楼层分组
     *
     * @param billingList
     * @return
     */
    public Map<String, List<BaseBuildingFloor>> groupBillingDataByFooor(List<BaseBuildingFloor> billingList) {
        Map<String, List<BaseBuildingFloor>> resultMap = new HashMap<String, List<BaseBuildingFloor>>();
        try {
            for (BaseBuildingFloor sysUser : billingList) {
                if (null != sysUser.getFloorName() && sysUser.getFloor() != null) {
                    //map中某值已存在，将该数据存放到同一个key（key存放的是该值）的map中
                    if (resultMap.containsKey(sysUser.getFloor() + "/" + sysUser.getFloorName())) {
                        resultMap.get(sysUser.getFloor() + "/" + sysUser.getFloorName()).add(sysUser);
                    } else {//map中不存在，新建key，用来存放数据
                        List<BaseBuildingFloor> sysUserList = new ArrayList<BaseBuildingFloor>();
                        sysUserList.add(sysUser);
                        resultMap.put(sysUser.getFloor() + "/" + sysUser.getFloorName(), sysUserList);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        return resultMap;
    }


    @Override
    public List<Map<String, Object>> getAlarmValvePressureContrast(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        //传的实际值是点位表主键id
        String id = (String) paramsMap.get("pointCode");
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位编号不能为空");
        }
        //查询建筑物下点位信息
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("id", id);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null == pointList || pointList.size() < 1 || pointList.get(0) == null) {
            throw new RuntimeException("没有该点位信息");
        }
        BaseDevicePoint devicePoint = pointList.get(0);
        List<Map<String, Object>> resultList = new LinkedList<>();
        //所属报警阀
        String alarmValve = devicePoint.getAlarmValve();
        if (StringUtils.isEmpty(alarmValve)) {
            return resultList;
        }
        //查询获取同报警阀水压点位信息
        queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        //queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("alarm_valve", alarmValve);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint point : pointList) {
                Map<String, Object> resultMap = new HashMap<>();
                List<PreInfluxData> preDataList = null;
                try {
                    if (ObjectUtil.isNotEmpty(point)) {
                        preDataList = influxDataDao.findPreDeviceByTime(buildId, point.getId(), DateUtils.getBeforeWeekDate(), new Date());
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                    throw new RuntimeException("解析日期时间失败");
                }
                resultMap.put("pointCode", point.getPointCode());
                resultMap.put("pointDesc", point.getPointDesc());
                resultMap.put("dataList", fiterList(preDataList));
                resultList.add(resultMap);
            }
        }
        return resultList;
    }

    @Override
    public List<Map<String, Object>> getSameFloorPressureContrast(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        //传的实际值是点位表主键id
        String id = (String) paramsMap.get("pointCode");
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位编号不能为空");
        }
        //查询建筑物下点位信息
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("id", id);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null == pointList || pointList.size() < 1 || pointList.get(0) == null) {
            throw new RuntimeException("没有该点位信息");
        }
        BaseDevicePoint devicePoint = pointList.get(0);
        String floorId = devicePoint.getFloorId();
        if (StringUtils.isEmpty(floorId)) {
            throw new RuntimeException("该点位所属楼层为空");
        }
        //查询获取同报警阀水压点位信息
        queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("floor_id", floorId);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint point : pointList) {
                Map<String, Object> resultMap = new HashMap<>();
                List<PreInfluxData> preDataList = null;
                try {
                    preDataList = influxDataDao.findPreDeviceByTime(buildId, point.getId(), DateUtils.getBeforeWeekDate(), new Date());
                } catch (ParseException e) {
                    e.printStackTrace();
                    throw new RuntimeException("解析日期时间失败");
                }
                resultMap.put("pointCode", point.getPointCode());
                resultMap.put("pointDesc", point.getPointDesc());
                resultMap.put("dataList", fiterList(preDataList));
                resultList.add(resultMap);
            }
        }
        return resultList;
    }

    @Override
    public Map<String, Object> getWaterPressureHistoryValue(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        //传的实际值是点位表主键id
        String id = (String) paramsMap.get("pointCode");
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位编号不能为空");
        }
        String beginTime = (String) paramsMap.get("beginTime");
        String endTime = (String) paramsMap.get("endTime");
        Date beginDate;
        Date endDate;
        if (StringUtils.isNotEmpty(beginTime) || StringUtils.isNotEmpty(endTime)) {
            if (StringUtils.isEmpty(beginTime)) {
                throw new RuntimeException("开始时间不能为空");
            }
            if (StringUtils.isEmpty(endTime)) {
                throw new RuntimeException("结束时间不能为空");
            }
            try {
                beginDate = DateUtils.parseDatetime(beginTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
            try {
                endDate = DateUtils.parseDatetime(endTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
        } else {
            try {
                beginDate = DateUtils.getBeforeWeekDate();
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RuntimeException("解析日期时间失败");
            }
            endDate = new Date();
        }
        /*List<PreData> preDataList = preDataDao.findDeviceByTime(buildId, id, beginDate, endDate,
                ConstantUtil.PRE_VALUE_CHANGE);*/
        List<PreInfluxData> preDataList = influxDataDao.findPreDeviceByTime(buildId, id, beginDate, endDate);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("pointCode", id);
        resultMap.put("dataList", fiterList(preDataList));
        return resultMap;
    }

    @Override
    public Map<String, Object> getLiquidHistoryValue(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        //传的实际值是点位表主键id
        String id = (String) paramsMap.get("pointCode");
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位编号不能为空");
        }
        String beginTime = (String) paramsMap.get("beginTime");
        String endTime = (String) paramsMap.get("endTime");
        Date beginDate;
        Date endDate;
        if (StringUtils.isNotEmpty(beginTime) || StringUtils.isNotEmpty(endTime)) {
            if (StringUtils.isEmpty(beginTime)) {
                throw new RuntimeException("开始时间不能为空");
            }
            if (StringUtils.isEmpty(endTime)) {
                throw new RuntimeException("结束时间不能为空");
            }
            try {
                beginDate = DateUtils.parseDatetime(beginTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
            try {
                endDate = DateUtils.parseDatetime(endTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
        } else {
            try {
                beginDate = DateUtils.getBeforeWeekDate();
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RuntimeException("解析日期时间失败");
            }
            endDate = new Date();
        }
        BaseDevicePoint baseDevicePoint = baseDevicePointMapper.selectById(id);
        if (ObjectUtil.isEmpty(baseDevicePoint)) {
            throw new RuntimeException("未查询到设备点位信息");
        }
        BigDecimal maxval = baseDevicePoint.getMaxval();
        String result = waterMonitorService.getMinValByDevTypeCode(baseDevicePoint.getDevTypeCode());
        BigDecimal minval = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(result)) {
            minval = new BigDecimal(result);
        }
        List<LiquidInfluxData> liquidDataList = influxDataDao.findLiquidDeviceByTime(buildId, id, beginDate, endDate);
        log.info("查询casandra液位数据数量为：" + liquidDataList.size());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("pointCode", id);
        resultMap.put("dataList", fiterliquidList(liquidDataList, maxval, minval));
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> getFloorPlanPointInfo(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        String floorId = (String) paramsMap.get("floorId");
        if (StringUtils.isEmpty(floorId)) {
            throw new RuntimeException("楼层id不能为空");
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("floor_id", floorId);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        List<Map<String, Object>> returnList = new LinkedList<>();
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint baseDevicePoint : pointList) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("pointCode", baseDevicePoint.getPointCode());
                tempMap.put("pointDesc", baseDevicePoint.getPointDesc());
                tempMap.put("wide", null != baseDevicePoint.getWide() ? baseDevicePoint.getWide().toString() : "");
                tempMap.put("tall", null != baseDevicePoint.getTall() ? baseDevicePoint.getTall().toString() : "");
                returnList.add(tempMap);
            }
        }
        return returnList;
    }

    @Override
    public List<Map<String, Object>> exportWaterAbnormalData(HttpServletRequest request, Map<String, Object> paramsMap) {
        paramsMap.put("buildList", new ArrayList<>());
        List<Map<String, Object>> dataList = this.baseMapper.getWaterPressureAbnormalList(paramsMap);
        List<Map<String, Object>> resultList = analyseDataList(dataList, 3);
        filterList(resultList);

        if (Objects.nonNull(dataList) && dataList.size() > 0) {
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());
            journalizing.setOperationContent("导出" + dataList.get(0).get("build_name") + "的水泵变化记录数据");
            journalizingService.setLogInfo(request, journalizing);
        }
        return resultList;
    }

    @Override
    public Map<String, Object> getPumpDeviceData(HttpServletRequest request, Map<String, Object> paramsMap) {
        List<String> buildingList = monitorConsoleService.getUserBuildingList(request, paramsMap);
        paramsMap.put("buildList", buildingList);
        Integer deviceTotalNum = queryWaterPumpAllPointNum(paramsMap);
        Integer normalTotalNum = queryWaterPumpNormalNum(paramsMap);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("deviceTotalNum", deviceTotalNum);
        returnMap.put("normalTotalNum", normalTotalNum);
        returnMap.put("abnormalTotalNum", Math.max(deviceTotalNum - normalTotalNum, 0));
        return returnMap;
    }

    @Override
    public Map<String, Object> getWaterLiquidAbnormalRankList(HttpServletRequest request,Map<String, Object> paramsMap) {
        String type = (String) paramsMap.get("type");
        List<String> buildingList = monitorConsoleService.getUserBuildingList(request, paramsMap);
        log.info("buildingList:{}",JSON.toJSONString(buildingList));
        paramsMap.put("buildList", buildingList);
        log.info("paramsMap:{}",JSON.toJSONString(paramsMap));
        List<Map<String, Object>> dataList = this.baseMapper.getWaterLiquidAbnormalList(paramsMap);
        //1-查询水泵异常, 2-查询设备异常
        List<Map<String, Object>> resultList = analyseDataList(dataList, "1".equals(type) ? 10 : 11);
        Map<String, Object> resultMap = new HashMap<>();
        filterList(resultList);
        resultMap.put("abnormalList", resultList);
        return resultMap;
    }

    @Override
    public BaseDevicePoint getPointInfoById(Map<String, Object> paramsMap) {
        String pointId = (String) paramsMap.get("pointId");
        if (StringUtils.isEmpty(pointId)) {
            throw new RuntimeException("点位id为空");
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", pointId);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null == pointList || pointList.size() < 1 || null == pointList.get(0)) {
            throw new RuntimeException("该点位信息查询为空");
        }
//        BaseDevicePoint point = pointList.get(0);
//        Map<String, Object> returnMap = new HashMap<>();
//        returnMap.put("id", point.getId());
//        returnMap.put("pointCode", point.getPointCode());
//        returnMap.put("pointDesc", point.getPointDesc());
//        returnMap.put("devTypeCode", point.getDevTypeCode());
//        returnMap.put("devTypeName", point.getDevTypeName());
//        returnMap.put("alarmValve", point.getAlarmValve());
//        returnMap.put("brandName", point.getBrandName());
//        returnMap.put("floor", point.getFloor());
//        returnMap.put("floorId", point.getFloorId());
//        returnMap.put("floorName", point.getFloorName());
//        returnMap.put("sensorNum", point.getSensorNum());
//        returnMap.put("maxval", point.getMaxval());
//        returnMap.put("minval", point.getMinval());
//        returnMap.put("buildId", point.getBuildingId());
//        returnMap.put("buildName", point.getBuildName());
//        returnMap.put("remark", point.getRemark());
//        returnMap.put("roomNo", point.getRoomNo());
        return pointList.get(0);
    }

    @Override
    public List<Map<String, Object>> exportPumpAbnormalData(HttpServletRequest request,Map<String, Object> paramsMap) {
        if (paramsMap == null){
            paramsMap =  new HashMap<>();
        }
        List<String> buildingList = monitorConsoleService.getUserBuildingList(request, paramsMap);
        paramsMap.put("buildList", buildingList);
        log.info("buildingList：{}", buildingList);
        log.info("paramsMap：{}", paramsMap);
        List<Map<String, Object>> dataList = this.baseMapper.getWaterLiquidAbnormalList(paramsMap);
        List<Map<String, Object>> resultList = analyseDataList(dataList, 12);
        filterList(resultList);
        return resultList;
    }

    @Override
    public JsonResult pumpRegister(HttpServletRequest request, Map<String, Object> paramsMap) {
        JsonResult jsonResult = new JsonResult(true);
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("建筑物id为空!");
        }
        String buildName = paramsMap.get("buildName").toString();
        //设备厂商编号
        String venderCode = paramsMap.get("venderCode").toString();
        //设备厂商名称
        String venderName = paramsMap.get("venderName").toString();
        //设备编号
        String deviceNo = paramsMap.get("deviceNo").toString();
        if (StringUtils.isEmpty(deviceNo)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("设备编号为空!");
        }
        //设备类型编号
        String devTypeCode = paramsMap.get("devTypeCode").toString();
        //设备类型名称
        String devTypeName = paramsMap.get("devTypeName").toString();
        if (StringUtils.isEmpty(devTypeCode)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("设备类型为空!");
        }
        //判断设备编号与设备类型唯一
//        Map<String, Object> queryMap1 = new HashMap<>();
//        queryMap1.put("building_id", buildId);
//        queryMap1.put("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
//        queryMap1.put("dev_type_code", devTypeCode);
//        queryMap1.put("device_no", deviceNo);
//        List<BaseDevicePoint> pointList1 = iBaseDevicePointService.listSelectedPoints(queryMap1);
//        if (null != pointList1 && pointList1.size() > 0) {
//            throw new RuntimeException("设备编号&设备类型:" + deviceNo + "_" + devTypeName + "已存在，不能重复添加");
//        }
        //DTU
        String dtuNo = paramsMap.get("dtuNo").toString();
        if (StringUtils.isEmpty(dtuNo)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("DTU编号为空!");
        }
        //接入点编号
        String joinCode = paramsMap.get("joinCode").toString();
        //接入点名称
        String joinName = paramsMap.get("joinName").toString();
        //检查接入点唯一
        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("building_id", buildId);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
        queryMap.put("pid", joinCode);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        if (null != pointList && pointList.size() > 0) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("DTU编号&接入点:" + joinCode + "已存在，不能重复添加");
        }
        //安装位置
        String pointDesc = paramsMap.get("pointDesc").toString();
        if (StringUtils.isEmpty(pointDesc)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("安装位置为空");
        }
        //楼层id
        String floorId = paramsMap.get("floorId").toString();
        //楼层名称
        String floorName = paramsMap.get("floorName").toString();
        if (StringUtils.isEmpty(floorId)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("楼层为空");
        }
        devTypeCode = "SBJC";
        //流量卡号
        String simNum = paramsMap.get("simNum").toString();
        BaseDevicePoint devicePoint = new BaseDevicePoint();
        devicePoint.setBuildingId(buildId);
        devicePoint.setBuildName(buildName);
        devicePoint.setVenderCode(venderCode);
        devicePoint.setVenderName(venderName);
        devicePoint.setDeviceNo(deviceNo);
        devicePoint.setPid(joinCode);
        String[] split = joinCode.split("_");
        devicePoint.setHostId(dtuNo);
        devicePoint.setDid(split[1]);
        devicePoint.setJoinCode(joinCode);
        devicePoint.setDeviceName(devTypeName + deviceNo);
        devicePoint.setJoinName(joinName);
        devicePoint.setDevTypeCode("SBJC");
        devicePoint.setDevTypeName(devTypeName);
        devicePoint.setPointDesc(pointDesc);
        devicePoint.setFloorId(floorId);
        devicePoint.setPointNumber(joinCode);
        if (null != paramsMap.get("floor")) {
            devicePoint.setFloor(Integer.parseInt((String) paramsMap.get("floor")));
        }
        if (StringUtils.isNotEmpty(floorName)) {
            devicePoint.setFloorName(floorName);
        }
        devicePoint.setSimNum(simNum);
        devicePoint.setSuperType(ConstantUtil.DEVICE_TYPE_PUMP);
        devicePoint.setPointStatus(ConstantUtil.POINT_STATUS_NORMAL);
        devicePoint.setDtuNo(dtuNo);
        devicePoint.setCreateTime(new Date());
        devicePoint.setUpdateTime(new Date());
        Boolean flag = false;
        try {
            flag = iBaseDevicePointService.savePointInfo(devicePoint);
        } catch (Exception e) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("点位重复无法新增");
        }
        //水泵数据发生改变后刷新维保相关数据
        List<BaseDevicePoint> baseDevicePointList = new ArrayList<>();
        baseDevicePointList.add(devicePoint);
        baseDeviceTemporaryService.restPlanByPoint(devicePoint.getBuildingId(), baseDevicePointList);
        if (flag) {
            setPointInfoToRedis(devicePoint);
            editStatWaterPumpTotal(buildId, 1);
            //日志插入
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationContent("新增" + buildName + "的⽔泵设备设备编号" + deviceNo);
            journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());
            journalizingService.setLogInfo(request, journalizing);
            return jsonResult;
        }
        return jsonResult;
    }

    @Override
    public List<Map<String, Object>> getPumpMonitorStatusInfo(Map<String, Object> paramsMap) {
        //建筑物id
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物id为空");
        }
        //查询建筑物下所有正常点位信息
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("super_type", ConstantUtil.DEVICE_TYPE_PUMP);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        //返回值
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint baseDevicePoint : pointList) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("id", baseDevicePoint.getId());
                tempMap.put("deviceName", baseDevicePoint.getDeviceName());
                tempMap.put("updateTime", baseDevicePoint.getUpdateTime());
                Object object = redisUtils.get(buildId + ConstantUtil.REDIS_KEY_PUMP + baseDevicePoint.getId());
                //手动状态，0-手动，1-自动（水泵）
                String manualStatus = "";
                //断电状态，0-断电，1-已上电（水泵）
                String outageStatus = "";
                //运行状态，0-停止，1-运行（水泵）
                String operationStatus = "";
                //设备状态 0-正常，2-离线
                String deviceStatus = "";
                //1-故障，
                String faultStatus = "";

                String pushTime = "";
                if (null != object) {
                    FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(object), FarEastoneCache.class);
                    manualStatus = farEastoneCache.getManualStatus();
                    outageStatus = farEastoneCache.getOutageStatus();
                    operationStatus = farEastoneCache.getOperationStatus();
                    faultStatus = farEastoneCache.getFaultStatus();
                    deviceStatus = farEastoneCache.getDeviceStatus();
                    pushTime = farEastoneCache.getPushTime();
                }
                tempMap.put("manualStatus", manualStatus);
                tempMap.put("outageStatus", outageStatus);
                tempMap.put("operationStatus", operationStatus);
                tempMap.put("faultStatus", faultStatus);
                tempMap.put("deviceStatus", deviceStatus);
                tempMap.put("pushTime", pushTime);
                //水泵异常：手动、断电、运行
                if (ConstantUtil.PUMP_MANUAL_STATUS.equals(manualStatus) || ConstantUtil.PUMP_OUTAGE_STATUS.equals(outageStatus)
                        || ConstantUtil.PUMP_RUN_ON_STATUS.equals(operationStatus)) {
                    //异常
                    tempMap.put("abnormalStatus", "1");
                } else {
                    //正常
                    tempMap.put("abnormalStatus", "0");
                }
                resultList.add(tempMap);
            }
        }
        return pumpDataOrder(resultList);
    }

    @Override
    public Map<String, Object> queryWaterMonitorStatisData() {
        Map<String, Object> preDataMap = this.baseMapper.queryPressureStatisData();
        Map<String, Object> pumpDataMap = this.baseMapper.queryPumpStatisData();
        Map<String, Object> resultMap = new HashMap<>();
        //水压监测总数
        String pressureTotalNum = "";
        //水压异常总数
        String pressureAbnormalNum = "";
        //水压设备异常总数
        String pressureDeviceAbnormalNum = "";
        //水泵监测总数
        String pumpTotalNum = "";
        //水泵异常总数
        String pumpAbnormalNum = "";
        //水泵设备异常总数
        String pumpDeviceAbnormalNum = "";
        if (null != preDataMap) {
            if (null != preDataMap.get("pointTotal")) {
                pressureTotalNum = String.valueOf(preDataMap.get("pointTotal"));
            }
            if (null != preDataMap.get("deviceTotal")) {
                pressureDeviceAbnormalNum = String.valueOf(preDataMap.get("deviceTotal"));
            }
            if (null != preDataMap.get("pressureTotal")) {
                pressureAbnormalNum = String.valueOf(preDataMap.get("pressureTotal"));
            }
        }
        if (null != pumpDataMap) {
            if (null != pumpDataMap.get("pointTotal")) {
                pumpTotalNum = String.valueOf(pumpDataMap.get("pointTotal"));
            }
            if (null != pumpDataMap.get("deviceTotal")) {
                pumpDeviceAbnormalNum = String.valueOf(pumpDataMap.get("deviceTotal"));
            }
            if (null != pumpDataMap.get("pumpTotal")) {
                pumpAbnormalNum = String.valueOf(pumpDataMap.get("pumpTotal"));
            }
        }
        resultMap.put("pressureTotalNum", pressureTotalNum);
        resultMap.put("pressureAbnormalNum", pressureAbnormalNum);
        resultMap.put("pressureDeviceAbnormalNum", pressureDeviceAbnormalNum);
        resultMap.put("pumpTotalNum", pumpTotalNum);
        resultMap.put("pumpAbnormalNum", pumpAbnormalNum);
        resultMap.put("pumpDeviceAbnormalNum", pumpDeviceAbnormalNum);
        return resultMap;
    }

    @Override
    public Map<String, Object> queryWaterPressureInfoEveryDay(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号为空");
        }
        Date date;
        if (StringUtils.isNotEmpty((String) paramsMap.get("recordDate"))) {
            date = DateUtil.parseDate((String) paramsMap.get("recordDate"));
        } else {
            date = DateUtil.parseDate(DateUtil.getCurrentDateStr());
        }
        paramsMap.put("recordDate", date);
        Map<String, Object> dataMap = this.baseMapper.queryWaterPressureInfoEveryDay(paramsMap);
        //水压异常
        String preAbnormalNum = "";
        //液位异常
        String liquidAbnormalNum = "";
        //设备离线数
        String deviceOffNum = "";
        //设备故障数
        String deviceFaultNum = "";
        //分析异常数
        String analyseAbnormalNum = "";
        if (null != dataMap) {
            if (null != dataMap.get("pre_abnormal_num")) {
                preAbnormalNum = String.valueOf(dataMap.get("pre_abnormal_num"));
            }
            if (null != dataMap.get("liquid_abnormal_num")) {
                liquidAbnormalNum = String.valueOf(dataMap.get("liquid_abnormal_num"));
            }
            if (null != dataMap.get("device_fault_num")) {
                deviceFaultNum = String.valueOf(dataMap.get("device_fault_num"));
            }
            if (null != dataMap.get("device_off_num")) {
                deviceOffNum = String.valueOf(dataMap.get("device_off_num"));
            }
            if (null != dataMap.get("analyse_abnormal_num")) {
                analyseAbnormalNum = String.valueOf(dataMap.get("analyse_abnormal_num"));
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("preAbnormalNum", preAbnormalNum);
        returnMap.put("liquidAbnormalNum", liquidAbnormalNum);
        returnMap.put("deviceFaultNum", deviceFaultNum);
        returnMap.put("deviceOffNum", deviceOffNum);
        returnMap.put("analyseAbnormalNum", analyseAbnormalNum);
        return returnMap;
    }

    @Override
    public Map<String, Object> queryPumpInfoEveryDay(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号为空");
        }
        Date date;
        if (StringUtils.isNotEmpty((String) paramsMap.get("recordDate"))) {
            date = DateUtil.parseDate((String) paramsMap.get("recordDate"));
        } else {
            date = DateUtil.parseDate(DateUtil.getCurrentDateStr());
        }
        paramsMap.put("recordDate", date);
        Map<String, Object> dataMap = this.baseMapper.queryPumpInfoEveryDay(paramsMap);
        //停电数
        String outageNum = "";
        //手动数
        String manualNum = "";
        //运行数
        String runNum = "";
        //设备故障数
        String faultNum = "";
        //离线数
        String offNum = "";
        if (null != dataMap) {
            if (null != dataMap.get("outage_num")) {
                outageNum = String.valueOf(dataMap.get("outage_num"));
            }
            if (null != dataMap.get("manual_num")) {
                manualNum = String.valueOf(dataMap.get("manual_num"));
            }
            if (null != dataMap.get("run_num")) {
                runNum = String.valueOf(dataMap.get("run_num"));
            }
            if (null != dataMap.get("fault_num")) {
                faultNum = String.valueOf(dataMap.get("fault_num"));
            }
            if (null != dataMap.get("off_num")) {
                offNum = String.valueOf(dataMap.get("off_num"));
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("outageNum", outageNum);
        returnMap.put("manualNum", manualNum);
        returnMap.put("runNum", runNum);
        returnMap.put("faultNum", faultNum);
        returnMap.put("offNum", offNum);
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> queryAlarmList(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号为空");
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        //queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("dev_type_code", ConstantUtil.DEVICE_TYPE_YK);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        List<Map<String, Object>> returnList = new LinkedList<>();
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint baseDevicePoint : pointList) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("pointCode", baseDevicePoint.getPointNumber());
                tempMap.put("pointDesc", baseDevicePoint.getPointDesc());
                returnList.add(tempMap);
            }
        }
        return returnList;
    }

    @Override
    public List<Map<String, Object>> queryPumpStatusHistoryData(Map<String, Object> paramsMap) {
        String buildId = (String) paramsMap.get("buildId");
        if (StringUtils.isEmpty(buildId)) {
            throw new RuntimeException("建筑物编号不能为空");
        }
        //点位表主键id
        String id = (String) paramsMap.get("id");
        if (StringUtils.isEmpty(id)) {
            throw new RuntimeException("点位编号不能为空");
        }
        String beginTime = (String) paramsMap.get("beginTime");
        String endTime = (String) paramsMap.get("endTime");
        Date beginDate;
        Date endDate;
        if (StringUtils.isNotEmpty(beginTime) || StringUtils.isNotEmpty(endTime)) {
            if (StringUtils.isEmpty(beginTime)) {
                throw new RuntimeException("开始时间不能为空");
            }
            if (StringUtils.isEmpty(endTime)) {
                throw new RuntimeException("结束时间不能为空");
            }
            try {
                beginDate = DateUtils.parseDatetime(beginTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
            try {
                endDate = DateUtils.parseDatetime(endTime);
            } catch (ParseException e) {
                throw new RuntimeException(e.getMessage());
            }
        } else {
            try {
                beginDate = DateUtils.getBeforeWeekDate();
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RuntimeException("解析日期时间失败");
            }
            endDate = new Date();
        }
        List<PumpInfluxData> dataList = influxDataDao.findPumpDataByTime(buildId, id, beginDate, endDate, (String) paramsMap.get("powerStatus"),
                (String) paramsMap.get("manualStatus"), (String) paramsMap.get("runStatus"), (String) paramsMap.get("faultStatus"));
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (PumpInfluxData pumpData : dataList) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("time", pumpData.getMsgTime());
                dataMap.put("powerStatus", StringUtils.isEmpty(pumpData.getPowerStatus()) ? "0" : pumpData.getPowerStatus());
                dataMap.put("manualStatus", StringUtils.isEmpty(pumpData.getManualStatus()) ? "0" : pumpData.getManualStatus());
                dataMap.put("runStatus", StringUtils.isEmpty(pumpData.getRunStatus()) ? "0" : pumpData.getRunStatus());
                dataMap.put("faultStatus", StringUtils.isEmpty(pumpData.getFaultStatus()) ? "0" : pumpData.getFaultStatus());
                resultList.add(dataMap);
            }
        }
        return resultList;
    }

    private List<Map<String, Object>> queryPointInfo(String buildId, String superType, String redisKey, String abnormalType) {
        //查询建筑物下所有正常点位信息
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("building_id", buildId);
        //queryMap.put("point_status", ConstantUtil.POINT_STATUS_NORMAL);
        queryMap.put("super_type", superType);
        List<BaseDevicePoint> pointList = iBaseDevicePointService.listSelectedPoints(queryMap);
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != pointList && pointList.size() > 0) {
            for (BaseDevicePoint baseDevicePoint : pointList) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("id", baseDevicePoint.getId());
                tempMap.put("pointDesc", baseDevicePoint.getPointDesc());
                tempMap.put("pointCode", baseDevicePoint.getPointNumber());
                tempMap.put("floorId", baseDevicePoint.getFloorId());
                tempMap.put("floor", baseDevicePoint.getFloor());
                tempMap.put("floorName", baseDevicePoint.getFloorName());
                tempMap.put("devTypeName", baseDevicePoint.getDevTypeName());
                tempMap.put("updateTime", baseDevicePoint.getUpdateTime());
                tempMap.put("pointNumber", baseDevicePoint.getPointNumber());
                String key = buildId + redisKey + baseDevicePoint.getId();
                System.out.println("====queryPointInfo=============================================key: " + key);
                Object object = redisUtils.get(key);
                System.out.println("====queryPointInfo=============================================key: " + key + ", value: " + object);
                String analyseAbnormalStatus = "";
                String signals = "";
                String pushTime = "";
                String status = "";
                String value = "";
                if (null != object) {
                    FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(object), FarEastoneCache.class);
                    analyseAbnormalStatus = farEastoneCache.getAnalyseAbnormalStatus();
                    signals = farEastoneCache.getSignals();
                    if (null != signals && !"".equals(signals)) {
                        signals = String.valueOf(Integer.parseInt(signals) > 0 ? (Integer.parseInt(signals) + 24) / 25 : 0);
                    }
                    pushTime = farEastoneCache.getPushTime();
                    status = farEastoneCache.getDeviceStatus();
                    value = farEastoneCache.getValue();
                    String faultStatus = farEastoneCache.getFaultStatus();
                    //只有设备是正常状态才去监测是否压力、水位高度异常
                    if (ConstantUtil.DEVICE_NORMAL.equals(status)) {
                        if (null != value && !"".equals(value)) {
                            BigDecimal currentValue = new BigDecimal(value);
                            BigDecimal maxval = baseDevicePoint.getMaxval();
                            String result = getMinValByDevTypeCode(baseDevicePoint.getDevTypeCode());
                            BigDecimal minval = BigDecimal.ZERO;
                            if (StringUtils.isNotEmpty(result)) {
                                minval = new BigDecimal(result);
                            }
                            if (null != maxval && null != minval) {
                                //只有压力值转换 KPa -> MPa
                                if (ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE.equals(superType)) {
                                    maxval = maxval.divide(new BigDecimal(1000));
                                }
                                //正常
                                if (currentValue.compareTo(minval) > -1 && currentValue.compareTo(maxval) < 1) {
                                    status = ConstantUtil.PRE_VALUE_NORMAL;
                                } else if (currentValue.compareTo(minval) == -1) {
                                    status = ConstantUtil.PRE_VALUE_LOW;
                                } else {
                                    status = ConstantUtil.PRE_VALUE_HIGHT;
                                }
                            }
                        } else {
                            value = "";
                        }
                    }
                    if (faultStatus != null && !"".equals(faultStatus) && !"1".equals(status) && "1".equals(faultStatus)) {
                        status = "2";
                    }
                }
                tempMap.put("pushTime", pushTime);
                tempMap.put("signals", signals);
                tempMap.put("value", value);
                tempMap.put("status", status);
                switch (abnormalType) {
                    case "0":
                        //全部
                        resultList.add(tempMap);
                        break;
                    case "1":
                        //压力、液位异常
                        if (ConstantUtil.PRE_VALUE_HIGHT.equals(tempMap.get("status")) || ConstantUtil.PRE_VALUE_LOW.equals(tempMap.get("status"))) {
                            resultList.add(tempMap);
                        }
                        break;
                    case "2":
                        //设备异常（故障、离线）
                        if (ConstantUtil.DEVICE_FAULT.equals(tempMap.get("status")) ||
                                ConstantUtil.DEVICE_OFFLINE.equals(tempMap.get("status"))) {
                            resultList.add(tempMap);
                        }
                        break;
                    case "3":
                        //分析异常
                        if (ConstantUtil.PRE_ANALYSE_ABNORMAL.equals(analyseAbnormalStatus)) {
                            resultList.add(tempMap);
                        }
                        break;
                    default:
                        resultList.add(tempMap);
                }
            }
        }
        return pressureDataOrder(resultList);
    }

    /**
     * flag 1-压力异常 2-设备异常 3-查询全部异常 4-液位异常
     *
     * @param paramsMap
     * @param flag
     * @return 异常总数
     */
    private int getPressureTotalNum(Map<String, Object> paramsMap, int flag) {
        int waterDownPoint = 0;
        if (null != paramsMap.get("water_down_point")) {
            waterDownPoint = Integer.parseInt(String.valueOf(paramsMap.get("water_down_point")));
        }
        int waterOnPoint = 0;
        if (null != paramsMap.get("water_on_point")) {
            waterOnPoint = Integer.parseInt(String.valueOf(paramsMap.get("water_on_point")));
        }
        int liquidOnPoint = 0;
        if (null != paramsMap.get("liquid_on_point")) {
            liquidOnPoint = Integer.parseInt(String.valueOf(paramsMap.get("liquid_on_point")));
        }
        int liquidDownPoint = 0;
        if (null != paramsMap.get("liquid_down_point")) {
            liquidDownPoint = Integer.parseInt(String.valueOf(paramsMap.get("liquid_down_point")));
        }
        int analyseAbnormalPoint = 0;
        if (null != paramsMap.get("analyse_abnormal_point")) {
            analyseAbnormalPoint = Integer.parseInt(String.valueOf(paramsMap.get("analyse_abnormal_point")));
        }
        int waterPointOff = 0;
        if (null != paramsMap.get("water_point_off")) {
            waterPointOff = Integer.parseInt(String.valueOf(paramsMap.get("water_point_off")));
        }
        int waterPointFault = 0;
        if (null != paramsMap.get("water_point_fault")) {
            waterPointFault = Integer.parseInt(String.valueOf(paramsMap.get("water_point_fault")));
        }
        //手动水泵数
        int manualPumps = 0;
        if (null != paramsMap.get("hands_pumps")) {
            manualPumps = Integer.parseInt(String.valueOf(paramsMap.get("hands_pumps")));
        }
        //断电水泵数
        int outagePumps = 0;
        if (null != paramsMap.get("outage_pumps")) {
            outagePumps = Integer.parseInt(String.valueOf(paramsMap.get("outage_pumps")));
        }
        //运行水泵数
        int runPumps = 0;
        if (null != paramsMap.get("run_pumps")) {
            runPumps = Integer.parseInt(String.valueOf(paramsMap.get("run_pumps")));
        }
        //故障水泵数
        int faultPumps = 0;
        if (null != paramsMap.get("fault_pumps")) {
            faultPumps = Integer.parseInt(String.valueOf(paramsMap.get("fault_pumps")));
        }
        //离线水泵数
        int offPumps = 0;
        if (null != paramsMap.get("off_pumps")) {
            offPumps = Integer.parseInt(String.valueOf(paramsMap.get("off_pumps")));
        }
        int total = 0;
        if (1 == flag) {
            total += waterDownPoint;
            total += waterOnPoint;
            total += liquidOnPoint;
            total += liquidDownPoint;
            total += analyseAbnormalPoint;
        } else if (2 == flag) {
            total += waterPointOff;
            total += waterPointFault;
        } else if (3 == flag) {
            total += waterDownPoint;
            total += waterOnPoint;
            total += liquidOnPoint;
            total += liquidDownPoint;
            total += analyseAbnormalPoint;
            total += waterPointOff;
            total += waterPointFault;
        } else if (4 == flag) {
            total += liquidOnPoint;
            total += liquidDownPoint;
        } else if (5 == flag) {
            total += waterDownPoint;
            total += waterOnPoint;
        } else if (6 == flag) {
            total += analyseAbnormalPoint;
        } else if (10 == flag) {
            total += manualPumps;
            total += outagePumps;
            total += runPumps;
        } else if (11 == flag) {
            total += faultPumps;
            total += offPumps;
        } else if (12 == flag) {
            total += manualPumps;
            total += outagePumps;
            total += runPumps;
            total += faultPumps;
            total += offPumps;
        }
        return total;
    }

    private void filterList(List<Map<String, Object>> list) {
        removeDataOfZoreAbnormal(list);
        sortList(list);
        for (Map<String, Object> centerMap : list) {
            List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
            sortList(areaList);
            for (Map<String, Object> areaMap : areaList) {
                List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                sortList(buildList);
            }
        }
    }

    private void sortList(List<Map<String, Object>> list) {
        sortList(list, "abnormalNum");
    }

    /**
     * 过滤掉异常为0的数据
     *
     * @param dataList
     */
    private void removeDataOfZoreAbnormal(List<Map<String, Object>> dataList) {
        Iterator<Map<String, Object>> centerIterator = dataList.iterator();
        while (centerIterator.hasNext()) {
            Map<String, Object> centerMap = centerIterator.next();
            Integer abnormalNum = getNumber(centerMap);
            if (null == abnormalNum || 0 == abnormalNum) {
                centerIterator.remove();
            } else {
                List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
                if (null != areaList && areaList.size() > 0) {
                    Iterator<Map<String, Object>> areaIterator = areaList.iterator();
                    while (areaIterator.hasNext()) {
                        Map<String, Object> areaMap = areaIterator.next();
                        abnormalNum = getNumber(areaMap);
                        if (null == abnormalNum || 0 == abnormalNum) {
                            areaIterator.remove();
                        } else {
                            List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                            if (null != buildList && buildList.size() > 0) {
                                Iterator<Map<String, Object>> buildIterator = buildList.iterator();
                                while (buildIterator.hasNext()) {
                                    Map<String, Object> buildMap = buildIterator.next();
                                    abnormalNum = getNumber(buildMap);
                                    if (null == abnormalNum || 0 == abnormalNum) {
                                        buildIterator.remove();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private Integer getNumber(Map<String, Object> dataMap) {
        return (Integer) dataMap.get("abnormalNum");
    }

    private List<Map<String, Object>> analyseDataList(List<Map<String, Object>> dataList, int flag) {
        List<Map<String, Object>> resultList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (Map<String, Object> tempMap : dataList) {
                if (null == tempMap) {
                    continue;
                }
                boolean isExists = false;
                //运营中心
                String centerCode = (String) tempMap.get("regional");
                Map<String, Object> centerMap = new HashMap<>();
                for (Map<String, Object> centerTempMap : resultList) {
                    if (Objects.equals(centerCode, centerTempMap.get("code"))) {
                        isExists = true;
                        centerMap = centerTempMap;
                        break;
                    }
                }
                if (!isExists) {
                    centerMap.put("code", centerCode);
                    centerMap.put("title", tempMap.get("safety_belt"));
                    centerMap.put("abnormalNum", 0);
                    resultList.add(centerMap);
                }
                //区域列表
                List<Map<String, Object>> areaList = (List<Map<String, Object>>) centerMap.get("children");
                if (null == areaList) {
                    areaList = new ArrayList<>();
                    centerMap.put("children", areaList);
                }
                //区域编号
                String areaCode = (String) tempMap.get("jurisdiction");
                Map<String, Object> areaMap = new HashMap<>();
                isExists = false;
                for (Map<String, Object> areaTempMap : areaList) {
                    if (Objects.equals(areaCode, areaTempMap.get("code"))) {
                        isExists = true;
                        areaMap = areaTempMap;
                        break;
                    }
                }
                if (!isExists) {
                    areaMap.put("code", areaCode);
                    areaMap.put("title", tempMap.get("jurisdiction_val"));
                    areaMap.put("abnormalNum", 0);
                    areaList.add(areaMap);
                }
                //建筑物id
                String buildId = (String) tempMap.get("build_id");
                //建筑物列表
                List<Map<String, Object>> buildList = (List<Map<String, Object>>) areaMap.get("children");
                if (null == buildList) {
                    buildList = new ArrayList<>();
                    areaMap.put("children", buildList);
                }
                Map<String, Object> buildMap = new HashMap<>();
                buildMap.put("code", buildId);
                buildMap.put("title", tempMap.get("build_name"));
                if (3 == flag) {
                    //压力监测排行榜导出
                    buildMap.put("deviceAbnormalNum", getPressureTotalNum(tempMap, 2));
                    buildMap.put("preAbnormalNum", getPressureTotalNum(tempMap, 5));
                    buildMap.put("liquidAbnormalNum", getPressureTotalNum(tempMap, 4));
                    buildMap.put("analyseAbnormalNum", getPressureTotalNum(tempMap, 6));
                } else if (12 == flag) {
                    //水泵监测排行榜导出
                    buildMap.put("deviceAbnormalNum", getPressureTotalNum(tempMap, 11));
                    buildMap.put("pumpAbnormalNum", getPressureTotalNum(tempMap, 10));
                }
                buildMap.put("abnormalNum", getPressureTotalNum(tempMap, flag));
                buildList.add(buildMap);
                areaMap.put("abnormalNum", (Integer) areaMap.get("abnormalNum") + (Integer) buildMap.get("abnormalNum"));
                centerMap.put("abnormalNum", (Integer) centerMap.get("abnormalNum") + (Integer) buildMap.get("abnormalNum"));
            }
        }
        return resultList;
    }

    private Map<String, Object> floorNameExists(HttpServletRequest request, String buildId, String floorName) {
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("exists", Boolean.FALSE);
        List<FloorInfoReturnVo> floorList = baseBuildingFloorService.getBuildingFloors(request, buildId);
        if (null != floorList && floorList.size() > 0) {
            for (FloorInfoReturnVo floorInfoReturnVo : floorList) {
                if (null != floorInfoReturnVo && floorInfoReturnVo.getType() != null) {
                    for (BaseFloorInfoVo baseFloorInfoVo : floorInfoReturnVo.getType()) {
                        if (floorName.equals(baseFloorInfoVo.getName())) {
                            returnMap.put("floorId", baseFloorInfoVo.getId());
                            returnMap.put("floor", floorInfoReturnVo.getFloor());
                            returnMap.put("exists", Boolean.TRUE);
                            return returnMap;
                        }
                    }
                }
            }
        }
        return returnMap;
    }

    private List<Map<String, Object>> fiterList(List<PreInfluxData> dataList) {
        List<Map<String, Object>> returnList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (PreInfluxData preData : dataList) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("time", preData.getMsgTime());
                dataMap.put("value", preData.getValue());
                returnList.add(dataMap);
            }
        }
        return returnList;
    }

    private List<Map<String, Object>> fiterliquidList(List<LiquidInfluxData> dataList, BigDecimal maxval, BigDecimal minval) {
        List<Map<String, Object>> returnList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (LiquidInfluxData preData : dataList) {
                String value = preData.getValue();
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("time", preData.getMsgTime());
                dataMap.put("value", value);
                //正常
                String status = null;
                BigDecimal pressureValue = new BigDecimal(value);
                if (pressureValue.compareTo(minval) > -1 && pressureValue.compareTo(maxval) < 1) {
                    status = ConstantUtil.PRE_VALUE_NORMAL;
                } else if (pressureValue.compareTo(minval) < 0) {
                    status = ConstantUtil.PRE_VALUE_LOW;
                } else {
                    status = ConstantUtil.PRE_VALUE_HIGHT;
                }
                dataMap.put("status", status);
                returnList.add(dataMap);
            }
        }
        return returnList;
    }

    /**
     * 根据点位所属设备类型来获取最小阈值
     *
     * @param devTypeCode
     * @return minval
     */
    @Override
    public String getMinValByDevTypeCode(String devTypeCode) {
        String minval = "";
        switch (devTypeCode) {
            case "SX":
                //TODO 该类型最小阈值默认值：1.5M
                minval = selectByConfigCode("sybj_7sc", 1.5);
                break;
            case "YLS":
                //TODO 该类型最小阈值默认值：0.1MPa
                minval = selectByConfigCode("sybj_0ssxt", 0.1);
                break;
            case "YLX":
                //TODO 该类型最小阈值默认值：0.1MPa
                minval = selectByConfigCode("sybj_1gsxt", 0.1);
                break;
            case "YLY":
                //TODO 该类型最小阈值默认值：0.01MPa
                minval = selectByConfigCode("sybj_4fxyc", 0.01);
                break;
            case "YLG":
                //TODO 该类型最小阈值默认值：0.01MPa
                minval = selectByConfigCode("sybj_2yzyxt", 0.01);
                break;
            case "SC":
                //TODO 该类型最小阈值默认值：1M
                minval = selectByConfigCode("sybj_5xhxxt", 1);
                break;
            case "SPYG":
                //TODO 该类型最小阈值默认值：0.2MPa
                minval = selectByConfigCode("sybj_3plbjfqhw", 0.2);
                break;
            case "FQYL":
                //TODO 该类型最小阈值默认值：0.15MPa
                minval = selectByConfigCode("sybj_6spxt", 0.15);
                break;
            default:
                minval = "0";
                break;
        }
        return minval;
    }

    @Override
    public void initStatData(Map<String, Object> paramMap) {
        String buildId = (String) paramMap.get("buildId");
        //运营中心编号
        String centerCode = (String) paramMap.get("centerCode");
        //运营中心名称
        String centerName = (String) paramMap.get("centerName");
        //区域编号
        String areaCode = (String) paramMap.get("areaCode");
        //区域名称
        String areaName = (String) paramMap.get("areaName");

        //初始化 stat_water_press_real
        QueryWrapper<StatWaterPressReal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("build_id", buildId);
        List<StatWaterPressReal> pressureList = statWaterPressRealMapper.selectList(queryWrapper);
        if (null == pressureList || pressureList.size() < 1) {
            StatWaterPressReal statWaterPressReal = new StatWaterPressReal();
            statWaterPressReal.setBuildId(buildId);
            statWaterPressReal.setCenterCode(centerCode);
            statWaterPressReal.setCenterName(centerName);
            statWaterPressReal.setAreaCode(areaCode);
            statWaterPressReal.setAreaName(areaName);
            statWaterPressReal.setWaterPressPoint(0);
            statWaterPressReal.setLiquidDownPoint(0);
            statWaterPressReal.setLiquidOnPoint(0);
            statWaterPressReal.setWaterPointOff(0);
            statWaterPressReal.setWaterOnPoint(0);
            statWaterPressReal.setWaterPointFault(0);
            statWaterPressReal.setWaterDownPoint(0);
            statWaterPressReal.setAnalyseAbnormalPoint(0);
            statWaterPressRealMapper.insert(statWaterPressReal);
        } else {
            StatWaterPressReal statWaterPressReal = pressureList.get(0);
            if (null != statWaterPressReal) {
                statWaterPressReal.setCenterCode(centerCode);
                statWaterPressReal.setCenterName(centerName);
                statWaterPressReal.setAreaCode(areaCode);
                statWaterPressReal.setAreaName(areaName);
                statWaterPressRealMapper.updateById(statWaterPressReal);
            }
        }

        //初始化 stat_pump_real
        QueryWrapper<StatPumpReal> queryPumpWrapper = new QueryWrapper<>();
        queryPumpWrapper.eq("build_id", buildId);
        List<StatPumpReal> pumpList = statPumpRealMapper.selectList(queryPumpWrapper);
        if (null == pumpList || pumpList.size() < 1) {
            StatPumpReal statPumpReal = new StatPumpReal();
            statPumpReal.setBuildId(buildId);
            statPumpReal.setPumpCount(0);
            statPumpReal.setAreaCode(areaCode);
            statPumpReal.setAreaName(areaName);
            statPumpReal.setCenterCode(centerCode);
            statPumpReal.setCenterName(centerName);
            statPumpReal.setFaultPumps(0);
            statPumpReal.setRunPumps(0);
            statPumpReal.setHandsPumps(0);
            statPumpReal.setOffPumps(0);
            statPumpReal.setOutagePumps(0);
            statPumpRealMapper.insert(statPumpReal);
        } else {
            StatPumpReal statPumpReal = pumpList.get(0);
            if (null != statPumpReal) {
                statPumpReal.setAreaCode(areaCode);
                statPumpReal.setAreaName(areaName);
                statPumpReal.setCenterCode(centerCode);
                statPumpReal.setCenterName(centerName);
                statPumpRealMapper.updateById(statPumpReal);
            }
        }


        //初始化 闭店统计功能数据
        QueryWrapper<StatCloseStoreReal> realQueryWrapper = new QueryWrapper<>();
        realQueryWrapper.eq("building_id", buildId);
        List<StatCloseStoreReal> closeStoreRealList = statCloseStoreRealMapper.selectList(realQueryWrapper);
        if (null == closeStoreRealList || closeStoreRealList.size() < 1) {
            StatCloseStoreReal storeReal = new StatCloseStoreReal();
            storeReal.setBuildingId(buildId);
            storeReal.setAreaCode(areaCode);
            storeReal.setAreaName(areaName);
            storeReal.setCenterCode(centerCode);
            storeReal.setCenterName(centerName);
            statCloseStoreRealMapper.insert(storeReal);
        } else {
            StatCloseStoreReal storeReal = closeStoreRealList.get(0);
            if (null != storeReal) {
                storeReal.setAreaCode(areaCode);
                storeReal.setAreaName(areaName);
                storeReal.setCenterCode(centerCode);
                storeReal.setCenterName(centerName);
                statCloseStoreRealMapper.updateById(storeReal);
            }
        }

        //初始化 厨房设备检测统计数据
        try {
            LambdaQueryWrapper<KitchenEquipStat> kitQw = new LambdaQueryWrapper<>();
            kitQw.eq(KitchenEquipStat::getBuildingId, buildId);
            List<KitchenEquipStat> kitchenEquipList = kitchenEquipStatMapper.selectList(kitQw);
            if (CollectionUtil.isEmpty(kitchenEquipList)) {
                KitchenEquipStat kitchenEquipStat = new KitchenEquipStat();
                kitchenEquipStat.setId(IdGenerator.getRedisIdStr());
                kitchenEquipStat.setBuildingId(buildId);
                kitchenEquipStat.setAreaCode(areaCode);
                kitchenEquipStat.setAreaName(areaName);
                kitchenEquipStat.setCenterCode(centerCode);
                kitchenEquipStat.setCenterName(centerName);
                kitchenEquipStatMapper.insert(kitchenEquipStat);
            } else {
                KitchenEquipStat kitchenEquipStat = kitchenEquipList.get(0);
                if (kitchenEquipStat != null) {
                    kitchenEquipStat.setAreaCode(areaCode);
                    kitchenEquipStat.setAreaName(areaName);
                    kitchenEquipStat.setCenterCode(centerCode);
                    kitchenEquipStat.setCenterName(centerName);
                    kitchenEquipStatMapper.updateById(kitchenEquipStat);
                }
            }
        } catch (Exception e) {
            log.error("[WaterMonitorServiceImpl]初始化 厨房设备检测统计数据 失败, buildingId:{}, 失败原因{}", buildId, e.getMessage());
        }
    }

    private String selectByConfigCode(String code, double defaultValue) {
//        List<Map<String, Object>> configRelationList = configRelationMapper.selectByConfigCode(code);
        List<Map<String, Object>> configRelationList = dropDownService.queryConfigMapByCode(code);
        if (null != configRelationList && configRelationList.size() > 0 && null != configRelationList.get(0)
                && null != configRelationList.get(0).get("config_double_data")) {
            return configRelationList.get(0).get("config_double_data") + "";
        } else {
            return defaultValue + "";
        }
    }

    /**
     * 当新增点位时，得同时往redis中写
     *
     * @param baseDevicePoint
     */
    public void setPointInfoToRedis(BaseDevicePoint baseDevicePoint) {
        String key = null;
        Map<String, String> valueMap = new HashMap<>();
        String deviceNo = null;
        String addressNo = null;
        switch (baseDevicePoint.getSuperType()) {
            case ConstantUtil.DEVICE_TYPE_PRESSURE_GAGE:
                //压力表点位
                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_WATER_PRESSURE + baseDevicePoint.getId();
                valueMap.put("lastHeartbeatTime", "");
                valueMap.put("pushTime", "");
                valueMap.put("deviceStatus", "0");
                valueMap.put("faultStatus", "0");
                valueMap.put("highStatus", "0");
                valueMap.put("lowStatus", "0");
                valueMap.put("analyseAbnormalStatus", "0");
                valueMap.put("signals", "");
                valueMap.put("value", "");
                deviceNo = baseDevicePoint.getHostId();
                addressNo = baseDevicePoint.getDid();
                break;
            case ConstantUtil.DEVICE_TYPE_LIQUID_GAGE:
                //液位仪点位
                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_WATER_LIQUID + baseDevicePoint.getId();
                valueMap.put("lastHeartbeatTime", "");
                valueMap.put("pushTime", "");
                valueMap.put("deviceStatus", "0");
                valueMap.put("highStatus", "0");
                valueMap.put("lowStatus", "0");
                valueMap.put("signals", "");
                valueMap.put("value", "");
                deviceNo = baseDevicePoint.getHostId();
                addressNo = baseDevicePoint.getDid();
                break;
            case ConstantUtil.DEVICE_TYPE_PUMP:
                //水泵点位
                key = baseDevicePoint.getBuildingId() + ConstantUtil.REDIS_KEY_PUMP + baseDevicePoint.getId();
                valueMap.put("lastHeartbeatTime", "");
                valueMap.put("pushTime", "");
                valueMap.put("deviceStatus", "0");
                valueMap.put("manualStatus", "1");
                valueMap.put("outageStatus", "1");
                valueMap.put("operationStatus", "0");
                valueMap.put("faultStatus", "0");
                deviceNo = baseDevicePoint.getHostId();
                addressNo = baseDevicePoint.getDid();
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(key)) {
            redisUtils.set(key, JSON.toJSONString(valueMap));
            setEquipmentToRedis(deviceNo, addressNo, baseDevicePoint.getBuildingId(), baseDevicePoint.getId(), baseDevicePoint.getSuperType());
        }
    }

    /**
     * @param deviceNo  设备号
     * @param addressNo 从设备号
     * @param buildId   建筑物id
     * @param id        点位表主键id
     */
    private void setEquipmentToRedis(String deviceNo, String addressNo, String buildId, String id, String deviceType) {
        String key = ConstantUtil.REDIS_DEVICE_KEY_EQUIPMENT + deviceNo + "-" + addressNo + "-" + deviceType;
        String value = buildId + "-" + id;
        System.out.println("=======================================equipment key:" + key);
        System.out.println("=======================================equipment value:" + value);
        redisUtils.set(key, value);
        Object str = redisUtils.get(key);
        System.out.println("=======================================equipment strOps.get(key:" + key + "): " + str);
    }

    /**
     * 更新stat_water_press_real表中总点位数
     *
     * @param buildId
     * @param num
     */
    private void editStatWaterPressureTotal(String buildId, int num) {
        Map<String, Object> waterMap = new HashMap<>();
        waterMap.put(buildId, num);
        editStatWaterPressureTotal(waterMap);
    }

    private void editStatWaterPressureTotal(String key, String buildId) {
        //更新水压redis
        Object o = redisUtils.get(key);
        if (o != null) {
            FarEastoneCache farEastoneCache = FastJSONUtils.toBean(String.valueOf(o), FarEastoneCache.class);
            String highStatus = farEastoneCache.getHighStatus();
            String lowStatus = farEastoneCache.getLowStatus();
            String deviceStatus = farEastoneCache.getDeviceStatus();
            List<StatWaterPressReal> list = iStatWaterPressRealService.list(new QueryWrapper<StatWaterPressReal>().eq("build_id", buildId));
            if (list != null && list.size() > 0) {
                StatWaterPressReal statWaterPressReal = list.get(0);
                if ("1".equals(highStatus)) {
                    Integer waterOnPoint = statWaterPressReal.getWaterOnPoint();
                    statWaterPressReal.setWaterOnPoint(waterOnPoint - 1);
                }
                if ("1".equals(lowStatus)) {
                    Integer waterDownPoint = statWaterPressReal.getWaterDownPoint();
                    statWaterPressReal.setWaterDownPoint(waterDownPoint - 1);
                }
                if ("1".equals(deviceStatus)) {
                    Integer waterPointOff = statWaterPressReal.getWaterPointOff();
                    statWaterPressReal.setWaterPointOff(waterPointOff - 1);
                }
                Integer current = statWaterPressReal.getWaterPressPoint();
                statWaterPressReal.setWaterPressPoint(current - 1);
                iStatWaterPressRealService.updateById(statWaterPressReal);
            }
        }
    }

    private void editStatWaterPressureTotal(Map<String, Object> paramMap) {
        iStatWaterPressRealService.editStatWaterPressure(paramMap);
    }

    /**
     * 更新stat_pump_real表中总点位数
     *
     * @param buildId
     * @param num
     */
    private void editStatWaterPumpTotal(String buildId, int num) {
        Map<String, Object> waterMap = new HashMap<>();
        waterMap.put(buildId, num);
        iStatPumpRealService.editStatWaterPump(waterMap);
    }

    private Integer queryWaterPressureAllPointNum(Map<String, Object> paramsMap) {
        List<Map> dataList = this.baseMapper.getWaterPressureDeviceData(paramsMap);
        Integer deviceTotalNum = 0;
        if (null != dataList && dataList.size() > 0) {
            for (Map dataMap : dataList) {
                Integer waterPressPoint = (Integer) dataMap.get("water_press_point");
                if (null != waterPressPoint) {
                    deviceTotalNum += waterPressPoint;
                }
            }
        }
        return deviceTotalNum;
    }

    private Integer queryWaterPressureAbnormalNum(Map<String, Object> paramsMap) {
        Map<String, BigDecimal> abnormalTotal = this.baseMapper.queryWaterPressureAbnormalPoint(paramsMap);
        int waterDown = 0;
        int waterOn = 0;
        int liquidOn = 0;
        int liquidDown = 0;
        int waterPointOff = 0;
        if (null != abnormalTotal) {
            if (null != abnormalTotal.get("waterDown")) {
                waterDown = abnormalTotal.get("waterDown").intValue();
            }
            if (null != abnormalTotal.get("waterOn")) {
                waterOn = abnormalTotal.get("waterOn").intValue();
            }
            if (null != abnormalTotal.get("liquidOn")) {
                liquidOn = abnormalTotal.get("liquidOn").intValue();
            }
            if (null != abnormalTotal.get("liquidDown")) {
                liquidDown = abnormalTotal.get("liquidDown").intValue();
            }
            if (null != abnormalTotal.get("waterPointOff")) {
                waterPointOff = abnormalTotal.get("waterPointOff").intValue();
            }
        }
        return waterDown + waterOn + liquidOn + liquidDown + waterPointOff;
    }

    private Integer queryWaterPumpAllPointNum(Map<String, Object> paramsMap) {
        Map<String, BigDecimal> dataList = this.baseMapper.getWaterLiquidDeviceData(paramsMap);
        Integer deviceTotalNum = 0;
        if (null != dataList) {
            if (dataList.get("pumpCount") != null) {
                deviceTotalNum = dataList.get("pumpCount").intValue();
            }
        }
        return deviceTotalNum;
    }

    private Integer queryWaterPumpNormalNum(Map<String, Object> paramsMap) {
        Map<String, BigDecimal> normalTotalNum = this.baseMapper.queryWaterPumpAbnormalPoint(paramsMap);
        Integer normalPumps = 0;
        if (null != normalTotalNum) {
            if (normalTotalNum.get("normalPumps") != null) {
                normalPumps = normalTotalNum.get("normalPumps").intValue();
            }
        }
        return normalPumps;
    }

    private List<Map<String, Object>> pumpDataOrder(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> normalList = new LinkedList<>();
        List<Map<String, Object>> abNormalList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (Map<String, Object> dataMap : dataList) {
                if ("0".equals(dataMap.get("abnormalStatus")) &&
                        (StringUtils.isEmpty((String) dataMap.get("deviceStatus")) || "0".equals(dataMap.get("deviceStatus")))) {
                    normalList.add(dataMap);
                } else {
                    abNormalList.add(dataMap);
                }
            }
        }
        sortList(normalList, "updateTime");
        sortList(abNormalList, "pushTime");
        abNormalList.addAll(normalList);
        return abNormalList;
    }

    private List<Map<String, Object>> pressureDataOrder(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> normalList = new LinkedList<>();
        List<Map<String, Object>> abNormalList = new LinkedList<>();
        if (null != dataList && dataList.size() > 0) {
            for (Map<String, Object> dataMap : dataList) {
                if (ConstantUtil.PRE_VALUE_NORMAL.equals(dataMap.get("status")) || StringUtils.isEmpty((String) dataMap.get("status"))) {
                    normalList.add(dataMap);
                } else {
                    abNormalList.add(dataMap);
                }
            }
        }
        sortList(normalList, "updateTime");
        sortList(abNormalList, "pushTime");
        abNormalList.addAll(normalList);
        return abNormalList;
    }

    private void sortList(List<Map<String, Object>> list, String keyName) {
        if (null == list || list.size() < 1) {
            return;
        }
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                return ((Comparable) o2.get(keyName)).compareTo(
                        (o1.get(keyName))
                );
            }
        });
    }


}
