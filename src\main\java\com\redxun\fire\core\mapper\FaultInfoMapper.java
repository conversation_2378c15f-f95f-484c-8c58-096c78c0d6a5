package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.FaultExport;
import com.redxun.fire.core.entity.FaultInfo;
import com.redxun.fire.core.pojo.dto.FaultListScreenDto;
import com.redxun.fire.core.pojo.dto.app.FaultListDto;
import org.apache.ibatis.annotations.Mapper;
import com.redxun.common.base.db.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * fault_info数据库访问层
 */
@Mapper
public interface FaultInfoMapper extends BaseDao<FaultInfo> {

    String selectDevType(String devTypeCode);

    //根据建筑id 点位id  首报时间和最后上报时间 统计火警信息
    int countFireInfo(FaultInfo faultInfo);

    int updatePoint(FaultInfo faultInfo);

    //根据建筑id 点位id  首报时间和最后上报时间 统计联动信息
    int countLinkage(FaultInfo faultInfo);

    //根据建筑id 点位id  首报时间和最后上报时间 统计监管信息
    int countSupervise(FaultInfo faultInfo);

    int updateFault(FaultInfo faultInfo);

    //根据建筑id 点位id  首报时间和最后上报时间 统计预警信息
    int countEarlywarning(FaultInfo faultInfo);

    //根据建筑id 点位id  首报时间和最后上报时间 统计水异常信息
    int countWater(FaultInfo faultInfo);

    //待处理
    List<FaultListDto> getFaultToDeal(@Param(value = "param") Map<String, Object> param);

    int getFaultToDealCountAllBuild(@Param(value = "param") Map<String, Object> param);

    //处理中
    List<FaultListDto> getFaultCasing(@Param(value = "param") Map<String, Object> param);

    //已完成
    List<FaultListDto> getFaultDone(@Param(value = "param") Map<String, Object> param);

    List<Map<String, Object>> getWbTeam(@Param(value = "param") Map<String, Object> param);

    List<Map<String, Object>> getWbPerson(@Param(value = "param") Map<String, Object> param);

    List<Map<String, Object>> selectTimeLine(@Param(value = "param") Map<String, Object> param);

    Map<String, Object> selectFaultDetails(@Param(value = "param") Map<String, Object> param);

    Map<String, Object> selectDealingInfo(@Param(value = "param") Map<String, Object> param);

    Map<String, Object> selectRepairmen(@Param(value = "id") String id);

    //维保人员视角 查处理中
    List<FaultListDto> getFaultOrFireInfo(Page<?> page, @Param(value = "param") Map<String, Object> param);

    int getFaultOrFireInfoCountAllBuild(@Param(value = "param") Map<String, Object> param);

    FaultListDto selectFaultToDealDetail(@Param(value = "param") Map<String, Object> param);

    //维保人员 已完成
    List<FaultListDto> getFaultOrFireInfoDone(Page<?> page, @Param(value = "param") Map<String, Object> param);

    List<FaultListDto> getFaultToDealAll(@Param(value = "param") Map<String, Object> param);

    List<FaultListDto> getFaultCasingAll(@Param(value = "param") Map<String, Object> param);

    List<FaultListDto> getFaultDoneAll(@Param(value = "param") Map<String, Object> param);

    List<FaultInfo> selectTodayFaultInfo(@Param(value = "param") Map<String, Object> fireParam);

    int selectFaultToScore(@Param(value = "param") Map<String, Object> param);

    /**
     * 用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectFaultInfoToExport(@Param("param") Map param);

    List<String> getFaultList(Map<String, Object> param);

    List<FaultExport> selectFault(@Param("param") Map<String, Object> param);

    Date getLatestTimeByLoopId(@Param("loopFaultId") String loopFaultId);

    /**
     * 大屏统计异常比例
     * @return
     */
    List<FaultListScreenDto> getFaultCountForScreen();
}
