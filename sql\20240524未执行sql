

-- UPDATE os_user set USER_FW_='1' where USER_FW_ is null and WZT_USER_ID_ is not null

//重复数据问题，等业务反馈再处理
select * from mid_building GROUP BY middle_id HAVING count(middle_id) >1
select * from os_user GROUP BY WZT_USER_ID_ HAVING count(WZT_USER_ID_)>1
-- delete from os_user where USER_ID_ in('1368014300461297665',
'1493628936300535809',
'1543038823639859201',
'1543233077737734145',
'1550384516222205954',
'1566840729659076610',
'1569618465377054722',
'1572390096604327938',
'1605819148534874114',
'1627544112866557953',
'1641324601439993858',
'1644228405252714497',
'1658361735166423041',
'1671398714053357569',
'1683768433209110529');
ALTER TABLE os_user add CONSTRAINT WZT_USER_ID_UNIQUE UNIQUE key(WZT_USER_ID_);


数据库修改脚本
#base_device_point
select count(1) from base_device_point;

ALTER TABLE `wd_iot_fire`.`base_device_point`
MODIFY COLUMN `building_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '建筑物id' AFTER `alarm_valve`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`, `building_id`) USING BTREE;

ALTER TABLE `wd_iot_fire`.`base_device_point`
DROP INDEX `index_host_id_did_super_type_`,
DROP INDEX `index_host_id_did`,
ADD INDEX `index_host_id_did_super_type_`(`host_id` ASC, `did` ASC, `super_type` ASC) USING BTREE;

#500万数据接近20分钟
alter table base_device_point PARTITION BY KEY(building_id) PARTITIONS 20;

#fault_info 300多万数据 6分钟
alter table fault_info PARTITION BY KEY(building_id) PARTITIONS 20;

ALTER TABLE `wd_iot_fire`.`fault_info`
ADD INDEX `IDX_LAST_TIME`(`last_time`) USING BTREE;

ALTER TABLE `wd_iot_fire`.`fire_info`
ADD INDEX `IDX_TYPE_SUBDIVISION`(`type_subdivision`) USING BTREE;




