package com.redxun.fire.core.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 商户查询QO
 * <AUTHOR>
 * @date 2020/12/1 22:51
 */
@Data
public class MidMerchantExcel {

    /**
     * 商户名称
     */
    @Excel(name = "商户名称")
    private String midMerchantName;

    /**
     * 商户编码(铺位号)
     */
    @Excel(name = "铺位号")
    private String midMerchantCode;

    /**
     * 商户类型名称
     */
    @Excel(name = "商户类型")
    private String midMerchantTypeName;

    /**
     * 商业类型 0大商业 1娱乐楼
     */
    @Excel(name = "商业类型")
    private String bizTypeName;




}
