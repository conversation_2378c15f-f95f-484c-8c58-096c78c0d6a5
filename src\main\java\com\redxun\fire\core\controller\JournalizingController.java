package com.redxun.fire.core.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hdwa.hdwaSearchService.result.JsonPage;
import com.hdwa.hdwaSearchService.xf.param.JournalizingPramDTO;
import com.hdwa.hdwaSearchService.xf.result.JournalizingDTO;
import com.hdwa.hdwaSearchService.xf.result.JournalizingExport;
import com.redxun.api.org.IUserService;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.Journalizing;
import com.redxun.fire.core.entity.OsUser;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.pojo.vo.WdOrgUserExportVo;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.fire.core.service.user.IUserDataPowerService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@RestController
@RequestMapping("/journalizing")
public class JournalizingController {

    @Autowired
    IJournalizingService iJournalizingService;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;
    @Autowired
    private IUserDataPowerService userDataPowerService;
    @Autowired
    private IOsUserService osUserService;
    /**
     * @param : null
     * @Description: （未使用）
     * @return: null
     * @Author: zhupj
     * @Date: 2021/03/08 11:39:35
     */
    @RequestMapping("queryAllJournalizing")
    public JsonResult queryAllJournalizing() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iJournalizingService.list());
        return jsonResult;
    }

    /**
     * 新增操作日志记录（未使用）
     *
     * @param journalizing
     * @return
     */
    @RequestMapping("addJournalizing")
    public JsonResult addJournalizing(@RequestBody Journalizing journalizing) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iJournalizingService.save(journalizing));
        return jsonResult;
    }

    /**
     * 前端调用日志操作（导出，重置等）
     *
     * @param journalizing
     * @return
     */
    @RequestMapping("/addOperationLog")
    public void addOperationLog(HttpServletRequest request, @RequestBody Journalizing journalizing) {
        //JsonResult jsonResult = JsonResult.Success();
        iJournalizingService.addOperationLog(request, journalizing);
        //return jsonResult;
    }

    /**
     * 添加资源授权操作日志
     *
     * @param
     * @return
     */
    @PostMapping("/setAuthorityJournalizingLog")
    public void setLogInfo(HttpServletRequest request, @RequestBody Journalizing journalizing) {
        //JsonResult jsonResult = JsonResult.Success();
        //Journalizing journalizing = JSON.parseObject(JSONObject.toJSONString(journalizingDto), Journalizing.class);
        iJournalizingService.setLogInfo(request, journalizing);
        //return jsonResult;
    }

    /**
     * 操作日志列表
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/journalizingList")
    @ApiOperation("操作日志列表")
    public com.hdwa.hdwaSearchService.result.JsonPageResult<JournalizingDTO> journalizingList(@RequestBody @Validated QueryData queryData) {
        com.hdwa.hdwaSearchService.result.JsonPageResult<JournalizingDTO> jsonPageResult=new com.hdwa.hdwaSearchService.result.JsonPageResult<>();
        Map<String, String> params = queryData.getParams();
        String wztUserId=params.get("wztUserId");
        /*List<BaseBuilding> buildingList = userDataPowerService.getUserDataPowerForList(wztUserId);
        String builingIds = buildingList.stream().map(e -> e.getId()).collect(Collectors.joining(","));*/
        QueryWrapper userQueryWrapper = new QueryWrapper();
        userQueryWrapper.eq("WZT_USER_ID_", wztUserId);
        List<OsUser> userList=osUserService.getBaseMapper().selectList(userQueryWrapper);
        if(userList!=null&&userList.size()>0){
            OsUser osUser=userList.get(0);
            String level=osUser.getLevelAyer();
            String organization="";
            if("4".equals(level)){
                organization=osUser.getGroupOrg();
            }else if("1".equals(level)){
                organization=osUser.getPiazza();
            }else if("2".equals(level)){
                organization=osUser.getCity();
            }else if("3".equals(level)){
                organization=osUser.getRegion();
            }
//            params.put("organization", organization);
            if(params.containsKey("organization") && StrUtil.isNotBlank(params.get("organization"))){
                params.put("organization", params.get("organization"));
            }else {
                params.put("organization", organization);
            }
            queryData.setParams(params);
            jsonPageResult = orgMiddleService.findESJournalizingList(queryData);
            return jsonPageResult;
        }else{
            JsonPage<JournalizingDTO> json = new JsonPage<JournalizingDTO>();
            json.setData(new ArrayList<>());
            json.setPageNo(queryData.getPageNo().longValue());
            json.setPageSize(queryData.getPageSize().longValue());
            json.setTotalCount(0L);
            json.setTotalPage(0L);
            jsonPageResult.setResult(json);
            return jsonPageResult;
        }
    }

    /**
     * 操作日志导出
     *
     * @return
     */
/*    @MethodDefine(title = "导出EXCEL", path = "/journalizingExport", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "操作日志导出", varName = "params")})
    @PostMapping("/journalizingExport")
    public void journalizingExport(HttpServletRequest request,@RequestParam JSONObject params) throws IOException {*/
    @MethodDefine(title = "导出EXCEL", path = "/journalizingExport", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "修改数据", varName = "params")})
    @PostMapping("/journalizingExport")
    public void exportUser(HttpServletRequest request, @RequestBody @Validated Map<String, String> params) throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
        JournalizingPramDTO journalizingPramDTO = new JournalizingPramDTO();
        if (params != null && params.size() > 0) {
            journalizingPramDTO.setOperationStaff(params.get("operationStaff"));
            journalizingPramDTO.setOperationTypeCode(params.get("operationTypeCode"));
            journalizingPramDTO.setOrganization(params.get("organization"));
            journalizingPramDTO.setType(params.get("type"));
            journalizingPramDTO.setOperationEndTime(params.get("operationEndTime"));
            journalizingPramDTO.setOperationStartTime(params.get("operationStartTime"));
        }
        //BeanUtils.copyProperties(params, journalizingPramDTO);
        List<JournalizingExport> data = orgMiddleService.findESJournalizingData(journalizingPramDTO);
        try {
            com.redxun.fire.core.utils.ExcelUtil.writeExcel(response, data, dataDto.getFullName(), "操作日志列表", new WdOrgUserExportVo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 单位下拉
     *
     * @param
     * @return
     */
    @PostMapping("/organizationList")
    public JsonResult organizationList(@RequestBody Map<String, Object> params) {
        JsonResult jsonResult = JsonResult.Success();
        //Journalizing journalizing = JSON.parseObject(JSONObject.toJSONString(journalizingDto), Journalizing.class);
        List<Map<String, Object>> result = iJournalizingService.organizationList(params);
        jsonResult.setData(result);
        return jsonResult;
    }
}
