package com.redxun.fire.core.kitchen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 万安厨设备校验检测记录
 */
@Data
public class KitchenDeviceCheckRecord implements Serializable {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String hostId;

    @NotBlank(message = "设备类型不能为空")
    private String number;

    /**
     * 是否带B版
     */
    @JsonProperty("hasB")
    @NotBlank(message = "是否带B版不能为空")
    private String hasB;

    /**
     * 测试次数
     */
    private Integer testCount;

    /**
     * 测试结果
     */
    private String testResult;

    /**
     * 测试状态字节
     */
    private String testStatus1;

    /**
     * 测试状态字节
     */
    private String testStatus2;

    /**
     * 测试状态字节
     */
    private String testStatus3;

    /**
     * 电池电量
     */
    private String battery;

    /**
     * 信号强度
     */
    private String signalLevel;

    /**
     * 校验时间
     */
    private Date checkTime;
}
