package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@TableName("stat_water_expection")
@Component
public class StatWaterExpection {

  /** 主键 */
  @TableId(type = IdType.UUID)
  @TableField(value = "id")
  private String id;
  /** 点位号 */
  @TableField(value = "point_code")
  private String pointCode;
  /** 点位描述 */
  @TableField(value = "point_desc")
  private String pointDesc;
  /** 设备类型 */
  @TableField(value = "dev_type")
  private String devType;
  /** 异常类型（0水压过高，1水压过低，  2液位过高、3液位过低、4设备故障，5设备离线，6分析异常） */
  @TableField(value = "expection_type")
  private String expectionType;
  /** 异常上报时间 */
  @TableField(value = "report_time")
  private String reportTime;
  /** 异常状态（0未处理，1已处理，2暂不处理） */
  @TableField(value = "expection_status")
  private String expectionStatus;
  /** 暂不处理结束时间 */
  @TableField(value = "end_time")
  private String endTime;
  /** 暂不处理原因 */
  @TableField(value = "reason")
  private String reason;
  /** 建筑物id */
  @TableField(value = "building_id")
  private String buildingId;
  /** 处理情况*/
  @TableField(value = "handling_info")
  private String handlingInfo;
  /** 暂不处理天数*/
  @TableField(value = "day")
  private String day;
  /** 暂不处理小时数*/
  @TableField(value = "hour")
  private String hour;
  /** 点位id*/
  @TableField(value = "point_id")
  private String pointId;
  /** 处理时间*/
  @TableField(value = "handling_time")
  private String handlingTime;
  /** 处理人id*/
  @TableField(value = "handling_id")
  private String handlingId;
  /** 处理人姓名*/
  @TableField(value = "handling_name")
  private String handlingName;
  /**
   * 所属部门的组织机构id
   */
  @TableField(value = "belong_dep")
  private String belongDep;

  /**
   * 审批状态（0 未审批 ，1 审批通过，2审批拒绝）
   */
  @TableField(value = "approve_status")
  private String approveStatus;

  /**
   * 审批时间
   */
  @TableField(value = "approve_time")
  private String approveTime;

  /**
   * 最后上报时间
   */
  @TableField(value = "report_end_time")
  private String reportEndTime;

  /**
   * 图片URL列表
   */
  @TableField(value = "pic_urls")
  private String picUrls;

  /**
   * 次数
   */
  @TableField(value = "alarm_times")
  private Integer alarmTimes;

  /**
   * 处理结果
   */
  @TableField(value = "handle_result")
  private String handleResult;

  /**
   * 反馈结果
   */
  @TableField(value = "feekback_result")
  private String feedbackResult;

  /**
   * 反馈时间
   */
  @TableField(value = "feekback_time")
  private String feedbackTime;

  /**
   * 反馈情况
   */
  @TableField(value = "feekback_info")
  private String feedbackInfo;

  /**
   * 反馈人
   */
  @TableField(value = "feekback_id")
  private String feedbackId;

  @TableField(value = "feekback_name")
  private String feedbackName;
}
