package com.redxun.fire.core.cassandra.dao;

//import com.datastax.driver.core.querybuilder.QueryBuilder;
//import com.datastax.driver.core.querybuilder.Select;

import com.redxun.fire.core.cassandra.entity.PreData;
import com.redxun.fire.core.cassandra.entity.PumpData;
import com.redxun.fire.core.consts.CassandraConstants;
import com.redxun.fire.core.pojo.dto.CassandraQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PreDataDao {

//    @Resource
//    private CassandraTemplate cassandraTemplate;

    @Autowired(required = false)
    CassandraHttpService cassandraHttpService;

    public PreData save(PreData entity) {
        return null;//cassandraTemplate.insert(entity);
    }

    public PreData update(PreData entity) {
        return null;//cassandraTemplate.update(entity);
    }

    public PumpData savePumpData(PumpData entity) {
        return null;//cassandraTemplate.insert(entity);
    }


    public List<PreData> findDeviceByTime(String buildingId, String pointId, Date startTime, Date endTime) {
//        Select query = QueryBuilder.select().from("pre_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.gte("ts", startTime))
//                .and(QueryBuilder.lte("ts", endTime))
////                .and(QueryBuilder.eq("status", "1"))
//                .allowFiltering();
//        String sql ="";
//        return cassandraTemplate.select(sql, PreData.class);
        CassandraQueryDTO dto = new CassandraQueryDTO( buildingId,  pointId,  startTime,  endTime,
                null,  null,  null,  null);
        List<PreData> data = cassandraHttpService.select(dto, CassandraConstants.Pre_DATA,PreData.class);
        return data;
    }

    public List<PumpData> findPumpDataByTime(String buildingId, String pointId, Date startTime, Date endTime,
                                             String powerStatus, String manualStatus, String runStatus, String faultStatus) {
//        Select query = QueryBuilder.select().from("pump_data");
//        Select.Where where = query.where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.gte("ts", startTime))
//                .and(QueryBuilder.lte("ts", endTime));
//        if (StringUtils.isNotEmpty(powerStatus)) {
//            where = where.and(QueryBuilder.eq("power_status", powerStatus));
//        }
//        if (StringUtils.isNotEmpty(manualStatus)) {
//            where = where.and(QueryBuilder.eq("manual_status", manualStatus));
//        }
//        if (StringUtils.isNotEmpty(runStatus)) {
//            where = where.and(QueryBuilder.eq("run_status", runStatus));
//        }
//        if (StringUtils.isNotEmpty(faultStatus)) {
//            where = where.and(QueryBuilder.eq("fault_status", faultStatus));
//        }
//        String sql ="";
//        return cassandraTemplate.select(sql, PumpData.class);
        CassandraQueryDTO dto = new CassandraQueryDTO( buildingId,  pointId,  startTime,  endTime,
                null,  null,  null,  faultStatus);
        dto.setPowerStatus(powerStatus);
        dto.setManualStatus(manualStatus);
        dto.setRunStatus(runStatus);
        List<PumpData> data = cassandraHttpService.select(dto, CassandraConstants.PUMP_DATA,PumpData.class);
        return data;
    }

    public List<PreData> findOneByStatus(String buildingId, String pointId, String status) {
//        Select query = QueryBuilder.select().from("pre_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.eq("status", status))
//                .limit(1);
//        String sql ="";
//        return cassandraTemplate.select(sql, PreData.class);
        return null;
    }
}
