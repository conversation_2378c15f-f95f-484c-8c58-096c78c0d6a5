package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 火警信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("fire_info")
public class FireInfo extends SuperEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    @TableField("id")
    private String id;

    /**
     * 建筑物id
     */
    @TableField("building_id")
    private String buildingId;

    /**
     * 点位id
     */
    @TableField("point_id")
    private String pointId;

    /**
     * 硬件信息
     */
    @TableField("pid")
    private String pid;

    /**
     * 传感器设备号
     */
    @TableField("sensor_num")
    private String sensorNum;

    /**
     * 点位描述
     */
    @TableField("point_desc")
    private String pointDesc;

    /**
     * 消防系统编号
     */
    @TableField("fas_code")
    private String fasCode;

    /**
     * 消防系统名称
     */
    @TableField("fas_name")
    private String fasName;

    /**
     * 设备类型
     */
    @TableField("dev_type")
    private String devType;

    /**
     * 设备名称
     */
    @TableField("dev_name")
    private String devName;

    /**
     * 火警类型（0 误报火警，1 测试火警，2 确认火警，3 自动火警，4 疑似火警）
     */
    @TableField("fire_type")
    private String fireType;

    /**
     * 火警状态（0 未处理，1 已处理，2 处理超时）
     */
    @TableField("fire_status")
    private String fireStatus;

    /**
     * 流水状态
     */
    @TableField("flow_status")
    private String flowStatus;

    /**
     * 火警等级
     */
    @TableField("fire_level")
    private String fireLevel;

    /**
     * 等级时间
     */
    @TableField("level_time")
    private String levelTime;

    /**
     * 火警来源
     */
    @TableField("fire_source")
    private String fireSource;

    /**
     * 火警描述
     */
    @TableField("comment")
    private String comment;

    /**
     * 是否领取
     */
    @TableField("is_pick")
    private String isPick;

    /**
     * 防火分区id
     */
    @TableField("zone_id")
    private String zoneId;

    /**
     * 防火分区名称
     */
    @TableField("zone_name")
    private String zoneName;

    /**
     * 首报时间
     */
    @TableField("first_time")
    private String firstTime;

    /**
     * 最后上报时间
     */
    @TableField("last_time")
    private String lastTime;

    /**
     * 上报次数
     */
    @TableField("report_times")
    private Integer reportTimes;

    /**
     * 上报人
     */
    @TableField(value = "report_user")
    private String reportUser;
    /**
     * 上报人id
     */
    @TableField(value = "report_user_id")
    private String reportUserId;
    /**
     * 上报描述
     */
    @TableField(value = "report_desc")
    private String reportDesc;
    /**
     * 处理人id
     */
    @TableField(value = "executor_id")
    private String executorId;

    /**
     * 处理人名称
     */
    @TableField(value = "executor")
    private String executor;
    /**
     * 处理时间
     */
    @TableField(value = "execute_time")
    private String executeTime;
    /**
     * 处理结果
     */
    @TableField(value = "execute_result")
    private String executeResult;
    /**
     * 是否应急维保
     */
    @TableField(value = "emergency_status")
    private String emergencyStatus;
    /**
     * 填报状态（0 未填报，1 已填报）
     */
    @TableField(value = "report_status")
    private String reportStatus;

    /**
     * 租户ID
     */
    @TableField("TENANT_ID_")
    private String tenantId;

    /**
     * 创建部门ID
     */
    @TableField(value = "CREATE_DEP_ID_")
    private String createDepId;
    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_BY_")
    private String createBy;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME_")
    private Date createTime;
    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_BY_")
    private String updateBy;
    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME_")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private java.sql.Timestamp updateTime;

    /**
     * 建筑名称
     */
    @TableField(value = "building_name")
    private String buildingName;
    /**
     * 建筑状态
     */
    @TableField(value = "building_status")
    private String buildingStatus;
    /**
     * 点位号
     */
    @TableField(value = "point_code")
    private String pointCode;
    /**
     * 位置描述
     */
    @TableField(value = "location_describe")
    private String locationDescribe;
    /**
     * 填报情况
     */
    @TableField(value = "report_situation")
    private String reportSituation;

    /**
     * 测试人员
     */
    @TableField(value = "tester")
    private String tester;
    /**
     * 组织人员
     */
    @TableField(value = "organizer")
    private String organizer;

    /**
     * 反馈时间
     */
    @TableField(value = "feedback_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime feedbackTime;

    /**
     * 评分
     */
    @TableField(value = "grade")
    private String grade;
    /**
     * 跑点时长
     */
    @TableField(value = "duration")
    private String duration;
    /**
     * 存在原因
     */
    @TableField(value = "cause")
    private String cause;
    /**
     * 核实图片
     */
    @TableField(value = "check_img")
    private String checkImg;
    /**
     *  核实图片json
     */
    @TableField(value = "check_img_json")
    private String checkImgJson;

    /**
     * 火警	0
     * 联动	1
     * 异常	2
     * 故障	3
     * 启动	4
     * 监管	5
     * 预警	6
     */
    @TableField(value = "type")
    private String type;
    /**
     * 填报时间
     */
    @TableField(value = "fillInTime")
    private String fillInTime;
    /**
     * 核实人Id
     */
    @TableField(value = "checker")
    private String checker;
    /**
     * 测试类型（0 设备测试，1 维保测试）
     */
    @TableField(value = "test_type")
    private String testType;

    /**
     * 7日误报次数
     */
    @TableField("wrongNumber7")
    private Integer wrongNumber7;

    /**
     * 14误报次数
     */
    @TableField("wrongNumber14")
    private Integer wrongNumber14;

    /**
     * 建筑状态名
     */
    @TableField(value = "building_status_str")
    private String buildingStatusStr;
    /**
     * 火警类型名
     */
    @TableField(value = "fire_type_str")
    private String fireTypeStr;
    /**
     * 测试类型名
     */
    @TableField(value = "test_type_str")
    private String testTypeStr;

    /**
     * 业态
     */
    @TableField("business_Type")
    private String businessType;

    /**
     * 业态名
     */
    @TableField("business_Type_str")
    private String businessTypeStr;

    /**
     * 视频记录
     */
    @TableField(value = "video_url")
    private String videoUrl;
    /**
     * 视频记录
     */
    @TableField(value = "video_url_json")
    private String videoUrlJson;
    /**
     * 是否步行街点位（0 否，1是）
     */
    @TableField(value = "is_walking_street")
    private String isWalkingStreet;
    /**
     * 误报原因
     */
    @TableField(value = "fire_remark")
    private String fireRemark;
    /**
     * 火警类型细分（0 设备测试或日常测试，1 跑点测试，2 真实火警，3 演示火警）
     */
    @TableField(value = "type_subdivision")
    private String typeSubdivision;
    /**
     * 测试单位
     */
    @TableField(value = "test_unit")
    private String testUnit;

    /**
     * 维修人id
     */
    @TableField(value = "repairman_id")
    private String repairmanId;
    /**
     * 指派时间
     */
    @TableField(value = "send_time")
    private String sendTime;
    /**
     * 所属部门的组织机构id
     */
    @TableField(value = "belong_dep")
    private String belongDep;
    /**
     * 核实人员id
     */
    @TableField(value = "checkerId")
    private String checkerId;
    /**
     * 消控室电话
     */
    @TableField(exist = false)
    private String ctrlPhone;

    /**
     * 火警类型细分（0 设备测试或日常测试，1 跑点测试，2 真实火警，3 演示火警）
     */
    @TableField(value = "type_subdivision_str")
    private String typeSubdivisionStr;

    @TableField(value = "feedback_result")
    private String feedbackResult;
    /**
     * 填报人
     */
    @TableField(value = "fill_user")
    private String fillUser;
    /**
     * 填报人id
     */
    @TableField(value = "fill_user_id")
    private String fillUserId;

    /**
     *  0-不上传；1-上传
     */
    @TableField(value = "upload_status")
    private String uploadStatus;
    /**
     *  超时报备状态,1:超时报备,其他:非超时报备
     */
    @TableField(value = "over_time_status")
    private String overTimeStatus;

    /**
     * 是否合格 0-不合格，1-合格
     * @return
     */
    private String checkResult;

    /**
     * 检查时间
     * @return
     */
    private Date checkTime;

    /**
     * 检查人
     * @return
     */
    private String checkUser;

    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
}
