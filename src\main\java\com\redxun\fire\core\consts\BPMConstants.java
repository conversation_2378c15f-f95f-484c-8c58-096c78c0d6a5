package com.redxun.fire.core.consts;

/**
 * <AUTHOR>
 * Created on 2020/10/27 0027.
 * 流程相关常量定义 各类流程的流程定义Id
 */
public class BPMConstants {

    /**
     * 单斜线
     */
    public final static String SINGLE_SLASH = "/";

    /**
     * 获取我发起的流程实例
     */
    public static final String BPM_GET_MY_START_INSTS_URL = "api/api-bpm/restApi/bpm/getMyStartInsts";

    /**
     * 启动流程
     * 请求参数：userAccount
     * 请求体：
     * {
     *     "checkType": "AGREE",
     *     "defId": "1288318003842125826",
     *     "formJson": "{'jdbd':{'mc':'123','dz':'123'}}"
     * }
     *
     */
    public static final String BPM_START_PROCESS_URL = "api/api-bpm/restApi/bpm/startProcess";

    /**
     * 根据实例ID获取表单数据   请求参数：userAccount & instId
     */
    public static final String BPM_GET_FROM_BY_INSTS_URL = "api/api-bpm/restApi/bpm/getFormDataByInstId";

    /**
     * 根据流程实例ID获取审批历史 请求参数：instId
     */
    public static final String BPM_GET_HISTORY_BY_ID_URL = "api/api-bpm/restApi/bpm/getCheckHistorys";

    /**
     * 根据用户账号获取待办列表
     * 请求参数：userAccount
     * 请求体：
     * {
     *     "pageNo": 1,
     *     "pageSize": 10,
     *     "params": {},
     *     "sortField": "",
     *     "sortOrder": "asc"
     * }
     */
    public static final String BPM_GET_TASK_BY_ACCOUNT_URL = "api/api-bpm/restApi/bpm/getTasksByUserAccount";

    /**
     * 返回我已审批的流程实例列表
     * 请求参数：userAccount
     * 请求体：
     * {
     *     "pageNo": 1,
     *     "pageSize": 10,
     *     "params": {},
     *     "sortField": "",
     *     "sortOrder": "asc"
     * }
     */
    public static final String BPM_GET_MY_APPROVED_URL = "api/api-bpm/restApi/bpm/getMyApproved";


    /**
     * 审批路径地址
     * 请求参数：userAccount
     * 请求体：
     *
     * {
     *     "taskId": "1288397808436527106",
     *     "checkType": "AGREE",
     *     "copyUserAccounts": "zhangsan",
     *     "formJson": "{'t':       {'INST_ID_':'1288374364525404162','r':'','ID_':'1288374366400274434','CREATE_TIME_':*************,'INST_STATUS_':'RUNNING','PARENT_ID_':'0','y':'','REF_ID_':'0','CREATE_BY_':'1'}}",
     *     "msgTypes": "",
     *     "nodeExecutors": {"UserTask_0clful0": [{"type": "user", "id": "1", "name": "管理员", "calcType": "none"}]},
     *     "opFiles": "[]",
     *     "opinion": "AAAAA",
     *     "opinionName": ""
     * }
     */
    public static final String BPM_APPROVE_URL = "api/api-bpm/restApi/bpm/completeTask";
    public static final String BPM_APPROVE_URL2 = "api/api-bpm/bpm/core/bpmTask/completeTask";
    /**
     * 根据任务ID获取后续节点 subsequent 请求参数：taskId
     */
    public static final String BPM_GET_SUBSEQUENT_NODE = "api/api-bpm/restApi/bpm/getTaskOutNodes";

    /**
     * 点位调改新增修改删除
     */
    public static final String DEF_ID_POINT = "Process_50696791538668";
    /**
     * 点位删除超比例
     */
    public static final String S_DEF_ID_POINT = "Process_50694791538682";
    /**
     * 点位删除未超比例
     */
    public static final String N_DEF_ID_POINT = "Process_50696791538868";
    /**
     * 特批点位超比例
     */
    public static final String SSP_DEF_ID_POINT = "Process_59694791538602";
    /**
     * 特批点位未超比例
     */
    public static final String NSP_DEF_ID_POINT = "Process_52694791538601";

    /**
     * 公告流程定义id
     */
    public static final String GG_DEF_ID = "Process_7464762539242";

    /**
     * 检查模式申请流程定义ID
     */
    public static final Object DEF_ID_CHECK_APPLY = "Process_18801578159321";
    /**
     * 点位故障申请修复
     */
    public static final Object DEF_ID_FAULT_APPLY = "Process_32548289087365";

    /**
     * 点位调改类型
     */
    public static final String CHECK_TYPE_POINT = "AGREE";

    /**
     * 普通流程的分类标识
     */
    public static final String BPM_DEF_CAT_NORMAL = "1";

    /**
     * 网关流程的分类标识
     */
    public static final String BPM_DEF_CAT_GATEWAY = "2";

    /**
     * 流程的节点数量
     */
    public static final Integer BPM_DEF_NODE_NUM = 6;

    /**
     * 水压事前报备申请 流程定义key
     */
    public static final String WATER_SYSQ_DEF_ID = "Process_50694791560001";

    /**
     * 水压事后报备申请 流程定义key
     */
    public static final String WATER_SYSH_DEF_ID = "Process_50694791560002";

    /**
     * 水泵事前报备申请 流程定义key
     */
    public static final String WATER_SBSQ_DEF_ID = "Process_50694791560011";

    /**
     * 水泵事后报备申请 流程定义key
     */
    public static final String WATER_SBSH_DEF_ID = "Process_50694791560012";

    /**
     * 检线设备报备(事后)申请 流程定义key
     */
    public static final String ROUTE_CHECK_SH_DEF_ID = "Process_4045574259666";
    /**
     * 实操考试申请 流程定义key
     */
    public static final String PRACTICAL_EXAM_APPLY_ID = "Process_92847415823010";

    /**
     * 岗位认证申请 流程定义key
     */
    public static final String AUTHENTICTION_APPLY_ID = "Process_202403128888";
    /**
     * 超时报备申请 流程定义key
     */
    public static final String OVERTIME_APPLY_ID = "Process_20887167523232";
    /**
     * 远传离线申请(超过基础时长)
     */
    public static final String FAROVERTIME_APPLY_ID = "Process_50696791538166";
    /**
     * 远传离线申请(未超过基础时长)
     */
    public static final String FARNOOVERTIME_APPLY_ID = "Process_50696791538066";
    /**
     * 实操考试申请 流程定义key
     */
    public static final String AUTHENTICTION_TREE_ID = "1404993489075351522";

    /**
     * 闭店监测设备报备(事后)申请 流程定义key
     */
    public static final String CLOSE_STORE_SH_DEF_ID = "Process_4045574259777";

    /**
     * 闭店监测设备报备(事后)申请 流程定义key
     */
    public static final String maintenancePlanProcess0 = "Process_66027188023977";
    /**
     * 闭店监测设备报备(事后)申请 流程定义key
     */
    public static final String maintenancePlanProcess2 = "Process_50694791538098";
    /**
     * 闭店监测设备报备(事后)申请 流程定义key
     */
    public static final String maintenancePlanProcess3 = "Process_17865997325380";

    public static final String DUTY_SHIFT_BPM_KEY = "Process_23799025341909";

    public static final String DUTY_PERSON_IMPORT_BPM_KEY = "Process_43219875915923";
    public static final String DUTY_PERSON_EXCHANGE_BPM_KEY = "Process_48692816049999";


}
