package com.redxun.fire.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.mapper.StatHostExpectionMapper;
import com.redxun.fire.core.mapper.StatLocalHostExpectionMapper;
import com.redxun.fire.core.mapper.StatRouteCheckExceptionMapper;
import com.redxun.fire.core.service.bpm.IBpmDefNodeReferenceService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.RedisUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 远传离线 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@RestController
@RequestMapping("/deviceDelay")
public class DeviceDelayController {

    @Resource
    private OrgManageClient orgManageClient;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Resource
    private StatHostExpectionMapper statHostExpectionMapper;
    @Resource
    private StatLocalHostExpectionMapper statLocalHostExpectionMapper;
    @Resource
    private StatRouteCheckExceptionMapper statRouteCheckExceptionMapper;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private IBpmDefNodeReferenceService bpmDefNodeReferenceService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    Gson gson = new Gson();

    @ApiOperation(value = "远传离线获取审批人")
    @GetMapping({"/deviceDelayJobApprove"})
    public List<TaskExecutor> deviceDelayJobApprove(@RequestParam(value = "nodeId") String nodeId, @RequestParam(value = "userId") String userId, @RequestParam(value = "bpmDefId") String bpmDefId,@RequestParam(value = "buildingId") String buildingId) {
        List<TaskExecutor> userList = new ArrayList<>();
        //UserOrgResponse userOrg = orgMiddleService.getOrgManageByUser(userId);
        BaseBuilding baseBuilding=baseBuildingService.getBaseMapper().selectById(buildingId);
        if (baseBuilding!=null) {
            QueryWrapper qw = new QueryWrapper();
            qw.eq("node_id", nodeId);
            qw.eq("bpm_def_id", bpmDefId);
            qw.eq("node_enable", 1);
            List<BpmDefNodeReference> bpmDefNodeReferenceList = bpmDefNodeReferenceService.getBaseMapper().selectList(qw);
            if (bpmDefNodeReferenceList != null && bpmDefNodeReferenceList.size() > 0) {
                String[] jobIds = bpmDefNodeReferenceList.get(0).getJobId().split(",");
                if (jobIds != null && jobIds.length > 0) {
                    for (String jobId : jobIds) {
                        OrgManageParam orgParam = new OrgManageParam();
                        String level = bpmDefNodeReferenceList.get(0).getLevelAyer();
                        orgParam.setBusinessId("1");
                        if ("1".equals(level)) {
                            orgParam.setPiazzaId(baseBuilding.getPiazza());
                        } else if ("2".equals(level)) {
                            orgParam.setCityId(baseBuilding.getCityCompany());
                        } else if ("3".equals(level)) {
                            orgParam.setRegionId(baseBuilding.getRegion());
                        } else if ("4".equals(level)) {
                            orgParam.setGroupId(baseBuilding.getGroupOrg());
                        }
                        orgParam.setJobId(jobId);
                        orgParam.setTenantId(baseBuilding.getTenantId());
                        JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
                        try {
                            UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                            List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                            if (data != null && data.size() > 0) {
                                for (UserOrgResponse.DataDTO dto : data) {
                                    userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                                }
                            }

                        } catch (Exception e) {
                            log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
                        }
                    }
                }
            }
        }else{
            log.info("远传离线获取审批人未查到指定的建筑信息！！！入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId);
        }
        if(userList!=null&&userList.size()>15){
            log.info("---------------->>>>>>>>>>>>>查询执行人数量过多入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId, userList.size());
            return new ArrayList<TaskExecutor>();
        }
        return userList;
    }

    @ResponseBody
    @PostMapping(value = "/deviceDelayPassApply")
    @ApiOperation("远传离线审批通过")
    public JsonResult deviceDelayPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("远传离线通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        try {
            StatHostExpection statHostExpection = statHostExpectionMapper.selectById(data);
            String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            if (statHostExpection != null) {
                statHostExpection.setApproveStatus("1");
                statHostExpection.setApproveTime(time);
                statHostExpectionMapper.updateById(statHostExpection);
            } else {
                StatLocalHostExpection statLocalHostExpection = statLocalHostExpectionMapper.selectById(data);
                if (statLocalHostExpection != null) {
                    statLocalHostExpection.setApproveStatus("1");
                    statLocalHostExpection.setApproveTime(time);
                    statLocalHostExpectionMapper.updateById(statLocalHostExpection);
                } else {
                    StatRouteCheckException statRouteCheckException = statRouteCheckExceptionMapper.selectById(data);
                    if (statRouteCheckException != null) {
                        statRouteCheckException.setApproveStatus("1");
                        statRouteCheckException.setApproveTime(time);
                        statRouteCheckExceptionMapper.updateById(statRouteCheckException);
                    }
                }
            }
        } catch (Exception e) {
            log.error("远传离线审批通过,相应的事件记录id为【{}】,对应的异常信息为：【{}】", data, e);
            log.error("修改失败，{}", e.getMessage(), e);
        }
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/deviceDelayNoPassApply")
    @ApiOperation("远传离线审批未通过")
    public JsonResult deviceDelayNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("远传离线未通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        try {
            StatHostExpection statHostExpection = statHostExpectionMapper.selectById(data);
            String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            if (statHostExpection != null) {
                log.info("主机延时处理完成" + data);
                //            statHostExpection.setEndTime(time);
                statHostExpection.setApproveStatus("2");
                statHostExpection.setApproveTime(time);
                statHostExpectionMapper.updateById(statHostExpection);
            } else {
                StatLocalHostExpection statLocalHostExpection = statLocalHostExpectionMapper.selectById(data);
                if (statLocalHostExpection != null) {
                    log.info("消防主机" + data);
                    //                statLocalHostExpection.setEndTime(time);
                    statLocalHostExpection.setApproveStatus("2");
                    statLocalHostExpection.setApproveTime(time);
                    statLocalHostExpectionMapper.updateById(statLocalHostExpection);
                } else {
                    StatRouteCheckException statRouteCheckException = statRouteCheckExceptionMapper.selectById(data);
                    if (statRouteCheckException != null) {
                        log.info("消防主机检线" + data);
                        //                    statLocalHostExpection.setEndTime(time);
                        statRouteCheckException.setApproveStatus("2");
                        statRouteCheckException.setApproveTime(time);
                        statRouteCheckExceptionMapper.updateById(statRouteCheckException);
                    }
                }

            }
        } catch (Exception e) {
            log.error("远传离线审批未通过,相应的事件记录id为【{}】,对应的异常信息为：【{}】", data, e);
            log.error("修改失败，{}", e.getMessage(), e);
        }
        return jsonResult;
    }
}
