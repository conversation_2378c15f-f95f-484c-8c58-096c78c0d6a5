package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.entity.FireInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class AuditFireRecord implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditRecordId;

    /**
     * 建筑名称
     */
    private String buildingName;

    /**
     * 设备类型
     */
    private String devTypeName;

    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 位置描述
     */
    private String pointDesc;

    /**
     * 上报时间
     */
    private String reportedTime;

    /**
     * 处理人
     */
    private String disposeUser;

    /**
     * 处理时间
     */
    private String disposeTime;

    /**
     * 填报时间
     */
    private String fillTime;

    /**
     * 测试类型
     */
    private String testType;

    public AuditFireRecord(FireInfo fireInfo) {
        this.buildingName = fireInfo.getBuildingName();
        this.devTypeName = fireInfo.getDevName();
        this.pointNumber = fireInfo.getPointCode();
        this.pointDesc = fireInfo.getPointDesc();
        this.reportedTime = fireInfo.getLastTime();
        this.disposeUser  = fireInfo.getExecutor();
        this.disposeTime = fireInfo.getExecuteTime();
        this.fillTime  = fireInfo.getFillInTime();
    }
}
