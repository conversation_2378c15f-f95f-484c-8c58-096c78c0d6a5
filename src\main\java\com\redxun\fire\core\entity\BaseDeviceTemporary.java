package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BaseDeviceTemporary implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 建筑ID
     */
    private String buildingId;

    /**
     * 主机号
     */
    private String hostNum;
    /**
     * 主机ID
     */
    private String hostId;
    /**
     * did
     */
    private String did;
    /**
     * 申请ID
     */
    private String applyId;
    /**
     * 申请人
     */
    private String applyUser;

    /**
     * 回路号
     */
    private String loopCode;
    /**
     * 点位类型
     */
    private String pointType;

    /**
     * 点位号
     */
    private String pointCode;

    /**
     * 设备类型分类0 消防主机  1 非消防主机'
     */
    private String devTypeCat;

    /**
     * 本地实际主机号
     */
    private String localHostNum;

    /**
     * 本地实际回路号
     */
    private String localLoopCode;
    /**
     * 用户传输装置编号
     */
    private String transmissionNumber;


    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 设备类型编号
     */
    private String devTypeCode;

    /**
     * 设备类型名称
     */
    private String devTypeName;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 防火分区编号
     */
    private String zoneId;

    /**
     * 防火分区名称
     */
    private String zoneName;

    /**
     * 楼层位置
     */
    private String floorAddr;
    /**
     * 所在楼层
     */
    private String floorId;

    /**
     * x轴
     */
    private BigDecimal pointX;

    /**
     * y轴
     */
    private BigDecimal pointY;

    /**
     * 建筑名称
     */
    private String buildName;

    /**
     * 申请类型：（0新增）（1修改）（2删除）
     */
    private String applyType;

    /**
     * 审批类型：（0未审批）（1审批通过）（2审批不通过）
     */
    private String approveType;
    /**
     * 消防系统编号
     */
    private String fasCode;
    /**
     * 消防系统名称
     */
    private String fasName;
    /**
     * 楼层名称(B1 B2 F2)
     */
    private String floorName;
    /**
     * 楼层
     */
    private String floor;
    /**
     * 原点位号
     */
    private String oldPointCode;
    /**
     * 原点位描述
     */
    private String oldDescribe;
    /**
     * 原点位内容
     */
    private String oldContent;

    /**
     * 原设备类型编号
     */
    private String oldDevTypeCode;


    /**
     * 原设备类型名称
     */
    private String oldDevTypeName;

    /**
     * 原防火分区
     */
    private String oldZone;

    /**
     * 原楼层
     */
    private String oldFloor;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_TIME_")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("UPDATE_TIME_")
    private Date updateTime;

    /**
     * 导入方法（模板）
     * */
    @TableField(exist = false)
    private String methodStr;

    @TableField(exist = false)
    private String wztBuilding;

    @TableField(exist = false)
    private String wztBuildingName;

    @TableField(exist = false)
    private String wztFloorId;

    @TableField(exist = false)
    private String wztFloorName;

    @TableField(exist = false)
    private String wztSnapId;

    @TableField(exist = false)
    private String wztSnapName;

    @TableField(exist = false)
    private String wztCompartmentId;

    @TableField(exist = false)
    private String wztCompartmentName;

    @TableField(exist = false)
    private String wztPointId;

    private BigDecimal birdPointX;
    private BigDecimal birdPointY;
    private BigDecimal birdPointZ;
}
