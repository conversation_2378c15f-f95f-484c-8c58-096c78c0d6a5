package com.redxun.fire.core.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSON;
import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.mapper.StatWaterExpectionMapper;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.service.bpm.IBpmDefNodeReferenceService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.service.alarm.impl.WaterAbnormalServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName WaterPressure
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/4 18:12
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/waterPressureApp")
public class WaterPressureController {

    @Resource
    private WaterAbnormalServiceImpl waterAbnormalService;
    @Resource
    private OrgManageClient orgManageClient;
    @Resource
    private StatWaterExpectionMapper statWaterExpectionMapper;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Autowired
    private IBpmDefNodeReferenceService bpmDefNodeReferenceService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    Gson gson = new Gson();

    /**
     * 水压异常列表
     *
     * @param buildingId
     * @param processStatus
     * @param pageIndex
     * @param pageRows
     * @return
     */
    @GetMapping("/waterProcessedList")
    public ResultMsg waterProcessedList(@RequestParam(value = "buildingId") String buildingId,
                                        @RequestParam(value = "processStatus") String processStatus,
                                        @RequestParam(value = "pageIndex") String pageIndex,
                                        @RequestParam(value = "pageRows") String pageRows) {
        if (StringUtils.isEmpty(buildingId)) {
            return ResultMsg.getResultMsg("建筑名为空", 10000);
        }
        return waterAbnormalService.waterProcessedList(buildingId, processStatus, pageIndex, pageRows);

    }

    /**
     * 压力列表
     *
     * @param buildingId
     * @param pageIndex
     * @param pageRows
     * @return
     */
    @GetMapping("/processedList")
    public ResultMsg processedList(@RequestParam(value = "buildingId") String buildingId,
                                   @RequestParam(value = "pageIndex") String pageIndex,
                                   @RequestParam(value = "pageRows") String pageRows,
                                   @RequestParam(value = "pointDesc", required = false) String pointDesc) {
        return waterAbnormalService.processedList(buildingId, pageIndex, pageRows, pointDesc);
    }

    /**
     * 压力值
     *
     * @param pageIndex
     * @param pointId
     * @return
     */
    @GetMapping("/processedRecords")
    public ResultMsg processedRecords(@RequestParam(value = "pageIndex") String pageIndex,
                                      @RequestParam(value = "pageRows") String pageRows,
                                      @RequestParam(value = "pointId") String pointId) {
        return waterAbnormalService.processedRecords(pointId, Integer.parseInt(pageIndex), Integer.parseInt(pageRows));
    }

    /**
     * 液位列表
     */
    @GetMapping("/waterList")
    public ResultMsg waterList(@RequestParam(value = "buildingId") String buildingId,
                               @RequestParam(value = "pageIndex") String pageIndex,
                               @RequestParam(value = "pageRows") String pageRows) {
        return waterAbnormalService.waterList(buildingId, pageIndex, pageRows);

    }


    /**
     * 液位值
     *
     * @param pageIndex
     * @param pointId
     * @return
     */
    @GetMapping("/waterRecord")
    public ResultMsg waterRecord(@RequestParam(value = "pageIndex") String pageIndex,
                                 @RequestParam(value = "pageRows") String pageRows,
                                 @RequestParam(value = "pointId") String pointId) {
        return waterAbnormalService.waterRecord(pointId, Integer.parseInt(pageIndex), Integer.parseInt(pageRows));
    }

    /**
     * 处理异常 水压/水泵
     *
     * @param param
     * @return
     */
    @PostMapping("/exceptionHandle")
    @ResponseBody
    public ResultMsg exceptionHandle(HttpServletRequest request, @RequestBody Map<String, Object> param) throws ParseException {
        return waterAbnormalService.exceptionHandle(request, param);
    }

    @ApiOperation(value = "水压获取审批人")
    @GetMapping({"/waterJobApprove"})
    public List<TaskExecutor> waterJobApprove(@RequestParam(value = "nodeId") String nodeId, @RequestParam(value = "userId") String userId, @RequestParam(value = "bpmDefId") String bpmDefId,@RequestParam(value = "buildingId") String buildingId) {
        //UsersResponse.DataDTO promoterUser = orgMiddleService.findUserByUserId(userId);
        BaseBuilding baseBuilding=baseBuildingService.getBaseMapper().selectById(buildingId);
        List<TaskExecutor> userList = new ArrayList<>();
        if(baseBuilding!=null){
/*            UserOrgResponse userOrg=orgMiddleService.getOrgManageByUser(userId);
            if(userOrg.getSuccess()){*/
            QueryWrapper qw=new QueryWrapper();
            qw.eq("node_id", nodeId);
            qw.eq("bpm_def_id", bpmDefId);
            qw.eq("node_enable", 1);
            List<BpmDefNodeReference> bpmDefNodeReferenceList=bpmDefNodeReferenceService.getBaseMapper().selectList(qw);
            if(bpmDefNodeReferenceList!=null&&bpmDefNodeReferenceList.size()>0) {
                String[] jobIds=bpmDefNodeReferenceList.get(0).getJobId().split(",");
                if(jobIds!=null&&jobIds.length>0) {
                    for(String jobId:jobIds){
                        OrgManageParam orgParam = new OrgManageParam();
                        String level = bpmDefNodeReferenceList.get(0).getLevelAyer();
                        orgParam.setBusinessId("1");
                        if ("1".equals(level)) {
                            orgParam.setPiazzaId(baseBuilding.getPiazza());
                        } else if ("2".equals(level)) {
                            orgParam.setCityId(baseBuilding.getCityCompany());
                        } else if ("3".equals(level)) {
                            orgParam.setRegionId(baseBuilding.getRegion());
                        } else if ("4".equals(level)) {
                            orgParam.setGroupId(baseBuilding.getGroupOrg());
                        }
                        orgParam.setJobId(jobId);
                        orgParam.setTenantId(baseBuilding.getTenantId());
                        JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
                        try {
                            UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                            List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                            if (data != null && data.size() > 0) {
                                for (UserOrgResponse.DataDTO dto : data) {
                                    userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                                }
                            }

                        } catch (Exception e) {
                            log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
                        }
                    }
                }
            }
            //}
        }else{
            log.info("水压获取审批人未查到指定的建筑信息！！！入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId);
        }
        if(userList!=null&&userList.size()>15){
            log.info("---------------->>>>>>>>>>>>>水压获取审批人查询执行人数量过多入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId, userList.size());
            return new ArrayList<TaskExecutor>();
        }
        return userList;
    }

    @ResponseBody
    @PostMapping(value = "/waterPassApply")
    @ApiOperation("水压审批通过")
    public JsonResult waterPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("水压审批通过，ID为：{}", JSON.toJSONString(jsonObject));
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        Date approveTime = new Date();
        try {
            StatWaterExpection statWaterExpection = statWaterExpectionMapper.selectById(data);
            if (statWaterExpection != null) {
                String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                statWaterExpection.setApproveStatus("1");
                statWaterExpection.setApproveTime(time);
                statWaterExpectionMapper.updateById(statWaterExpection);
            }
        } catch (Exception e) {
            log.info("---------------->>>>>>>>>>>>>水压审批通过异常" + JSON.toJSONString(jsonObject));
        }

        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/waterNoPassApply")
    @ApiOperation("水压审批未通过")
    public JsonResult waterNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("水压审批未通过，ID为：{}", JSON.toJSONString(jsonObject));
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        try {
            StatWaterExpection statWaterExpection = statWaterExpectionMapper.selectById(data);
            if (statWaterExpection != null) {
                String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                statWaterExpection.setEndTime(time);
                statWaterExpection.setApproveStatus("2");
                statWaterExpection.setApproveTime(time);
                statWaterExpectionMapper.updateById(statWaterExpection);
            }
        } catch (Exception e) {
            log.info("---------------->>>>>>>>>>>>>水压审批未通过异常" + JSON.toJSONString(jsonObject));
        }
        return jsonResult;
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return waterAbnormalService.flowEvent(dataJson);
    }

}
