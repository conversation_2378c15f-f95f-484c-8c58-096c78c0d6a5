package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.BaseEventStatistics;
import com.redxun.fire.core.entity.BaseEventStatisticsList;
import com.redxun.fire.core.mapper.ConfigRelationMapper;
import com.redxun.fire.core.pojo.dto.BaseEventStatisticsListDto;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.SelectByBuildId;
import com.redxun.fire.core.utils.validated.SelectById;
import com.redxun.fire.core.utils.validated.SelectByJJType;
import com.redxun.fire.core.service.alarm.IBaseEventStatisticsService;
import com.redxun.fire.core.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <p>
 * 事件统计表 为了配合Jpaas来实现 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-25
 */
@RestController
@RequestMapping("/base-event-statistics")
public class BaseEventStatisticsController {

    @Autowired
    RedisUtils redisUtils;
    @Resource
    private ConfigRelationMapper configRelationMapper;
    @Autowired
    private IBaseEventStatisticsService iBaseEventStatisticsService;

    @Autowired
    DropDownServiceImpl dropDownService;

    /**
     * 获取所有数据
     *
     * @param page
     * @return
     */
    @RequestMapping("/queryAll")
    public JsonResult queryAll(@RequestBody Page page) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.queryAll(page));
        return jsonResult;
    }

    /**
     * 查询事件类型
     *
     * @param baseEventStatistics
     * @return
     */
    @RequestMapping("/queryEventType")
    public Object queryEventType(@RequestBody @Validated(SelectByJJType.class) BaseEventStatistics baseEventStatistics) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.queryEventType(baseEventStatistics.getJjType()));
        return jsonResult;
    }

    /**
     * 查询火警类型
     *
     * @return
     */
    @RequestMapping("/queryireAlarmType")
    public JsonResult queryireAlarmType(@RequestBody @Validated(SelectByJJType.class) BaseEventStatistics baseEventStatistics) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("查询火警类型成功!");
        JsonPage result = iBaseEventStatisticsService.queryFireAlarmType(baseEventStatistics.getPage(), baseEventStatistics.getJjType());
        jsonResult.setResult(result);
        return jsonResult;
    }


    /**
     * 根据建筑id查询
     *
     * @param baseEventStatistics
     * @return
     */
    @RequestMapping("/queryDataByBuildId")
    public JsonResult queryDataByBuildId(@RequestBody @Validated(SelectByBuildId.class) BaseEventStatistics baseEventStatistics) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.queryDataByBuildId(baseEventStatistics.getPage(), baseEventStatistics));
        return jsonResult;
    }

    /**
     * 根据id查询
     *
     * @param baseEventStatisticsList
     * @return
     */
    @RequestMapping("/queryDataById")
    public JsonResult queryDataById(@RequestBody @Validated(Select.class) BaseEventStatisticsList baseEventStatisticsList) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.queryDataById(baseEventStatisticsList));
        return jsonResult;
    }

    /**
     * 单个受理
     *
     * @param baseEventStatistics
     * @return
     */
    @RequestMapping("updateDataBatch")
    public JsonResult updateDataBatch(HttpServletRequest request, @RequestBody @Validated(Select.class) BaseEventStatistics baseEventStatistics) throws ServletException, IOException, InterruptedException {
        return iBaseEventStatisticsService.updateData(request, baseEventStatistics);
    }

    /**
     * 批量受理
     *
     * @param baseEventStatisticsList
     * @return
     */
    @PostMapping("updateData")
    public JsonResult updateData(HttpServletRequest request, @RequestBody @Validated(Select.class) BaseEventStatisticsList baseEventStatisticsList) throws ServletException, IOException, InterruptedException {
        return iBaseEventStatisticsService.updateDataBatch(request, baseEventStatisticsList);
    }

    /**
     * 全部受理
     *
     * @param baseEventStatistics
     * @return
     * @throws ServletException
     * @throws IOException
     * @throws InterruptedException
     */
    @PostMapping("updateAll")
    public JsonResult updateAll(HttpServletRequest request, @RequestBody BaseEventStatistics baseEventStatistics) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.updateAll(request, baseEventStatistics));
        return jsonResult;
    }

    /**
     * 根据事件类型Id查询点位信息
     */
    @RequestMapping("queryPointByEventTypeId")
    public JsonResult queryPointByEventTypeId(@RequestBody @Validated(SelectById.class) BaseEventStatisticsListDto baseEventStatisticsList) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseEventStatisticsService.queryPointByEventTypeId(baseEventStatisticsList));
        return jsonResult;
    }

    /**
     * 查询误报原因
     * 95528  监控台火警误报原因
     *
     * @return
     */
    @RequestMapping("queryCause/{code}")
    public JsonResult queryCause(@PathVariable("code") String code) {
        JsonResult jsonResult = JsonResult.Success();
//        jsonResult.setData(configRelationMapper.queryConfigByCodeNew(code));
        jsonResult.setData(dropDownService.queryConfigByCode(code));
        return jsonResult;
    }

    /**
     * 根据key值删除redis
     *
     * @param key
     * @return
     */
    @RequestMapping("removeRedisKey")
    public JsonResult removeRedisKey(String key) {
        JsonResult jsonResult = JsonResult.Success();
        redisUtils.remove(key);
        return jsonResult;
    }

}
