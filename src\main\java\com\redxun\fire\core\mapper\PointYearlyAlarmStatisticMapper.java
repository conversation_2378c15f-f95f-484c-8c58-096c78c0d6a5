package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.pointalarmstatistic.PointYearlyAlarmStatistic;
import com.redxun.fire.core.pojo.vo.PointStatisticVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【point_yearly_alarm_statistic(点位年度报警统计)】的数据库操作Mapper
* @createDate 2025-06-12 09:27:40
* @Entity generator.domain.PointYearlyAlarmStatistic
*/
public interface PointYearlyAlarmStatisticMapper extends BaseMapper<PointYearlyAlarmStatistic> {

    PointStatisticVo getPointStatistic(@Param("buildingId") String buildingId, @Param("nowYear") String nowYear);

    PointYearlyAlarmStatistic getRefreshStatisticNum(@Param("buildingId") String buildingId, @Param("pointId") String pointId
            , @Param("alarmYear") String alarmYear);
}




