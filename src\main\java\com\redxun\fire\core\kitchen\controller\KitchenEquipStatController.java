
package com.redxun.fire.core.kitchen.controller;

import com.alibaba.fastjson.JSONObject;
import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.kitchen.entity.KitchenEquipStat;
import com.redxun.fire.core.kitchen.fvo.KitchenEquipStatFvo;
import com.redxun.fire.core.kitchen.service.KitchenEquipStatServiceImpl;
import com.redxun.web.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/fire/kitchenEquipStat")
@Api(tags = "厨房设备检测数据统计表")
@ClassDefine(title = "厨房设备检测数据统计表", alias = "KitchenEquipStatController", path = "/fire/kitchenEquipStat", packages = "core", packageName = "子系统名称")
public class KitchenEquipStatController extends BaseController<KitchenEquipStat> {

    @Autowired
    KitchenEquipStatServiceImpl kitchenEquipStatService;


    @Override
    public BaseService getBaseService() {
        return kitchenEquipStatService;
    }

    @Override
    public String getComment() {
        return "厨房设备检测数据统计表";
    }


    @MethodDefine(title = "查询厨房设备检测信息", path = "/queryKitchenEquipStatInfo", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "查询厨房设备检测信息", varName = "dataJson")})
    @PostMapping("/queryKitchenEquipStatInfo")
    public JsonResult queryKitchenEquipStatInfo(HttpServletRequest request, @RequestBody KitchenEquipStatFvo dataJson) {
        return kitchenEquipStatService.queryKitchenEquipStatInfo(request, dataJson);
    }

    @MethodDefine(title = "查询厨房设备数量", path = "/queryKitchenCountInfo", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "查询厨房设备数量", varName = "dataJson")})
    @PostMapping("/queryKitchenCountInfo")
    public JsonResult queryKitchenCountInfo(HttpServletRequest request, @RequestBody KitchenEquipStatFvo dataJson) {
        return kitchenEquipStatService.queryKitchenCountInfo(request, dataJson);
    }

    @MethodDefine(title = "初始化统计表脚本", path = "/syncKitchenEquipStat", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "初始化统计表脚本", varName = "dataJson")})
    @PostMapping("/syncKitchenEquipStat")
    public JsonResult syncKitchenEquipStat(@RequestBody JSONObject dataJson) {
        return kitchenEquipStatService.syncKitchenEquipStat(dataJson);
    }

    /**
     * 获取闭店监测按异常类型排行信息
     * 参考 getWaterLiquidAbnormalRankList
     */
    @PostMapping("/getKitchenRankList")
    public JsonResult getKitchenRankList(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        Map<String, Object> data = kitchenEquipStatService.getKitchenRankList(request, paramsMap);
        return JsonResult.getSuccessResult(data);
    }



}

