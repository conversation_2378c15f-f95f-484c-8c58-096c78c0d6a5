package com.redxun.fire.core.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redxun.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <p>
 * 反复误报点位数据列表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MisReportes extends SuperEntity implements Serializable, Comparable<MisReportes> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 建筑物id
     */
    @TableField("building_id")
    private String buildingId;
    /**
     * 建筑物名称
     */
    @TableField("building_name")
    private String buildingName;
    /**
     * 点位号id
     */
    @TableField("point_id")
    private String pointId;
    /**
     * 点位号
     */
    @TableField("point_code")
    private String pointCode;
    /**
     * 点位描述
     */
    @TableField("point_desc")
    private String pointDesc;
    /**
     * 7日内误报次数
     */
    @TableField("sevenDayCount")
    private int sevenDayCount;
    /**
     * 14日内误报次数
     */
    @TableField("fourteenDayCount")
    private Integer fourteenDayCount;
    /**
     * 设备类型名称
     */
    @TableField("dev_type_name")
    private String devTypeName;

    /**
     * 设备类型编号
     */
    @TableField("dev_type_code")
    private String devTypeCode;
    /**
     * 上报时间(最近误报时间)
     */
    @TableField("report_time")
    private Object reportTime;
    /**
     * 连续误报天数（当前天（最近上报时间结束时间）开始往前推）
     */
    @TableField("seriesDays")
    private int seriesDays;
    /**
     * 最近上报时间开始时间
     */
    @TableField("sevenDayCount")
    private Object reportStartTime;
    /**
     * 最近上报时间结束时间（默认是当前天）
     */
    @TableField("sevenDayCount")
    private Object reportEndTime;
    /**
     * 统计时间
     */
    @TableField("count_time")
    private Date countTime;
    /**
     * 14天内误报天数
     * */
    @TableField("date_count")
    private Integer dateCount;
    /**
     * 当前标志 次数>=6次 天数>=5天 红色（1）。其他 黄色（0）
     * */
    @TableField("flag")
    private Integer flag;


    public MisReportes(Object reportTime) {
        this.reportTime = reportTime;
    }

    public MisReportes() {

    }


    @Override
    public int compareTo(MisReportes o) {
        try {
            Long num = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(this.getReportTime().toString()).getTime()) - (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(o.getReportTime().toString()).getTime());
            if (num > 0) {
                return -1;
            } else if (num < 0) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
}
