package com.redxun.fire.core.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.audit.*;
import com.redxun.fire.core.mapper.*;
import com.redxun.fire.core.pojo.vo.DevicePointInfoVo;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.IMessageLogService;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.maintenance.*;
import com.redxun.fire.core.service.other.IAppointmentApScopeService;
import com.redxun.fire.core.service.other.IAppointmentApplyService;
import com.redxun.fire.core.service.other.IAppointmentPointService;
import com.redxun.fire.core.utils.DelayTaskManager;
import com.redxun.fire.core.utils.RedisUtils;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2022-01-20
 */
@Slf4j
@RestController
@RequestMapping("/wbApply")
public class WbApplyController {

    @Resource
    RedisUtils redisUtils;
    @Resource
    AppointmentApplyMapper appointmentApplyMapper;
    @Resource
    MaintenanceScopeTestMapper maintenanceScopeTestMapper;
    @Resource
    MaintenanceConfigMapper maintenanceConfigMapper;
    @Resource
    private IAppointmentApScopeService iAppointmentApScopeService;
    @Resource
    IBaseDevicePointService iBaseDevicePointService;
    @Resource
    IAppointmentPointService iAppointmentPointService;
    @Resource
    IWbTaskService iWbTaskService;
    @Resource
    IBaseBuildingService baseBuildingService;
    @Resource
    MaintenancePlanService maintenancePlanService;
    @Resource
    private IMessageLogService messageLogService;
    @Resource
    private MaintenanceScopeTestService scopeTestService;

    @Resource
    MaintenancePlanTestService maintenancePlanTestService;

    @Resource
    IAppointmentApplyService appointmentApplyService;
    @Autowired
    private IAppointmentApplyService applicationService;
    private ScheduledExecutorService timer = new ScheduledThreadPoolExecutor(0);

    private static final String WB_APPLY_PASS_KEY = "WB_APPLY_PASS_KEY_";

    @ResponseBody
    @PostMapping(value = "/wbApplyPass")
    @ApiOperation("维保申请审批通过")
    public JsonResult wbApplyPass(@RequestBody @Validated Map<String, String> jsonObject) {
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        String lockKey = WB_APPLY_PASS_KEY + data;
        if (redisUtils.tryLock(lockKey)) {
            try {
                WeibaoDto weibaoDto = new WeibaoDto();
                log.info("维保审批申请通过,相应的事件记录id为：【{}】", data);
                //根据事件记录id查询维保申请表
//                AppointmentApply appointment = appointmentApplyMapper.selectById(data);
                AppointmentApply appointment = appointmentApplyMapper.queryDataById(data);
                if (appointment == null) {
                    log.info("当前预约不存在，预约id为：" + data);
                    throw new RuntimeException("当前预约不存在");
                }
                weibaoDto.setBuildingId(appointment.getBuildingId());
                weibaoDto.setStartTime(appointment.getStartTime());
                weibaoDto.setEndTime(appointment.getEndTime());
                weibaoDto.setMaintenanceScopeId(appointment.getMaintenanceScopeId());
                weibaoDto.setAppointmentApplyId(data);
                weibaoDto.setMaintenanceStart("1");
                log.info("获取维保范围====");
                affirm(data, weibaoDto);
                // 先删除维保任务数据 一个建筑只能预约一次
                QueryWrapper<WbTask> wbtaskRemoveWrapper2 = new QueryWrapper<>();
                wbtaskRemoveWrapper2.eq("task_key", "weibao-" + appointment.getBuildingId());
                iWbTaskService.remove(wbtaskRemoveWrapper2);
                //将维保申请放入redis
//                redisUtils.set("weibao-" + appointment.getBuildingId(), JSON.toJSONString(weibaoDto));
//                redisUtils.updateWbData("weibao-" + appointment.getBuildingId(), weibaoDto);
                //只对当前最新的一个申请计划更新redis里面的维保点位
                String applyId = appointmentApplyMapper.selectLatestOneByBuildingId(appointment.getBuildingId());
                if (!appointment.getId().equals(applyId)) {
                    log.info("延迟审批通过的维保不是最新维保，不更新redis数据,且将该维保信息直接结束");
                    if (System.currentTimeMillis() >= appointment.getEndTime().getTime()) {
                        appointment.setRealEndTime(appointment.getEndTime());
                    } else {
                        appointment.setRealEndTime(new Date());
                    }
                    appointment.setMaintenanceStart("4");
                    appointment.setApplicationStatus("2");
                    appointmentApplyMapper.updateById(appointment);
                    return jsonResult;
                }
                //将维保申请放入redis
                if (appointment.getId().equals(applyId)) {
//                        redisUtils.set("weibao-" + appointment.getBuildingId(), JSON.toJSONString(weibaoDto));
                    redisUtils.updateWbData("weibao-" + appointment.getBuildingId(), weibaoDto);
                    log.info("维保申请入redis成功，redis得key为{},维保信息为{}", "weibao-" + appointment.getBuildingId(), JSON.toJSONString(weibaoDto));
                }
                String maintenancePlanId = appointment.getMaintenancePlanId();
//                MaintenancePlan plan = maintenancePlanService.getById(maintenancePlanId);
                MaintenancePlan plan = maintenancePlanService.getMainDataById(maintenancePlanId);
                redisUtils.set(appointment.getId(), plan.getEffectiveTime(), 24L, TimeUnit.HOURS);

                //修改审核状态为已审核 (因点位数据处理太慢 后移到redis点位数据处理结束)
                log.info("修改审核状态为已审核");
                AppointmentApply query = new AppointmentApply();
                query.setId(data);
                query.setApplicationStatus("2");
                appointmentApplyMapper.updateById(query);

                // 维保申请确认后计算结束时修改建筑状态，录入维保范围、计算维保计划已维保点位数和月度计划完成率
                DelayTaskManager delayTaskManager = DelayTaskManager.getInstance();
                // 移除当前建筑下任务
                delayTaskManager.removeTaskByTaskName("weibao-" + appointment.getBuildingId());
                delayTaskManager.removeTaskByTaskName(appointment.getId());
                log.info("计算距离维保结束" + appointment.getEndTime() + "剩余时间s");
                Instant instant = appointment.getEndTime().toInstant();//An instantaneous point on the time-line.(时间线上的一个瞬时点。)
                ZoneId zoneId = ZoneId.systemDefault();//A time-zone ID, such as {@code Europe/Paris}.(时区)
                LocalDateTime endDateTime = instant.atZone(zoneId).toLocalDateTime();
                // 離結束時間還有多少秒
                long end_now = Duration.between(LocalDateTime.now(), endDateTime).getSeconds();
                //将维保结束任务入库
                log.info("将维保结束任务入库");
                iWbTaskService.save(new WbTask(getUUID(), "weibao-" + appointment.getBuildingId(), endDateTime, "1", null, appointment.getBuildingId(), data));
                log.info("当前时间" + end_now);
                log.info("开启" + appointment.getBuildingId() + "建筑" + end_now + "秒后结束维保");
                if (end_now >= 0) {
                    new Thread(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() {
                            //如  已過結束時間   則  全部入庫  為  未爲保
                            delayTaskManager.putTask(new Runnable() {
                                @SneakyThrows
                                @Override
                                public void run() {
                                    wbEnd(appointment, delayTaskManager);
                                }
                            }, end_now, TimeUnit.SECONDS, "weibao-" + appointment.getBuildingId());
                            //delayTaskManager.executeTask();
                        }
                    }).start();
                } else {
                    log.info("维保审批超时结束:" + data);
                    //维保结束的时候 修改建筑物状态 为正常
                    baseBuildingService.updateBuildingStatus(appointment.getBuildingId(), 0);
                    log.info("修改预约申请记录为已结束");
//                    AppointmentApply appointment2 = appointmentApplyMapper.selectById(data);
                    AppointmentApply appointment2 = appointmentApplyMapper.queryDataById(data);
                    if (System.currentTimeMillis() >= appointment2.getEndTime().getTime()) {
                        appointment2.setRealEndTime(appointment2.getEndTime());
                    } else {
                        appointment2.setRealEndTime(new Date());
                    }
                    appointment2.setMaintenanceStart("4");
                    appointment2.setApplicationStatus("2");
                    appointmentApplyMapper.updateById(appointment2);
                    if (data.equals(weibaoDto.getAppointmentApplyId())) {
                        weibaoDto.setMaintenanceStart("4");
                    }
                }

                log.info("维保即将结束推送消息记录开始");
                if (end_now > 0 && end_now <= 600) {
                    log.info("维保预约记录1：" + appointment.toString());
                    //维保计划到时前10分钟内，推送维保即将结束消息记录
                    messageLogService.wbEndNode(appointment.getBuildingId(), appointment.getStartTime(), appointment.getEndTime(), end_now, appointment.getId());
                    if(end_now <=30){
                        messageLogService.wbAutoOutNode(appointment.getBuildingId(), appointment.getStartTime(), appointment.getEndTime(), appointment.getId());
                    }else{
                        long runTime1 = end_now - 30;
                        timer.schedule(new TimerTask() {
                            @Override
                            public void run() {
                                messageLogService.wbAutoOutNode(appointment.getBuildingId(), appointment.getStartTime(), appointment.getEndTime(), appointment.getId());
                            }
                        }, runTime1 * 1000L, TimeUnit.MILLISECONDS);

                    }
                } else if (end_now > 600) {
                    log.info("维保预约记录2：" + appointment.toString());
                    long runTime = end_now - 600;
                    //维保计划到期前10分钟之前，推送维保即将结束消息记录
                    timer.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            messageLogService.wbEndNode(appointment.getBuildingId(), appointment.getStartTime(), appointment.getEndTime(), 599, appointment.getId());
                        }
                    }, runTime * 1000L, TimeUnit.MILLISECONDS);

                    long runTime1 = end_now - 30;
                    timer.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            messageLogService.wbAutoOutNode(appointment.getBuildingId(), appointment.getStartTime(), appointment.getEndTime(), appointment.getId());
                        }
                    }, runTime1 * 1000L, TimeUnit.MILLISECONDS);
                }
                log.info("维保结束执行完毕:" + data);
            } catch (Exception e) {
                log.info("维保结束执行异常:" + data);
                log.error("维保结束请求异常:", e);
                e.printStackTrace();
            } finally {
                redisUtils.delete(lockKey);
            }
        }
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/wbApplyNoPass")
    @ApiOperation("维保申请审批未通过")
    public JsonResult wbApplyNoPass(@RequestBody @Validated Map<String, String> jsonObject) {
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        log.info("维保申请审批未通过---------------------applyId:" + data);
        // 审批状态为未审批 且id相等
        List<AppointmentApply> list = applicationService.list(new QueryWrapper<AppointmentApply>().eq("application_status", "1").eq("id", data));
        if (CollectionUtils.isEmpty(list)) {
            log.info("维保申请审批未通过未查到指定信息---------------------applyId:" + data);
            return new JsonResult(false);
        }
        list.forEach(apply -> {
            apply.setApplicationStatus("3");
        });
        applicationService.saveOrUpdateBatch(list);
        return jsonResult;
    }

    @Autowired
    private IAuditRecordService auditRecordService;
    public void wbEnd(AppointmentApply appointment, DelayTaskManager delayTaskManager) {

        try {
            String buildingId = appointment.getBuildingId();
            log.info("建筑" + buildingId + "维保结束");

            appointmentApplyService.sendPumpWaterException(buildingId);
            //修改建筑物状态 为正常
            baseBuildingService.updateBuildingStatus(buildingId, 0);
            WeibaoDto weibaoDto1 = JSON.parseObject(redisUtils.get("weibao-" + buildingId).toString(), WeibaoDto.class);
            if (weibaoDto1.getAppointmentApplyId().equals(appointment.getId())) {
                return;
            }
            // 判断当前建筑点位记录是否异常 异常修改已入库的点位记录为未维保
            if (weibaoDto1.getBuildFlag() && weibaoDto1.getAppointmentPointList().size() > 0) {
                iAppointmentPointService.updateBatchById(weibaoDto1.getAppointmentPointList());
            }
            Map<String, Boolean> baseDevicePointMap = weibaoDto1.getBaseDevicePointMap();
            log.info("开始录入未维保记录" + baseDevicePointMap.size());
            int[] countAll = {0};// 维保计划所有已维保点位数
            //List<AppointmentPoint> list = new ArrayList<>();
            baseDevicePointMap.forEach((pointId, bool) -> {
           /*     if (!bool) {
                    list.add(creatAppoint(pointId));
                } else {
                    countAll[0]++;
                }*/
                if (bool) {
                    countAll[0]++;
                }
            });
      /*      if(list.size()>0){
                iAppointmentPointService.saveBatch(list);
            }*/
            //修改预约状态为结束
            log.info("修改预约申请记录为已结束");
            AppointmentApply appointment2 = appointmentApplyMapper.selectById(appointment.getId());
            if (ObjectUtil.isEmpty(appointment2.getRealEndTime())) {
                if (System.currentTimeMillis() >= appointment2.getEndTime().getTime()) {
                    appointment2.setRealEndTime(appointment2.getEndTime());
                } else {
                    appointment2.setRealEndTime(new Date());
                }
            }
            appointment2.setMaintenanceStart("4");

            //根据维保计划id获取维保计划
            MaintenancePlan plan = maintenancePlanService.getById(appointment2.getMaintenancePlanId());
            String effectiveTime = plan.getEffectiveTime();

            //预约对应的所有已维保点位
            Map<String, Map<String, Boolean>> scopeDevicePointMap = weibaoDto1.getScopeDevicePointMap();

            //预约，根据测试范围，以前预约且已维保的也算作本次预约的进度
            AtomicReference<Integer> appointmentFinish = new AtomicReference<>(0);
            AtomicReference<Integer> appointmentTotal = new AtomicReference<>(0);

            //临时计划、联动计划同步到测试计划
            if (plan.getScheduling().equals("2") || plan.getScheduling().equals("3")) {
                log.info("修改维保范围1");
                maintenancePlanTestService.updateTestPlanRate(buildingId, effectiveTime);

            } else {
                scopeDevicePointMap.forEach((scopeid, value) -> {
                    List<DevicePointInfoVo> webScopeTestPoint = scopeTestService.getWebScopeTestPointByEffTime(scopeid, buildingId, effectiveTime, null);

                    Integer finishCt = 0;
                    for (DevicePointInfoVo pointInfoVo : webScopeTestPoint) {
                        if (pointInfoVo.getMaintenanceStatus() == 1) {
                            finishCt++;
                        }
                    }

                    appointmentFinish.getAndSet(appointmentFinish.get() + finishCt);
                    appointmentTotal.getAndSet(appointmentTotal.get() + value.size());


                    MaintenanceScopeTest maintenanceScopeTest = new MaintenanceScopeTest();
                    maintenanceScopeTest.setId(scopeid);
                    if (finishCt > 0) {
                        // 已维保状态
                        maintenanceScopeTest.setMaintenanceStatus("1");
                    } else {
                        // 未维保状态
                        maintenanceScopeTest.setMaintenanceStatus("0");
                    }

                    maintenanceScopeTest.setMaintenancePoint(finishCt.toString());
                    // 更新预约维保范围表的维保状态和已维保点位数
                    maintenanceScopeTestMapper.updateById(maintenanceScopeTest);
                });
            }

            appointment2.setMaintenancePoint(appointmentFinish.get());
            appointment2.setPointNum(appointmentTotal.get());
            appointmentApplyMapper.updateById(appointment2);

            log.info("开始计算维保计划已维保点位数和月度计划完成率");

            Thread.sleep(10000);

            Integer finishMaintenancePoint = maintenanceScopeTestMapper.sumFinishPointByPlanId(appointment2.getMaintenancePlanId());

            plan.setMaintenancePoint(finishMaintenancePoint.toString());
            if (plan.getPointNum() != null && !"0".equals(plan.getPointNum())) {
                plan.setPercentageMon(String.format("%.3f", new BigDecimal(finishMaintenancePoint).divide(new BigDecimal(plan.getPointNum()), 3, BigDecimal.ROUND_HALF_UP)));
                if ("1.000".equals(plan.getPercentageMon())) {
                    plan.setState("2");
                }
            } else {
                plan.setPercentageMon("0");
            }
            log.info("已维保点位数" + finishMaintenancePoint + "月度计划完成率" + plan.getPercentageMon());
            maintenancePlanService.updateById(plan);

            Date startTime = plan.getStartTime();
            String scheduling = plan.getScheduling();
            Double yearPercent = maintenancePlanService.countPercentageYear(buildingId, startTime, scheduling);
            maintenancePlanService.updateByYear(buildingId, startTime, scheduling, yearPercent);

            delayTaskManager.removeTaskByTaskName("weibao-" + buildingId + "Close");
            //删除维保任务数据
            QueryWrapper<WbTask> wbtaskRemoveWrapper = new QueryWrapper<>();
            wbtaskRemoveWrapper.eq("task_key", "weibao-" + buildingId);
            iWbTaskService.remove(wbtaskRemoveWrapper);

            if (appointment.getId().equals(weibaoDto1.getAppointmentApplyId())) {
                redisUtils.remove("weibao-" + buildingId);
            }
        } catch (Exception e) {
            log.info("维保延时任务异常:" + appointment.getId());
            e.printStackTrace();
        }
    }





    /**
     * 确认维保
     *
     * @param applyId
     */
    public void affirm(String applyId, WeibaoDto weibaoDto) {
        log.info("查询预约建筑的维保范围开始");
        Map<String, Boolean> baseDevicePointMap = new HashMap<>();
        //  key预约范围id    map key 点位id  val: 是否维保
        Map<String, Map<String, Boolean>> scopeDevicePointMap = new HashMap<>();
        // 根据预约申请id获取预约信息
//        AppointmentApply appointmentApply = appointmentApplyMapper.selectById(applyId);
        AppointmentApply appointmentApply = appointmentApplyMapper.queryDataById(applyId);
        if (appointmentApply != null) {
            if ("2".equals(appointmentApply.getPlanType()) || "3".equals(appointmentApply.getPlanType())) {
                String fasCodes = appointmentApply.getFasCodes();
                String[] fasCodesArray = fasCodes.split(",");
                List<String> fasCodeList = Arrays.asList(fasCodesArray);
                if (fasCodeList.size() > 0) {
                    Set<String> deviceCodes = new HashSet<>();
                    List<MaintenanceConfig> maintenanceConfigs = maintenanceConfigMapper.selectList(new LambdaQueryWrapper<MaintenanceConfig>().in(MaintenanceConfig::getFireproofSysId, fasCodeList));
                    for (MaintenanceConfig maintenanceConfig : maintenanceConfigs) {
                        deviceCodes.add(maintenanceConfig.getDeviceCode());
                    }
                    List<BaseDevicePoint> list1 = iBaseDevicePointService.list(new LambdaQueryWrapper<BaseDevicePoint>()
                            .eq(BaseDevicePoint::getBuildingId, appointmentApply.getBuildingId())
                            .in(BaseDevicePoint::getDevTypeCode, deviceCodes)
                            .ne(BaseDevicePoint::getPointType, "1"));
                    for (BaseDevicePoint baseDevicePoint : list1) {
                        baseDevicePointMap.put(baseDevicePoint.getId(), false);
                    }
                }
            } else {
                // 根据预约申请id查询维保范围
                log.info("根据预约申请id查询维保范围");
                QueryWrapper<AppointmentApScope> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("appointment_apply_id", appointmentApply.getId());
                List<AppointmentApScope> list = iAppointmentApScopeService.list(queryWrapper);
                for (int i = 0; i < list.size(); i++) {
                    AppointmentApScope appointmentApScope = list.get(i);
                    // 根据维保范围id查询
//                    log.info("根据维保范围id查询");
                    MaintenanceScopeTest maintenanceScopeTest = maintenanceScopeTestMapper.selectById(appointmentApScope.getMaintenanceScopeTestId());
                    if (maintenanceScopeTest != null) {
                        // 根据维保项id查询生成规则（楼层、回路、分区、批次）
                        String generationRule = maintenanceScopeTestMapper.getMaintenanceConfigByScopeTestId(maintenanceScopeTest.getId());
                        String batchCode = null;
                        switch (generationRule) {
                            case "1":
                                batchCode = "floor_id";
                                break;
                            case "2":
                                batchCode = "loop_code";
                                break;
                            case "3":
                                batchCode = "zone_name";
                                break;
                        }
                        List<String> scopeTestIdList = new ArrayList<>();
                        scopeTestIdList.add(maintenanceScopeTest.getId());
                        // 根据维保范围查询点位
//                        List<DevicePointInfoVo> devicePointInfoVoList = maintenanceScopeTestMapper.getWebDevicePointInfoByScopeTestId(scopeTestIdList, batchCode);
                        List<String> devicePointInfoVoList = maintenanceScopeTestMapper.getWebDevicePointIdByScopeTestId(scopeTestIdList, batchCode);
                        Map<String, Boolean> map = new HashMap<>();
                        log.info("维保申请通过，根据维保范围查询到的点位数据为：{},查询传参为：scopeTestIdList:{},batchCode:{}", devicePointInfoVoList, scopeTestIdList, batchCode);
                        devicePointInfoVoList.forEach(devicePointId -> {
                            map.put(devicePointId, false);
                            // 下面map是之前使用的 后面可以删除
                            baseDevicePointMap.put(devicePointId, false);
                        });
                        scopeDevicePointMap.put(maintenanceScopeTest.getId(), map);

                    }

                }
            }
        }
        weibaoDto.setBaseDevicePointMap(baseDevicePointMap);
        weibaoDto.setScopeDevicePointMap(scopeDevicePointMap);
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
