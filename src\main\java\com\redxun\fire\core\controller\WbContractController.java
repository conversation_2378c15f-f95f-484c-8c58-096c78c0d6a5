package com.redxun.fire.core.controller;


import cn.hutool.db.Page;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.fire.core.entity.WbContract;
import com.redxun.fire.core.pojo.dto.WbContractAddDto;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.utils.validated.SelectJudgeContract;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.maintenance.IWbContractService;
import com.redxun.fire.core.service.maintenance.IWbPlanFormulate;
import com.redxun.fire.core.service.maintenance.MaintenancePlanTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.ooxml.extractor.POIXMLTextExtractor;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维保合同表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-27
 */
@RestController
@RequestMapping("/wb-contract")
@Api(tags = "维保合同")
@Slf4j
public class WbContractController {

    @Autowired
    IWbContractService wbContractService;
    @Autowired
    DataDownloadController dataDownloadController;

    private String message;

    @Autowired
    MaintenancePlanTestService maintenancePlanTestService;
    @Autowired
    private IWbPlanFormulate wbPlanFormulate;

    /**
     * 合同列表
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/contractList")
    @ApiOperation("合同列表")
    public JsonPageResult addWbContract(HttpServletRequest request, @RequestBody QueryData queryData) {
        JsonPageResult jsonResult = JsonPageResult.getSuccess("返回数据成功!");
        jsonResult.setPageData(wbContractService.contractList(request, queryData));
        return jsonResult;
    }

    /**
     * 新增合同信息
     *
     * @param wbContractDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/addWbContract")
    @ApiOperation("新增合同信息")
    public JsonResult addWbContract(HttpServletRequest request, @RequestBody @Validated WbContractAddDto wbContractDto, BindingResult validResult) {
        JsonResult jsonResult = JsonResult.Success();
        if (validResult.hasErrors()) {
            jsonResult = JsonResult.Fail("编辑配置信息失败");
            List<ObjectError> allErrors = validResult.getAllErrors();
            StringBuilder sb = new StringBuilder();
            for (ObjectError error : allErrors) {
                if (error instanceof FieldError) {
                    sb.append("[" + ((FieldError) error).getField() + "]");
                }
                sb.append(error.getDefaultMessage());
                sb.append("\r\n");
            }
            jsonResult.setData(sb.toString());
            return jsonResult;
        }
        WbContract wbContract = new WbContract();
        BeanUtils.copyProperties(wbContractDto, wbContract);
        Map<String, Object> dataResult = wbContractService.addContractInfo(request, wbContract);
        jsonResult.setData(dataResult);
        try {
            if ((Boolean) dataResult.get("status")) {
                maintenancePlanTestService.createWbPlanAsync(request, wbContract);
            }
        } catch (Exception e) {
            log.error("新增维保合同异常:{}", e.getMessage());
        }
        return jsonResult;
    }

    /**
     * 生成维保计划
     *
     * @param
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/generateWbPlan/{id}")
    @ApiOperation("根据合同生成计划")
    public JsonResult generateWbPlan(@PathVariable String id) {
        JsonResult jsonResult = JsonResult.Success();
        try {
            wbPlanFormulate.generateWbPlan(wbContractService.getById(id));
        } catch (Exception e) {
            log.error("根据合同生成计划:{}", e.getMessage());
        }
        return jsonResult;
    }

    /**
     * 根据ID获取合同信息
     *
     * @param wbContract
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getContractInfoById")
    @ApiOperation("根据ID获取合同信息")
    public JsonResult getContractInfoById(@RequestBody @Validated(Select.class) WbContract wbContract) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(wbContractService.selectContractInfoById(wbContract));
        return jsonResult;
    }

    /**
     * 根据ID获取合同信息
     *
     * @param wbContract
     * @return
     */
    @ResponseBody
    @PostMapping("/update")
    @ApiOperation("编辑")
    public JsonResult update(HttpServletRequest request, @RequestBody @Validated(Update.class) WbContract wbContract) throws Exception {
        try {
            wbContractService.updateWbContract(request, wbContract);
            return JsonResult.Success();
        } catch (Exception e) {
            message = "修改失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    @GetMapping(value = "/delete/{ids}")
    public JsonResult delete(HttpServletRequest request, @PathVariable String[] ids) throws Exception {
        try {
            wbContractService.deleteWbContractById(request, ids);
            return JsonResult.Success();
        } catch (Exception e) {
            message = "删除失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }

    @GetMapping(value = "/relateInfo")
    @ApiOperation("获取关联信息")
    public JsonResult relateInfo(HttpServletRequest request) {

        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(wbContractService.getRelateInfo(request));
        return jsonResult;
    }

    @GetMapping(value = "/relateInfoNew")
    @ApiOperation("获取关联信息")
    public JsonResult relateInfoNew(HttpServletRequest request) {

        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(wbContractService.getRelateInfoNew(request));
        return jsonResult;
    }

    /**
     * 导出excel
     *
     * @return
     */
    @ResponseBody
    @MethodDefine(title = "导出EXCEL", path = "/export", method = HttpMethodConstants.GET,
            params = {@ParamDefine(title = "修改数据", varName = "wbContract")})
    @PostMapping("/export")
    public void export(@RequestBody WbContract wbContract, HttpServletRequest request, HttpServletResponse response) throws IOException {
/*        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response=attributes.getResponse();
        List<WbContract> result = wbContractService.findAllContract(wbContract);
        //导出操作
        ExcelUtil.exportExcel(result, null, "合同", WbContract.class, "user", response);
       */
        wbContractService.export(wbContract, request, response);
    }


    /**
     * 下载
     *
     * @param request
     * @param response
     * @throws FileNotFoundException
     */
    @RequestMapping("download")
    public void download(HttpServletRequest request, HttpServletResponse response) throws FileNotFoundException, UnsupportedEncodingException {
        String url = request.getParameter("downUrl");
        String fileName = request.getParameter("fileName");
        downloadLocal(response, url, fileName);
    }


    /**
     * 判断合同情况
     *
     * @param wbContract
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/judgeContract")
    @ApiOperation("判断合同情况")
    public JsonResult judgeContract(@RequestBody @Validated(SelectJudgeContract.class) WbContract wbContract) {
        JsonResult jsonResult = JsonResult.Success();
        int status = wbContractService.judgeContract(wbContract);
        jsonResult.setData(status);
        return jsonResult;
    }


    public void downloadLocal(HttpServletResponse response, String dataUrl, String fileName) throws FileNotFoundException, UnsupportedEncodingException {
        File file = new File(dataUrl);
        if (!file.exists()) {
            file.mkdirs();
        }
        // 下载本地文件
//        String fileName = file.getName(); // 文件的默认保存名

        // 读到流中
        InputStream inStream = new FileInputStream(file);// 文件的存放路径
        // 设置输出的格式
        response.reset();
        response.setContentType("bin");
        fileName = new String(fileName.getBytes("UTF-8"), StandardCharsets.ISO_8859_1);
        response.addHeader("Content-Disposition", String.format("attachment; filename=\"" + fileName + "\""));
        // 循环取出流中的数据
        byte[] b = new byte[100];
        int len;
        try {
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 生成合同
     *
     * @return
     */

    public void generateContract() {
        String modelPath = "D:\\wdjar\\消防维保合同.docx";
        //word 2007 图片不会被读取， 表格中的数据会被放在字符串的最后
        try {
            OPCPackage opcPackage = POIXMLDocument.openPackage(modelPath);
            POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
            String text2007 = extractor.getText();
            System.out.println(text2007);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }


    /**
     * 获取所有合同信息
     *
     * @param page
     * @return
     */
    public JsonResult getContractInfoAll(@RequestBody Page page) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(wbContractService.selectContractInfoAll(page));
        return jsonResult;
    }

    /**
     * 修改点位
     *
     * @return JsonResult
     */
    @GetMapping(value = "testScope")
    public JsonResult testScope(String buildingId, String effTime) throws Exception {
        try {
            log.info("修改维保范围2");
            maintenancePlanTestService.updateTestPlanRate(buildingId, effTime);
            return JsonResult.Success().setData(1);
        } catch (Exception e) {
            message = "获取失败";
            log.error(message, e);
            throw new Exception(message);
        }
    }


}
