package com.redxun.fire.core.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface WaterMonitorMapper extends BaseMapper<Map> {

    /**
     * 水压监测：获取设备总数，正常、异常总数，如果传值buildId，则查询单店设备统计情况
     *
     * @param params buildId
     * @return
     */
    List<Map> getWaterPressureDeviceData(@Param("params") Map params);

    /**
     * 水压检测：查询压力、设备异常排行榜数据
     *
     * @param params
     * @return
     */
    List<Map<String, Object>> getWaterPressureAbnormalList(@Param("param") Map param);

    /**
     * 水泵监测：获取设备总数，正常、异常总数，如果传值buildId，则查询单店设备统计情况
     *
     * @param param buildId
     * @return
     */
    Map<String, BigDecimal> getWaterLiquidDeviceData(@Param("param") Map param);

    /**
     * 水泵检测：查询压力、设备异常排行榜数据
     *
     * @param param
     * @return
     */
    List<Map<String, Object>> getWaterLiquidAbnormalList(@Param("param") Map<String, Object> param);

    /**
     * 查询压力异常统计数据
     *
     * @return
     */
    Map<String, Object> queryPressureStatisData();

    /**
     * 查询水泵异常统计数据
     *
     * @return
     */
    Map<String, Object> queryPumpStatisData();

    /**
     * 获取水压监测每日信息
     *
     * @param paramsMap
     * @return
     */
    Map<String, Object> queryWaterPressureInfoEveryDay(Map<String, Object> paramsMap);

    /**
     * 获取水泵监测每日信息
     *
     * @param paramsMap
     * @return
     */
    Map<String, Object> queryPumpInfoEveryDay(Map<String, Object> paramsMap);

    /**
     * 查询水压设备异常总数
     *
     * @param params
     * @return
     */
    Map<String, BigDecimal> queryWaterPressureAbnormalPoint(@Param("params") Map params);

    /**
     * 查询水泵设备异常总数
     *
     * @param params
     * @return
     */
    Map<String, BigDecimal> queryWaterPumpAbnormalPoint(@Param("params") Map params);
}
