package com.redxun.fire.core.controller;


import com.gexin.fastjson.JSON;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.StatPumpExpection;
import com.redxun.fire.core.mapper.StatPumpExpectionMapper;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.service.alarm.impl.WaterAbnormalServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/pumpApp")
public class PumpController {

    @Resource
    private WaterAbnormalServiceImpl waterAbnormalService;
    @Resource
    private StatPumpExpectionMapper statPumpExpectionMapper;

    /**
     * 获取水泵异常列表
     *
     * @param buildingId
     * @param exceptionStatus
     * @param pageIndex
     * @param pageRows
     * @return
     * @throws ParseException
     */
    @GetMapping("/getPumpException")
    public ResultMsg getPumpExpection(@RequestParam(value = "buildingId") String buildingId,
                                      @RequestParam(value = "exceptionStatus") String exceptionStatus,
                                      @RequestParam(value = "pageIndex") String pageIndex,
                                      @RequestParam(value = "pageRows") String pageRows) throws ParseException {
        return waterAbnormalService.getPumpException(buildingId, exceptionStatus, pageIndex, pageRows);
    }

    /**
     * 获取水泵列表
     *
     * @param buildingId
     * @param pageIndex
     * @param pageRows
     * @return
     */
    @GetMapping("/getPumpList")
    public ResultMsg getPumpList(@RequestParam(value = "buildingId") String buildingId,
                                 @RequestParam(value = "pageIndex") String pageIndex,
                                 @RequestParam(value = "pageRows") String pageRows) {
        return waterAbnormalService.getPumpList(buildingId, pageIndex, pageRows);
    }

    /**
     * 获取水泵操作记录
     *
     * @param pointId
     * @param pageIndex
     * @param pageRows
     * @return
     */
    @GetMapping("/getOpsRecords")
    public ResultMsg getOpsRecords(@RequestParam("pointId") String pointId,
                                   @RequestParam("pageIndex") String pageIndex,
                                   @RequestParam("pageRows") String pageRows
    ) {
        return waterAbnormalService.getOpsRecords(pointId, Integer.parseInt(pageIndex), Integer.parseInt(pageRows));
    }

//    @GetMapping("/addTest")
//    public void test(@RequestParam(value = "pointId")String pointId,
//                     @RequestParam(value = "num")String num){
//        waterAbnormalService.test(pointId,num);
//    }

    //    @GetMapping("/test")
//    public void test(){
//        waterAbnormalService.get();
//    }
    @ResponseBody
    @PostMapping(value = "/waterPumpPassApply")
    @ApiOperation("水泵审批通过")
    public JsonResult waterPumpPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("水泵审批通过，ID为：{}", JSON.toJSONString(jsonObject));
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        Date approveTime = new Date();
        try {
            StatPumpExpection statPumpExpection = statPumpExpectionMapper.selectById(data);
            if (statPumpExpection != null) {
                statPumpExpection.setApproveStatus("1");
                String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                statPumpExpection.setApproveTime(time);
                statPumpExpectionMapper.updateById(statPumpExpection);
            }
        } catch (Exception e) {
            log.info("---------------->>>>>>>>>>>>>水泵审批通过异常" + JSON.toJSONString(jsonObject));
        }

        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/waterPumpNoPassApply")
    @ApiOperation("水泵审批未通过")
    public JsonResult waterPumpNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("水泵审批未通过，ID为：{}", JSON.toJSONString(jsonObject));
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        try {
            StatPumpExpection statPumpExpection = statPumpExpectionMapper.selectById(data);
            if (statPumpExpection != null) {
                String time = DateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
                statPumpExpection.setEndTime(time);
                statPumpExpection.setApproveStatus("2");
                statPumpExpection.setApproveTime(time);
                statPumpExpectionMapper.updateById(statPumpExpection);
            }
        } catch (Exception e) {
            log.info("---------------->>>>>>>>>>>>>水泵审批未通过异常" + JSON.toJSONString(jsonObject));
        }
        return jsonResult;
    }
}





