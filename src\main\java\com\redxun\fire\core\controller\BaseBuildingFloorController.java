
package com.redxun.fire.core.controller;

import cn.hutool.core.util.StrUtil;
import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.pojo.dto.AddSandWichDto;
import com.redxun.fire.core.pojo.vo.BaseBuildingFloorDictVo;
import com.redxun.fire.core.pojo.vo.FloorInfoReturnVo;
import com.redxun.fire.core.pojo.vo.SelectedReturnPointsVo;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/base-building-floor")
@Api(tags = "建筑物楼层信息表")
@ClassDefine(title = "建筑物楼层信息表", alias = "BaseBuildingFloorController", path = "/base-building-floor", packages = "core", packageName = "子系统名称")
public class BaseBuildingFloorController {

//    @Autowired
//    private IFileService fileService;

    private final IBaseBuildingFloorService baseBuildingFloorService;

    public BaseBuildingFloorController(IBaseBuildingFloorService baseBuildingFloorService) {
        this.baseBuildingFloorService = baseBuildingFloorService;
    }

    @PostMapping("/editFloorName")
    public JsonResult editFloorName(@RequestBody BaseBuildingFloor baseBuildingFloor) {
        if (StringUtils.isBlank(baseBuildingFloor.getId()) || StringUtils.isBlank(baseBuildingFloor.getFloorName())) {
            throw new RuntimeException("编辑楼层时未传楼层id或楼层名称！！！");
        }
        if (StringUtils.isBlank(baseBuildingFloor.getBuildingId())) {
            throw new RuntimeException("编辑楼层时未传建筑物id！！！");
        }
        return baseBuildingFloorService.editFloorName(baseBuildingFloor);
    }

    /**
     * 通过建筑物来进行楼层信息的获取 如果是当前用户 那么buildingId 传入-1
     *
     * @param buildId 建筑物id
     * @return {@link JsonResult}
     */
    @GetMapping("/getFloors/{buildId}")
    public JsonResult getBuildingFloors(HttpServletRequest request, @PathVariable("buildId") String buildId) {
        JsonResult<List<FloorInfoReturnVo>> result = JsonResult.Success();
        List<FloorInfoReturnVo> list = baseBuildingFloorService.getBuildingFloors(request, buildId);
        result.setData(list);
        //baseDeviceTemporaryService.setPointRedis(buildId);
        return result;
    }

    /**
     * 通过建筑物来进行楼层信息的获取 如果是当前用户 那么buildingId 传入-1
     *
     * @param buildId 建筑物id
     * @return {@link JsonResult}
     */
    @GetMapping("/getFloorsByBuildId")
    public JsonResult getFloorsByBuildId(@RequestParam("buildId") String buildId) {
        JsonResult<List<BaseBuildingFloorDictVo>> result = JsonResult.Success();
        if (StrUtil.isBlank(buildId)) {
            result = JsonResult.getFailResult("入参不规范");
            return result;
        }
        List<BaseBuildingFloorDictVo> list = baseBuildingFloorService.getFloorsByBuildId(buildId);
        result.setData(list);
        return result;
    }

    /**
     * 通过夹层id来进行楼层的删除
     *
     * @param floorId 夹层id
     * @return {@link JsonResult}
     */
    @DeleteMapping("/getFloors/{floorId}")
    public JsonResult deleteBuildingFloor(@PathVariable("floorId") String floorId) {
        JsonResult<String> result = baseBuildingFloorService.deleteBuildingFloor(floorId);
        return result;
    }

    /**
     * 通过建筑物来进行楼层信息的获取 如果是当前用户 那么buildingId 传入-1
     *
     * @param buildId 建筑物id
     * @return {@link JsonResult}
     */
    @GetMapping("/getBuildingFloorsByBuildId/{buildId}")
    public JsonResult getBuildingFloorsByBuildId(@PathVariable("buildId") String buildId) {
        JsonResult result = JsonResult.Success();
        List<Map<String, Object>> list = baseBuildingFloorService.getBuildingFloorsByBuildId(buildId);
        result.setData(list);
        return result;
    }


    /**
     * 通过建筑物来进行物理楼层的获取 返回字符串列表
     *
     * @param buildId 建筑物id
     * @return {@link JsonResult}
     */
    @GetMapping("/getBuildingFloorNums/{buildId}")
    public JsonResult getBuildingFloorNums(HttpServletRequest request, @PathVariable("buildId") String buildId) {
        JsonResult<List<FloorInfoReturnVo>> result = JsonResult.Success();
        List<FloorInfoReturnVo> list = baseBuildingFloorService.getBuildingFloors(request, buildId);
        result.setData(list);
        return result;
    }

    /**
     * 通过楼层id来进行楼层图片和已选择点位获取 数据方式和万达一期一致
     *
     * @param floorId 楼层id
     * @return {@link JsonResult}
     */
    @GetMapping("/getFloorAndPoints/{floorId}")
    public JsonResult getFloorAndPoints(@PathVariable("floorId") String floorId, @RequestHeader(value = "xfUserId", required = false) String xfUserId) {
        JsonResult<SelectedReturnPointsVo> result = JsonResult.Success();
        SelectedReturnPointsVo selectedReturnPoints = baseBuildingFloorService.getFloorAndPoints(floorId);
        result.setData(selectedReturnPoints);
        log.info(xfUserId);
        return result;
    }

    /**
     * 编辑楼层图片信息
     *
     * @param baseBuildingFloor 楼层编辑信息
     * @return {@link JsonResult}
     */
    @PostMapping("/editFloorPic")
    public JsonResult<String> editFloorPic(HttpServletRequest request, @RequestBody BaseBuildingFloor baseBuildingFloor) {
        JsonResult<String> result = JsonResult.Success();
        baseBuildingFloorService.editFloorPic(request, baseBuildingFloor);
        result.setData("edit picture success");
        return result;
    }

    /**
     * 根据楼层id来进行楼层图片信息的删除
     *
     * @param floorId 楼层id
     * @return {@link JsonResult}
     */
    @GetMapping("/deleteFloorPic/{floorId}")
    public JsonResult deletePic(HttpServletRequest request, @PathVariable("floorId") String floorId) {
        JsonResult result = JsonResult.Success();
        baseBuildingFloorService.deleteFloorPic(request, floorId);
        return result;
    }

//    /**
//     * 上传楼层图片
//     *
//     * @param uploadFile 楼层图片文件
//     * @return {@link JsonResult}
//     */
//    @PostMapping("/uploadPic")
//    public JsonResult<String> uploadStatic(@RequestPart("file") MultipartFile uploadFile) {
//        JsonResult result = JsonResult.Success();
//        return fileService.saveFile("floorPic", uploadFile);
////        result.setData(filePath);
////        return result;
//    }


    /**
     * 新增夹层
     *
     * @param addSandWichDto
     * @return
     */
    @PostMapping("/addSandwich")
    public JsonResult<String> addSandwich(HttpServletRequest request, @RequestBody @Validated AddSandWichDto addSandWichDto) {
        JsonResult result = JsonResult.Success();
        baseBuildingFloorService.addSandwich(request, addSandWichDto);
        result.setData("新增夹层成功");
        return result;
    }


    /**
     * 删除夹层
     *
     * @param sandwichId 夹层id
     * @return
     */
    @GetMapping("/deleteSandwich/{sandwichId}")
    public JsonResult<String> deleteSandwich(HttpServletRequest request, @PathVariable("sandwichId") String sandwichId) {
        JsonResult result = JsonResult.Success();
        baseBuildingFloorService.deleteSandwich(request, sandwichId);
        result.setData("删除成功");
        return result;
    }

}

