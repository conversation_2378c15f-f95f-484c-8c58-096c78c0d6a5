package com.redxun.fire.core.controller;

import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.FireInfo;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.ConfigRelationMapper;
import com.redxun.fire.core.mapper.FireInfoMapper;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.dto.RouteCheckSendDto;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.service.other.ITimeTaskService;
import com.redxun.fire.core.consts.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 火警，水压，水泵定时任务
 */
@RestController
@RequestMapping("/time-task")
public class TimeTaskController {

    @Resource
    private ITimeTaskService timeTaskServiceImpl;

    @Resource
    private BaseBuildingMapper baseBuildingMapper;

    @Resource
    private FireInfoMapper fireInfoMapper;

    @Resource
    private ConfigRelationMapper configRelationMapper;

    @Autowired
    DropDownServiceImpl dropDownService;

    /**
     * 火警超时推送接警中心定时
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/fireTimeTask", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg fireTimeTask(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.fireOutTime(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 火警超时推送接警中心定时
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/warningFireTimeTask", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg warningFireTimeTask(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.warningFireOutTime(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水压超时推送接警中心定时
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/waterTimeTask", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg waterTimeTask(@RequestBody Map<String, Object> param) {
//        timeTaskServiceImpl.waterOutTime(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水泵超时推送接警中心定时
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/pumpTimeTask", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg pumpTimeTask(@RequestBody Map<String, Object> param) {
//        timeTaskServiceImpl.pumpOutTime(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 远传主机超时推送接警中心定时
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/remoteHostOutTime", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg remoteHostOutTime(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.remoteHostOutTime(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 确认火警定时短信(测试用)
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/affirmFireShortNote", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg affirmFireShortNote() {
        FireInfo fireInfo = fireInfoMapper.selectById("090a5c982ea14cf09180589471c95f26");
        BaseBuilding baseBuilding = baseBuildingMapper.selectById("151");
//        List<Map<String, Object>> configRelations = configRelationMapper.selectByConfigCode("dxtz_0_txgn");
        List<Map<String, Object>> configRelations = dropDownService.queryConfigMapByCode("dxtz_0_txgn");
        timeTaskServiceImpl.affirmFireShortNote(configRelations.get(1), "090a5c982ea14cf09180589471c95f26", baseBuilding);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 超时火警定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/fireOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg fireOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.fireOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 超时预警定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/warningOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg warningOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.warningOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 故障超时定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/faultOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg faultOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.faultOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水压超时定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/waterOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg waterOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.waterOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 闭店设备超时定时短信
     * @param param
     * @return
     */
    @RequestMapping(value = "/closeStoreOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg CloseStoreOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.CloseStoreOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 上海项目推送闭店异常短信
     */
    @RequestMapping(value = "/closeStoreException", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg closeStoreException(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.specialHandleBuildingCloseException(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水泵超时定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/pumpOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg pumpOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.pumpOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 压力联动水泵超时定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/pressureLinkPumpOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg pressureLinkPumpOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.pressureLinkPumpOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 远传设备超时定时短信
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/hostOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg hostOutTimeShortNote(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.hostOutTimeShortNote(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水压安全认证
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/waterSafetyPush", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg waterSafetyPush(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.waterSafetyPush(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水泵安全认证
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/pumpSafetyPush", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg pumpSafetyPush(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.pumpSafetyPush(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 水泵安全认证
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/hostSafetyPush", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg hostSafetyPush(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.hostSafetyPush(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 故障维修复安全认证
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/faultSafetyPush", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg faultSafetyPush(@RequestBody Map<String, Object> param) {
        timeTaskServiceImpl.faultSafetyPush(param);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 检线设备异常超时短信通知
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/routeCheckOutTimeShortNote", method = RequestMethod.POST)
    @ResponseBody
    public ResultMsg routeCheckOutTimeShortNote(@RequestBody RouteCheckSendDto dto) {
        timeTaskServiceImpl.routeCheckOutTimeShortNote(dto);
        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

//    /**
//     * 检线设备异常频次短信通知
//     *
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/routeCheckFrequencyShortNote", method = RequestMethod.POST)
//    @ResponseBody
//    public ResultMsg routeCheckFrequencyShortNote(@RequestBody Map<String, Object> param) {
//        timeTaskServiceImpl.routeCheckFrequencyShortNote(param);
//        return ResultMsg.getResultMsg("操作成功", Constant.RESPONSE_STATUS_CODE_SUCCESS);
//    }
}
