package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.MaintenanceConfig;
import com.redxun.fire.core.pojo.vo.MaintenancePlanSysVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维保项配置表（字典表） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-08
 */
public interface MaintenanceConfigMapper extends BaseMapper<MaintenanceConfig> {

    /**
     * 查询维保计划规则
     * @param maintenanceConfig
     * @return
     */
    public List<MaintenanceConfig> queryRule(MaintenanceConfig maintenanceConfig);



    /**
     * 查询检查计划规则没有设备类型
     * @param params
     * @return
     */
    List<MaintenanceConfig> queryRuleByContentNotDevType(Map<String,Object> params);


    List<MaintenanceConfig> getMaintenanceByPlanId(@Param("planId") String planId);

    List<MaintenanceConfig> getMaintenanceDeviceTypeByPlanId(@Param("planId") String planId);

    List<MaintenanceConfig> getMaintenanceConfigByPlanId(@Param("planId") String planId, @Param("fireproofSysId") String fireproofSysId);

    List<MaintenancePlanSysVo> getMaintenanceSysByParam(@Param("planId")String planId, @Param("fireproofSysId") String fireproofSysId);

    List<MaintenancePlanSysVo> getMaintenanceSysByPlanId(@Param("planId") String id,@Param("effectiveTime") String effectiveTime);

    MaintenanceConfig getMaintenanceConfigByScopeTestId(@Param("scopeTestId") String scopeTestId);

    List<MaintenancePlanSysVo> getTestFireproofSysName(@Param("appointmentApplyId") String appointmentApplyId);

    String getFireproofSysName(@Param("fireproofSysId") String fireproofSysId);

    IPage<MaintenancePlanSysVo> getMaintenanceSysByParamConfigPage(@Param("buildingId") String buildingId,
                                                                   @Param("startTime") Date startTime,
                                                                   @Param("endTime") Date endTime,
                                                                   Page page);

    List<MaintenancePlanSysVo> getMaintenanceSysByParamConfig(@Param("buildingId") String buildingId,
                                                              @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime);
}

