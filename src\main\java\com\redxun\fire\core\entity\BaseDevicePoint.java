
/**
 * <pre>
 *
 * 描述：建筑物设备资产信息表实体类定义
 * 表:base_device_point
 * 作者：gl
 * 邮箱:
 * 日期:2024-02-29 18:17:19
 * 版权：万达
 * </pre>
 */
package com.redxun.fire.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.common.base.entity.BaseExtEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;


@Setter
@Getter
@Accessors(chain = true)
@TableName(value = "base_device_point")
public class BaseDevicePoint extends BaseExtEntity<java.lang.String> {

    @JsonCreator
    public BaseDevicePoint() {
    }

    //主键
    @TableId(value = "id", type = IdType.UUID)
    private String id;

    //所属报警阀
    @TableField(value = "alarm_valve")
    private String alarmValve;
    //申请人
    @TableField(value = "apply_user")
    private String applyUser;
    //审批时间
    @TableField(value = "approval_time")
    private java.util.Date approvalTime;
    //该点位最近一次审批时间
    @TableField(value = "approve_time")
    private java.util.Date approveTime;
    //批次号
    @TableField(value = "batch_id")
    private String batchId;
    //运营商品牌名称
    @TableField(value = "brand_name")
    private String brandName;
    //建筑物名称
    @TableField(value = "build_name")
    private String buildName;
    //建筑物id
    @TableField(value = "building_id")
    private String buildingId;
    //设备id
    @TableField(value = "dev_id")
    private String devId;
    //设备类型分类0 消防主机  1 非消防主机
    @TableField(value = "dev_type_cat")
    private String devTypeCat;
    //设备类型编号
    @TableField(value = "dev_type_code")
    private String devTypeCode;
    //设备类型名称
    @TableField(value = "dev_type_name")
    private String devTypeName;
    //设备名称
    @TableField(value = "device_name")
    private String deviceName;
    //设备编号
    @TableField(value = "device_no")
    private String deviceNo;
    //部件地址id
    @TableField(value = "did")
    private String did;
    //DTU编号
    @TableField(value = "dtu_no")
    private String dtuNo;
    //流量卡到期时间
    @TableField(value = "expired_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date expiredTime;
    //消防系统编号
    @TableField(value = "fas_code")
    private String fasCode;
    //消防系统名称
    @TableField(value = "fas_name")
    private String fasName;
    //故障状态
    @TableField(value = "fault_status")
    private String faultStatus;
    //物理楼层(-1 2 3 4A-1)
    @TableField(value = "floor")
    private Integer floor;
    //楼层位置
    @TableField(value = "floor_addr")
    private String floorAddr;
    //所在楼层
    @TableField(value = "floor_id")
    private String floorId;
    //楼层名称(B1 B2 F2)
    @TableField(value = "floor_name")
    private String floorName;
    //楼层号
    @TableField(value = "floor_no")
    private Integer floorNo;
    //是否补全
    @TableField(value = "has_completion")
    private String hasCompletion;
    //0 未描点  1 已描点
    @TableField(value = "has_tracing")
    private String hasTracing;
    //主机id
    @TableField(value = "host_id")
    private String hostId;
    //主机号
    @TableField(value = "host_num")
    private String hostNum;
    //接入点
    @TableField(value = "input_point")
    private String inputPoint;
    //接入点编号
    @TableField(value = "join_code")
    private String joinCode;
    //接入点名称
    @TableField(value = "join_name")
    private String joinName;
    //本地实际主机号
    @TableField(value = "local_host_num")
    private String localHostNum;
    //本地实际回路号
    @TableField(value = "local_loop_code")
    private String localLoopCode;
    //回路号
    @TableField(value = "loop_code")
    private String loopCode;
    //最大阈值
    @TableField(value = "maxval")
    private BigDecimal maxval;
    //商户id
    @TableField(value = "mid_merchant_id")
    private String midMerchantId;
    //最小阀值
    @TableField(value = "minval")
    private BigDecimal minval;
    //平面图id
    @TableField(value = "pic_id")
    private String picId;
    //硬件编号
    @TableField(value = "pid")
    private String pid;
    //点位号
    @TableField(value = "point_code")
    private String pointCode;
    //点位描述
    @TableField(value = "point_desc")
    private String pointDesc;
    //点位名称
    @TableField(value = "point_name")
    private String pointName;
    //点位编号
    @TableField(value = "point_number")
    private String pointNumber;
    //0 非误报点位  1 误报点位
    @TableField(value = "point_repeate")
    private String pointRepeate;
    //是否屏蔽点位 0 正常 1 屏蔽
    @TableField(value = "point_shield")
    private String pointShield;
    //点位状态，0 - 正常 1 - 故障
    @TableField(value = "point_status")
    private String pointStatus;
    //点位类型 (0 正常点位  1 特批点位)
    @TableField(value = "point_type")
    private String pointType;
    //点位类型名称
    @TableField(value = "point_type_name")
    private String pointTypeName;
    //图片坐标x
    @TableField(value = "point_x")
    private BigDecimal pointX;
    //图片坐标y
    @TableField(value = "point_y")
    private BigDecimal pointY;
    //位置描述
    @TableField(value = "position_desc")
    private String positionDesc;
    //点位管理id 和base_device_application的主键相关联
    @TableField(value = "pt_id")
    private String ptId;
    //备注
    @TableField(value = "remark")
    private String remark;
    //上报时间
    @TableField(value = "reported_time")
    private java.util.Date reportedTime;
    //空间id
    @TableField(value = "room_id")
    private String roomId;
    //房间号
    @TableField(value = "room_no")
    private String roomNo;
    //传感器设备号
    @TableField(value = "sensor_num")
    private String sensorNum;
    //signals
    @TableField(value = "signals")
    private String signals;
    //流量卡号
    @TableField(value = "sim_num")
    private String simNum;
    //来源
    @TableField(value = "source")
    private String source;
    //设备分类(0 报警主机  1 水泵 2 压力表 3 水流表 16闭店监测设备 20 电弧【20以后为第三方拓展】) 
    @TableField(value = "super_type")
    private String superType;
    //tall
    @TableField(value = "tall")
    private Float tall;
    //用户传输装置编号
    @TableField(value = "transmission_number")
    private String transmissionNumber;
    //设备厂商编号
    @TableField(value = "vender_code")
    private String venderCode;
    //设备厂商名称
    @TableField(value = "vender_name")
    private String venderName;
    //voltage
    @TableField(value = "voltage")
    private String voltage;
    //wide
    @TableField(value = "wide")
    private Float wide;
    //防火分区id
    @TableField(value = "zone_id")
    private String zoneId;
    //防火分区名称
    @TableField(value = "zone_name")
    private String zoneName;

    @TableField(value = "wzt_building")
    private String wztBuilding;
    @TableField(value = "wzt_building_name")
    private String wztBuildingName;
    @TableField(value = "wzt_floor_id")
    private String wztFloorId;
    @TableField(value = "wzt_floor_name")
    private String wztFloorName;
    @TableField(value = "wzt_snap_id")
    private String wztSnapId;
    @TableField(value = "wzt_snap_name")
    private String wztSnapName;
    @TableField(value = "wzt_compartment_id")
    private String wztCompartmentId;
    @TableField(value = "wzt_compartment_name")
    private String wztCompartmentName;
    @TableField(value = "wzt_point_id")
    private String wztPointId;

    @TableField(exist = false)
    private String syncType;

    //图片坐标x
    @TableField(value = "bird_point_x")
    private BigDecimal birdPointX;
    //图片坐标y
    @TableField(value = "bird_point_y")
    private BigDecimal birdPointY;

    @TableField(value = "bird_point_z")
    private BigDecimal birdPointZ;

    @Override
    public String getPkId() {
        return id;
    }

    @Override
    public void setPkId(String pkId) {
        this.id = pkId;
    }

    /**
     生成子表属性的Array List
     */

}



