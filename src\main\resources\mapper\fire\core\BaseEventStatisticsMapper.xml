<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.BaseEventStatisticsMapper">


    <!--查询所有数据-->
    <select id="queryAll" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * FROM base_event_statistics
    </select>



    <select id="queryDangerInfo" resultType="com.redxun.fire.core.pojo.vo.DangerInfoVo">
        SELECT bes.id,event_id,security_duty_name,security_duty_level_name,di.building_name,bes.tel as ctrlPhone
        FROM base_event_statistics bes INNER JOIN danger_info di ON bes.event_id = di.id
        WHERE bes.event_type in ('10','11')
        <if test="type !=null and type !=''">
            AND bes.type = #{type}
        </if>
        <if test="buildingName !=null and buildingName !=''">
            AND di.building_name like concat('%', #{buildingName},'%')
        </if>
        <if test="securityDutyName !=null and securityDutyName !=''">
            AND di.security_duty_name like concat('%', #{securityDutyName},'%')
        </if>
        <if test="securityDutyLevelName !=null and securityDutyLevelName !=''">
            AND di.security_duty_level_name like concat('%', #{securityDutyLevelName},'%')
        </if>
        <if test="eventType !=null and eventType !=''">
            AND bes.event_type  = #{eventType}
        </if>
    </select>


    <!--查询事件类型-->
    <select id="queryEventType" parameterType="String" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT event_type, event_type_str, COUNT(*) eventCount FROM `base_event_statistics`
        WHERE jj_type = #{jjType} and `type` = '0'
        GROUP BY event_type
    </select>
    <!--   /*     	SELECT a.*,
       (SELECT COUNT(1) from receiving_point b WHERE a.event_type = 0  AND a.event_id = b.event_id ) AS pointCount
       FROM `base_event_statistics`  a
       WHERE a.jj_type = #{baseEventStatistics.jjType} AND (a.event_type = 0 OR a.event_type = 1 OR a.event_type = 2)*/-->
    <!--查询火警类型-->
    <select id="queryFireAlarmType" parameterType="String" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
    select a.*, b.point_type as bp,b.point_describe,
    COUNT(CASE WHEN a.event_type = '1' THEN b.id END) AS pointCount
        from base_event_statistics a
            LEFT JOIN receiving_point b ON a.id=b.receiving_id
                WHERE a.jj_type = #{jjType} AND (a.event_type = 0 OR a.event_type = 1 OR a.event_type = 2) AND `type` = 0
                    GROUP BY a.id,b.point_type
                        ORDER BY a.event_type
    </select>

    <select id="queryFireAlarmTypeNew" parameterType="String" resultType="com.redxun.fire.core.entity.BaseEventStatisticsVo">
        SELECT
            a.build_name,
            a.event_type_str,
            a.event_type,
            a.event_id,
            a.id,
            a.point_code,
            a.point_desc,
            b.point_describe,
            COUNT( CASE WHEN a.event_type = '1' THEN b.id END ) AS pointCount
        FROM
            base_event_statistics a
                LEFT JOIN receiving_point b ON a.id = b.receiving_id
        WHERE
            a.jj_type = #{jjType}
          AND a.event_type IN ('0','1','2')
          AND a.`type` = '0'
        GROUP BY
            a.id,
            b.point_type
        ORDER BY
            a.event_type
    </select>

    <!--根据建筑id查询数据-->
    <select id="queryDataByBuildId" parameterType="com.redxun.fire.core.entity.BaseEventStatistics"
            resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * FROM `base_event_statistics`
        WHERE jj_type = #{baseEventStatistics.jjType} AND `build_id` = #{baseEventStatistics.buildId}
    </select>

    <!--根据建筑id查询数据-->
    <select id="queryDataById" parameterType="com.redxun.fire.core.entity.BaseEventStatisticsList"
            resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * FROM `base_event_statistics`
        WHERE `id` IN
        <foreach collection="baseEventStatisticsList" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </select>

    <!--根据建筑id查询数据-->
    <select id="queryById" parameterType="String" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * FROM `base_event_statistics`
        WHERE `id` = #{id}
    </select>
    <!--批量修改数据-->
    <update id="updateData" parameterType="com.redxun.fire.core.entity.BaseEventStatistics">
        UPDATE `base_event_statistics` SET
        `attach` = #{attach},
        `situation` = #{situation},
        `accept_type` = #{acceptType},
        `accept_name` = #{acceptName},
        `cause_name` = #{causeName},
        `cause_type` = #{causeType},
        `type` = #{type},
        `UPDATE_BY_` = #{updateBy},
        process_result = #{processResult},
        process_result_str = #{processResultStr},
        WHERE `id` = #{id}
    </update>
    <!--       WHERE `id` IN
           <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
               #{item}
           </foreach>-->
    <!--根据事件类型id查询点位信息-->
    <select id="queryPointByEventTypeId" parameterType="com.redxun.fire.core.entity.BaseEventStatisticsList"
            resultType="com.redxun.fire.core.entity.ReceivingPoint">
        SELECT b.* from base_event_statistics AS a
        INNER JOIN receiving_point AS b ON a.id = b.receiving_id
        WHERE
        <foreach collection="baseEventStatisticsList" index="index" item="item" open="(" separator=")||(" close=")">
            b.receiving_id = #{item.id}
            <if test="item.bp != null and item.bp != ''">
                AND b.point_type = #{item.bp}
            </if>
        </foreach>
        <!--   and b.point_type IN
           <foreach collection="baseEventStatisticsList" index="index" item="item" open="(" separator="," close=")">
               #{item.bp}
           </foreach>-->
    </select>

<!--    <select id="queryById" parameterType="string" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * from base_event_statistics
            WHERE id =  #{id}
    </select>-->

    <select id="selectByType" parameterType="com.redxun.fire.core.entity.BaseEventStatistics"
            resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT * from base_event_statistics
            WHERE jj_type =  #{jjType} and event_type=#{eventType} and type = 0
    </select>

    <select id="selectBaseEventStatisticsToExport" resultType="java.util.Map">
        SELECT s.id,
        s.build_id,
        s.build_name,
        s.event_type,
        s.event_type_str,
        s.event_id,
        DATE_FORMAT(s.report_time, '%Y-%m-%d %H:%i:%s') as report_time,
        s.point_id,
        s.point_code,
        s.point_desc,
        s.point_type,
        s.point_type_str,
        s.process_result,
        s.process_result_str,
        s.attach,
        s.situation,
        s.tel,
        s.type,
        s.jj_type,
        s.filePath,
        s.accept_type,
        s.accept_name,
        s.cause_type,
        s.cause_name,
        s.zone_id,
        s.zone_name,
        s.receiver,
        s.belong_dep,
        s.point_describe11111,
        p.dev_type_name,
        concat(
        case s.situation when null then '' when '' then '' else concat('情况描述：' ,s.situation,";") end,
        case s.cause_name when null then '' when '' then '' else concat('误报原因：' ,s.cause_name) end
        )  result,
        DATE_FORMAT(s.UPDATE_TIME_, '%Y-%m-%d %H:%i:%s') as UPDATE_TIME_
        FROM base_event_statistics s
        LEFT JOIN base_device_point p
        on s.point_id = p.id
        WHERE 1=1
        <if test="param.jjType !=null and param.jjType !=''">
            and s.jj_type = #{param.jjType}
        </if>
        <if test="param.type !=null and param.type !=''">
            and s.type = #{param.type}
        </if>
        <if test="param.eventType !=null and param.eventType !=''">
            and s.event_type = #{param.eventType}
        </if>
        <if test="param.eventType !=null and param.eventType !=''">
            and s.event_type = #{param.eventType}
        </if>
        <if test="param.buildId !=null and param.buildId !=''">
            and s.build_id = #{param.buildId}
        </if>
        <if test="param.eventType !=null and param.eventType !=''">
            and s.event_type = #{param.eventType}
        </if>
        <if test="param.reportStartTime !=null and param.reportStartTime !=''">
            and s.report_time <![CDATA[>=]]> #{param.reportStartTime}
        </if>
        <if test="param.reportEndTime !=null and param.reportEndTime !=''">
            and s.report_time <![CDATA[<=]]> #{param.reportEndTime}
        </if>
        <if test="param.pointCode !=null and param.pointCode !=''">
            and s.point_code = #{param.pointCode}
        </if>
        <if test="param.processResult !=null and param.processResult !=''">
            and s.process_result = #{param.processResult}
        </if>
        <if test="param.pointType !=null and param.pointType !=''">
            and s.point_type = #{param.pointType}
        </if>
        <if test="param.tel !=null and param.tel !=''">
            and s.tel = #{param.tel}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and s.build_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        ORDER BY s.CREATE_TIME_ desc, s.build_name desc, s.point_type_str desc

    </select>

    <select id="selectEventIdById" resultType="java.util.Map" parameterType="java.lang.String">
        select
            point_code as pointCode,
            event_type as eventType,
            event_type_str as eventTypeStr
        from  base_event_statistics
        where id = #{id}
    </select>


    <select id="selectAccumulatedFireCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT count(1) FROM `base_event_statistics` bes
        WHERE  type = '2' and jj_type = '1' and event_type in ('1','2')
          and report_time <![CDATA[>=]]> #{date}
    </select>

    <select id="selectCloseStoreEventIdAndEventTypeAndTypeAndPointTypeStr" resultType="com.redxun.fire.core.entity.BaseEventStatistics"  parameterType="java.lang.String">
        SELECT * FROM base_event_statistics
        WHERE event_type = '3' AND event_id = #{eventId} AND point_type_str = '闭店监测设备' AND type != '2'
    </select>

    <select id="queryPage" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT *,date_format(report_time, '%Y-%m-%d %H:%i:%s') AS reportTimeStr FROM base_event_statistics
        WHERE 1=1
        <if test="params.jjType !=null and params.jjType !=''">
            and jj_type = #{params.jjType}
        </if>
        <if test="params.eventType !=null and params.eventType !=''">
            and event_type = #{params.eventType}
        </if>
        <if test="params.eventTypeStr !=null and params.eventTypeStr !=''">
            and event_type_str = #{params.eventTypeStr}
        </if>
        <if test="params.buildingId !=null and params.buildingId !=''">
            and build_id = #{params.buildingId}
        </if>
        <if test="params.inspectType !=null and params.inspectType !=''">
            and face_inspect_type = #{params.inspectType}
        </if>
        <if test="params.beginTime !=null and params.beginTime !=''">
            and report_time <![CDATA[>=]]> #{params.beginTime}
        </if>
        <if test="params.endTime !=null and params.endTime !=''">
            and report_time <![CDATA[<=]]> #{params.endTime}
        </if>
        order by report_time desc
    </select>
    <select id="selectById" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        SELECT *,date_format(report_time, '%Y-%m-%d %H:%i:%s') AS reportTimeStr FROM base_event_statistics
        WHERE id = #{id}
    </select>

    <select id="queryByFaceId" resultType="com.redxun.fire.core.entity.BaseEventStatistics">
        select *
        from base_event_statistics
        where face_inspect_id = #{faceId}
    </select>

    <select id="countFireAlarms" resultType="map">
        SELECT event_type as eventType, COUNT(*) AS count
        FROM base_event_statistics
        WHERE event_type in ('0','1','2')
        <if test='type != null'>
            AND type = #{type}
        </if>
        <if test='startTime != null'>
            AND report_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and build_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY event_type
    </select>

    <select id="countFireInfo" resultType="long">
        SELECT COUNT(*) FROM fire_info where fire_status = '1' AND building_status_str = '正常' AND last_time between #{startTime} and #{endTime}
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="countFault" resultType="map">
        SELECT count(*) as faultCount,count(distinct building_id) as buildingCount
        FROM fault_info where fault_status in ('0','2')
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND last_time <![CDATA[ <= ]]> #{endTime}
    </select>


    <select id="countRouteDeviceException" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.point_code)
        FROM stat_host_expection a
        INNER JOIN base_host_info b ON a.point_code = b.transmission_code AND a.building_id = b.building_id
        WHERE expection_status = '0' AND a.report_time <![CDATA[ <= ]]> #{endTime}
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countRouteDevice" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.transmission_code)
        FROM base_host_info a
        WHERE 1 = 1
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countFireHostException" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.point_code)
        FROM stat_local_host_expection a inner join base_local_host_info b
        ON a.point_code = CONCAT(b.transmission_code, b.dev_no) AND a.building_id = b.building_id
        WHERE a.expection_status = '0' AND a.report_time <![CDATA[ <= ]]> #{endTime}
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countFireHost" resultType="java.lang.Long">
        SELECT count(*)
        FROM base_local_host_info a
        WHERE 1 = 1
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countWaterException" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.point_id)
        FROM stat_water_expection a inner join water_abnormal b
        ON a.id = b.id
        WHERE a.expection_status in ('0','2') AND b.building_status_str = '正常'
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND a.report_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="batchWaterExceptions" resultType="com.redxun.fire.core.entity.StatWaterExpection">
        SELECT a.point_desc, a.point_code, a.point_id,  a.expection_type, a.report_time
        FROM stat_water_expection a inner join water_abnormal b
        ON a.id = b.id
        WHERE  a.building_id = #{buildingId} and  a.expection_status in ('0','2') AND b.building_status_str = '正常'
          AND a.report_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="countPumpException" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.point_id)
        FROM stat_pump_expection a inner join water_abnormal b
        ON a.id = b.id
        WHERE a.expection_status in ('0','2') AND b.building_status_str = '正常'
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND a.report_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="batchPumpExceptions" resultType="com.redxun.fire.core.entity.StatPumpExpection">
        SELECT a.point_desc, a.point_code, a.point_id,  a.expection_type, a.report_time
        FROM stat_pump_expection a inner join water_abnormal b
        ON a.id = b.id
        WHERE  a.building_id = #{buildingId} and  a.expection_status in ('0','2') AND b.building_status_str = '正常'
          AND a.report_time <![CDATA[ <= ]]> #{endTime}
    </select>

    <select id="countCloseShopException" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.point_id) as closeShopExceptionCount
        FROM stat_close_store_expection a inner join close_store_abnormal b
        ON a.id = b.abnirmal_id
        WHERE a.expection_status in ('0','2') AND b.building_status_str = '正常'
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and a.building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND a.report_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    </select>

    <select id="batchCloseShopExceptions" resultType="com.redxun.fire.core.entity.StatCloseStoreExpection">
        SELECT a.point_desc, a.point_code, a.point_id,  a.expection_type, a.report_time
        FROM stat_close_store_expection a
            inner join close_store_abnormal b ON a.id = b.abnirmal_id
        WHERE  a.building_id = #{buildingId} and  a.expection_status in ('0','2') AND b.building_status_str = '正常'
          AND a.report_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    </select>

    <select id="countDevicePoint" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM base_device_point
        WHERE 1 = 1
        <if test='buildingIds != null and buildingIds.size() > 0'>
            and building_id in
            <foreach item="item" index="index" collection="buildingIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test='superType != null and superType.size() > 0'>
            and super_type in
            <foreach item="item" index="index" collection="superType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countBuildingsWithMainTimeout" resultType="map">
        SELECT building_id, SUM(TIMESTAMPDIFF(MINUTE, start_time, end_time)) AS mainTimeLong
        FROM appointment_apply
        WHERE application_status = '2'
        AND plan_type IN ('0', '2', '3')
        AND application_time BETWEEN #{todayStart} AND #{todayEnd}
        AND building_id IN
        <foreach collection="buildingIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY building_id
        HAVING mainTimeLong > 360
    </select>



    <!--新增所有列-->
    <insert id="insertData" keyProperty="id">
        insert into base_event_statistics(id, build_id, build_name, event_type, event_type_str, report_time, point_type_str,  tel, jj_type, face_inspect_id, face_inspect_type, type)
        values (#{id}, #{buildId}, #{buildName}, #{eventType}, #{eventTypeStr}, #{reportTime}, #{pointTypeStr}, #{tel}, #{jjType}, #{faceInspectId}, #{faceInspectType}, #{type})
    </insert>
</mapper>
