package com.redxun.fire.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.annotation.ParamDefine;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.exception.BusinessException;
import com.redxun.fire.core.consts.Constant;
import com.redxun.fire.core.dto.bpm.TaskExecutor;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.service.bpm.IBpmDefNodeReferenceService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.common.IAdjustptService;
import com.redxun.fire.core.service.device.IBaseDeviceApplicationService;
import com.redxun.fire.core.service.device.IBaseDeviceTemporaryService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.*;
import com.redxun.idempotence.IdempotenceRequired;
import com.redxun.log.annotation.AuditLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <p>
 * 点位调改 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@RestController
@RequestMapping("/point-adjust")
public class PointAdjustController {

    @Resource
    private OrgManageClient orgManageClient;
    @Resource
    private OrgMiddleServiceImpl orgMiddleService;
    @Autowired
    private IBaseDeviceApplicationService applyService;
    @Autowired
    private IBaseDeviceTemporaryService deviceTemporaryService;
    @Autowired
    private IBaseDeviceApplicationService applicationService;
    @Autowired
    IAdjustptService iAdjustptService;
    @Autowired
    private IBaseBuildingService buildingService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private IBpmDefNodeReferenceService bpmDefNodeReferenceService;
    Gson gson = new Gson();

    @ApiOperation(value = "点位调改获取审批人")
    @GetMapping({"/pointAdjustJobApprove"})
    public List<TaskExecutor> pointAdjustJobApprove(@RequestParam(value = "nodeId") String nodeId,@RequestParam(value = "nodeId") String userId,@RequestParam(value = "bpmDefId") String bpmDefId,@RequestParam(value = "buildingId") String buildingId) {
        List<TaskExecutor> userList = new ArrayList<>();
        //UserOrgResponse userOrg=orgMiddleService.getOrgManageByUser(userId);
        BaseBuilding baseBuilding=buildingService.getBaseMapper().selectById(buildingId);
        if(baseBuilding!=null){
            QueryWrapper qw=new QueryWrapper();
            qw.eq("node_id", nodeId);
            qw.eq("bpm_def_id", bpmDefId);
            qw.eq("node_enable", 1);
            List<BpmDefNodeReference> bpmDefNodeReferenceList=bpmDefNodeReferenceService.getBaseMapper().selectList(qw);
            if(bpmDefNodeReferenceList!=null&&bpmDefNodeReferenceList.size()>0) {
                String[] jobIds=bpmDefNodeReferenceList.get(0).getJobId().split(",");
                if(jobIds!=null&&jobIds.length>0) {
                    for(String jobId:jobIds){
                        OrgManageParam orgParam = new OrgManageParam();
                        String level = bpmDefNodeReferenceList.get(0).getLevelAyer();
                        orgParam.setBusinessId("1");
                        if ("1".equals(level)) {
                            orgParam.setPiazzaId(baseBuilding.getPiazza());
                        } else if ("2".equals(level)) {
                            orgParam.setCityId(baseBuilding.getCityCompany());
                        } else if ("3".equals(level)) {
                            orgParam.setRegionId(baseBuilding.getRegion());
                        } else if ("4".equals(level)) {
                            orgParam.setGroupId(baseBuilding.getGroupOrg());
                        }
                        orgParam.setJobId(jobId);
                        orgParam.setTenantId(baseBuilding.getTenantId());
                        JsonResult jsonResult = orgManageClient.queryOrgManage(orgParam);
                        try {
                            UserOrgResponse userOrgResponse = gson.fromJson(gson.toJson(jsonResult), (Type) UserOrgResponse.class);
                            List<UserOrgResponse.DataDTO> data = userOrgResponse.getData();
                            if (data != null && data.size() > 0) {
                                for (UserOrgResponse.DataDTO dto : data) {
                                    userList.add(new TaskExecutor("user", dto.getUser(), dto.getUserName(), dto.getUserNo()));
                                }
                            }

                        } catch (Exception e) {
                            log.info("---------------->>>>>>>>>>>>>自定义执行人调用万中台获取执行人,orgParam{},jsonResult：{}", orgParam, userList);
                        }
                    }
                }
            }
        }else{
            log.info("点位调改获取审批人未查到指定的建筑信息！！！入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId);
        }
        if(userList!=null&&userList.size()>15){
            log.info("---------------->>>>>>>>>>>>>点位调改获取审批人查询执行人数量过多入参nodeId:{},userId:{},bpmDefId:{},buildingId:{},数量:{}" ,nodeId,userId,bpmDefId,buildingId, userList.size());
            return new ArrayList<TaskExecutor>();
        }
        return userList;
    }

    @ResponseBody
    @PostMapping(value = "/pointAdjustPassApply")
    @ApiOperation("点位调改审批通过")
    public JsonResult pointAdjustPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("点位调改通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        Date approveTime = new Date();
        log.info("点位调改申请同意,相应的事件记录id为：【{}】", data);
        BaseDeviceApplication firstCheckOne = applicationService.getById(data);
        if (null == firstCheckOne) {
            log.info("点位调改申请同意,相应的事件记录id为：【{}】，未找到相应的数据", data);
            throw new BusinessException("点位调改申请同意,准备重试 id为:" + data);
//            return;
        }
        // 不为0 那么应该是审批通过或者拒绝的
        if (!"0".equals(firstCheckOne.getApprovCondition().trim())) {
            log.info("点位调改申请同意,相应的事件记录id为：【{}】，非未审核状态", data);
            return new JsonResult(false);
        }
        // 入点位信息 这边开多线程进行 因为这边比较耗时
//        executorService.submit(()->{
        QueryWrapper<BaseDeviceTemporary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("apply_id", data);
        List<BaseDeviceTemporary> deviceTemporaries = deviceTemporaryService.list(queryWrapper);
        try {
            if (deviceTemporaries.size() > 0) {
                BaseDeviceTemporary baseDeviceTemporary = deviceTemporaries.get(0);
                String applyType = baseDeviceTemporary.getApplyType();
                baseDeviceTemporary.setApproveType("1");
                baseDeviceTemporary.setApplyId(data);
                if ("0".equals(applyType)) {
                    //新增并且审批通过
                    deviceTemporaryService.addApplyPoint(baseDeviceTemporary, approveTime);
                } else if ("1".equals(applyType)) {
                    //编辑并且审批通过
                    deviceTemporaryService.editPointInfo(deviceTemporaries, approveTime);
                } else if ("2".equals(applyType)) {
                    //删除并且审批通过
                    deviceTemporaryService.deletePointInfo(deviceTemporaries, approveTime);
                } else {
                    // 特批点位申请通过
                    deviceTemporaryService.addSpecialPoint(deviceTemporaries, approveTime);
                }
            }
            // 处理相应的申请记录 变更状态
            // 变更审批记录
            log.info("点位调改-修改相应状态，审批的记录id为【{}】", data);
            BaseDeviceApplication one = applicationService.getById(data);
            log.info("点位调改-修改相应状态，审批的记录为【{}】", JSONObject.toJSONString(one));
            // 有可能未生成审批记录 未生成的情况下 直接算成功
            if (one != null && "0".equals(one.getApprovCondition())) {
                one.setApprovOverTime(DateUtils.formatDatetime(approveTime));
                one.setApprovCondition("1");
                one.setUpdateTime(new Date());
                applicationService.updateById(one);
                log.info("点位调改-修改相应状态，审批的记录id为【{}】,修改成功", data);
            }
            redisUtils.remove(Constant.IMPORT_POINT_KEY + data);
        } catch (Exception e) {
            log.error("点位调改申请同意,相应的事件记录id为【{}】,对应的异常信息为：【{}】", data, e);
            log.error("修改失败，{}", e.getMessage(), e);
        }
        return jsonResult;
    }

    @ResponseBody
    @PostMapping(value = "/pointAdjustNoPassApply")
    @ApiOperation("点位调改审批未通过")
    public JsonResult pointAdjustNoPassApply(@RequestBody @Validated Map<String, String> jsonObject) {
        log.info("点位调改未通过，ID为：{}", jsonObject);
        JsonResult jsonResult = new JsonResult(true);
        String data = jsonObject.get("applyId");
        String type = jsonObject.get("type");
        if ("cancel".equals(type)) {
            log.info("-------点位调改审批作废开始，applyId为{}--------", data);
        }
        Date approveTime = new Date();
        List<BaseDeviceApplication> list = applyService.list(new QueryWrapper<BaseDeviceApplication>().eq("approv_condition", "0").eq("id", data));
        if (CollectionUtils.isEmpty(list)) {
            return new JsonResult(false).setMessage("无对应点位信息");
        }
        list.forEach(appointmentApply -> {
            appointmentApply.setApprovCondition("2");
            appointmentApply.setUpdateTime(new Date());
            appointmentApply.setApprovOverTime(DateUtils.formatDatetime(approveTime));
        });
        applyService.updateBatchById(list);
        BaseDeviceApplication appointmentApply = list.get(0);
        String buildingId = appointmentApply.getBuildingId();
        BaseBuilding baseBuilding = buildingService.getById(buildingId);
        if (null == baseBuilding) {
            return new JsonResult(false).setMessage("无对应建筑信息");
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        String adjustTypeStr = "";
        String adjustType = "";
        if ("0".equals(appointmentApply.getModulationType())) {
            adjustTypeStr = "点位新增";
        } else if ("1".equals(appointmentApply.getModulationType())) {
            adjustTypeStr = "点位修改";
        } else if ("2".equals(appointmentApply.getModulationType())) {
            adjustTypeStr = "点位删除";
        } else if ("3".equals(appointmentApply.getModulationType())) {
            adjustTypeStr = "特批点位申请";
        } else {
            adjustTypeStr = "未知操作";
        }
        // 拒绝事件入事件中心记录
        boolean save = iAdjustptService.save(iAdjustptService.setAdjustpt(appointmentApply.getBuildingId(), baseBuilding.getBuildingName(),
                baseBuilding.getCenter(), baseBuilding.getOperatingCenter(), baseBuilding.getJurisdiction(), baseBuilding.getJurisdictionVal(),
                appointmentApply.getModulationType(), adjustTypeStr, String.valueOf(appointmentApply.getModulationQuantity()), decimalFormat.format(new BigDecimal(appointmentApply.getModulationProportion())) + "%", appointmentApply.getProposer(),
                appointmentApply.getApplicantTime(), DateUtil.formatDate(approveTime, "yyyy-MM-dd HH:mm:ss"), "审批拒绝"));
        return jsonResult;
    }

    @MethodDefine(title = "流程事件数据处理", path = "/flowEvent", method = HttpMethodConstants.POST,
            params = {@ParamDefine(title = "流程事件数据处理", varName = "dataJson")})
    @AuditLog(operation = "流程事件数据处理")
    @IdempotenceRequired
    @PostMapping("/flowEvent")
    public Object flowEvent(@RequestBody JSONObject dataJson) {
        return iAdjustptService.flowEvent(dataJson);
    }

}
