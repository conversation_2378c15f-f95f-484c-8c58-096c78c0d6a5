package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.pojo.vo.WaterSystemDicVo;
import com.redxun.fire.core.pojo.vo.WdDevConfigInfoVo;
import com.redxun.fire.core.pojo.vo.WdDevConfigVo;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.common.IDropDownService;
import com.redxun.fire.core.service.device.IWdDevConfigService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <p>
 * 设备类型配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-28
 */
@RestController
@RequestMapping("/wd-dev-config")
public class WdDevConfigController {

    @Autowired
    private IWdDevConfigService wdDevConfigService;

    @Autowired
    private IDropDownService iDropDownService;

    @GetMapping("/getWaterSystemType/{type}")
    public JsonResult getWaterType(@PathVariable("type") String type) {
        JsonResult result = JsonResult.Success();
        List<WaterSystemDicVo> returnList = Lists.newArrayList();
        if ("1".equals(type)) {
            // 液位仪相关
            returnList.add(new WaterSystemDicVo("SX", "消防水箱液位传感器"));
            returnList.add(new WaterSystemDicVo("SC", "消防水池液位传感器"));
        } else if ("2".equals(type)) {
            // 压力表相关
            returnList.add(new WaterSystemDicVo("YLS", "无线压力传感器喷淋系统(湿式)"));
            returnList.add(new WaterSystemDicVo("YLX", "无线压力传感器消火栓系统"));
            returnList.add(new WaterSystemDicVo("YLY", "无线压力传感器喷淋系统(预作用)"));
            returnList.add(new WaterSystemDicVo("YLG", "无线压力传感器喷淋系统(干式)"));
            returnList.add(new WaterSystemDicVo("SPYG", "消防水炮系统"));
            returnList.add(new WaterSystemDicVo("FQYL", "喷淋报警阀前环网"));
        } else if ("3".equals(type)) {
            // 水泵相关
            returnList.add(new WaterSystemDicVo("PLB", "喷淋泵"));
            returnList.add(new WaterSystemDicVo("SNXHSB", "室内消火栓泵"));
            returnList.add(new WaterSystemDicVo("SWXHSB", "室外消火栓泵"));
            returnList.add(new WaterSystemDicVo("CPB", "窗喷泵"));
            returnList.add(new WaterSystemDicVo("XJG", "巡检柜"));
            returnList.add(new WaterSystemDicVo("PLWYB", "喷淋稳压泵"));
            returnList.add(new WaterSystemDicVo("XHSWYB", "消火栓稳压泵"));
            returnList.add(new WaterSystemDicVo("PLWYB", "喷淋稳压泵"));
            returnList.add(new WaterSystemDicVo("XFSPB", "消防水炮泵"));
            returnList.add(new WaterSystemDicVo("XFSMB", "消防水幕泵"));
        } else if ("4".equals(type)) {
            // 水泵相关
            returnList.add(new WaterSystemDicVo("CAMERA", "摄像头"));
//            returnList.add(new WaterSystemDicVo("SMARTCAMERA", "烟温感摄像头"));
        } else if ("5".equals(type)) {
            // 水泵相关
            returnList.add(new WaterSystemDicVo("SMARTCAMERA", "烟温感一体摄像头"));
        } else {
            result = JsonResult.Fail("未找到相应编码");
        }
        result.setData(returnList);
        return result;
    }


    /**
     * 报警定义-批量修改
     *
     * @param wdDevConfigVo
     * @return
     */
    @PostMapping("/updateList")
    public JsonResult updateList(HttpServletRequest request, @RequestBody @Validated(Update.class) WdDevConfigVo wdDevConfigVo) {
        JsonResult result = JsonResult.Success();
        result.setData(wdDevConfigService.updateList(request, wdDevConfigVo.getWdDevConfigList()));
        return result;
    }

    /**
     * 查询报警类型
     * 95520  报警类型
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryPoliceType")
    public JsonResult queryPoliceType() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDropDownService.queryNameByTreeId("95520"));
        return jsonResult;
    }

    /**
     * 报警定义界面查询
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryWdDevConfig")
    public JsonResult queryWdDevConfig() {
        JsonResult<List<WdDevConfigInfoVo>> jsonResult = JsonResult.Success();
        jsonResult.setData(wdDevConfigService.queryWdDevConfig());
        return jsonResult;
    }

}
