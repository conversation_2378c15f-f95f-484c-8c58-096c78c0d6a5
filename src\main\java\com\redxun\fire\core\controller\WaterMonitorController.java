package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.entity.Journalizing;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.service.building.impl.BaseBuildingFloorServiceImpl;
import com.redxun.fire.core.service.other.impl.JournalizingServiceImpl;
import com.redxun.fire.core.service.alarm.impl.WaterMonitorServiceImpl;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.ExcelUtil;
import com.redxun.fire.core.utils.POIExcelUtil;
import com.redxun.fire.core.utils.ServletsUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 水系统监测（水泵、水压）
 * @since 20200921
 */
@RestController
@RequestMapping("/fire/water")
public class WaterMonitorController {

    @Resource
    private WaterMonitorServiceImpl waterMonitorService;

    @Resource
    private JournalizingServiceImpl journalizingService;

    @Resource
    BaseBuildingFloorServiceImpl buildingFloorService;

    /**
     * 压力监测：查询水压设备总数，以及正常、异常占比
     *
     * @param buildId
     * @return
     */
    @GetMapping("/pressure/getWaterPressureDeviceData")
    public JsonResult getWaterPressureDeviceData(HttpServletRequest request, @RequestParam(value = "buildId", required = false) String buildId) {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("buildId", buildId);
        return JsonResult.getSuccessResult(waterMonitorService.getWaterPressureDeviceData(request, paramsMap));
    }

    /**
     * 压力监测：查询压力异常、设备异常排行榜
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getWaterPressureAbnormalRankList")
    public JsonResult getWaterPressureAbnormalRankList(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.getWaterPressureAbnormalRankList(request, paramsMap));
    }

    /**
     * 压力表注册
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/pressureDeviceRegister")
    public JsonResult pressureDeviceRegister(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.pressureDeviceRegister(request, paramsMap);
    }

    /**
     * 液位仪注册
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/liquidDeviceRegister")
    public JsonResult liquidDeviceRegister(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.liquidDeviceRegister(request, paramsMap)
                ? JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }

    /**
     * 查询压力监测、液位监测数据
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/queryPressureMonitorData")
    public JsonResult queryPressureMonitorData(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.queryPressureMonitorData(paramsMap));
    }

    /**
     * 删除水压监测点位信息
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/deletePointInfo")
    public JsonResult deletePointInfo(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.deletePointInfo(request, paramsMap) ?
                JsonResult.Success() : JsonResult.getFailResult("删除失败");
    }

    /**
     * 压力点位信息修改
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/editPressurePointInfoById")
    public JsonResult editPressurePointInfoById(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.editPressurePointInfoById(request, paramsMap) ?
                JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }

    /**
     * 液位点位信息修改
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/editLiquidPointInfoById")
    public JsonResult editLiquidPointInfoById(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.editLiquidPointInfoById(request, paramsMap) ?
                JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }

    /**
     * 水泵点位信息修改
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pump/editPumpPointInfoById")
    public JsonResult editPumpPointInfoById(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return waterMonitorService.editPumpPointInfoById(request, paramsMap) ?
                JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }


    /**
     * 批量水压设备点位导入
     *
     * @param file
     * @return
     */
    @PostMapping("/pressure/batchImportPointInfo")
    public JsonResult batchImportPointInfo(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("buildingId") String buildingId) {
        JsonResult jsonResult = new JsonResult(true);
        if (StringUtils.isEmpty(buildingId)) {
            jsonResult.setMessage("建筑物id不能为空");
            //throw new RuntimeException("建筑物id不能为空");
        }
        QueryWrapper<BaseBuildingFloor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("building_id", buildingId);
        List<BaseBuildingFloor> list = buildingFloorService.list(queryWrapper);
        if (list == null || list.size() == 0) {
            jsonResult.setMessage("没有楼层无法新增");
            //throw new RuntimeException("没有楼层无法新增");
        }
        return waterMonitorService.batchImportPointInfo(request, file, buildingId);
    }

    /**
     * 导出水压异常数据
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/pressure/exportWaterAbnormalData")
    public void exportWaterAbnormalData(HttpServletRequest request,
                                        HttpServletResponse response) throws IOException {
        List<Map<String, Object>> resultList =
                waterMonitorService.exportWaterAbnormalData(request, new HashMap<>());
        HSSFWorkbook workbook =
                POIExcelUtil.exportExcel(resultList, new String[]{"中心", "区域", "建筑名", "设备异常", "压力异常", "液位异常", "分析异常"},
                        new String[]{"deviceAbnormalNum", "preAbnormalNum", "liquidAbnormalNum", "analyseAbnormalNum"});
        String fileName = System.currentTimeMillis() + ".xls";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(response.getOutputStream());

        //日志插入
        Journalizing journalizing = new Journalizing();
        journalizing.setOperationTypeCode(LogTypeEnums.PUMP_MONITOR.getType());
        journalizing.setOperationContent("导出水泵监测建筑列表数据");
        journalizingService.setLogInfo(request, journalizing);
    }

    /**
     * 设备详情-查询同湿式报警阀压力对比
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getAlarmValvePressureContrast")
    public JsonResult getAlarmValvePressureContrast(@RequestBody Map<String, Object> paramsMap) {
        List<Map<String, Object>> resultList =
                waterMonitorService.getAlarmValvePressureContrast(paramsMap);
        return JsonResult.getSuccessResult(resultList);
    }

    /**
     * 设备详情-查询同楼层压力对比
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getSameFloorPressureContrast")
    public JsonResult getSameFloorPressureContrast(@RequestBody Map<String, Object> paramsMap) {
        List<Map<String, Object>> resultList =
                waterMonitorService.getSameFloorPressureContrast(paramsMap);
        return JsonResult.getSuccessResult(resultList);
    }

    /**
     * 查询历史压力值
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getWaterPressureHistoryValue")
    public JsonResult getWaterPressureHistoryValue(@RequestBody Map<String, Object> paramsMap) {
        Map<String, Object> resultMap =
                waterMonitorService.getWaterPressureHistoryValue(paramsMap);
        return JsonResult.getSuccessResult(resultMap);
    }

    /**
     * 查询历史液位值
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getLiquidHistoryValue")
    public JsonResult getLiquidHistoryValue(@RequestBody Map<String, Object> paramsMap) {
        Map<String, Object> resultMap =
                waterMonitorService.getLiquidHistoryValue(paramsMap);
        return JsonResult.getSuccessResult(resultMap);
    }

    /**
     * 导出液位历史数据
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/pressure/exportLiquidHistoryData")
    public void exportLiquidHistoryData(HttpServletRequest request,
                                        HttpServletResponse response) throws IOException {
        Map paramsMap = ServletsUtil.getParameters(request);
        Map<String, Object> resultList = waterMonitorService.getLiquidHistoryValue(paramsMap);
        if (resultList != null) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) resultList.get("dataList");

            List<List<Object>> data = new ArrayList<>();
            dataList.forEach(map -> {
                List<Object> objects = new ArrayList<>();
                objects.add(map.get("time"));
                objects.add(map.get("value"));
                data.add(objects);
            });
            List<List<String>> headers = new ArrayList<>();
            List<String> head = new ArrayList<>();
            head.add("时间");
            headers.add(head);
            List<String> head1 = new ArrayList<>();
            head1.add("液位值");
            headers.add(head1);
            try {
                ExcelUtil.writeExcelT(response, data, "液位历史数据", headers);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("导出异常！");
            }
        } else {
            throw new RuntimeException("无导出数据！");
        }
    }

    /**
     * 查询楼层平面图
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getFloorPlanPointInfo")
    public JsonResult getFloorPlanPointInfo(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.getFloorPlanPointInfo(paramsMap));
    }

    /**
     * 水泵监测：查询水泵设备总数，以及正常、异常占比
     *
     * @param buildId
     * @return
     */
    @GetMapping("/pump/getPumpDeviceData")
    public JsonResult getPumpDeviceData(HttpServletRequest request, @RequestParam(value = "buildId", required = false) String buildId) {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("buildId", buildId);
        return JsonResult.getSuccessResult(waterMonitorService.getPumpDeviceData(request, paramsMap));
    }

    /**
     * 水泵监测：查询水泵异常、设备异常排行榜
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pump/getWaterLiquidAbnormalRankList")
    public JsonResult getWaterLiquidAbnormalRankList(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.getWaterLiquidAbnormalRankList(request, paramsMap));
    }

    /**
     * 通过点位id来获取点位信息
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/getPointInfoById")
    public JsonResult getPointInfoById(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.getPointInfoById(paramsMap));
    }

    /**
     * 水泵监测：异常数据导出
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/pump/exportPumpAbnormalData")
    public void exportPumpAbnormalData(HttpServletRequest request,
                                       HttpServletResponse response) throws IOException {
        List<Map<String, Object>> resultList =
                waterMonitorService.exportPumpAbnormalData(request, null);
        HSSFWorkbook workbook =
                POIExcelUtil.exportExcel(resultList, new String[]{"中心", "区域", "建筑名", "设备异常", "水泵异常"},
                        new String[]{"deviceAbnormalNum", "pumpAbnormalNum"});
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        response.flushBuffer();
        workbook.write(response.getOutputStream());
    }

    /**
     * 水泵注册
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pump/pumpRegister")
    public JsonResult pumpRegister(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {

        return waterMonitorService.pumpRegister(request, paramsMap);
    }

    /**
     * 查询水泵监测状态信息
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pump/getPumpMonitorStatusInfo")
    public JsonResult getPumpMonitorStatusInfo(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.getPumpMonitorStatusInfo(paramsMap));
    }

    /**
     * 总部监控台水压、水泵异常统计
     *
     * @return
     */
    @GetMapping("/home/<USER>/queryWaterMonitorStatisData")
    public JsonResult queryWaterMonitorStatisData() {
        return JsonResult.getSuccessResult(waterMonitorService.queryWaterMonitorStatisData());
    }

    /**
     * 获取水压监测每日信息
     *
     * @return
     */
    @PostMapping("/pressure/queryWaterPressureInfoEveryDay")
    public JsonResult queryWaterPressureInfoEveryDay(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.queryWaterPressureInfoEveryDay(paramsMap));
    }

    /**
     * 获取水泵监测每日信息
     *
     * @return
     */
    @PostMapping("/pump/queryPumpInfoEveryDay")
    public JsonResult queryPumpInfoEveryDay(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.queryPumpInfoEveryDay(paramsMap));
    }

    /**
     * 查询所属报警阀列表
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pressure/queryAlarmList")
    public JsonResult queryAlarmList(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.queryAlarmList(paramsMap));
    }

    /**
     * 查询水泵状态历史数据
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/pump/queryPumpStatusHistoryData")
    public JsonResult queryPumpStatusHistoryData(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(waterMonitorService.queryPumpStatusHistoryData(paramsMap));
    }

    /**
     * 通过设备类型获取最小阈值
     *
     * @param devTypeCode
     * @return
     */
    @GetMapping("/pressure/getMinValByDevTypeCode")
    public JsonResult getMinValByDevTypeCode(@RequestParam(value = "devTypeCode") String devTypeCode,
                                             @RequestParam(value = "type") String type) {
        Map<String, Object> resultMap = new HashMap<>();
        String minval = waterMonitorService.getMinValByDevTypeCode(devTypeCode);
        if (StringUtils.isNotEmpty(minval)) {
            //压力值需要转换
            if ("1".equals(type)) {
                resultMap.put("minval", new BigDecimal(minval).multiply(new BigDecimal(1000)).setScale(2) + "");
            } else {
                resultMap.put("minval", new BigDecimal(minval).setScale(2) + "");
            }
        } else {
            resultMap.put("minval", "");
        }
        return JsonResult.getSuccessResult(resultMap);
    }

    /**
     * 导出水泵历史数据
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/pump/exportPumpHistoryData")
    public void exportPumpHistoryData(HttpServletRequest request,
                                      HttpServletResponse response) throws IOException {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("buildId", request.getParameter("buildId"));
        paramsMap.put("id", request.getParameter("id"));
        paramsMap.put("beginTime", request.getParameter("beginTime"));
        paramsMap.put("endTime", request.getParameter("endTime"));
        paramsMap.put("powerStatus", request.getParameter("powerStatus"));
        paramsMap.put("manualStatus", request.getParameter("manualStatus"));
        paramsMap.put("runStatus", request.getParameter("runStatus"));
        paramsMap.put("faultStatus", request.getParameter("faultStatus"));
        List<Map<String, Object>> resultList =
                waterMonitorService.queryPumpStatusHistoryData(paramsMap);
        HSSFWorkbook workbook =
                POIExcelUtil.exportPumpExcel(resultList, new String[]{"时间", "电源状态", "手自动状态", "启停状态", "故障状态"});
        String fileName = DateUtils.formatDatetime(new Date()) + ".xls";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
        response.flushBuffer();
        workbook.write(response.getOutputStream());
    }
}
