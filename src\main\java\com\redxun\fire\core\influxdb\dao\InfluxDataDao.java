package com.redxun.fire.core.influxdb.dao;

import cn.hutool.core.util.StrUtil;
import com.redxun.fire.core.influxdb.entity.*;
import com.redxun.fire.core.pojo.base.PageParam;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.base.TablePageData;
import com.redxun.fire.core.pojo.dto.CassandraQueryDTO;
import com.redxun.fire.core.pojo.dto.KitchenInfluxDataQueryDto;
import com.redxun.fire.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import plus.ojbk.influxdb.core.InfluxdbTemplate;
import plus.ojbk.influxdb.core.Op;
import plus.ojbk.influxdb.core.Query;
import plus.ojbk.influxdb.core.enums.Order;
import plus.ojbk.influxdb.core.model.QueryModel;
import plus.ojbk.influxdb.util.InfluxdbUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class InfluxDataDao {

    @Resource
    private InfluxdbTemplate influxdbTemplate;


    /**
     * 根据时间获取水压信息
     * @param buildingId
     * @param pointId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<PreInfluxData> findPreDeviceByTime(String buildingId, String pointId, Date startTime, Date endTime) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);

        QueryModel queryModel = buildQueryModel(PreInfluxData.class, conditions, startTime, endTime);
        log.info("从influxdb中获取水压信息,请求参数{},{},{},{}", buildingId, pointId, startTime, endTime);
        return executeNonPagedQuery(queryModel, PreInfluxData.class);
    }


    /**
     * 根据时间获取水泵信息
     * @param buildingId
     * @param pointId
     * @param startTime
     * @param endTime
     * @param powerStatus
     * @param manualStatus
     * @param runStatus
     * @param faultStatus
     * @return
     */
    public List<PumpInfluxData> findPumpDataByTime(String buildingId, String pointId, Date startTime, Date endTime,
                                                   String powerStatus, String manualStatus, String runStatus, String faultStatus){
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        if (StrUtil.isNotBlank(powerStatus)) {
            conditions.put("power_status", powerStatus);
        }
        if (StrUtil.isNotBlank(manualStatus)) {
            conditions.put("manual_status", manualStatus);
        }
        if (StrUtil.isNotBlank(runStatus)) {
            conditions.put("run_status", runStatus);
        }
        if (StrUtil.isNotBlank(faultStatus)) {
            conditions.put("fault_status", faultStatus);
        }

        QueryModel queryModel = buildQueryModel(PumpInfluxData.class, conditions, startTime, endTime);
        log.info("从influxdb中获取水泵信息,请求参数{},{},{},{}", buildingId, pointId, startTime, endTime);
        return executeNonPagedQuery(queryModel, PumpInfluxData.class);    }

    /**
     * 根据时间获取液压信息
     * @param buildingId
     * @param pointId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<LiquidInfluxData> findLiquidDeviceByTime(String buildingId, String pointId, Date startTime, Date endTime) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);

        QueryModel queryModel = buildQueryModel(LiquidInfluxData.class, conditions, startTime, endTime);
        log.info("从influxdb中获取液压信息,请求参数{},{},{},{}", buildingId, pointId, startTime, endTime);
        return executeNonPagedQuery(queryModel, LiquidInfluxData.class); }

    /**
     * 根据时间获取闭店信息
     * @param buildingId
     * @param pointId
     * @param startTime
     * @param endTime
     * @param outageStatus
     * @param deviceStatus
     * @param electricalStatus
     * @param faultStatus
     * @return
     */
    public List<CloseInfluxData> findCloseStoreDataByTime(String buildingId, String pointId, Date startTime, Date endTime,
                                                          String outageStatus, String deviceStatus, String electricalStatus, String faultStatus) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        if (StrUtil.isNotBlank(outageStatus)) {
            conditions.put("outage_status", outageStatus);
        }
        if (StrUtil.isNotBlank(deviceStatus)) {
            conditions.put("device_status", deviceStatus);
        }
        if (StrUtil.isNotBlank(electricalStatus)) {
            conditions.put("electrical_status", electricalStatus);
        }
        if (StrUtil.isNotBlank(faultStatus)) {
            conditions.put("fault_status", faultStatus);
        }

        QueryModel queryModel = buildQueryModel(CloseInfluxData.class, conditions, startTime, endTime);
        log.info("从influxdb中获取闭店信息,请求参数{},{},{},{}", buildingId, pointId, startTime, endTime);
        return executeNonPagedQuery(queryModel, CloseInfluxData.class);
    }



    public TablePageData findCloseDataPage(String buildingId, String pointId, Date startTime, Date endTime,
                                           Long pageNum, Long pageSize, String electricalStatus, Class clazz,
                                           String electricalType, String storeStatus) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        if (StrUtil.isNotBlank(electricalStatus)) {
            conditions.put("electrical_status", electricalStatus);
        }
        if (StrUtil.isNotBlank(electricalType)) {
            conditions.put("electrical_type", electricalType);
        }
        if (StrUtil.isNotBlank(storeStatus)) {
            conditions.put("store_status", storeStatus);
        }

        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        CassandraQueryDTO dto = new CassandraQueryDTO();
        dto.setPageIndex(pageNum.intValue());
        dto.setPageRows(pageSize.intValue());

        TablePageData tablePageData = executePagedQuery(queryModel, clazz, dto);
        return tablePageData;
    }

    public TablePageData findCloseDataPage(String buildingId, String pointId, Date startTime, Date endTime,
                                           Long pageNum, Long pageSize, String electricalStatus, Class clazz,
                                           String electricalType) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        if (StrUtil.isNotBlank(electricalStatus)) {
            conditions.put("electrical_status", electricalStatus);
        }
        if (StrUtil.isNotBlank(electricalType)) {
            conditions.put("electrical_type", electricalType);
        }

        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        CassandraQueryDTO dto = new CassandraQueryDTO();
        dto.setPageIndex(pageNum.intValue());
        dto.setPageRows(pageSize.intValue());

        TablePageData tablePageData = executePagedQuery(queryModel, clazz, dto);
        return tablePageData;
    }

    public TablePageData findCloseControlDataPage(String buildingId, String pointId, Date startTime, Date endTime,
                                                  Long pageNum, Long pageSize,Class clazz) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        CassandraQueryDTO dto = new CassandraQueryDTO();
        dto.setPageIndex(pageNum.intValue());
        dto.setPageRows(pageSize.intValue());

        TablePageData tablePageData = executePagedQuery(queryModel, clazz, dto);
        return tablePageData;
    }

    public List findCloseControlData(String buildingId, String pointId, Date startTime, Date endTime,
                                                  Class clazz) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        CassandraQueryDTO dto = new CassandraQueryDTO();

        List nonPagedQuery = executeNonPagedQuery(queryModel, clazz);
        return nonPagedQuery;
    }

    /**
     * 根据时间获取闭店恒定电流信息
     * @param buildingId
     * @param pointId
     * @param startTime
     * @param endTime
     * @return
     */
    public List findCloseInfluxDataNoPage(String buildingId, String pointId, String storeStatus ,Date startTime, Date endTime,Class clazz,String electricalType) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        if (StrUtil.isNotBlank(electricalType)) {
            conditions.put("electrical_type", electricalType);
        }
        if (StrUtil.isNotBlank(storeStatus)) {
            conditions.put("store_status", storeStatus);
        }
        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        return executeNonPagedQuery(queryModel, clazz);
    }


    /**
     * 查找最大的回归时长
     * @param
     * @return
     */
    public CloseInfluxDuration findMaxDuration(String buildingId,String pointId,List<Long> intervalIds) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        conditions.put("init_data_id", intervalIds); // 使用 List 表示 IN 查询的值
        Date startTime = DateUtils.addDay(new Date(),-25);
        Date endTime = new Date();
        QueryModel queryModel = buildQueryModel(CloseInfluxDuration.class, conditions, startTime, endTime);
        // 执行查询
        List<CloseInfluxDuration> results = executeNonPagedQuery(queryModel, CloseInfluxDuration.class);

        // 返回最大值
        return results.stream()
                .max(Comparator.comparingDouble(result -> Double.parseDouble(result.getElectricalValue())))
                .orElse(null);
    }


    /**
     * 查询区间内的特征值
     * @return
     */
//    public List<CloseInfluxMutantThreshold> findCloseInfluxMutantThreshold(String buildingId,String pointId,List<Long> intervalIds) {
//        Map<String, Object> conditions = new HashMap<>();
//        conditions.put("building_id", buildingId);
//        conditions.put("point_id", pointId);
//        conditions.put("init_data_id", intervalIds); // 使用 List 表示 IN 查询的值
//        Date startTime = DateUtils.addDay(new Date(),-25);
//        Date endTime = new Date();
//        QueryModel queryModel = buildQueryModel(CloseInfluxMutantThreshold.class, conditions, startTime, endTime);
//        // 执行查询
//        List<CloseInfluxMutantThreshold> results = executeNonPagedQuery(queryModel, CloseInfluxMutantThreshold.class);
//        return results;
//    }


    public ResultMsg selectObject(CassandraQueryDTO dto){
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", dto.getBuildingId());
        conditions.put("point_id", dto.getPointId());

        Date startTime, endTime;
        if (StrUtil.isBlank(dto.getFirstTime())) {
            startTime = Date.from(LocalDateTime.now().minusDays(7).atZone(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        } else {
            startTime = Date.from(LocalDateTime.parse(dto.getFirstTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(LocalDateTime.parse(dto.getLastTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
        }

        QueryModel queryModel = buildQueryModel(FireInfluxData.class, conditions, startTime, endTime);
        TablePageData tablePageData = executePagedQuery(queryModel, FireInfluxData.class, dto);
        log.info("从influxdb中获取报警信息,请求参数{},{},{},{}", dto.getBuildingId(), dto.getPointId(), dto.getLastTime(), dto.getEndTime());
        return ResultMsg.getResultMsg("操作成功", tablePageData, 200);    }

    public TablePageData selectObject(CassandraQueryDTO dto,String measurement,Class clazz){
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", dto.getBuildingId());
        conditions.put("point_id", dto.getPointId());

        Date startTime, endTime;
        if (StrUtil.isBlank(dto.getFirstTime())) {
            startTime = Date.from(LocalDateTime.now().minusDays(100).atZone(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        } else {
            startTime = Date.from(LocalDateTime.parse(dto.getFirstTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
            endTime = Date.from(LocalDateTime.parse(dto.getLastTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
        }

        QueryModel queryModel = buildQueryModel(clazz, conditions, startTime, endTime);
        CassandraQueryDTO pageDto = new CassandraQueryDTO();
        pageDto.setPageIndex(dto.getPageIndex());
        pageDto.setPageRows(dto.getPageRows());

        TablePageData tablePageData = executePagedQuery(queryModel, clazz, pageDto);
        return tablePageData;
    }


    private TablePageData executePagedQuery(QueryModel queryModel, Class clazz, CassandraQueryDTO dto) {
        String build = Query.build(queryModel);
        long count = influxdbTemplate.selectList(build,clazz).size();

        PageParam pageParam = PageParam.getPage(dto.getPageIndex(), dto.getPageRows());
        pageParam.setTotalRows(count);

        queryModel.setCurrent(Long.valueOf(dto.getPageIndex()));
        queryModel.setSize(Long.valueOf(dto.getPageRows()));

        List dataList = influxdbTemplate.selectList(Query.build(queryModel), clazz);
        TablePageData tablePageData = new TablePageData();
        tablePageData.setPageParam(pageParam);
        tablePageData.setTableData(dataList);
        return tablePageData;
    }

    private  QueryModel buildQueryModel(Class clazz, Map<String, Object> conditions, Date startTime, Date endTime) {
        QueryModel queryModel = new QueryModel();
        log.info("查询条件：{}", conditions);
        startTime = DateUtils.addHour(startTime, -8);
        endTime = DateUtils.addHour(endTime, -8);
        queryModel.setMeasurement(InfluxdbUtils.getMeasurement(clazz));
        StringBuilder whereClause = new StringBuilder();
        for (Map.Entry<String, Object> entry : conditions.entrySet()) {
            if (entry.getValue() instanceof List) {
                // 处理 IN 查询
                List<?> valueList = (List<?>) entry.getValue();
                if (!valueList.isEmpty()) {
                    if (whereClause.length() > 0) {
                        whereClause.append(" AND ");
                    }
                    whereClause.append("(");
                    whereClause.append(valueList.stream()
                            .map(value -> entry.getKey() + " = '" + value.toString() + "'")
                            .collect(Collectors.joining(" OR ")));
                    whereClause.append(")");
                }
            } else {
                // 普通条件
                if (whereClause.length() > 0) {
                    whereClause.append(" AND ");
                }
                whereClause.append(entry.getKey()).append(" = '").append(entry.getValue()).append("'");
            }
        }

        // 添加时间条件
        if (whereClause.length() > 0) {
            whereClause.append(" AND ");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        whereClause.append("time >= '").append(startTime.toInstant().atZone(ZoneId.systemDefault()).format(formatter)).append("'");
        whereClause.append(" AND ");
        whereClause.append("time <= '").append(endTime.toInstant().atZone(ZoneId.systemDefault()).format(formatter)).append("'");

        queryModel.setWhere(whereClause.toString());
        queryModel.setOrder(Order.DESC);

        return queryModel;
    }

    private  List executeNonPagedQuery(QueryModel queryModel, Class clazz) {
        return influxdbTemplate.selectList(Query.build(queryModel), clazz);
    }

    public List<KitchenInfluxData> queryKitchenStateData(Date beginDate, Date endDate, KitchenInfluxDataQueryDto queryDto) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", queryDto.getBuildingId());
        conditions.put("address", queryDto.getAddress());
        conditions.put("host_type", queryDto.getHostType());
        conditions.put("device_type", queryDto.getDeviceType());
        if (StrUtil.isNotBlank(queryDto.getFaultState())) {
            conditions.put("fault_state", queryDto.getFaultState());
        }
        if (StrUtil.isNotBlank(queryDto.getOutageState())) {
            conditions.put("outage_state", queryDto.getOutageState());
        }
        if (StrUtil.isNotBlank(queryDto.getAlarmState())) {
            conditions.put("alarm_state", queryDto.getAlarmState());
        }
        if (StrUtil.isNotBlank(queryDto.getDeviceType())) {
            conditions.put("device_type", queryDto.getDeviceType());
        }
        QueryModel queryModel = buildQueryModel(KitchenInfluxData.class, conditions, beginDate, endDate);
        return executeNonPagedQuery(queryModel, KitchenInfluxData.class);
    }

    public List<CloseInfluxConstantData> queryConstant(String buildingId, String pointId, Date startTime, Date endTime,String electricalType) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("building_id", buildingId);
        conditions.put("point_id", pointId);
        conditions.put("electrical_type", electricalType);
        QueryModel queryModel = new QueryModel();
        queryModel.setMeasurement(InfluxdbUtils.getMeasurement(CloseInfluxConstantData.class));
        queryModel.setStart(startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        queryModel.setEnd(endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        queryModel.setMap(conditions);
        queryModel.setWhere(Op.where(queryModel));
        queryModel.setOrder(Order.ASC);
        return influxdbTemplate.selectList(plus.ojbk.influxdb.core.Query.build(queryModel), CloseInfluxConstantData.class);
    }



}
