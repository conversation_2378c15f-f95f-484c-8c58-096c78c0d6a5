package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redxun.common.model.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 考勤记录表
 * </p>
 *
 * <AUTHOR> @since 2020-11-22
 */

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "InspectAttendanceRecord", description = "考勤对象")
public class InspectAttendanceRecord extends SuperEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.UUID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 建筑物id
     */
    @ApiModelProperty(value = "建筑物id")
    private String buildId;
    /**
     * 建筑物名称
     */
    @ApiModelProperty(value = "建筑物名称")
    private String buildName;
    /**
     * 考勤用户id
     */
    @ApiModelProperty(value = "考勤用户id")
    private String userId;
    /**
     * 考勤用户姓名
     */
    @ApiModelProperty(value = "考勤用户姓名")
    private String userName;
    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private String orgId;
    /**
     * 组织机构姓名
     */
    @ApiModelProperty(value = "组织机构姓名")
    private String orgName;
    /**
     * 上班打卡时间
     */
    @ApiModelProperty(value = "上班打卡时间")
    private String clockOnTime;
    /**
     * 下班打卡时间
     */
    @ApiModelProperty(value = "下班打卡时间")
    private String clockOffTime;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    @TableField("TENANT_ID_")
    private String tenantId;
    /*    *//**
     * 创建部门ID
     *//*
	@ApiModelProperty(value = "创建部门ID" )
	@TableField("CREATE_DEP_ID_")
	private String createDepId;
    *//**
     * 创建人ID
     *//*
	@ApiModelProperty(value = "创建人ID" )
	@TableField("CREATE_BY_")
	private String createBy;
    *//**
     * 创建时间
     *//*
	@ApiModelProperty(value = "创建时间" )
	@TableField("CREATE_TIME_")
	private LocalDateTime createTime;
    *//**
     * 更新人ID
     *//*
	@ApiModelProperty(value = "更新人ID" )
	@TableField("UPDATE_BY_")
	private String updateBy;
    *//**
     * 更新时间
     *//*
	@ApiModelProperty(value = "更新时间" )
	@TableField("UPDATE_TIME_")
	private LocalDateTime updateTime;*/
    /**
     * 打卡地址
     */
    @ApiModelProperty(value = "打卡地址")
    private String recordAddress;
    /**
     * 考勤状态 0-正常，1-异常
     */
    @ApiModelProperty(value = "考勤状态 0-正常，1-异常")
    private String recordStatus;
    /**
     * 0-维保人员，1-跑点人员
     */
    @ApiModelProperty(value = "0-维保人员，1-跑点人员")
    private String userType;
    /**
     * 所属维保团队
     */
    @ApiModelProperty(value = "所属维保团队")
    private String teamName;
    /**
     * 考勤日期
     */
    @ApiModelProperty(value = "考勤日期")
    private String recordDate;

    @ApiModelProperty(value = "下班打卡地址")
    private String offRecordAddress;

    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
}
