package com.redxun.fire.core.entity.wzt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> guo shiqi
 * @createTime : 2022/12/29
 * @Description :
 */
@NoArgsConstructor
@Data
public class ProjectResponse implements Serializable {

    /**
     * success
     */
    @JsonProperty("success")
    private Boolean success;
    /**
     * message
     */
    @JsonProperty("message")
    private String message;
    /**
     * detailMsg
     */
    @JsonProperty("detailMsg")
    private String detailMsg;
    /**
     * show
     */
    @JsonProperty("show")
    private Boolean show;
    /**
     * code
     */
    @JsonProperty("code")
    private Integer code;
    /**
     * data
     */
    @JsonProperty("data")
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * createTime
         */
        @JsonProperty("createTime")
        private String createTime;
        /**
         * tenantId
         */
        @JsonProperty("tenantId")
        private String tenantId;
        /**
         * companyId
         */
        @JsonProperty("companyId")
        private String companyId;
        /**
         * id
         */
        @JsonProperty("id")
        private String id;
        /**
         * business
         */
        @JsonProperty("business")
        private String business;
        /**
         * businessName
         */
        @JsonProperty("businessName")
        private String businessName;
        /**
         * city
         */
        @JsonProperty("city")
        private String city;
        /**
         * cityName
         */
        @JsonProperty("cityName")
        private String cityName;
        /**
         * dept
         */
        @JsonProperty("dept")
        private String dept;
        /**
         * deptName
         */
        @JsonProperty("deptName")
        private String deptName;
        /**
         * group
         */
        @JsonProperty("group")
        private String group;
        /**
         * groupName
         */
        @JsonProperty("groupName")
        private String groupName;
        /**
         * piazza
         */
        @JsonProperty("piazza")
        private String piazza;
        /**
         * piazzaName
         */
        @JsonProperty("piazzaName")
        private String piazzaName;
        /**
         * region
         */
        @JsonProperty("region")
        private String region;
        /**
         * regionName
         */
        @JsonProperty("regionName")
        private String regionName;
        /**
         * updateVersion
         */
        @JsonProperty("updateVersion")
        private Integer updateVersion;
        /**
         * level
         */
        @JsonProperty("level")
        private String level;
        /**
         * pkId
         */
        @JsonProperty("pkId")
        private String pkId;

        @JsonProperty("projectId")
        private String projectId;

        @JsonProperty("projectName")
        private String projectName;
    }
}
