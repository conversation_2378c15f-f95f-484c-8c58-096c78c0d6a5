
/**
 * <pre>
 *
 * 描述：厨房监测设备基础信息实体类定义
 * 表:kitchen_device_basic_info
 * 作者：cgd
 * 邮箱: 
 * 日期:2025-03-07 16:42:39
 * 版权：万达
 * </pre>
 */
package com.redxun.fire.core.kitchen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.redxun.common.base.entity.BaseExtEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


@Setter
@Getter
@Accessors(chain = true)
@TableName(value = "kitchen_device_basic_info")
public class KitchenDeviceBasic extends BaseExtEntity<String> {

    @JsonCreator
    public KitchenDeviceBasic() {
    }

    //主键 主机号 16进制(8位)
    @TableId(value = "id",type = IdType.INPUT)
	private String id;
    @TableField(value = "host_id")
    private String hostId;

    //油温预警温度
    @TableField(value = "alarm_oil_temperature")
    private String alarmOilTemperature;
    //动火离人报警时间(s)
    @TableField(value = "alarm_time")
    private String alarmTime;
    //环境温度
    @TableField(value = "ambient_temperature")
    private String ambientTemperature;
    //电源状态
    @TableField(value = "battery_state")
    private String batteryState;
    //建筑id
    @TableField(value = "building_id")
    private String buildingId;
    //厨自灭动作信号（0：报警、动作，1：正常）
    @TableField(value = "czm_action_signal")
    private String czmActionSignal;
    //厨自灭接入信号（0：未接入；1：已接入）
    @TableField(value = "czm_join_signal")
    private String czmJoinSignal;
    //厨自灭断电信号（0：断电，1：正常）
    @TableField(value = "czm_outage_signal")
    private String czmOutageSignal;
    //点磁阀动作信号（0：报警、动作，1：正常）
    @TableField(value = "dcf_action_signal")
    private String dcfActionSignal;
    //动火探测1状态（0：报警、动作，1：正常）
    @TableField(value = "dh_detection_signal_one")
    private String dhDetectionSignalOne;
    //动火探测3状态（0：报警、动作，1：正常）
    @TableField(value = "dh_detection_signal_three")
    private String dhDetectionSignalThree;
    //动火探测2状态（0：报警、动作，1：正常）
    @TableField(value = "dh_detection_signal_two")
    private String dhDetectionSignalTwo;
    //动火1接入状态（0：未接入；1：已接入）
    @TableField(value = "dh_join_signal_one")
    private String dhJoinSignalOne;
    //动火3接入状态（0：未接入；1：已接入）
    @TableField(value = "dh_join_signal_three")
    private String dhJoinSignalThree;
    //动火2接入状态（0：未接入；1：已接入）
    @TableField(value = "dh_join_signal_two")
    private String dhJoinSignalTwo;
    //动火离人报警信号（0：报警、动作，1：正常）
    @TableField(value = "dhlr_alarm_signal")
    private String dhlrAlarmSignal;
    //动火离人接入信号（0：未接入；1：已接入）
    @TableField(value = "dhlr_join_signal")
    private String dhlrJoinSignal;
    //动火离人断电信号（0：断电，1：正常）
    @TableField(value = "dhlr_outage_signal")
    private String dhlrOutageSignal;
    //故障状态 30 故障/31 故障恢复
    @TableField(value = "fault_state")
    private String faultState;
    //主机类型（0:A版;1:B1;2:B2;3:B3）
    @TableField(value = "host_type")
    private String hostType;
    //离人探测1状态（0：报警、动作，1：正常）
    @TableField(value = "lr_detection_signal_one")
    private String lrDetectionSignalOne;
    //离人探测2状态（0：报警、动作，1：正常）
    @TableField(value = "lr_detection_signal_two")
    private String lrDetectionSignalTwo;
    //离人1接入状态（0：未接入；1：已接入）
    @TableField(value = "lr_join_signal_one")
    private String lrJoinSignalOne;
    //离人3接入状态（0：未接入；1：已接入）
    @TableField(value = "lr_join_signal_three")
    private String lrJoinSignalThree;
    //离人2接入状态（0：未接入；1：已接入）
    @TableField(value = "lr_join_signal_two")
    private String lrJoinSignalTwo;
    //油温最高温度
    @TableField(value = "max_oil_temperature")
    private String maxOilTemperature;
    //油炸炉油温
    @TableField(value = "oil_temperature")
    private String oilTemperature;
    //在线状态（1:在线；2：离线）
    @TableField(value = "online_state")
    private String onlineState;
    //断电状态 50 断电/51 断电恢复
    @TableField(value = "power_state")
    private String powerState;
    //燃气报警接入信号（0：未接入；1：已接入）
    @TableField(value = "rq_alarm_join_signal")
    private String rqAlarmJoinSignal;
    //燃气报警信号（0：报警、动作，1：正常）
    @TableField(value = "rq_alarm_signal")
    private String rqAlarmSignal;
    //燃气断电信号（0：断电，1：正常）
    @TableField(value = "rq_outage_signal")
    private String rqOutageSignal;
    //事故风机接入信号（0：未接入；1：已接入）
    @TableField(value = "sgfj_join_signal")
    private String sgfjJoinSignal;
    //事故风机信号（0：报警、动作，1：正常）
    @TableField(value = "sgfj_signal")
    private String sgfjSignal;
    //脱扣反馈（0：报警、动作，1：正常）
    @TableField(value = "tk_signal")
    private String tkSignal;

    @TableField(value = "feedback_time")
    private String feedbackTime;

    @TableField(value = "czm_fault_state")
    private String czmFaultState;

    @TableField(value = "dhlr_fault_state")
    private String dhlrFaultState;

    @TableField(value = "rq_fault_state")
    private String rqFaultState;

    @TableField(value = "czm_fault_text")
    private String czmFaultText;

    @TableField(value = "dhlr_fault_text")
    private String dhlrFaultText;

    @TableField(value = "rq_fault_text")
    private String rqFaultText;

    private String signalLevel;

    @Override
    public String getPkId() {
        return id;
    }

    @Override
    public void setPkId(String pkId) {
        this.id=pkId;
    }


    /**
    生成子表属性的Array List
    */

}



