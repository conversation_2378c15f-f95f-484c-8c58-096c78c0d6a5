package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.MaintenancePlan;
import com.redxun.fire.core.pojo.dto.AppointmentApplyDto;
import com.redxun.fire.core.pojo.dto.MaintenancePlanDto;
import com.redxun.fire.core.pojo.dto.MaintenancePlanSysDto;
import com.redxun.fire.core.pojo.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维保计划表_new Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2020-11-07
 */
@Mapper
public interface MaintenancePlanMapper extends BaseMapper<MaintenancePlan> {

    IPage<MaintenancePlan> page(Page<MaintenancePlan> page, @Param("param") Map<String, String> param, @Param("buildingIdList") List<String> buildingIdList);

    IPage<MaintenancePlanPageVo> pageNew(Page<MaintenancePlan> page, @Param("param") Map<String, String> param, @Param("buildingIdList") List<String> buildingIdList);

    List<MaintenancePlan> getMaintenancePlan(@Param("param") Object object);

    List<YearMonthVo> getMaintenancePlanByWbId(@Param("wbId") String wbId);

    List<AppointmentApplyVo> getMaintenancePlanYear(@Param("param") MaintenancePlanSysDto maintenancePlanSysDto);

    List<MaintenancePlan> getAppTestPlanList(@Param("param") AppointmentApplyDto appointmentApplyDto);

    List<MaintenancePlan> selectMaintenanceCheckPlan(@Param("param") MaintenancePlanDto maintenancePlanDto);

    IPage<MaintenancePlan> getWebTestPlanPage(Page<MaintenancePlan> page, @Param("param") Map<String, String> params, @Param("buildingIdList") List<String> buildingIdList);

    IPage<DevicePointInfoVo> getWebTestPointPage(Page<MaintenancePlan> page, @Param("param") Map<String, String> params);

    List<MaintenancePlan> selectMaintenanceReport(@Param("param") MaintenancePlanDto maintenancePlanDto);

    List<MaintenancePlanDetailListVo> getPlanSys(@Param("scheduling") String scheduling, @Param("buildingId") String buildingId, @Param("effectiveTime") String effectiveTime);

    IPage<MaintenancePlan> getAppTestPlanPage(Page<MaintenancePlan> page, @Param("param") Map<String, String> param, @Param("buildingIdList") List<String> buildingListByDeptId);

    IPage<BaseBuildInfoVo> maintenancePlanPage(Page<MaintenancePlan> page, @Param("param") Map<String, String> params, @Param("buildingIdList") List<String> buildingIdList);

    List<BaseBuildInfoVo> exportMaintenancePlan(@Param("param") Map<String, Object> param, @Param("buildingIdList") List<String> buildingIdList);

    Long getTimeByPlanId(@Param("planId") String id);

    List<MaintenancePlan> exportLinkTestPlan(@Param("param") MaintenancePlanSysDto maintenancePlanSysDto);

    List<MaintenancePlan> selectScore(@Param("buildingId") String buildingId, @Param("time") String time);

    //测试用
    List<MaintenancePlan> getMaintenancePlanTmp(@Param("param") MaintenancePlanDto maintenancePlanDto);

    MaintenancePlan getMaintenancePlanByScopeId(@Param("scopeTestId") String scopeTestId);

    MaintenancePlan planProgress(@Param("effectiveTime") String effTime, @Param("buildingId") String buildingId);

    Double countPercentageYear(@Param("buildingId") String buildingId, @Param("startTime") Date startTime, @Param("scheduling") String scheduling);

    void updateByYear(@Param("buildingId") String buildingId, @Param("startTime") Date startTime, @Param("scheduling") String scheduling, @Param("yearCent") Double yearCent);

    IPage<BaseBuildInfoVo> getTimeOutPlan(Page<MaintenancePlan> page, @Param("param") Map<String, String> param, @Param("buildingIdList") List<String> buildingIdList);

    IPage<MaintenancePlanVo> getNewWebTestPlanPage(Page<MaintenancePlanVo> page, @Param("param") Map<String, String> params, @Param("buildingIdList") List<String> buildingIdList);

    List<String> findPlanListByStartTime(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("planId") String planId);

    void deleteAppointmentApScope(@Param("planId") String planId);

    void deletePointBatch(@Param("planId") String planId);

    void deleteBatch(@Param("planId") String planId);

    void deleteMaintenanceScopeTest(@Param("planId") String planId);

    void deleteMaintenanceItmesRPlan(@Param("planId") String planId);

    void deleteMaintenancePlan(@Param("planId") String planId);

    MaintenancePlan getMaintenancePlanDataByScopeId(@Param("scopeTestId") String scopeTestId, @Param("effectiveTime") String effectiveTime);

    IPage<MaintenancePlan> getMaintenancePlanTmpOnPage(Page<MaintenancePlan> page, @Param("param") MaintenancePlanDto maintenancePlanDto);

    IPage<MaintenancePlan> selectMaintenanceCheckPlanOnPage(Page<MaintenancePlan> page, @Param("param") MaintenancePlanDto maintenancePlanDto);

    IPage<MaintenancePlan> selectMaintenanceReportOnPage(Page<MaintenancePlan> page, @Param("param") MaintenancePlanDto maintenancePlanDto);

    MaintenancePlan getMainDataById(@Param("id") String maintenancePlanId);

    int selectPointBatch(String planId);


    int insertPointBatch(String planId);

    int insertMaintenanceScopeTest(String planId);

    int insertAppointmentApScope(String planId);

    int insertMaintenanceItmesRPlan(String planId);

    List<String> findAppointmentPointByEffectiveTime(@Param("effectiveTime") String effectiveTime);

    int insertAppointmentPoint(String id);

    int insertBatchNew(String planId);

    List<Map<String, String>> getSystemNameByPlanIdList(@Param("planIdList") List<String> collect);

    Integer getCheckPlan(@Param("buildingId") String buildingId,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime,
                         @Param("checkResult") String checkResult);







}
