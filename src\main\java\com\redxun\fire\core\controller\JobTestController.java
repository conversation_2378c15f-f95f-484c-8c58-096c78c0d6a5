package com.redxun.fire.core.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.redxun.fire.core.consts.RedisKeys;
import com.redxun.fire.core.job.AutoDetectJob;
import com.redxun.fire.core.job.BaseLocalHostInfoAutoRefreshJob;
import com.redxun.fire.core.job.FaultFireStatisticsJob;
import com.redxun.fire.core.job.FaultTimeTaskHandler;
import com.redxun.fire.core.job.SuspectedFireHandler;
import com.redxun.fire.core.server.schedule.BaseBuildingStatSchedule;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:定时任务测试
 * @date 2024/5/22 7:21 PM
 */
@Slf4j
@RestController
@RequestMapping("/testJob")
public class JobTestController {

    @Autowired
    AutoDetectJob autoDetectJob;

    @Autowired
    FaultFireStatisticsJob faultFireStatisticsJob;

    @Autowired
    FaultTimeTaskHandler faultTimeTaskHandler;

    @Autowired
    SuspectedFireHandler suspectedFireHandler;

    @Autowired
    BaseBuildingStatSchedule baseBuildingStatSchedule;

    @GetMapping("/autoDetectJob")
    void autoDetectJobTest() throws Exception {
        //自动查岗任务
        //autoDetectJob.execute();

        //单店监控台故障率误报率统计
        //faultFireStatisticsJob.execute();

        //生成回路故障
        //faultTimeTaskHandler.execute();

        //疑似火警
//        suspectedFireHandler.execute();

        //建筑物误报点位逻辑验证
        baseBuildingStatSchedule.startScheduleTask(null);
    }

    @Autowired
    BaseLocalHostInfoAutoRefreshJob baseLocalHostInfoAutoRefreshJob;

    @Autowired
    RedisUtils redisUtils;

    @GetMapping("/baseLocalHostInfoAutoRefreshJob")
    void baseLocalHostInfoAutoRefreshJob() {
        baseLocalHostInfoAutoRefreshJob.execute();
    }

    @GetMapping("/setRefreshBuild")
    void setRefreshBuild(@RequestParam String buildId) {
        try {
            if (StrUtil.isBlank(buildId)) {
                return;
            }
            final val o = redisUtils.get(RedisKeys.LOCAL_HOST_INIT_BUILD_LIST);
            List<String> list = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(o)) {
                list = (List<String>)o;
            }
            list.add(buildId);
            redisUtils.set(RedisKeys.LOCAL_HOST_INIT_BUILD_LIST, list);
        } catch (Exception e) {
            log.error("设置初始化本地主机数据建筑异常：", e);
        }
    }

}
