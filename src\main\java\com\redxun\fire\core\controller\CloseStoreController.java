package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.redxun.common.annotation.MethodDefine;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.constant.HttpMethodConstants;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.StatCloseStoreReal;
import com.redxun.fire.core.influxdb.entity.CloseInfluxData;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.qo.CloseStorePointQO;
import com.redxun.fire.core.pojo.vo.CloseStorePointVO;
import com.redxun.fire.core.pojo.vo.CloseStoreVPointVo;
import com.redxun.fire.core.service.alarm.CloseStoreService;
import com.redxun.fire.core.service.alarm.IStatCloseStoreExpectionService;
import com.redxun.fire.core.service.alarm.IStatCloseStoreRealService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 闭店监测功能
 */
@RestController
@RequestMapping("/fire/close-store")
public class CloseStoreController {

    @Resource
    IStatCloseStoreRealService statCloseStoreRealService;

    @Resource
    IStatCloseStoreExpectionService statCloseStoreExpectionService;

    @Resource
    CloseStoreService closeStoreService;

    @Resource
    private OrgMiddleServiceImpl orgMiddleServiceImpl;


    /**
     * 闭店监测-广场列表页面
     * 获取闭店监测广场统计信息
     *
     * @param buildId
     * @return
     */
    @GetMapping("/getCloseStoreDeviceData")
    public JsonResult getCloseStoreDeviceData(HttpServletRequest request, @RequestParam(value = "buildId", required = false) String buildId) {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("buildId", buildId);
        JsonResult result = JsonResult.Success();
        Map<String, Object> data = statCloseStoreRealService.getCloseStoreDeviceData(request, paramsMap);
        return JsonResult.getSuccessResult(data);
    }

    /**
     * 闭店监测-单店页面
     * 通过广场id获取闭店监测广场统计信息
     *
     * @param buildId
     * @return
     */
    @GetMapping("/getStatisticbyBuildingId")
    public JsonResult getStatisticbyBuildingId(@RequestParam(value = "buildId", required = false) String buildId) {
        StatCloseStoreReal data = statCloseStoreRealService.getStatisticbyBuildingId(buildId);
        return JsonResult.getSuccessResult(data);
    }

    /**
     * 闭店监测-集团页面-监测模块
     * 获取集团闭店监测统计信息
     *
     * @return
     */
    @GetMapping("/getStatisticByGroup")
    public JsonResult getStatisticByGroup() {
        Map<String, Integer> statisticByGroup = statCloseStoreRealService.getStatisticByGroup();
        return JsonResult.getSuccessResult(statisticByGroup);
    }

    /**
     * 闭店监测-集团页面-设备异常总数分布top
     * 获取集团闭店监测统计信息
     *
     * @return
     */
    @GetMapping("/getStatisticDeviceExceptionByGroup")
    public JsonResult getStatisticDeviceExceptionByGroup() {
        List<StatCloseStoreReal> list = statCloseStoreRealService.getStatisticDeviceExceptionByGroup();
        return JsonResult.getSuccessResult(list);
    }

    /**
     * 闭店监测-集团页面-异常模块数量统计
     * 获取集团闭店监测统计信息
     *
     * @return
     */
    @GetMapping("/getStatisticExceptionCountByGroup")
    public JsonResult getStatisticExceptionCountByGroup() {
        Map<String, Integer> map = statCloseStoreRealService.getStatisticExceptionCountByGroup();
        return JsonResult.getSuccessResult(map);
    }

    @MethodDefine(title = "获取今日闭店用电异常项目统计", path = "/getMerchantExceptionTodayGroup", method = HttpMethodConstants.GET)
    @ApiOperation(value = "获取今日闭店用电异常项目")
    @GetMapping("getMerchantExceptionTodayGroup")
    public JsonResult getMerchantExceptionTodayGroup() {
        return orgMiddleServiceImpl.getMerchantExceptionTodayGroup();
    }

    /**
     * 闭店监测-用于大屏接口-替换中台建筑id
     * 通过广场id获取闭店监测广场统计信息，前端传参为piazzaId中台id
     *
     * @param buildId
     * @return
     */
    @GetMapping("/getBuildingIdByMiddleId")
    public JsonResult getBuildingIdByMiddleId(@RequestParam(value = "buildId") String buildId) {
        String buildingIdByMiddleId = statCloseStoreRealService.getBuildingIdByMiddleId(buildId);
        JsonResult result = JsonResult.Success();
        result.setData(buildingIdByMiddleId);
        return JsonResult.getSuccessResult(result);
    }

    /**
     * 获取闭店监测按异常类型排行信息
     * 参考 getWaterLiquidAbnormalRankList
     */
    @PostMapping("/getCloseStoreAbnormalRankList")
    public JsonResult getCloseStoreAbnormalRankList(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        Map<String, Object> data = statCloseStoreRealService.getCloseStoreAbnormalRankList(request, paramsMap);
        return JsonResult.getSuccessResult(data);
    }

    /**
     * 获取闭店监测点位列表
     * getPumpMonitorStatusInfo
     */
    @PostMapping("/getPointList")
    public JsonResult getPointList(@RequestBody CloseStorePointQO qo) {

        List<CloseStorePointVO> list = closeStoreService.getPointList(qo);
        return JsonResult.getSuccessResult(list);
    }

    /**
     * 获取A2型点位列表
     */
    @PostMapping("/getA2PointList")
    public JsonResult getA2PointList(@RequestBody CloseStorePointQO qo) {
        List<CloseStorePointVO> list = closeStoreService.getA2PointList(qo);
        return JsonResult.getSuccessResult(list);
    }

    /**
     * 获取A2型设备数据详情
     */
    @PostMapping("/getA2PointDataInfo")
    public JsonResult getA2PointDataInfo(@RequestBody QueryData queryData) {
        return closeStoreService.getA2PointDataInfo(queryData);
    }

    /**
     * 点位注册
     * 参考水泵 pumpRegister
     * 设备类型写死
     */
    @PostMapping("/pointRegister")
    public JsonResult pointRegister(@RequestBody @Validated CloseStorePointQO qo) {
        return closeStoreService.pointRegister(qo);
    }

    /**
     * 根据id查询点位信息
     *
     * @param qo
     * @return
     */
    @PostMapping("/getPointInfoById")
    public JsonResult getPointInfoById(@RequestBody CloseStorePointQO qo) {
        CloseStorePointVO date = closeStoreService.getPointInfoById(qo);
        return JsonResult.getSuccessResult(date);
    }

    /**
     * 编辑点位信息
     * 参考水泵 editPumpPointInfoById
     */
    @PostMapping("/editPointInfoById")
    public JsonResult editPointInfoById(@RequestBody @Validated CloseStorePointQO qo) {
        return closeStoreService.editPointInfoById(qo);
    }

    /**
     * 删除点位信息
     * 参考水泵 /pressure/deletePointInfo
     */
    @PostMapping("/deletePointInfo")
    public JsonResult deletePointInfo(@RequestBody CloseStorePointQO qo) {
        return closeStoreService.deletePointInfo(qo);
    }



    /**
     * 获取闭店监测设备异常统计
     */
    @GetMapping("/getExceptionStatistics")
    public ResultMsg getExceptionStatistics(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取闭店监测异常统计信息成功", statCloseStoreExpectionService.getCloseStoreStatistic(request), 200);
    }

    /**
     * 获取闭店监测设备异常列表
     */
    @GetMapping("/getExceptionList")
    public ResultMsg getExceptionList(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取闭店监测异常信息成功", statCloseStoreExpectionService.getCloseStoreExpHandleInfo(request), 200);
    }

    /**
     * 获取闭店监测设备异常列表
     */
    @GetMapping("/getExceptionListForApp")
    public ResultMsg getExceptionListForApp(HttpServletRequest request) {
        return ResultMsg.getResultMsg("获取闭店监测列表信息成功", statCloseStoreExpectionService.getCloseStoreExpHandleInfoForApp(request), 200);
    }

    /**
     * 闭店监测异常处理
     */

    @PostMapping("/updateException")
    public ResultMsg updateException(@RequestBody Map<String, Object> map, HttpServletRequest request) throws Exception {
        return statCloseStoreExpectionService.updateCloseStoreExpHandleInfo(map, request);
    }


    @PostMapping("/updateExceptionNew")
    public ResultMsg updateExceptionNew(@RequestBody Map<String, Object> map, HttpServletRequest request) throws Exception {
        return statCloseStoreExpectionService.updateCloseStoreExpHandleInfoNew(map, request);
    }

    /**
     * app端获取闭店监测点位列表
     */
    @PostMapping("/getPointListForApp")
    public JsonResult getPointListForApp(@RequestBody CloseStorePointQO qo) {
        IPage<CloseStorePointVO> pageInfo = closeStoreService.getPointListForApp(qo);
        JsonPageResult jsonResult = JsonPageResult.getSuccess("");
        jsonResult.setPageData(pageInfo);
        return jsonResult;
    }
    @PostMapping("/getPointListForAppNew")
    public JsonResult getPointListForAppNew(@RequestBody CloseStorePointQO qo) {
        IPage<CloseStoreVPointVo> pageInfo = closeStoreService.getPointListForAppNew(qo);
        JsonPageResult jsonResult = JsonPageResult.getSuccess("");
        jsonResult.setPageData(pageInfo);
        return jsonResult;
    }

    /**
     * 查询闭店状态历史数据
     *
     * @param qo
     * @return
     */
    @PostMapping("/queryCloseStoreStatusHistoryData")
    public JsonResult queryCloseStoreStatusHistoryData(@RequestBody CloseStorePointQO qo) throws ParseException {
        return JsonResult.getSuccessResult(closeStoreService.queryCloseStoreStatusHistoryData(qo));
    }

    @GetMapping("/getCloseStoreTimeRemind")
    public ResultMsg getCloseStoreTimeRemind(HttpServletRequest request) {
        statCloseStoreRealService.closeStoreTimeRemind();
        return ResultMsg.getResultMsg("获取闭店监测信息成功",  200);
    }

    /**
     * 批量闭店设备点位导入
     *
     * @param file
     * @return
     */
    @PostMapping("/batchImportPointInfo")
    public JsonResult batchImportPointInfo(@RequestParam("file") MultipartFile file, @RequestParam("buildingId") String buildingId) {
        if (StringUtils.isEmpty(buildingId)) {
            throw new RuntimeException("建筑物id不能为空");
        }
        return closeStoreService.batchImportPointInfo(file, buildingId) ?
                JsonResult.Success() : JsonResult.getFailResult("导入失败");
    }

    /**
     * 获取闭店设备-分页查询
     */
    @PostMapping("/getPointPage")
    public JsonResult getPointPage(@RequestBody CloseStorePointQO qo) {
        IPage<CloseStorePointVO> pageInfo = closeStoreService.getPointPage(qo);
        JsonPageResult jsonResult = JsonPageResult.getSuccess("");
        jsonResult.setPageData(pageInfo);
        return jsonResult;
    }

    /**
     * 获取闭店设备-导出
     */
    @PostMapping("/exportForPointPage")
    public void exportForPointPage(HttpServletResponse response, @RequestBody CloseStorePointQO qo) throws IOException {
        closeStoreService.exportForPointPage(qo, response);
    }

    @MethodDefine(title = "获取今日闭店用电异常商户", path = "/getMerchantExceptionToday", method = HttpMethodConstants.GET)
    @ApiOperation(value = "获取今日闭店用电异常商户")
    @GetMapping("getMerchantExceptionToday")
    public JsonResult getMerchantExceptionToday(@RequestParam String piazza) {
        return JsonResult.Success().setData(orgMiddleServiceImpl.getMerchantExceptionToday(piazza));
    }

    @MethodDefine(title = "获取7天或月内闭店用电异常商户统计", path = "/getMerchantExceptionCount", method = HttpMethodConstants.GET)
    @ApiOperation(value = "获取7天或月内闭店用电异常商户统计")
    @GetMapping("getMerchantExceptionCount")
    public JsonResult getMerchantExceptionCount(@RequestParam String piazza,@RequestParam(required = false) String type) {
        return JsonResult.Success().setData(orgMiddleServiceImpl.getMerchantExceptionCount(piazza, type));
    }

    @MethodDefine(title = "获取7天或月内闭店用电异常项目统计", path = "/getProjectExceptionCount", method = HttpMethodConstants.GET)
    @ApiOperation(value = "获取7天或月内闭店用电异常项目统计")
    @GetMapping("getProjectExceptionCount")
    public JsonResult getProjectExceptionCount(@RequestParam(required = false) String type) {
        return orgMiddleServiceImpl.getProjectExceptionCount(type);
    }


    /**
     * 小程序端合闸开关
     * getPumpMonitorStatusInfo
     */
    @GetMapping("/gateOnOff/{hostId}/{gateStatus}")
    public JsonResult gateOnOff(@PathVariable("hostId") String hostId,
                                @PathVariable("gateStatus") String gateStatus) {
        return closeStoreService.gateOnOff(hostId,gateStatus);

    }

    @MethodDefine(title = "获取广场下商户数据", path = "/getMerchantSelect", method = HttpMethodConstants.GET)
    @ApiOperation(value = "获取广场下商户数据")
    @GetMapping("getMerchantSelect")
    public JsonResult getMerchantSelect(@RequestParam String piazza, @RequestParam(required = false) String merchantType) {
        return JsonResult.Success().setData(orgMiddleServiceImpl.getMerchantListByOrgId(piazza, merchantType));
    }

    /**
     * 延迟报警
     */
    @PostMapping("/delayAlarm")
    public JsonResult delayAlarm(@RequestBody QueryData queryData) {
        return closeStoreService.delayAlarm(queryData);
    }

    /**
     * 获取获取商户店长信息
     */
    @PostMapping ("/getMerchantManagerInfo")
    public JsonResult getMerchantManagerInfo(@RequestBody QueryData queryData) {
        return JsonResult.Success().setData(orgMiddleServiceImpl.getMerchantManagerInfo(queryData));
    }

}
