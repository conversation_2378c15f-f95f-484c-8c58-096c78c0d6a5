package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.pojo.dto.FaceRecognitionResponseDto;
import com.redxun.fire.core.pojo.dto.FaceRecognitionResultDto;
import com.redxun.fire.core.service.face.FaceRecognitionService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project hdwa-xf-fire
 * @description 人脸识别web
 * @date 2024/10/15 09:55:43
 */
@Slf4j
@RestController
@RequestMapping("/face/recognition/public")
public class FaceRecognitionController {

    @Autowired
    private FaceRecognitionService faceRecognitionService;

    @PostMapping("/save")
    @ApiOperation(value = "保存人脸识别结果")
    public JsonResult saveFaceRecognition(@RequestBody List<FaceRecognitionResultDto> recognitionResultDtos) {
        faceRecognitionService.addFaceRecognitionResult(recognitionResultDtos);
        return JsonResult.Success();
    }

    @GetMapping("/queryAlarm")
    @ApiOperation(value = "保存人脸识别结果")
    public JsonResult queryFaceRecognitionResult(@RequestParam String projectId) {
        JsonResult<List<FaceRecognitionResponseDto>> result = JsonResult.Success();
        List<FaceRecognitionResponseDto> faceRecognitionResponseDtos = faceRecognitionService.queryFaceRecognitionResult(projectId);
        result.setData(faceRecognitionResponseDtos);
        return result;
    }


}
