package com.redxun.fire.core.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gexin.fastjson.JSON;
import com.redxun.api.model.param.FireQueryBuildFloorParam;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.entity.wzt.ProjectResponse;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @program jpaas
 * @description 同步楼层信息
 * @create 2024-06-12
 **/
@Slf4j
@Service
public class BuildingFloorSyncJob extends IJobHandler {
    @Autowired
    private IBaseBuildingFloorService baseBuildingFloorService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;
    @Resource
    private RedisUtils redisUtils;
    @Override
    @XxlJob("buildingFloorSyncJob")
    public void execute() throws Exception {
        log.info("同步楼层信息任务开始");
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.isNotNull("project_id");
        List<BaseBuilding> baseBuildingList=baseBuildingService.getBaseMapper().selectList(queryWrapper);
        if(baseBuildingList!=null&&baseBuildingList.size()>0){
            log.info("建筑信息------------------------------baseBuildingList："+baseBuildingList.size());
            Date endTime=new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.HOUR_OF_DAY, -3);
            Date startTime = calendar.getTime();
            for(BaseBuilding baseBuilding:baseBuildingList){
                FireQueryBuildFloorParam fireQueryBuildFloorParam=new FireQueryBuildFloorParam();
                fireQueryBuildFloorParam.setProjectId(baseBuilding.getProjectId());
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String stime = simpleDateFormat.format(startTime);
                String etime = simpleDateFormat.format(endTime);
                fireQueryBuildFloorParam.setStartTime(stime);
                fireQueryBuildFloorParam.setEndTime(etime);
                JsonResult jsonResult=orgMiddleService.findFloorData(fireQueryBuildFloorParam);
                log.info("刷新楼层调用万中台返回---------------"+JSON.toJSONString(jsonResult));
                if(jsonResult.getSuccess()!=null&&jsonResult.getSuccess()){
                    JSONArray dataDTO=JSONArray.parseArray(com.alibaba.fastjson.JSON.toJSONString(jsonResult.getData()));
                    if(dataDTO!=null&&dataDTO.size()>0){
                        for(Object o:dataDTO){
                            List<BaseBuildingFloor> baseBuildingFloorList=new ArrayList<>();
                            JSONObject jsonObject=(JSONObject)o;
                            String wztConstructId=jsonObject.getString("id");//楼栋Id
                            String wztConstructName=jsonObject.getString("localName");//楼栋名称
                            JSONArray floorJson=jsonObject.getJSONArray("dwsObjectFloorList");
                            if(floorJson!=null&&floorJson.size()>0){
                                log.info("解析楼栋floorJson-----------------------"+JSON.toJSONString(floorJson));
                                for(Object floor:floorJson){
                                    JSONObject floorJsonObject=(JSONObject)floor;
                                    String wztFloorId=floorJsonObject.getString("id");
                                    String wztFloorName=floorJsonObject.getString("localName");
                                    String sn=floorJsonObject.getString("sn");
                                    Date createTime=floorJsonObject.getDate("createTime");
                                    Date updateTime=floorJsonObject.getDate("updateTime");
                                    String createBy=floorJsonObject.getString("createBy");
                                    JSONArray xfFloorIdList=floorJsonObject.getJSONArray("fireFloorIdSet");
                                    if(xfFloorIdList!=null&&xfFloorIdList.size()>0){
                                        log.info("解析楼层存在消防floorJson-----------------------"+JSON.toJSONString(xfFloorIdList));
                                        for(Object xfFloorId:xfFloorIdList){
                                            BaseBuildingFloor baseBuildingFloor=baseBuildingFloorService.getById(xfFloorId.toString());
                                            if(baseBuildingFloor!=null){
                                                baseBuildingFloor.setWztFloorId(wztFloorId);
                                                baseBuildingFloor.setFloorName(wztFloorName);
                                                baseBuildingFloor.setFloorNum(Integer.parseInt(sn));
                                                baseBuildingFloor.setWztFloorName(wztFloorName);
                                                baseBuildingFloor.setWztConstructId(wztConstructId);
                                                baseBuildingFloor.setWztConstructName(wztConstructName);
                                                baseBuildingFloor.setCreateTime(createTime);
                                                baseBuildingFloor.setUpdateTime(updateTime);
                                                baseBuildingFloor.setCreateBy(createBy);
                                                //baseBuildingFloorService.saveOrUpdate(baseBuildingFloor);
                                                baseBuildingFloorList.add(baseBuildingFloor);
                                            }
                                        }
                                    }else{//消防不存在的楼层，新增
                                        log.info("解析楼层不存在消防floorJson-----------------------"+JSON.toJSONString(floor));
                                        QueryWrapper qq=new QueryWrapper();
                                        qq.eq("wzt_floor_id",wztFloorId);
                                        List<BaseBuildingFloor> baseBuildingFloorXF=baseBuildingFloorService.list(qq);
                                        if(baseBuildingFloorXF!=null&&baseBuildingFloorXF.size()>0){//存在就编辑
                                            for(BaseBuildingFloor bbf:baseBuildingFloorXF){
                                                bbf.setWztFloorName(wztFloorName);
                                                bbf.setFloorName(wztFloorName);
                                                bbf.setWztConstructName(wztConstructName);
                                                baseBuildingFloorList.add(bbf);
                                                //baseBuildingFloorService.getBaseMapper().updateById(bbf);
                                            }
                                        }else{
                                            BaseBuildingFloor baseBuildingFloor=new BaseBuildingFloor();
                                            baseBuildingFloor.setId(IdGenerator.getIdStr());
                                            baseBuildingFloor.setWztFloorId(wztFloorId);
                                            baseBuildingFloor.setWztFloorName(wztFloorName);
                                            baseBuildingFloor.setFloorName(wztFloorName);
                                            baseBuildingFloor.setWztConstructId(wztConstructId);
                                            baseBuildingFloor.setWztConstructName(wztConstructName);
                                            baseBuildingFloor.setCreateTime(createTime);
                                            baseBuildingFloor.setUpdateTime(updateTime);
                                            baseBuildingFloor.setCreateBy(createBy);
                                            if(StringUtils.isNotEmpty(sn)){
                                                baseBuildingFloor.setFloorNum(Integer.parseInt(sn));
                                            }
                                            baseBuildingFloor.setBuildingId(baseBuilding.getId());
                                            baseBuildingFloor.setTenantId(baseBuilding.getTenantId());
                                            log.info("插入楼层数据----------------------"+JSON.toJSONString(baseBuildingFloor));
                                            //baseBuildingFloorService.getBaseMapper().insert(baseBuildingFloor);
                                            baseBuildingFloorList.add(baseBuildingFloor);
                                        }
                                    }
                                }
                            }
                            if(baseBuildingFloorList!=null&&baseBuildingFloorList.size()>0){
                                log.info("同步楼层信息批量更新baseBuildingFloorList:"+baseBuildingFloorList.size());
                                baseBuildingFloorService.saveOrUpdateBatch(baseBuildingFloorList);
                            }
                        }
                    }

                }else{
                    log.info("调用万中台获取楼层信息失败jsonResult:"+ JSON.toJSONString(jsonResult));
                }
            }
        }

        log.info("同步楼层信息任务结束");
    }
}
