package com.redxun.fire.core.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.consts.CloseStoreRedisConstants;
import com.redxun.fire.core.consts.ConstantUtil;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.kitchen.entity.KitchenDeviceBasic;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.BaseDevicePointMapper;
import com.redxun.fire.core.mapper.CloseStoreModuleMapper;
import com.redxun.fire.core.mapper.MidMerchantMapper;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.pojo.qo.CloseStorePointQO;
import com.redxun.fire.core.pojo.qo.CloseStoreTimerQO;
import com.redxun.fire.core.pojo.qo.ReceiveCloseStoreTimerVo;
import com.redxun.fire.core.pojo.sync.param.SyncFireInfoQO;
import com.redxun.fire.core.pojo.vo.BaseManagementScoreVo;
import com.redxun.fire.core.pojo.vo.CloseStoreTimerVO;
import com.redxun.fire.core.service.alarm.CloseStoreService;
import com.redxun.fire.core.service.alarm.ICloseStoreTimerService;
import com.redxun.fire.core.service.building.BussinessService;
import com.redxun.fire.core.service.kitchen.KitchenDeviceRedisService;
import com.redxun.fire.core.service.monitor.IBuildingConsoleService;
import com.redxun.fire.core.utils.FastJSONUtils;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 各系统数据对接接口
 */
@Slf4j
@RestController
@RequestMapping("/dataSync")
public class DataSyncController {

    @Resource
    private IBuildingConsoleService buildingConsoleService;
    @Resource
    BussinessService bussinessService;
    @Autowired
    private BaseBuildingMapper baseBuildingMapper;
    @Autowired
    private BaseDevicePointMapper baseDevicePointMapper;

    @Autowired
    private CloseStoreService closeStoreService;


    /**
     * 履职需求-每天获取各个广场设施系统的综合管理评分在履职系统保存一份,含各分项得分
     */
    @GetMapping("/getBuildingManagementScore")
    public JsonResult<BaseManagementScoreVo> getBuildingConsoleScore(HttpServletRequest request) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonResult result = JsonResult.Success();

        List<BaseManagementScoreVo> list = null;
        try {
            list = buildingConsoleService.midBuildingManagementScore();
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        result.setData(list);
        return result;
    }

    /**
     * 履职需求-根据条件获取报警信息
     */
    @PostMapping("/getFireInfo")
    public JsonResult getFireInfo(HttpServletRequest request, @Validated @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonPageResult jsonResult = null;
        try {
            jsonResult = bussinessService.getFireInfo(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        return jsonResult;
    }

    /**
     * 履职需求-获取app火警信息列表接口
     */
    @PostMapping("/getFireInfoApp")
    public ResultMsg getFireInfoApp(HttpServletRequest request, @Validated @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return ResultMsg.getResultMsg("请求失败,Authorization无效!", 500);
//        }
        ResultMsg resultMsg = null;
        try {
            resultMsg = bussinessService.getFireInfoApp(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultMsg.getResultMsg("系统内部异常", 500);
        }
        return resultMsg;
    }


    /**
     * 履职需求-获取广场当日开闭店时间
     */
    @PostMapping("/getCloseStoreTime")
    public JsonResult getCloseStoreTime(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreTimeList(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        return jsonResult;
    }


    /**
     * 履职需求-获取商户店铺状态？用电状态
     */
    @PostMapping("/getCloseStoreStatus")
    public JsonResult getCloseStoreStatus(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreStatus(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        return jsonResult;
    }

    /**
     * 履职需求-批量获取商户店铺状态？用电状态
     */
    @PostMapping("/getCloseStoreStatusByIds")
    public JsonResult getCloseStoreStatusByIds(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreStatusByIds(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        return jsonResult;
    }

    /**
     * 履职需求-按商户下发闭店设备问询
     */
    @PostMapping("/sendCommandByMerchantId")
    public JsonResult sendCommandByMerchantId(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        // 请求头校验
//        String authorization = request.getHeader("Authorization");
//        if (StringUtils.isEmpty(authorization) || !authorization.startsWith("syscode_")) {
//            return JsonResult.Fail(500, "请求失败,Authorization无效!");
//        }
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.sendCommandByMerchantId(qo, request);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        return jsonResult;
    }

    /**
     * 履职需求-获取商户下设备信息
     */
    @PostMapping("/getCloseStoreInfo")
    public JsonResult getCloseStoreInfo(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        long start = System.currentTimeMillis();
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreInfo(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        long useTime = System.currentTimeMillis() - start;
        if (useTime > 1000) {
            log.info("闭店getCloseStoreInfo {}-耗时较长{}ms", qo.getMidMerchantId(), useTime);
        } else {
            log.info("闭店getCloseStoreInfo {}-耗时{}ms", qo.getMidMerchantId(), useTime);
        }
        return jsonResult;
    }


    /**
     * 履职需求-获取商户下设备信息
     */
    @PostMapping("/getCloseStoreInfoNew")
    public JsonResult getCloseStoreInfoNew(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {
        long start = System.currentTimeMillis();
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreInfoNew(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }
        long useTime = System.currentTimeMillis() - start;
        if (useTime > 1000) {
            log.info("闭店getCloseStoreInfo {}-耗时较长{}ms", qo.getMidMerchantId(), useTime);
        } else {
            log.info("闭店getCloseStoreInfo {}-耗时{}ms", qo.getMidMerchantId(), useTime);
        }
        return jsonResult;
    }
    /**
     * 履职需求-批量获取商户下设备信息
     */
    @PostMapping("/getCloseStoreInfoByIds")
    public JsonResult getCloseStoreInfoByIds(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {

        long start = System.currentTimeMillis();
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreInfoByIds(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }

        long useTime = System.currentTimeMillis() - start;
        if (useTime > 1000) {
            log.info("闭店getCloseStoreInfoByIds {}-耗时较长{}ms", qo.getMidMerchantId(), useTime);
        } else {
            log.info("闭店getCloseStoreInfoByIds {}-耗时{}ms", qo.getMidMerchantId(), useTime);
        }
        return jsonResult;
    }

    @PostMapping("/getCloseStoreInfoByIdsNew")
    public JsonResult getCloseStoreInfoByIdsNew(HttpServletRequest request, @RequestBody SyncFireInfoQO qo) {

        long start = System.currentTimeMillis();
        JsonResult jsonResult = null;
        try {
            jsonResult = bussinessService.getCloseStoreInfoByIdsNew(qo);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.Fail(500, "系统内部异常");
        }

        long useTime = System.currentTimeMillis() - start;
        if (useTime > 1000) {
            log.info("闭店getCloseStoreInfoByIds {}-耗时较长{}ms", qo.getMidMerchantId(), useTime);
        } else {
            log.info("闭店getCloseStoreInfoByIds {}-耗时{}ms", qo.getMidMerchantId(), useTime);
        }
        return jsonResult;
    }

    @Autowired
    private RedisUtils redisUtils;

    @Resource
    private CloseStoreModuleMapper closeStoreModuleMapper;

    @Resource
    private MidMerchantMapper midMerchantMapper;

    @Resource
    ICloseStoreTimerService closeStoreTimerService;

    @GetMapping(value = "getCloseStoreStatusByB/{dtuNo}")
    public JsonResult getCloseStoreStatusByB(@PathVariable String dtuNo) {
        try {
            // 获取点位状态
            String equipmentKey = CloseStoreRedisConstants.getEquipmentKey(dtuNo, 1);
            if (!redisUtils.exists(equipmentKey)) {
                return JsonResult.Fail(500, "设备不存在");
            }

            String equipmentValue = (String)redisUtils.get(equipmentKey);

            // 获取点位状态
            FarEastoneCache farEastoneCacheA = getFarEastoneCache(dtuNo, 1);
            FarEastoneCache farEastoneCacheB = getFarEastoneCache(dtuNo, 2);
            FarEastoneCache farEastoneCacheC = getFarEastoneCache(dtuNo, 3);

            // 获取 CloseStoreModule
            CloseStoreModule storeModule = getCloseStoreModule(equipmentValue);

            if (storeModule == null) {
                return JsonResult.Fail(500, "CloseStoreModule 不存在");
            }

            // 构建结果
            Map<String, Integer> map = new HashMap<>();
            map.put("moduleInitializationStatus", "3".equals(storeModule.getModuleInitializationStatus()) ? 0 : 1);
            map.put("deviceStatus", calculateDeviceStatus(farEastoneCacheA, farEastoneCacheB, farEastoneCacheC));

            return JsonResult.getSuccessResult(map);
        } catch (Exception e) {
            log.error("获取闭店状态失败, dtuNo: {}", dtuNo, e);
            return JsonResult.Fail(500, "系统内部异常");
        }
    }

    private FarEastoneCache getFarEastoneCache(String dtuNo, int index) {
        String equipmentValue = CloseStoreRedisConstants.getEquipmentKey(dtuNo, index);
        String pointInfoKey = CloseStoreRedisConstants.getPointInfoKey(equipmentValue.split("-")[0], equipmentValue.split("-")[1]);
        String cacheStr = (String) redisUtils.get(pointInfoKey);
        if (StringUtils.isEmpty(cacheStr)) {
            log.warn("缓存不存在, pointInfoKey: {}", pointInfoKey);
            return null;
        }
        return FastJSONUtils.toBean(cacheStr, FarEastoneCache.class);
    }

    private CloseStoreModule getCloseStoreModule(String equipmentKey) {
        String pointId = equipmentKey.split("-")[1];
        return closeStoreModuleMapper.selectOne(new LambdaQueryWrapper<CloseStoreModule>().eq(CloseStoreModule::getPointId, pointId));
    }

    private int calculateDeviceStatus(FarEastoneCache... caches) {
        int deviceStatus = 0;
        for (FarEastoneCache cache : caches) {
            if (cache != null) {
                deviceStatus += Integer.valueOf(cache.getRecoveryTimeStatus())
                        + Integer.valueOf(cache.getConstantCurrentStatus())
                        + Integer.valueOf(cache.getMutantCurrentStatus());
            }
        }
        return deviceStatus;
    }


    /**
     * 接收商户闭店开始时间
     */
    @PostMapping("/receiveCloseStoreTime")
    public JsonResult getCloseStoreTime(@RequestBody ReceiveCloseStoreTimerVo qo) {
        log.info("接收商户闭店开始时间{}",FastJSONUtils.toJSONString(qo));
        try {
            // 验证商户是否存在
            MidMerchant midMerchant = getMidMerchant(qo.getMidMerchantId());
            if (midMerchant == null) {
                return JsonResult.Fail(500, "商户不存在");
            }

            // 验证建筑是否存在
            BaseBuilding baseBuilding = getBaseBuilding(midMerchant.getMiddleBuildingId());
            if (baseBuilding == null) {
                return JsonResult.Fail(500, "商户对应的建筑不存在");
            }

            // 验证点位是否存在
            List<BaseDevicePoint> points = getDevicePoints(baseBuilding.getId(), midMerchant.getId());
            if (points.isEmpty()) {
                return JsonResult.Fail(500, "商户对应的点位不存在");
            }

            // 获取设备点位的开启时间
            String openTimeString = getOpenTime(points.get(0).getId());
            if (StrUtil.isBlank(openTimeString)) {
                log.warn("设备点位的开启时间不存在, 使用默认时间 07:00");
                openTimeString = "07:00";
            }

            // 解析时间
            LocalTime endTime = LocalTime.parse(openTimeString);
            LocalTime startTime = LocalTime.parse(qo.getStartTime());

            // 计算时间间隔
            String dayNum = calculateDayNum(startTime, endTime);
            Duration duration = calculateDuration(startTime, endTime, dayNum);
            if (duration.toHours() < 4) {
                return JsonResult.Fail(500, "闭店开始时间与闭店结束时间不得小于4小时");
            }

            // 构建 CloseStoreTimerQO 和 CloseStoreTimerVO
            CloseStoreTimerQO closeStoreTimerQO = buildCloseStoreTimerQO(baseBuilding.getId(), qo.getStartTime(), openTimeString, dayNum,qo);
            closeStoreTimerService.apply(closeStoreTimerQO, dayNum);
            String finalOpenTimeString = openTimeString;
            points.stream().forEach(point -> {
                redisUtils.hmSet(CloseStoreRedisConstants.getDeviceTimeKey(point.getId()), "openTime", finalOpenTimeString);
                redisUtils.hmSet(CloseStoreRedisConstants.getDeviceTimeKey(point.getId()), "closeTime", qo.getStartTime());
            });
            return JsonResult.getSuccessResult("操作成功");
        } catch (Exception e) {
            log.error("接收商户闭店开始时间失败, qo: {}", qo, e);
            return JsonResult.Fail(500, "系统内部异常");
        }
    }

    private MidMerchant getMidMerchant(String midMerchantId) {
        return midMerchantMapper.selectById(midMerchantId);
    }

    private BaseBuilding getBaseBuilding(String middleBuildingId) {
        return baseBuildingMapper.selectOne(new LambdaQueryWrapper<BaseBuilding>().eq(BaseBuilding::getPiazza, middleBuildingId));
    }

    private List<BaseDevicePoint> getDevicePoints(String buildingId, String midMerchantId) {
        LambdaQueryWrapper<BaseDevicePoint> devicePointLambdaQueryWrapper = new LambdaQueryWrapper<BaseDevicePoint>()
                .eq(BaseDevicePoint::getBuildingId, buildingId)
                .eq(BaseDevicePoint::getSuperType, "16")
                .eq(BaseDevicePoint::getMidMerchantId, midMerchantId);
        return baseDevicePointMapper.selectList(devicePointLambdaQueryWrapper);
    }

    private String getOpenTime(String deviceId) {
        String timeKey = CloseStoreRedisConstants.getDeviceTimeKey(deviceId);
        String openTimeString = (String) redisUtils.hmGet(timeKey, "openTime");
        return StrUtil.isBlank(openTimeString) ? "" : openTimeString.replaceAll("^\"|\"$", "");
    }

    private String calculateDayNum(LocalTime startTime, LocalTime endTime) {
        if (endTime.isBefore(startTime)) {
            return "1";
        }
        return "0";
    }

    private Duration calculateDuration(LocalTime startTime, LocalTime endTime, String dayNum) {
        LocalDate currentDate = LocalDate.now();
        LocalDateTime startDateTime = LocalDateTime.of(currentDate, startTime);
        LocalDateTime endDateTime = LocalDateTime.of(currentDate, endTime);

        if ("1".equals(dayNum)) {
            endDateTime = endDateTime.plusDays(1);
        }

        return Duration.between(startDateTime, endDateTime);
    }

    private CloseStoreTimerQO buildCloseStoreTimerQO(String buildId, String startTime, String openTimeString, String dayNum, ReceiveCloseStoreTimerVo qo) {
        CloseStoreTimerVO closeStoreTimerVO = new CloseStoreTimerVO()
                .setBizCloseTime(startTime)
                .setPlayCloseTime(startTime)
                .setOpenTime(openTimeString)
                .setStartDate(DateUtil.formatDate(new Date()))
                .setEndDate(DateUtil.formatDate(DateUtil.offsetDay(new Date(), 1)))
                .setMerchantId(qo.getMidMerchantId()) // 确保 qo 是方法参数
                .setDayNum(dayNum)
                .setDateType(5);

        return new CloseStoreTimerQO()
                .setBuildId(buildId)
                .setSetLevel(3)
                .setTimerList(Arrays.asList(closeStoreTimerVO));
    }



    /**
     * 判断是否包含某个值
     */
    @PostMapping("/containsValue")
    public boolean containsValue(String str, String value) throws IOException {
        return redisUtils.isValueInIntervals("close_store_device_mutant_data:"+str, Double.parseDouble(value));
    }

    @Autowired
    private KitchenDeviceRedisService kitchenDeviceRedisService;
    @PostMapping("/getState")
    public JsonResult getState(@RequestBody Map<String, String> qo) {
        if (StringUtils.isEmpty(qo.get("hostId"))) {
            throw new RuntimeException("设备编码不能为空");
        }
        Map<String, String> map = new HashMap<>();
        String[] hostIds = qo.get("hostId").split(",");
        String deviceType = qo.get("deviceType");
        if (ConstantUtil.DEVICE_TYPE_CLOSE_STORE.equals(deviceType)) {
            // 查询闭店设备
            LambdaQueryWrapper<BaseDevicePoint> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(BaseDevicePoint::getHostId, Arrays.asList(hostIds));
            queryWrapper.eq(BaseDevicePoint::getSuperType, deviceType);
            List<BaseDevicePoint> devicePointList = baseDevicePointMapper.selectList(queryWrapper);
            if (devicePointList.isEmpty()) {
                throw new RuntimeException("设备不存在");
            }
            // 批量获取 Redis 缓存
            List<String> keys = devicePointList.stream()
                    .map(dp -> CloseStoreRedisConstants.getPointInfoKey(dp.getBuildingId(), dp.getId()))
                    .collect(Collectors.toList());

            List<String> redisValues = redisUtils.multiGet(keys);

            for (int i = 0; i < devicePointList.size(); i++) {
                BaseDevicePoint dp = devicePointList.get(i);
                String oj = redisValues.get(i);
                if (StringUtils.isNotBlank(oj)) {
                    try {
                        FarEastoneCache cache = JSON.parseObject(oj, FarEastoneCache.class);
                        map.put(dp.getHostId(), "0".equals(cache.getDeviceStatus()) ? "0" : "1");
                    } catch (Exception e) {
                        log.warn("解析设备 {} 状态失败: {}", dp.getHostId(), e.getMessage());
                    }
                }
            }

        } else {
            // 其他设备类型（如厨房设备）
            for (String hostId : hostIds) {
                String key = kitchenDeviceRedisService.getEquipmentRegisterStateDataKey(hostId, "1");
                String oj = (String) redisUtils.get(key);
                if (StringUtils.isNotBlank(oj)) {
                    try {
                        KitchenDeviceBasic basicInfo = JSON.parseObject(oj, KitchenDeviceBasic.class);
                        map.put(hostId, "1".equals(basicInfo.getOnlineState()) ? "0" : "1");
                    } catch (Exception e) {
                        log.warn("解析厨房设备 {} 状态失败: {}", hostId, e.getMessage());
                    }
                }
            }
        }

        return JsonResult.getSuccessResult(map);
    }

    @PostMapping("/getPowerExceptionCount")
    public JsonResult getPowerExceptionCount(@RequestBody Map<String, Object> qo) {
        return closeStoreService.getPowerExceptionCount(qo);
    }
}
