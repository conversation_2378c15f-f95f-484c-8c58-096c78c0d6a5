package com.redxun.fire.core.cassandra.dao;

//import com.datastax.driver.core.querybuilder.QueryBuilder;
//import com.datastax.driver.core.querybuilder.Select;
import com.redxun.fire.core.cassandra.entity.CloseData;
import com.redxun.fire.core.cassandra.entity.LiquidData;
//import org.springframework.data.cassandra.core.CassandraTemplate;
import com.redxun.fire.core.consts.CassandraConstants;
import com.redxun.fire.core.pojo.dto.CassandraQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class LiquidDataDao {

//    @Resource
//    private CassandraTemplate cassandraTemplate;

    @Autowired(required = false)
    CassandraHttpService cassandraHttpService;

    /**
     * @param entity
     * @return
     */
    public LiquidData save(LiquidData entity) {
        return new LiquidData();
//        return cassandraTemplate.insert(entity);
    }

    public LiquidData update(LiquidData entity) {
        return new LiquidData();
//        return cassandraTemplate.update(entity);
    }

    public List<LiquidData> findDeviceByTime(String buildingId, String pointId, Date startTime, Date endTime) {
//        Select query = QueryBuilder.select().from("liquid_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId))
//                .and(QueryBuilder.gte("ts", startTime))
//                .and(QueryBuilder.lte("ts", endTime)).allowFiltering();
//        String sql = "";
//        return cassandraTemplate.select(sql, LiquidData.class);
        CassandraQueryDTO dto = new CassandraQueryDTO( buildingId,  pointId,  startTime,  endTime,
                null,  null,  null,  null);
        List<LiquidData> data = cassandraHttpService.select(dto, CassandraConstants.LIQUID_DATA,LiquidData.class);
        return data;
    }

    /**
     * @param buildingId 建筑物id
     * @param pointId    点位id
     * @param num        页数
     * @return
     */
    public List<LiquidData> findDeviceByLimite(String buildingId, String pointId, int num) {
//        Select query = QueryBuilder.select().from("liquid_data").
//                where(QueryBuilder.eq("building_id", buildingId))
//                .and(QueryBuilder.eq("point_id", pointId)).limit(num * 10);
//        String sql = "";
//        return cassandraTemplate.select(sql, LiquidData.class);
        return new ArrayList<LiquidData>();
    }
}
