package com.redxun.fire.core.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 楼层信息
 */
@Data
public class FloorInfo implements Serializable {

    /**
     * 楼层id
     */
    private String id;
    /**
     * 楼层名称
     */
    private String name;
    /**
     * 楼层
     */
    private Integer floor;
    /**
     * 图片坐标x
     */
    private Double pointX;
    /**
     * 图片坐标y
     */
    private Double pointY;

    /**
     * 蜂鸟坐标
     */
    private Double birdPointX;
    private Double birdPointY;
    private Double birdPointZ;
}
