package com.redxun.fire.core.consts;

/**
 * 闭店监测 元数据
 */
public interface CloseStoreTimerConstants {
    /**
     * 设置级别 1门店(1,2,6)
     */
     Integer LV_1 =1;
    /**
     * 设置级别 2日历(3,4)
     */
    Integer LV_2 =2;
    /**
     * 设置级别 2商户(5,7)
     */
    Integer LV_3 =3;

    /**
     * 设置级别 00 特殊情况
     */
    Integer LV_00 =0;

    /**
     * 日期类型 1工作日
     */
    Integer DATE_TYPE_1 =1;
    /**
     * 日期类型 2周末
     */
    Integer DATE_TYPE_2 =2;
    /**
     * 日期类型 3日历节假日
     */
    Integer DATE_TYPE_3 =3;
    /**
     * 日期类型 4日历自定义
     */
    Integer DATE_TYPE_4 =4;
    /**
     * 日期类型 5手动设置日期
     */
    Integer DATE_TYPE_5 =5;
    /**
     * 日期类型 6门店节假日
     */
    Integer DATE_TYPE_6 =6;

    /**
     * 日期类型 7 24小时营业
     */
    Integer DATE_TYPE_7 =7;


    /**
     * 商业类型 0大商业
     */
    Integer BIZ_TYPE_0 =0;
    /**
     * 商业类型 1娱乐楼
     */
    Integer BIZ_TYPE_1 =1;


    /**
     * 审批状态 0待审批
     */
    String APPROVAL_TYPE_0 = "0";
    /**
     * 审批状态 1拒绝
     */
    String APPROVAL_TYPE_1 = "1";
    /**
     * 审批状态 2通过
     */
    String APPROVAL_TYPE_2 = "2";
    /**
     * 审批状态 3作废
     */
    String APPROVAL_TYPE_3 = "3";
    /**
     * 全天营业开店时间
     */
    String OPEN_ALL_DAY_OPEN_TIME = "00:00";
    /**
     * 全天营业闭店时间
     */
    String OPEN_ALL_DAY_CLOSE_TIME = "23:59";

    /**
     * 开店时间限制不早于6点
     */
    String OPEN_TIME_LIMIT = "06:00";

    /**
     * 设置闭店时间后开启监管报警时长
     */
    String ALARM_DURATION_CLOSE= "yclxsz_1_xxkqjgbjsc";

    /**
     * 设置开店时间前关闭监管报警时长
     */
    String ALARM_DURATION_OPEN  = "yclxsz_1_xxgbjgbjsc";

}
