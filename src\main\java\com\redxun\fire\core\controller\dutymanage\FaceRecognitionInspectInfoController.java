package com.redxun.fire.core.controller.dutymanage;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.service.dutymanage.FaceRecognitionInspectInfoService;
import com.redxun.fire.core.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @createTime 2025/2/11
 * @description
 */
@RestController
@RequestMapping("faceRecognitionInspectInfo")
@Slf4j
public class FaceRecognitionInspectInfoController {

    @Autowired
    FaceRecognitionInspectInfoService inspectInfoService;

    @PostMapping(value = "queryPage")
    public JsonResult queryPage(@RequestBody QueryData queryData, HttpServletRequest request) {
        try {
            return inspectInfoService.queryPage(queryData, request);
        } catch (Exception e) {
            log.error("边端人脸识别接口分页查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @GetMapping(value = "getDetails")
    public JsonResult getDetails(@RequestParam String id) {
        try {
            if (StrUtil.isBlank(id)) {
                return JsonResult.getSuccessResult("传参不规范");
            }
            return inspectInfoService.getDetails(id);
        } catch (Exception e) {
            log.error("边端人脸识别接口详情查询异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }


    @PostMapping(value = "queryPicList")
    public JsonResult queryPicList(@RequestBody JSONObject jsonObject) {
        try {
            return inspectInfoService.queryPicList(jsonObject);
        } catch (Exception e) {
            log.error("查询图片集合异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @PostMapping(value = "downloadImages")
    public void downloadImages(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        try {
            inspectInfoService.downloadImages(jsonObject, response);
        } catch (Exception e) {
            log.error("下载图片异常：", e);
            try {
                response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "下载图片并压缩成ZIP文件异常");
            } catch (IOException ex) {
                log.error("发送错误响应异常：", ex);
            }
        }
    }

    @PostMapping(value = "getPic")
    public void getPic(@RequestBody Map<String, String> map, HttpServletResponse response) {
        try {
            URL url = new URL(map.get("imageUrl"));
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            int responseCode = httpConn.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 设置响应头
                response.setContentType(MediaType.IMAGE_JPEG_VALUE); // 根据实际情况调整MediaType
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=image.jpg"); // 根据实际情况调整文件名

                InputStream inputStream = httpConn.getInputStream();
                byte[] bytesIn = new byte[4096];
                int read = 0;

                while ((read = inputStream.read(bytesIn)) != -1) {
                    response.getOutputStream().write(bytesIn, 0, read);
                }

                inputStream.close();
            } else {
                log.info("No file to download. Server replied HTTP code: " + responseCode);
                response.sendError(HttpStatus.NOT_FOUND.value(), "Image not found");
            }
            httpConn.disconnect();
        } catch (Exception e) {
            log.error("下载图片异常：", e);
            try {
                response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "下载图片异常");
            } catch (IOException ex) {
                log.error("发送错误响应异常：", ex);
            }
        }
    }


    @PostMapping(value = "getUserTrainInfoByUserId")
    public Object getUserTrainInfoByUserId(@RequestBody JSONObject jsonObject) {
        try {
            return inspectInfoService.getUserTrainInfoByUserId(jsonObject);
        } catch (Exception e) {
            log.error("获取人员实操考试信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

    @Autowired
    RedisUtils redisUtils;

    @GetMapping(value = "removeRedisPattern")
    public Object removeRedisPattern(@RequestBody String buildingId) {
        try {
            String key1 = "faceRecognitionInfo:" + buildingId + ":double";
            String key2 = "faceRecognitionInfo:" + buildingId + ":single";
            String key3 = "faceRecognitionInfo:" + buildingId + ":double-no-power";
            log.info("------------建筑{}移除redis缓存数据-----------------", buildingId);
            redisUtils.remove(key1, key2, key3);
        } catch (Exception e) {
            log.error("获取人员实操考试信息异常：", e);
        }
        return JsonResult.getFailResult("请求异常");
    }

}
