package com.redxun.fire.core.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.BaseBuildingFloor;
import com.redxun.fire.core.entity.CameraBindPointDto;
import com.redxun.fire.core.pojo.dto.CameraQuery;
import com.redxun.fire.core.service.device.CameraMonitorService;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.utils.ExcelUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 */
@RestController
@RequestMapping("/fire/camera")
public class CameraMonitorController {

    @Resource
    private CameraMonitorService cameraMonitorService;
    @Resource
    IBaseBuildingFloorService buildingFloorService;

    /**
     * 查询摄像头基础数据
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/queryCameraMonitorData")
    public JsonResult queryCameraMonitorData(@RequestBody Map<String, Object> paramsMap) {
        return JsonResult.getSuccessResult(cameraMonitorService.queryCameraMonitorData(paramsMap));
    }

    /**
     * 摄像头数据注册
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/cameraRegister")
    public JsonResult cameraRegister(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return cameraMonitorService.cameraRegister(request, paramsMap)
                ? JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }

    /**
     * 摄像头数据修改
     *
     * @param paramsMap
     * @return
     */
    @PostMapping("/editCameraById")
    public JsonResult editCameraById(HttpServletRequest request, @RequestBody Map<String, Object> paramsMap) {
        return cameraMonitorService.editPressurePointInfoById(request, paramsMap) ?
                JsonResult.Success() : JsonResult.getFailResult("操作失败");
    }

    /**
     * 批量摄像头设备点位导入
     *
     * @param file
     * @return
     */
    @PostMapping("/batchImportPointInfo")
    public JsonResult batchImportPointInfo(@RequestParam("file") MultipartFile file, @RequestParam("buildingId") String buildingId) {
        if (StringUtils.isEmpty(buildingId)) {
            throw new RuntimeException("建筑物id不能为空");
        }
        QueryWrapper<BaseBuildingFloor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("building_id", buildingId);
        List<BaseBuildingFloor> list = buildingFloorService.list(queryWrapper);
        if (list == null || list.size() == 0) {
            throw new RuntimeException("没有楼层无法新增");
        }
        return cameraMonitorService.batchImportPointInfo(file, buildingId) ?
                JsonResult.Success() : JsonResult.getFailResult("导入失败");
    }

    /**
     * 查询绑定点位记录
     *
     * @param
     * @return
     */
    @PostMapping("/queryBindPoint/{cameraPointId}")
    public JsonResult queryBindPoint(@PathVariable("cameraPointId") String cameraPointId) {
        if (StringUtils.isEmpty(cameraPointId)) {
            throw new RuntimeException("摄像头点位不能为空");
        }
        return JsonResult.Success().setData(cameraMonitorService.queryBindPoint(cameraPointId));
    }

    /**
     * 查询绑定点位记录
     *
     * @param
     * @return
     */
    @PostMapping("/queryCameraByPointId/{pointId}")
    public JsonResult queryCameraByPointId(@PathVariable("pointId") String pointId) {
        if (StringUtils.isEmpty(pointId)) {
            throw new RuntimeException("点位不能为空");
        }
        return JsonResult.Success().setData(cameraMonitorService.queryCameraByPointId(pointId));
    }

    /**
     * 绑定摄像头点位和报警点位
     *
     * @param
     * @return
     */
    @PostMapping("/bindPoint")
    public JsonResult bindPoint(@RequestBody CameraQuery cameraQuery) {
//        if (CollectionUtils.isEmpty(cameraQuery.getPointIdList())) {
//            throw new RuntimeException("绑定点位不能为空");
//        }
        if (StringUtils.isEmpty(cameraQuery.getCameraPointId())) {
            throw new RuntimeException("摄像头点位不能为空");
        }
        return cameraMonitorService.bindPoint(cameraQuery.getCameraPointId(), cameraQuery.getPointIdList()) ?
                JsonResult.Success() : JsonResult.getFailResult("导入失败");
    }


    /**
     * 导入excel-批量绑定摄像头点位和报警点位
     *
     * @param file
     * @return
     */
    @PostMapping("/ImportBindPoint")
    public JsonResult ImportBindPoint(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("buildingId") String buildingId) throws IOException {
        if (StringUtils.isEmpty(buildingId)) {
            throw new RuntimeException("建筑物id不能为空");
        }
        QueryWrapper<BaseBuildingFloor> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("building_id", buildingId);
        List<BaseBuildingFloor> list = buildingFloorService.list(queryWrapper);
        if (list == null || list.size() == 0) {
            throw new RuntimeException("没有楼层无法新增");
        }
        return cameraMonitorService.ImportBindPoint(request, file, buildingId) ?
                JsonResult.Success() : JsonResult.getFailResult("导入失败");
    }

    @GetMapping("exportTemplate")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<CameraBindPointDto> cameraBindPointDtoList = new ArrayList<>();
        CameraBindPointDto cameraBindPointDto = new CameraBindPointDto();
        cameraBindPointDto.setNum("1");
        cameraBindPointDto.setHostId("01001");
        cameraBindPointDto.setPointNumber("2-11-250110");
        cameraBindPointDto.setPointDesc("地下三层八区丁戊类库房南楼梯前室外通道烟感");
        cameraBindPointDto.setCameraIp("34020000001110000006");
        cameraBindPointDto.setCameraChannel("34020000001320023063");
        cameraBindPointDto.setCameraFloor("-3/B3");
        cameraBindPointDto.setCameraDesc("首层楼梯4东口");
        cameraBindPointDtoList.add(cameraBindPointDto);
        ExcelUtil.writeExcel(response, cameraBindPointDtoList, "摄像头联动关系导入模板", "sheet1", new CameraBindPointDto());
    }

}
