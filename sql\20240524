CREATE TABLE `exam_question` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `NAME_` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '试题名称',
  `SCORE_` double DEFAULT '0' COMMENT '分数',
  `REQUIRED_` int DEFAULT '0' COMMENT '是否必答:0 非必须、1 必须',
  `RANDOM_` int DEFAULT '0' COMMENT '选项是否随机:0 否、1 是',
  `QUESTION_TYPE_` int DEFAULT '0' COMMENT '试题类型',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实操试题表';

CREATE TABLE `exam_question_option` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `QUESTION_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '试题ID',
  `QUESTION_NUM_` int DEFAULT '0' COMMENT '题号',
  `NAME_` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '标题',
  `DESCP_` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '选项简介',
  `SCORE_` double DEFAULT '0' COMMENT '分数',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `QUESTION_ID_INDEX` (`QUESTION_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实操试题选项表';

CREATE TABLE `exam_question_record` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `PRACTICAL_EXAM_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实操考试申请ID',
  `QUESTION_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '试题ID',
  `OPTION_ID_` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '选项ID',
  `SCORE_` double(11,1) NOT NULL DEFAULT '0.0' COMMENT '用户得分',
  `USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ID',
  `EXAM_RESULT_` int DEFAULT '0' COMMENT '0 不正确 1正确',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `QUESTION_ID_INDEX` (`QUESTION_ID_`),
  KEY `OPTION_ID_INDEX` (`OPTION_ID_`),
  KEY `USER_ID_INDEX` (`USER_ID_`),
  KEY `PRACTICAL_EXAM_ID_INDEX` (`PRACTICAL_EXAM_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实操考试试题记录表';

CREATE TABLE `theory_exam_apply` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `SCORE_` double(11,1) NOT NULL DEFAULT '0.0' COMMENT '用户理论最终得分',
  `USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ID',
  `BUILDING_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广场id',
  `USER_NAME_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名称',
  `STATUS_` int DEFAULT '0' COMMENT '申请状态0未通过1通过2申请中',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `USER_ID_INDEX` (`USER_ID_`),
  KEY `BUILDING_ID_INDEX` (`BUILDING_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='理论考试申请表';


CREATE TABLE `practical_exam_apply` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `FLOW_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程审批ID',
  `SCORE_` double(11,1) NOT NULL DEFAULT '0.0' COMMENT '用户实操得分',
  `BUILDING_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广场id',
  `EXAM_USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考试用户ID',
  `INVIGILATE_USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '监考用户ID',
  `INVIGILATE_USER_NAME_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '监考用户姓名',
  `APPLY_USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人ID',
  `STATUS_` int DEFAULT '0' COMMENT '审批逻辑 0 开始 1 审批中 2审批完成 3审批驳回',
  `EXAM_RESULT_` int DEFAULT '0' COMMENT '考试结果 0 未通过1 通过',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `APPLY_PASS_TIME_` datetime DEFAULT NULL COMMENT '申请通过时间',
  `START_TIME_` datetime DEFAULT NULL COMMENT '考试开始时间',
  `END_TIME_` datetime DEFAULT NULL COMMENT '考试结束时间',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `EXAM_USER_ID_INDEX` (`EXAM_USER_ID_`),
  KEY `FLOW_ID_INDEX` (`FLOW_ID_`),
  KEY `BUILDING_ID_INDEX` (`BUILDING_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实操考试申请表';


CREATE TABLE `authentication_apply` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `FLOW_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程审批ID',
  `USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '申请用户ID',
  `BUILDING_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广场id',
  `APPROVE_USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人Id',
  `BIRTHDAY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生日',
  `ENTRY_TIME_` datetime DEFAULT NULL COMMENT '入职时间',
  `EDUCATION_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学历',
  `SPECIALITY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '专业',
  `FACE_PICTURE_` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人脸图片ID',
  `CAREER_PICTURE_` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职业资格证书ID',
  `CERTIFICATE_NUM_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书编号',
  `CERTIFICATE_OTHER_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他类证书',
  `TRAIN_TYPE_` int DEFAULT '0' COMMENT '培训类型',
  `CERTIFICATE_TYPE_` int DEFAULT '0' COMMENT '证书类型',
  `STATUS_` int DEFAULT '0' COMMENT '审批逻辑 0 开始 1 审批中 2审批完成',
  `PASS_STATUS_` int DEFAULT '0' COMMENT '通过状态,0未通过 1已通过',
  `APPLY_RESULT_` int DEFAULT '0' COMMENT '长短期 0 短期 1 永久',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `START_TIME_` datetime DEFAULT NULL COMMENT '证书开始时间',
  `ENT_TIME_` datetime DEFAULT NULL COMMENT '证书结束时间',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `APPLY_PASS_TIME_` datetime DEFAULT NULL COMMENT '审批通过时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `USER_ID_INDEX` (`USER_ID_`),
  KEY `BUILDING_ID_INDEX` (`BUILDING_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='岗位认证申请表';

CREATE TABLE `authentication_apply_record` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `USER_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '申请用户ID',
  `BUILDING_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广场id',
  `USER_NAME_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '申请用户ID',
  `OLD_INFO_` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '修改前信息',
  `BIRTHDAY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生日',
  `ENTRY_TIME_` datetime DEFAULT NULL COMMENT '入职时间',
  `EDUCATION_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学历',
  `SPECIALITY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '专业',
  `FACE_PICTURE_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人脸图片ID',
  `CAREER_PICTURE_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职业资格证书ID',
  `CERTIFICATE_NUM_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书编号',
  `EDIT_TYPE_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新类型',
  `TRAIN_TYPE_` int DEFAULT '0' COMMENT '培训类型',
  `CERTIFICATE_TYPE_` int DEFAULT '0' COMMENT '证书类型',
  `STATUS_` int DEFAULT '0' COMMENT '审批逻辑 0 开始 1 审批中 2审批完成',
  `PASS_STATUS_` int DEFAULT '0' COMMENT '通过状态,0未通过 1已通过',
  `APPLY_RESULT_` int DEFAULT '0' COMMENT '长短期 0 短期 1 永久',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `START_TIME_` datetime DEFAULT NULL COMMENT '证书开始时间',
  `ENT_TIME_` datetime DEFAULT NULL COMMENT '证书结束时间',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `USER_ID_INDEX` (`USER_ID_`),
  KEY `BUILDING_ID_INDEX` (`BUILDING_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='岗位认证申请记录表';

CREATE TABLE `sys_dict_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dict_type_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '字典类型名称',
  `dict_code` varchar(255) NOT NULL COMMENT '数据编码',
  `dict_value` int DEFAULT '0' COMMENT '值',
  `dict_name` varchar(255) NOT NULL COMMENT '字典名称',
  `dict_desc` varchar(255) DEFAULT NULL COMMENT '描述',
  `sort` int DEFAULT NULL COMMENT '排序',
  `status` enum('0','1') NOT NULL DEFAULT '1' COMMENT '0 禁用；1 启用',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_by_user_id` int DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_by_user_id` int DEFAULT NULL COMMENT '修改人id',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3 COMMENT='系统字典条目表';

CREATE TABLE `exam_question_operate` (
  `ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `USER_NAME_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人姓名',
  `NAME_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考题名称',
  `BUILDING_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广场id',
  `OPERATE_TYPE_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型',
  `DELETED_` int DEFAULT '0' COMMENT '逻辑删除 0 正常 1 删除',
  `COMPANY_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司ID',
  `TENANT_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  `CREATE_DEP_ID_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建部门ID',
  `CREATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME_` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY_` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME_` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  KEY `BUILDING_ID_INDEX` (`BUILDING_ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实操试题操作记录表';


INSERT INTO `jpaas_system`.`sys_tree` (`TREE_ID_`, `CODE_`, `NAME_`, `PATH_`, `PARENT_ID_`, `ALIAS_`, `DESCP_`, `CAT_KEY_`, `SN_`, `DATA_SHOW_TYPE_`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1404993489075351522', NULL, '岗位认证', '0.1404993489075351522.', '0', 'GWRZ', NULL, 'BPM', '14', 'FLAT', NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `wd_iot_fire`.`bpm_def_reference` (`id`, `bpm_def_id`, `bpm_def_name`, `bpm_def_tree_id`, `bpm_def_cat`, `bpm_def_gateway_key`, `bpm_def_gateway_key_con`, `bpm_def_sort`, `enable`) VALUES ('666', '1765212265360019458', '实操考试申请', '1404993489075351522', '1', NULL, NULL, '0', '1');
INSERT INTO `wd_iot_fire`.`bpm_def_reference` (`id`, `bpm_def_id`, `bpm_def_name`, `bpm_def_tree_id`, `bpm_def_cat`, `bpm_def_gateway_key`, `bpm_def_gateway_key_con`, `bpm_def_sort`, `enable`) VALUES ('777', '1760955867711569922', '岗位认证申请', '1404993489075351522', '1', NULL, NULL, '1', '1');

INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504178475010', '1765212265360019458', 'UserTask_1s72jpj', '第六个', '0', 'NOBODY', '第六个', NULL, '5');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504182669313', '1765212265360019458', 'UserTask_0tknm5m', '第五个', '0', 'NOBODY', '第五个', 'UserTask_1s72jpj', '4');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504182669314', '1765212265360019458', 'UserTask_0aaasv8', '城市公司安全总监', '1', 'AREA_SAFETY_MANAGER', '城市公司安全总监', 'UserTask_0tknm5m', '3');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504182669315', '1765212265360019458', 'UserTask_096n9kc', '安全经理', '1', 'SAFE_QUALITY_MANAGER', '安全经理', 'UserTask_0aaasv8', '2');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504186863617', '1765212265360019458', 'UserTask_1fjq045', '工程副总', '1', 'PRE_MANAGER', '工程副总', 'UserTask_096n9kc', '1');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504186863618', '1765212265360019458', 'UserTask_06s0lw6', '工程经理', '1', 'PROJECT_MANAGER', '工程经理', 'UserTask_1fjq045', '0');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6666', '1765212265360019458', 'UserTask_06s0lw6', '工程经理', 'UserTask_1fjq045', '0');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6667', '1765212265360019458', 'UserTask_1fjq045', '工程副总', 'UserTask_096n9kc', '1');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6668', '1765212265360019458', 'UserTask_096n9kc', '安全经理', 'UserTask_0aaasv8', '2');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6669', '1765212265360019458', 'UserTask_0aaasv8', '城市公司安全总监', 'UserTask_0tknm5m', '3');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6670', '1765212265360019458', 'UserTask_0tknm5m', '第五个', 'UserTask_1s72jpj', '4');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('6672', '1765212265360019458', 'UserTask_1s72jpj', '第六个', NULL, '5');

INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504270749698', '1760955867711569922', 'UserTask_0covocr', '第六个', '0', 'NOBODY', '第六个', NULL, '5');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504270749699', '1760955867711569922', 'UserTask_101vydc', '第五个', '0', 'NOBODY', '第五个', 'UserTask_0covocr', '4');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504274944001', '1760955867711569922', 'UserTask_0vcraw8', '第四个', '0', 'NOBODY', '第四个', 'UserTask_101vydc', '3');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504274944002', '1760955867711569922', 'UserTask_0vjy147', '第三个', '0', 'NOBODY', '第三个', 'UserTask_0vcraw8', '2');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504274944003', '1760955867711569922', 'UserTask_0kea05t', '第二个', '0', 'NOBODY', '第二个', 'UserTask_0vjy147', '1');
INSERT INTO `wd_iot_fire`.`bpm_def_node_reference` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_enable`, `node_pos_key`, `node_pos_name`, `node_next_id`, `node_sort`) VALUES ('1765270504274944004', '1760955867711569922', 'UserTask_1dg12um', '城市公司安全总监', '1', 'AREA_SAFETY_MANAGER', '城市公司安全总监', 'UserTask_0kea05t', '0');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66666', '1760955867711569922', 'UserTask_1dg12um', '城市公司安全总监', 'UserTask_0kea05t', '0');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66677', '1760955867711569922', 'UserTask_0kea05t', '第二个', 'UserTask_0vjy147', '1');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66688', '1760955867711569922', 'UserTask_0vjy147', '第三个', 'UserTask_0vcraw8', '2');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66699', '1760955867711569922', 'UserTask_0vcraw8', '第四个', 'UserTask_101vydc', '3');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66701', '1760955867711569922', 'UserTask_101vydc', '第五个', 'UserTask_0covocr', '4');
INSERT INTO `wd_iot_fire`.`bpm_def_node_config` (`id`, `bpm_def_id`, `node_id`, `node_name`, `node_next_id`, `node_sort`) VALUES ('66702', '1760955867711569922', 'UserTask_0covocr', '第六个', NULL, '5');

INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1000', 'approve_authentication', '岗位认证', '12', '1000', '1000', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1001', 'approve_authentication_no_list', '待审批列表', '1000', '1001', '1001', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1002', 'approve_authentication_finish_list', '已审批列表', '1000', '1002', '1002', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1003', 'approve_authentication_no_detail', '待审批详情', '1001', '1003', '1003', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1004', 'approve_authentication_no_immediate', '立即审批', '1001', '1004', '1004', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1005', 'approve_authentication_no_approve', '审批', '1003', '1005', '1005', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wd_iot_fire`.`wd_app_dic` (`id`, `app`, `app_show`, `parent_id`, `sort`, `path`, `TENANT_ID_`, `CREATE_DEP_ID_`, `CREATE_BY_`, `CREATE_TIME_`, `UPDATE_BY_`, `UPDATE_TIME_`) VALUES ('1006', 'approve_authentication_finish_detail', '已审批详情', '1002', '1006', '1006', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('1', '培训类型', 'PXLX', '1', '国家统考', NULL, '1', '1', NULL, NULL, '2024-02-21 14:03:31', NULL, NULL, '2024-02-21 14:03:37');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('2', '培训类型', 'PXLX', '2', '专业学校培训', NULL, '2', '1', NULL, NULL, '2024-02-21 14:03:45', NULL, NULL, '2024-02-21 14:06:14');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('3', '证书类型', 'ZSLX', '1', '消防设施操作员(初级)', NULL, '1', '1', NULL, NULL, '2024-02-21 14:07:39', NULL, NULL, '2024-02-21 14:07:39');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('4', '证书类型', 'ZSLX', '2', '消防设施操作员(中级)', NULL, '2', '1', NULL, NULL, '2024-02-21 14:07:44', NULL, NULL, '2024-02-21 14:08:11');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('5', '证书类型', 'ZSLX', '3', '消防设施操作员(结业证书)', NULL, '3', '1', NULL, NULL, '2024-02-21 14:08:15', NULL, NULL, '2024-02-21 14:09:44');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('6', '证书类型', 'ZSLX', '4', '消防设施操作员(高级)', NULL, '4', '1', NULL, NULL, '2024-02-21 14:09:21', NULL, NULL, '2024-02-21 14:09:47');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('7', '证书类型', 'ZSLX', '5', '注册消防工程师', NULL, '5', '1', NULL, NULL, '2024-02-21 14:10:05', NULL, NULL, '2024-02-21 14:10:26');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('8', '试题类型', 'STLX', '1', '智慧消防', NULL, '1', '1', NULL, NULL, '2024-02-22 15:08:12', NULL, NULL, '2024-02-22 15:08:12');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('9', '试题类型', 'STLX', '2', '应急处置', NULL, '2', '1', NULL, NULL, '2024-02-22 15:08:16', NULL, NULL, '2024-02-22 15:08:59');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('10', '试题类型', 'STLX', '3', '基础消防', NULL, '3', '1', NULL, NULL, '2024-02-22 15:08:42', NULL, NULL, '2024-02-22 15:09:03');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('11', '试题类型', 'STLX', '4', '安防', NULL, '4', '1', NULL, NULL, '2024-02-22 15:09:06', NULL, NULL, '2024-02-22 15:09:27');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`id`, `dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('12', '试题类型', 'STLX', '5', '消控中心', NULL, '5', '1', NULL, NULL, '2024-02-22 15:09:37', NULL, NULL, '2024-02-22 15:10:49');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '1', '单店管理', NULL, '1', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '2', '单店信息', NULL, '2', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');

INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '3', '单店管理-人员管理', NULL, '3', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '4', '设备台账', NULL, '4', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '5', '消防主机点位', NULL, '5', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '6', '点位调改', NULL, '6', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '7', '故障点位', NULL, '7', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '8', '反复误报点位', NULL, '8', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '9', '屏蔽点位', NULL, '9', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '10', '特批点位申请', NULL, '10', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '11', '非消防主机点位', NULL, '11', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '12', '楼层平面图', NULL, '12', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '13', '值班管理', NULL, '13', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '14', '考勤管理', NULL, '14', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '15', '水压监测', NULL, '15', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '16', '水泵监测', NULL, '16', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '17', '维保计划', NULL, '17', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '18', '维保合同', NULL, '18', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '19', '维保单位', NULL, '19', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '20', '值班查岗', NULL, '20', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '21', '数据中心', NULL, '21', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '22', '操作日志', NULL, '22', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '23', '建筑管理', NULL, '23', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '24', '终端管理', NULL, '24', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '25', '门店调试', NULL, '25', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '26', '报警定义', NULL, '26', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '27', '功能设置-接警中心', NULL, '27', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '28', '功能设置-审批流程', NULL, '28', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '29', '功能设置-短信通知', NULL, '29', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '30', '功能设置-维保计划', NULL, '30', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '31', '功能设置-值班查岗', NULL, '31', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '32', '功能设置-水压报警', NULL, '32', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '33', '功能设置-评分设置', NULL, '33', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '34', '功能设置-安全系统推送', NULL, '34', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '35', '误报原因设置', NULL, '35', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '36', '离线报警', NULL, '36', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '37', '资料下载', NULL, '37', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '38', '数据清理', NULL, '38', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '39', '数据导出', NULL, '39', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '40', '平台公告', NULL, '40', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '41', '平台管理-人员管理', NULL, '41', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '42', '资源授权', NULL, '42', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');
INSERT INTO `wd_iot_fire`.`sys_dict_item` (`dict_type_name`, `dict_code`, `dict_value`, `dict_name`, `dict_desc`, `sort`, `status`, `create_by`, `create_by_user_id`, `create_time`, `update_by`, `update_by_user_id`, `update_time`) VALUES ('操作类型', 'CZLX', '43', '检查模式', NULL, '43', '1', NULL, NULL, '2024-03-21 10:27:31', NULL, NULL, '2024-03-21 10:27:31');


--  历史数据暂时先空着
ALTER TABLE os_user ADD INDEX USER_NO_INDEX ( USER_NO_ );
ALTER TABLE os_user ADD `WZT_USER_ID_` VARCHAR(64) DEFAULT NULL COMMENT '万中台用户id';
ALTER TABLE os_user ADD `RANK_LEVEL_` VARCHAR(64) DEFAULT NULL COMMENT '层级 1总部,2地方';
ALTER TABLE os_user ADD `GROUP_ORG_` VARCHAR(64) DEFAULT NULL COMMENT '集团';
ALTER TABLE os_user ADD `REGION_` VARCHAR(64) DEFAULT NULL COMMENT '大区';
ALTER TABLE os_user ADD `CITY_` VARCHAR(64) DEFAULT NULL COMMENT '城市公司';
ALTER TABLE os_user ADD `PIAZZA_` VARCHAR(64) DEFAULT NULL COMMENT '广场';
ALTER TABLE os_user ADD `GROUP_ORG_NAME_` VARCHAR(64) DEFAULT NULL COMMENT '集团名称';
ALTER TABLE os_user ADD `REGION_NAME_` VARCHAR(64) DEFAULT NULL COMMENT '大区名称';
ALTER TABLE os_user ADD `CITY_NAME_` VARCHAR(64) DEFAULT NULL COMMENT '城市公司名称';
ALTER TABLE os_user ADD `PIAZZA_NAME_` VARCHAR(64) DEFAULT NULL COMMENT '广场名称';
ALTER TABLE os_user ADD `LEVEL_AYER_` VARCHAR(64) DEFAULT NULL COMMENT '万信层级';
ALTER TABLE os_user ADD `DEPARTMENT_IDS_` VARCHAR(255) DEFAULT NULL COMMENT '非商管部门ID(逗号分隔)';
ALTER TABLE os_user ADD `USER_FW_` VARCHAR(64) DEFAULT NULL COMMENT '是否非万(1:万达人员,2:非商管,3:非万人员)';
ALTER TABLE os_user ADD `ROLE_KEY_` VARCHAR(255) DEFAULT NULL COMMENT '角色Key多个逗号分隔';
ALTER TABLE os_user ADD `DEPARTMENT_NAME_` VARCHAR(255) DEFAULT NULL COMMENT '部门名称';

-- 人员信息表中关联万中台userId
UPDATE os_user a LEFT JOIN dwd_user b on a.USER_NO_ = b.user_no and b.status=2 SET a.WZT_USER_ID_ = b.user_id WHERE a.USER_NO_ = b.user_no;

ALTER TABLE mid_building ADD `project_id` VARCHAR(64) DEFAULT NULL COMMENT '项目PGID';
ALTER TABLE mid_building ADD INDEX project_idINDEX ( project_id );

UPDATE mid_building a LEFT JOIN w_project c ON a.middle_id=c.ID_ SET a.project_id = c.LOCAL_ID_ WHERE a.middle_id=c.ID_;

ALTER TABLE stat_pump_real ADD `COMPANY_ID_` VARCHAR(64) DEFAULT NULL COMMENT '公司id';

ALTER TABLE stat_water_press_real ADD `COMPANY_ID_` VARCHAR(64) DEFAULT NULL COMMENT '公司id';

ALTER TABLE inspect_duty_certificate ADD `COMPANY_ID_` VARCHAR(64) DEFAULT NULL COMMENT '公司id';
alter table inspect_duty_certificate add column certificate_url_json varchar(512) default NULL COMMENT '证书扫描件json';

ALTER TABLE config_relation ADD `config_tzr_id_new` VARCHAR(255) DEFAULT NULL COMMENT '短信通知人id集';


ALTER TABLE data ADD `data_url_new` VARCHAR(500) DEFAULT NULL COMMENT '资料路径(版本升级后)';

ALTER table os_group add column BUILDING_ID_ varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL;
ALTER TABLE os_group ADD `COMPANY_ID_` VARCHAR(64) DEFAULT NULL COMMENT '公司id';
UPDATE os_group a LEFT JOIN base_building b on a.PARENT_ID_ = b.belong_dep SET a.PARENT_ID_ = b.id WHERE a.PARENT_ID_ = b.belong_dep;

ALTER table drop_down add column COMPANY_ID_ varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL;

update os_user set TENANT_ID_='1580760662337761282';
update base_building set TENANT_ID_='1580760662337761282';

ALTER TABLE user_data_power ADD `ORG_NAME_` VARCHAR(255) DEFAULT NULL COMMENT '组织名称';

-- 该表增加所属公司Id 544万
ALTER TABLE base_device_point ADD `COMPANY_ID_` VARCHAR(64) DEFAULT NULL COMMENT '公司id';
ALTER TABLE fire_info ADD `check_img_json` VARCHAR(255) DEFAULT NULL COMMENT '误报图片';
ALTER TABLE fire_info ADD `video_url_json` VARCHAR(255) DEFAULT NULL COMMENT '视频json';

ALTER TABLE `wd_iot_fire`.`maintenance_plan`
ADD COLUMN `is_delete` tinyint NULL DEFAULT 0 COMMENT '删除状态 0:未删除 1:已删除' AFTER `state`;

ALTER TABLE fire_check_attach ADD `img_url_json` text DEFAULT NULL COMMENT 'app火警提交图片';


ALTER TABLE `wd_iot_fire`.`fault_export`
MODIFY COLUMN `building_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '建筑id' AFTER `fault_status_str`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`, `building_id`) USING BTREE;

#fault_info 800多万数据 6分钟
alter table fault_export PARTITION BY KEY(building_id) PARTITIONS 20;

ALTER TABLE `wd_iot_fire`.`inspect_record`
ADD INDEX `IDX_BUILD_ID`(`build_id`) USING BTREE,
ADD INDEX `IDX_INSPECT_NO`(`inspect_no`) USING BTREE;
ALTER TABLE `wd_iot_fire`.`inspect_record`
ADD INDEX `IDX_CREATE_TIME`(`CREATE_TIME_` DESC) USING BTREE;

ALTER TABLE `wd_iot_fire`.`base_building_floor`
ADD COLUMN `floor_pic_json` varchar(255) NULL COMMENT '新版本上次图片json' AFTER `floor_pic`;

ALTER TABLE `wd_iot_fire`.`route_check_record`
ADD INDEX `IDX_CHECK_TIME`(`check_time`) USING BTREE;

ALTER TABLE `wd_iot_fire`.`water_abnormal`
ADD INDEX `IDX_TYPE`(`type`) USING BTREE;

ALTER TABLE bpm_def_node_reference ADD `job_id` VARCHAR(64) DEFAULT NULL COMMENT '职务id';
ALTER TABLE bpm_def_node_reference ADD `level_ayer` VARCHAR(64) DEFAULT NULL COMMENT '所属层级';

select bb.building_name,bbf.* from base_building_floor bbf join base_building bb on bbf.building_id=bb.id where bbf.id in (select floor_id from base_device_point group by floor_id) order by building_id,floor

-- 点位表添加字段 2024-06-12
alter table base_device_point add column `wzt_building` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台楼座id';
alter table base_device_point add column `wzt_building_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台楼座';
alter table base_device_point add column `wzt_floor_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台楼层id';
alter table base_device_point add column `wzt_floor_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台楼层';
alter table base_device_point add column `wzt_snap_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台管理单元id';
alter table base_device_point add column `wzt_snap_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台管理单元';
alter table base_device_point add column `wzt_compartment_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台隔间ID';
alter table base_device_point add column `wzt_compartment_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台隔间';
alter table base_device_point add column `wzt_point_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '万中台点位id';

-- 楼层信息添加万中台楼层信息字段2024-06-12
ALTER TABLE base_building_floor ADD `wzt_floor_id` VARCHAR(64) DEFAULT NULL COMMENT '万中台楼层Id';
ALTER TABLE base_building_floor ADD `wzt_floor_name` VARCHAR(64) DEFAULT NULL COMMENT '万中台楼层名称';
ALTER TABLE base_building_floor ADD `wzt_construct_id` VARCHAR(64) DEFAULT NULL COMMENT '万中台楼栋Id';
ALTER TABLE base_building_floor ADD `wzt_construct_name` VARCHAR(64) DEFAULT NULL COMMENT '万中台楼栋名称';

-- 20240708新增表结构
CREATE TABLE `point_sync_log` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
`point_number` VARCHAR ( 64 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '点位编号',
`building_id` VARCHAR ( 32 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '建筑物id',
`sync_state` CHAR ( 2 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步状态（1：成功；2：失败）',
`sync_type` VARCHAR ( 8 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步类型',
`cause` VARCHAR ( 1024 ) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步失败原因',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
PRIMARY KEY ( `id` ) USING BTREE,
KEY `idx_build_and_number` ( `building_id`, `point_number` ) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=4399 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '点位同步日志';

-- 添加索引
ALTER TABLE `wd_iot_fire`.`appointment_point`
DROP INDEX `building_id_index`,
ADD INDEX `building_id_index`(`building_id` ASC, `effective_time` ASC) USING BTREE,ALGORITHM=INPLACE, LOCK=NONE;
