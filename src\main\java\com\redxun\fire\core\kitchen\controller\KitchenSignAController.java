
package com.redxun.fire.core.kitchen.controller;

import com.alibaba.fastjson.JSONObject;
import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.base.db.BaseService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.kitchen.entity.KitchenSignA;
import com.redxun.fire.core.kitchen.fvo.AddBasePointReqFvo;
import com.redxun.fire.core.kitchen.fvo.KitchenSupplyFvo;
import com.redxun.fire.core.kitchen.service.KitchenDeviceBasicServiceImpl;
import com.redxun.fire.core.kitchen.service.KitchenSignAServiceImpl;
import com.redxun.web.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/fire/kitchenSignA")
@Api(tags = "厨房监测设备A注册信息表")
@ClassDefine(title = "厨房监测设备A注册信息表", alias = "KitchenSignAController", path = "/fire/kitchenSignA", packages = "core.kitchen", packageName = "子系统名称")
public class KitchenSignAController extends BaseController<KitchenSignA> {

    @Autowired
    KitchenSignAServiceImpl kitchenSignAService;


    @Override
    public BaseService getBaseService() {
        return kitchenSignAService;
    }

    @Override
    public String getComment() {
        return "厨房监测设备A注册信息表";
    }

    /**
     * 注册新增A版设备信息
     * addBasePointInfo
     */
    @PostMapping("/addBasePointInfo")
    public JsonResult addBasePointInfo(@RequestBody AddBasePointReqFvo param) throws Exception {
        return kitchenSignAService.addBasePointInfo(param);
    }

    /**
     * 查看
     * getPointDetail
     */
    @PostMapping("/getPointDetail")
    public JsonResult getPointDetail(@RequestBody JSONObject param) {
        return kitchenSignAService.getPointDetail(param);
    }

    /**
     * 补充内容
     * supplyPoint
     */
    @PostMapping("/supplyPoint")
    public JsonResult supplyPoint(@RequestBody List<KitchenSupplyFvo> param) throws ParseException {
        return kitchenSignAService.supplyPoint(param);
    }

    @Autowired
    KitchenDeviceBasicServiceImpl kitchenDeviceBasicService;

    @GetMapping("/refreshStatistic")
    public void supplyPoint(@RequestParam String buildingId) {
        kitchenDeviceBasicService.statisticalKitchen(buildingId);
    }




}

