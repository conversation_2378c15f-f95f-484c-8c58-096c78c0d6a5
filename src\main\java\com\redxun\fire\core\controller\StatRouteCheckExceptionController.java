package com.redxun.fire.core.controller;


import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.StatRouteCheckException;
import com.redxun.fire.core.pojo.base.ResultMsg;
import com.redxun.fire.core.service.alarm.IStatRouteCheckExceptionService;
import com.redxun.fire.core.consts.Constant;
import com.redxun.fire.core.utils.ServletsUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <p>
 * 终端异常表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@RestController
@RequestMapping("/stat-route-check-exception")
public class StatRouteCheckExceptionController {

    @Resource
    private IStatRouteCheckExceptionService statRouteCheckExceptionService;

    /**
     * 获取统计信息
     *
     * @param id 建筑id
     */
    @RequestMapping(value = "/getRouteCheckExceptionStatistics", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getRouteCheckExceptionStatistics(String id) {
        return ResultMsg.getResultMsg("筑物远传主机获取成功", statRouteCheckExceptionService.getRouteCheckExceptionStatistics(id), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 获取列表
     *
     * @param
     */
    @RequestMapping(value = "/getRouteCheckExceptionList", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getRouteCheckExceptionList(@RequestParam("pageIndex") Integer pageIndex, @RequestParam("pageRows") Integer pageRows, HttpServletRequest request) {
        Map param = ServletsUtil.getParameters(request);
        return ResultMsg.getResultMsg("筑物远传主机获取成功", statRouteCheckExceptionService.getRouteCheckExceptionList(pageIndex, pageRows, param), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }

    /**
     * 处理异常
     *
     * @param
     */
    @RequestMapping(value = "/handleRouteCheckException", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult handleRouteCheckException(HttpServletRequest request, @RequestBody StatRouteCheckException statRouteCheckException) {
        return statRouteCheckExceptionService.handleRouteCheckException(request, statRouteCheckException);
    }

    /**
     * 获取统计信息
     *
     */
    @RequestMapping(value = "/getRouteCheckExceptionStatisticsAll", method = RequestMethod.GET)
    @ResponseBody
    public ResultMsg getRouteCheckExceptionStatisticsAll() {
        return ResultMsg.getResultMsg("筑物远传主机获取成功", statRouteCheckExceptionService.getRouteCheckExceptionStatisticsAll(), Constant.RESPONSE_STATUS_CODE_SUCCESS);
    }
}
