package com.redxun.fire.core.controller;


import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.consts.CloseStoreTimerConstants;
import com.redxun.fire.core.pojo.qo.CloseStoreTimerQO;
import com.redxun.fire.core.pojo.vo.CloseStoreTimerVO;
import com.redxun.fire.core.service.alarm.ICloseStoreTimerService;
import com.redxun.fire.core.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 闭店监测时间管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@RestController
@RequestMapping("/fire/close-store-timer")
public class CloseStoreTimerController {

    @Resource
    ICloseStoreTimerService closeStoreTimerService;

    /**
     * 根据广场id获取指定日期开闭店时间
     */
    @PostMapping("/getCloseStoreTimerByDate")
    public JsonResult getCloseStoreTimerByDate(@RequestBody @Validated CloseStoreTimerQO qo) {
        CloseStoreTimerVO vo = closeStoreTimerService.getCloseStoreTimerByDate(qo);
        return JsonResult.getSuccessResult(vo);
    }

    /**
     * 获取广场指定月份开闭店时间
     */
    @PostMapping("/getCloseStoreTimerByMonth")
    public JsonResult getCloseStoreTimerByMonth(@RequestBody @Validated CloseStoreTimerQO qo) {
//        List<CloseStoreTimerVO> list = closeStoreTimerService.getCloseStoreTimerByMonth(qo);
        JsonResult result = closeStoreTimerService.getMidCloseStoreTimerByMonth(qo);
        return result;
    }

    /**
     * 获取广场级开闭店时间列表
     */
    @PostMapping("/selectBuildingTimeList")
    public JsonResult selectBuildingTimeList(@RequestBody @Validated CloseStoreTimerQO qo) {
        List<CloseStoreTimerVO> list = closeStoreTimerService.selectBuildingTimeList(qo);
        return JsonResult.getSuccessResult(list);
    }

    /**
     * 点位开闭店时间申请
     */
    @PostMapping("/apply")
    public JsonResult apply(@RequestBody @Validated CloseStoreTimerQO qo) {
        // 1验证数据格式
        Integer setLevel = qo.getSetLevel();
        List<CloseStoreTimerVO> voList = qo.getTimerList();

        String dayNum = voList.get(0).getDayNum();

        // 通用校验
        if(!"1".equals(dayNum)) {
            if (CollectionUtils.isNotEmpty(voList) && StringUtils.isNotEmpty(voList.get(0).getBizCloseTime())) {
                if (validateCloseTime(voList)) {
                    return JsonResult.Fail("开店时间应早于闭店时间");
                }
                if (validateOpenTimeAfterClockTime(voList, CloseStoreTimerConstants.OPEN_TIME_LIMIT)) {
                    return JsonResult.Fail("开店时间不应早于" + CloseStoreTimerConstants.OPEN_TIME_LIMIT);
                }
            }
        }
        // 门店级必须将1 2 6 类型同时设置
        if (setLevel != CloseStoreTimerConstants.LV_3.intValue()){
            return JsonResult.Fail("非商户开闭店时间无法设置");
        }else {
            // 1.如果周期包含今天-主动设置当天商户开闭店，限制闭店时间不早于系统当前时间+1小时  2.当天商户开闭店被作废在apply方法中处理
            LocalDate now = LocalDate.now();
            LocalDate parseStart = LocalDate.parse(voList.get(0).getStartDate());
            LocalDate parseEnd = LocalDate.parse(voList.get(0).getEndDate());
            // 结束日期加一天
            parseEnd = parseEnd.plusDays(1);
//            String dayNum = voList.get(0).getDayNum();
            if ("0".equals(dayNum) && now.isBefore(parseEnd) && !now.isBefore(parseStart) && !CloseStoreTimerConstants.DATE_TYPE_7.equals(voList.get(0).getDateType())) {
                // 限制闭店时间不早于系统时间+1小时
                if (validateCloseTimeDelayOneHour(voList)) {
                    return JsonResult.Fail("闭店时间不应早于当前时间延后1小时");
                }
            }
            // 设置次日结束时间需小于开始时间
            if("1".equals(dayNum)){
                // 定义时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

                // 将字符串转换为 LocalTime
                LocalTime startTime = LocalTime.parse(voList.get(0).getOpenTime(), formatter);
                LocalTime endTime = LocalTime.parse(voList.get(0).getBizCloseTime(), formatter);
                Duration duration = Duration.between(startTime, endTime);
                long longmillis = duration.toMillis();
                if(longmillis > 0 ){
                    return JsonResult.Fail("次日闭店时间不应晚于当日开店时间");
                }
            }
        }

        if("00:00".equals(voList.get(0).getOpenTime()) && "24:00".equals(voList.get(0).getBizCloseTime())){
            dayNum = "1";
        }

        return closeStoreTimerService.apply(qo,dayNum);
    }

    /**
     * 广场设置时，验证开闭店时间参数是否存在空值
     *
     * @param voList 开闭店时间
     * @return true-存在空值 false-不存在
     */
    private boolean validateCloseTimeComplete(List<CloseStoreTimerVO> voList) {
        Optional<CloseStoreTimerVO> any = voList.stream().filter(obj -> StringUtils.isEmpty(obj.getOpenTime()) ||
                StringUtils.isEmpty(obj.getBizCloseTime()) ||
                StringUtils.isEmpty(obj.getPlayCloseTime())).findAny();
        return any.isPresent();
    }

    /**
     * 广场设置时，验证开店时间是否早于闭店时间
     *
     * @param voList 开闭店时间
     * @return true-存在开店时间不早于闭店时间 false-开店时间均早于闭店时间
     */
    private boolean validateCloseTime(List<CloseStoreTimerVO> voList) {
        Optional<CloseStoreTimerVO> any = voList.stream().filter(obj -> !(compareTime(obj.getOpenTime(), obj.getBizCloseTime(),obj.getDayNum()) &&
                compareTime(obj.getOpenTime(), obj.getPlayCloseTime(),obj.getDayNum()))).findAny();
        return any.isPresent();
    }

    /**
     * 查找开店时间早于6点的时间配置
     *
     * @param voList
     * @param clockTime
     * @return true-存在早于clockTime的开闭店时间 false-开店时间均晚于clockTime
     */
    private boolean validateOpenTimeAfterClockTime(List<CloseStoreTimerVO> voList, String clockTime) {
        Optional<CloseStoreTimerVO> any = voList.stream().filter(obj -> compareTime(obj.getOpenTime(), clockTime,"0")).findAny();
        return any.isPresent();
    }

    /**
     * 判断闭店时间是否存在不满足当前时间延后一小时
     *
     * @param voList
     * @return true- 存在不满足  false-闭店时间都满足当前延后一小时
     */
    private boolean validateCloseTimeDelayOneHour(List<CloseStoreTimerVO> voList) {
        Optional<CloseStoreTimerVO> any = voList.stream().filter(obj -> !closeStoreTimerService.isTimeDelayOneHour(obj.getBizCloseTime(), obj.getPlayCloseTime())).findAny();
        return any.isPresent();
    }

    /**
     * 判断开店时间是否早于闭店时间
     *
     * @param openTime 开店时间
     * @param endTime  闭店时间
     * @return true-早于 false-不早于（包含相等）
     */
    private boolean compareTime(String openTime, String endTime,String dayNum) {
        LocalTime t1 = LocalTime.parse(openTime);
        LocalTime t2 = LocalTime.parse(endTime);
        if("1".equals(dayNum)){
            return t2.isBefore(t1);
        }else {
            return t1.isBefore(t2);
        }
    }
}
