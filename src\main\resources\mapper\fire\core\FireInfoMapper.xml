<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.FireInfoMapper">

    <resultMap id="FireInfo" type="com.redxun.fire.core.entity.FireInfo">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="belongDep" column="belong_dep" jdbcType="VARCHAR"/>
        <result property="buildingId" column="building_id" jdbcType="VARCHAR"/>
        <result property="buildingName" column="building_name" jdbcType="VARCHAR"/>
        <result property="buildingStatus" column="building_status" jdbcType="VARCHAR"/>
        <result property="buildingStatusStr" column="building_status_str" jdbcType="VARCHAR"/>
        <result property="businessType" column="business_Type" jdbcType="VARCHAR"/>
        <result property="businessTypeStr" column="business_Type_str" jdbcType="VARCHAR"/>
        <result property="cause" column="cause" jdbcType="VARCHAR"/>
        <result property="checkImg" column="check_img" jdbcType="VARCHAR"/>
        <result property="checkImgJson" column="check_img_json" jdbcType="VARCHAR"/>
        <result property="checker" column="checker" jdbcType="VARCHAR"/>
        <result property="checkerid" column="checkerId" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY_" jdbcType="VARCHAR"/>
        <result property="createDepId" column="CREATE_DEP_ID_" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME_" jdbcType="TIMESTAMP"/>
        <result property="devName" column="dev_name" jdbcType="VARCHAR"/>
        <result property="devType" column="dev_type" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="VARCHAR"/>
        <result property="emergencyStatus" column="emergency_status" jdbcType="VARCHAR"/>
        <result property="executeResult" column="execute_result" jdbcType="VARCHAR"/>
        <result property="executeTime" column="execute_time" jdbcType="VARCHAR"/>
        <result property="executor" column="executor" jdbcType="VARCHAR"/>
        <result property="executorId" column="executor_id" jdbcType="VARCHAR"/>
        <result property="fasCode" column="fas_code" jdbcType="VARCHAR"/>
        <result property="fasName" column="fas_name" jdbcType="VARCHAR"/>
        <result property="feedbackResult" column="feedback_result" jdbcType="VARCHAR"/>
        <result property="feedbackTime" column="feedback_time" jdbcType="TIMESTAMP"/>
        <result property="fillUser" column="fill_user" jdbcType="VARCHAR"/>
        <result property="fillUserId" column="fill_user_id" jdbcType="VARCHAR"/>
        <result property="fillintime" column="fillInTime" jdbcType="VARCHAR"/>
        <result property="fireLevel" column="fire_level" jdbcType="VARCHAR"/>
        <result property="fireRemark" column="fire_remark" jdbcType="VARCHAR"/>
        <result property="fireSource" column="fire_source" jdbcType="VARCHAR"/>
        <result property="fireStatus" column="fire_status" jdbcType="VARCHAR"/>
        <result property="fireType" column="fire_type" jdbcType="VARCHAR"/>
        <result property="fireTypeStr" column="fire_type_str" jdbcType="VARCHAR"/>
        <result property="firstTime" column="first_time" jdbcType="VARCHAR"/>
        <result property="flowStatus" column="flow_status" jdbcType="VARCHAR"/>
        <result property="grade" column="grade" jdbcType="VARCHAR"/>
        <result property="hasLinkedPump" column="has_linked_pump" jdbcType="VARCHAR"/>
        <result property="isPick" column="is_pick" jdbcType="VARCHAR"/>
        <result property="isWalkingStreet" column="is_walking_street" jdbcType="VARCHAR"/>
        <result property="lastTime" column="last_time" jdbcType="VARCHAR"/>
        <result property="levelTime" column="level_time" jdbcType="VARCHAR"/>
        <result property="locationDescribe" column="location_describe" jdbcType="VARCHAR"/>
        <result property="organizer" column="organizer" jdbcType="VARCHAR"/>
        <result property="pid" column="pid" jdbcType="VARCHAR"/>
        <result property="pointCode" column="point_code" jdbcType="VARCHAR"/>
        <result property="pointDesc" column="point_desc" jdbcType="VARCHAR"/>
        <result property="pointId" column="point_id" jdbcType="VARCHAR"/>
        <result property="repairmanId" column="repairman_id" jdbcType="VARCHAR"/>
        <result property="reportDesc" column="report_desc" jdbcType="VARCHAR"/>
        <result property="reportSituation" column="report_situation" jdbcType="VARCHAR"/>
        <result property="reportStatus" column="report_status" jdbcType="VARCHAR"/>
        <result property="reportTimes" column="report_times" jdbcType="NUMERIC"/>
        <result property="reportUser" column="report_user" jdbcType="VARCHAR"/>
        <result property="reportUserId" column="report_user_id" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="VARCHAR"/>
        <result property="sensorNum" column="sensor_num" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR"/>
        <result property="testType" column="test_type" jdbcType="VARCHAR"/>
        <result property="testTypeStr" column="test_type_str" jdbcType="VARCHAR"/>
        <result property="testUnit" column="test_unit" jdbcType="VARCHAR"/>
        <result property="tester" column="tester" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="typeSubdivision" column="type_subdivision" jdbcType="VARCHAR"/>
        <result property="typeSubdivisionStr" column="type_subdivision_str" jdbcType="VARCHAR"/>
        <result property="updateBy" column="UPDATE_BY_" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME_" jdbcType="TIMESTAMP"/>
        <result property="uploadStatus" column="upload_status" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="videoUrlJson" column="video_url_json" jdbcType="VARCHAR"/>
        <result property="wrongnumber14" column="wrongNumber14" jdbcType="NUMERIC"/>
        <result property="wrongnumber7" column="wrongNumber7" jdbcType="NUMERIC"/>
        <result property="zoneId" column="zone_id" jdbcType="VARCHAR"/>
        <result property="zoneName" column="zone_name" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="query" resultType="com.redxun.fire.core.entity.FireInfo" parameterType="java.util.Map">
        select
        belong_dep,building_id,building_name,building_status,building_status_str,business_Type,business_Type_str,cause,check_img,checker,checkerId,comment,CREATE_BY_,CREATE_DEP_ID_,CREATE_TIME_,dev_name,dev_type,duration,emergency_status,execute_result,execute_time,executor,executor_id,fas_code,fas_name,feedback_result,feedback_time,fill_user,fill_user_id,fillInTime,fire_level,fire_remark,fire_source,fire_status,fire_type,fire_type_str,first_time,flow_status,grade,has_linked_pump,id,is_pick,is_walking_street,last_time,level_time,location_describe,organizer,pid,point_code,point_desc,point_id,repairman_id,report_desc,report_situation,report_status,report_times,report_user,report_user_id,send_time,sensor_num,TENANT_ID_,test_type,test_type_str,test_unit,tester,type,type_subdivision,type_subdivision_str,UPDATE_BY_,UPDATE_TIME_,upload_status,video_url,wrongNumber14,wrongNumber7,zone_id,zone_name,check_img_json,video_url_json
        from fire_info
        <where>
            <if test="@rx.Ognl@isNotEmpty(w.whereSql)">
                ${w.whereSql}
            </if>
        </where>
        <if test="@rx.Ognl@isNotEmpty(w.orderBySql)">
            ORDER BY ${w.orderBySql}
        </if>
        <if test="@rx.Ognl@isEmpty(w.orderBySql)">
            ORDER BY id DESC
        </if>
    </select>


    <select id="selectInfoById" resultType="com.redxun.fire.core.entity.FireInfo">
        select *
        from fire_info
        where id = #{fireId}
    </select>

    <select id="selectWaitFillInfo" resultType="map">
        select t1.id           as id
             , case
                   when t1.fire_type = '0' then '误报火警'
                   when t1.fire_type = '1' then '测试火警'
                   when t1.fire_type = '2' then '确认火警'
            end                as fireType
             , t1.first_time   as firstTime
             , t2.point_number as pointCode
             , t2.point_desc   as pointDesc
             , t1.execute_time as executeTime
        from fire_info t1
                 left join
             base_device_point t2
             on t1.point_id = t2.id
        where t1.building_id = #{param.buildingId}
          and t1.fire_status = '1'
          and t1.report_status = '0'
          and t1.last_time <![CDATA[>]]> #{param.startTime}
          and t1.last_time <![CDATA[<=]]> #{param.endTime}
          and t1.type = '0'
        order by t1.first_time desc
    </select>

    <select id="selectWaitFillInfoAll" resultType="map">
        select
        t1.id as id
        ,case when t1.fire_type = '0' then '误报火警'
        when t1.fire_type = '1' then '测试火警'
        when t1.fire_type = '2' then '确认火警'
        end as fireType
        ,t1.first_time as firstTime
        ,ifnull(t2.point_number,t1.point_code) as pointCode
        ,ifnull(t2.point_desc,t1.point_desc) as pointDesc
        ,t1.execute_time as executeTime
        ,t1.point_id as pointId
        from fire_info t1
        left join
        base_device_point t2
        on t1.point_id = t2.id
        where
        t1.building_id = #{param.buildingId}
        and
        t1.fire_status = '1'
        and
        t1.report_status = '0'
        and
        t1.last_time <![CDATA[<=]]> #{param.endTime}
        and
        t1.type = '0'
        <if test="param.devType == 'RQZJ'">
            and t1.dev_type in ('RQZJ','ROTT','RQMJ')
        </if>
        <if test="param.devType == 'SJBJ'">
            and dev_type = 'SJBJ'
        </if>
        <if test="param.devType == 'FIRE'">
            and t1.dev_type not in ('SJBJ','RQZJ','ROTT','RQMJ')
        </if>
        <if test="param.fireType != null and param.fireType != '' ">
            and t1.fire_type = #{param.fireType}
        </if>
        order by t1.first_time desc
    </select>

    <select id="selectFireExpection" resultType="com.redxun.fire.core.entity.FireInfo">
        select *
        from fire_info
        where building_id = #{param.buildingId}
          and type = #{param.type}
          and last_time <![CDATA[>]]> #{param.startTime}
          and last_time <![CDATA[<=]]> #{param.endTime}
          and fire_status = '0'
          and building_status_str != '特批'
        order by first_time desc
    </select>

    <select id="selectFireExpectionAll" resultType="com.redxun.fire.core.entity.FireInfo">
        select
        id,execute_time,fillInTime,first_time,fire_status,report_status
        from fire_info
        where building_id = #{param.buildingId}
        and last_time <![CDATA[<=]]> #{param.endTime}
        and type = #{param.type}
        and building_status_str = '正常'
        <if test="param.devType == 'RQZJ'">
            and dev_type in ('RQZJ','ROTT','RQMJ', 'WXRQZJ')
        </if>
        <if test="param.devType == 'SJBJ'">
            and dev_type = 'SJBJ'
        </if>
        <if test="param.devType == 'FIRE'">
            and dev_type not in ('SJBJ','RQZJ','ROTT','RQMJ','WXRQZJ')
        </if>
        order by last_time desc
    </select>

    <select id="selectFireExpectionAllBefore" resultType="com.redxun.fire.core.entity.FireInfo">
        select id,
               execute_time,
               first_time,
               fire_status
        from fire_info
        where building_id = #{param.buildingId}
          and last_time <![CDATA[<=]]> #{param.endTime}
          and type = #{param.type}
          and building_status_str != '特批'
        order by last_time desc
    </select>

    <select id="selectWaitFill" resultType="com.redxun.fire.core.entity.FireInfo">
        select t1.id           as id
             , t1.fire_type    as fireType
             , t1.first_time   as firstTime
             , t2.point_number as pointCode
             , t2.point_desc   as pointDesc
             , t1.execute_time as executeTime
        from fire_info t1
                 left join
             base_device_point t2
             on t1.point_id = t2.id
        where t1.building_id = #{param.buildingId}
          and t1.fire_status = '1'
          and t1.report_status = '0'
          and t1.last_time <![CDATA[>]]> #{param.startTime}
          and t1.last_time <![CDATA[<=]]> #{param.endTime}
          and t1.type = '0'
        order by t1.first_time desc
    </select>

    <select id="selectTodayFireInfo" resultType="map">
        select
        case when t1.type = '0' then '火警' when t1.type = '6' then '预警' end as eventType
        ,ifnull(t2.point_number,t1.point_code) as pointCode
        ,ifnull(t2.point_desc,t1.point_desc) as pointDesc
        ,t2.point_type_name as pointTypeName
        ,ifnull(t2.dev_type_name,t1.dev_name) as devTypeName
        ,t1.first_time as firstTime
        ,t1.fire_status as fireStatus
        ,t1.report_status as reportStatus
        ,t1.execute_time as executeTime
        ,t1.fillInTime as fillInTime
        from
        fire_info t1
        left join
        base_device_point t2 on t1.point_id = t2.id
        where
        t1.type = #{param.infoType}
        <if test="param.buildingId !=null and param.buildingId !=''">
            and t1.building_id = #{param.buildingId}
        </if>
        and t1.last_time <![CDATA[>]]> #{param.startTime}
        and t1.last_time <![CDATA[<=]]> #{param.endTime}
        and t1.building_status_str != '特批'
        order by t1.last_time desc
    </select>

    <!--<select id="selectBeforeFireInfo" resultType="map">
        select
        case when t1.type = '0' then '火警' when t1.type = '6' then '预警' end as eventType
        ,ifnull(t2.point_number,t1.point_code) as pointCode
        ,ifnull(t2.point_desc,t1.point_desc) as pointDesc
        ,t2.point_type_name as pointTypeName
        ,ifnull(t2.dev_type_name,t1.dev_name) as devTypeName
        ,t1.first_time as firstTime
        ,t1.fire_status as fireStatus
        ,t1.report_status as reportStatus
        ,t1.execute_time as executeTime
        ,t1.fillInTime as fillInTime
        from
        fire_info t1
        left join
        base_device_point t2 on t1.point_id = t2.id
        where
        t1.type = #{param.infoType}
        <if test="param.buildingId !=null and param.buildingId !=''">
            and t1.building_id = #{param.buildingId}
        </if>
        and t1.last_time <![CDATA[<=]]> #{param.startTime}
        order by t1.first_time desc
    </select>-->

    <select id="selectTodayFire" resultType="com.redxun.fire.core.entity.FireInfo">
        select id as id,
        building_id as buildingId
        from
        fire_info
        where
        fire_status = '0'
        <if test="param.buildingId !=null and param.buildingId !=''">
            and building_id = #{param.buildingId}
        </if>
        and last_time <![CDATA[>]]> #{param.startTime}
        and last_time <![CDATA[<=]]> #{param.endTime}


    </select>
    <select id="selectFireToScore" resultType="java.lang.Integer">
        select count(point_id) as pointTotle
        from (select point_id
              from fire_info
              where building_id = #{param.buildingId}
                and fire_type = '0'
                and last_time <![CDATA[>]]> #{param.startTime}
                and last_time <![CDATA[<=]]> #{param.endTime}
              group by point_id) t

    </select>
    <select id="selectEmergencyToScore" resultType="java.lang.Double">
        select sum(grade) / count(1) `grade`
        from fire_info
        where building_id = #{param.buildingId}
          and fire_type = '0'
          and last_time <![CDATA[>]]> #{param.startTime}
          and last_time <![CDATA[<=]]> #{param.endTime}
          and grade is not null
          and over_time_status is null

    </select>

    <!--<select id="selectFillInOutTime" resultType="map">

    </select>-->

    <select id="getByFireid" resultType="java.util.Map">
        select t1.check_time as checkTime,
               t2.path       as path
        from fire_check t1
                 left join fire_check_attach t2 on t1.id = t2.check_id
        where t1.fire_id = #{fireId}
    </select>

    <select id="getWbPointDays" resultType="int">
        SELECT COUNT(DISTINCT DATE(last_time))
        FROM fire_info
        WHERE point_id = #{pointId} AND type = #{type} AND last_time between #{fourteenTime} and #{endTime}
          AND building_status_str != #{buildingStatus}
    </select>

    <select id="selectFireInfoToExport" resultType="java.util.Map">
        select
        id,
        building_id,
        point_id,
        pid,
        sensor_num,
        point_desc,
        fas_code,
        fas_name,
        dev_type,
        dev_name,
        fire_type,
        fire_status,
        flow_status,
        fire_level,
        level_time,
        fire_source,
        comment,
        is_pick,
        zone_id,
        zone_name,
        first_time,
        last_time,
        report_times,
        report_user,
        report_user_id,
        report_desc,
        executor_id,
        executor,
        execute_time,
        execute_result,
        emergency_status,
        report_status,
        building_name,
        building_status,
        point_code,
        location_describe,
        report_situation,
        tester,
        organizer,
        DATE_FORMAT(feedback_time,'%Y-%m-%d %H:%i:%s') as feedback_time,
        grade,
        duration,
        cause,
        check_img,
        type,
        fillInTime,
        checker,
        test_type,
        wrongNumber7,
        wrongNumber14,
        building_status_str,
        fire_type_str,
        test_type_str,
        business_Type,
        business_Type_str,
        video_url,
        is_walking_street,
        fire_remark,
        type_subdivision,
        test_unit,
        repairman_id,
        send_time,
        belong_dep,
        checkerId,
        feedback_result,
        type_subdivision_str,
        (select jurisdiction_val from base_building where id = fire_info.building_id ) jurisdiction_val
        from fire_info
        where 1=1
        <if test="param.typeSubdivision !=null and param.typeSubdivision !=''">
            and
            (type_subdivision = #{param.typeSubdivision}
            <if test="param.typeSubdivision == '0'.toString()">
                or type_subdivision = '99'
            </if>
            )
        </if>
        <if test="param.type_subdivision !=null and param.type_subdivision !=''">
            and
            (type_subdivision = #{param.type_subdivision}
            <if test="param.type_subdivision == '0'.toString()">
                or type_subdivision = '99'
            </if>
            )
        </if>
        <if test="param.businessType !=null and param.businessType !=''">
            and business_Type = #{param.businessType}
        </if>
        <if test="param.fireType !=null and param.fireType !=''">
            and fire_type = #{param.fireType}
        </if>
        <if test="param.type !=null and param.type !=''">
            and type = #{param.type}
        </if>
        <if test="param.building_id !=null and param.building_id !=''">
            and building_id = #{param.building_id}
        </if>
        <if test="param.point_code !=null and param.point_code !=''">
            and (CONCAT(point_code) like CONCAT('%',#{param.point_code,jdbcType=VARCHAR},'%'))
        </if>
        <if test="param.dev_type !=null and param.dev_type !=''">
            and dev_type = #{param.dev_type}
        </if>
        <if test="param.point_desc !=null and param.point_desc !=''">
            and (CONCAT(point_desc) like CONCAT('%',#{param.point_desc,jdbcType=VARCHAR},'%'))
        </if>
        <if test="param.lastStartTime !=null and param.lastStartTime !=''">
            and last_time <![CDATA[>=]]> #{param.lastStartTime}
        </if>
        <if test="param.lastEndTime !=null and param.lastEndTime !=''">
            and last_time <![CDATA[<=]]> #{param.lastEndTime}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        order by last_time desc
    </select>
    <select id="selectSyncPage" resultType="com.redxun.fire.core.pojo.sync.dto.SyncFIreInfoDTO">
        SELECT * FROM fire_info a
        WHERE a.building_id = #{qo.buildingId}
        <if test="qo.type !=null and qo.type !=''">
            and a.type = #{qo.type}
        </if>
        <if test="qo.devType !=null and qo.devType !=''">
            and a.dev_type = #{qo.devType}
        </if>
        <if test="qo.devTypeList != null and qo.devTypeList.size() > 0">
            and a.dev_type IN
            <foreach collection="qo.devTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fireStatus !=null and qo.fireStatus !=''">
            and a.fire_status = #{qo.fireStatus}
        </if>
        <if test="qo.buildingStatusStr !=null and qo.buildingStatusStr !=''">
            and a.building_status_str = #{qo.buildingStatusStr}
        </if>
        <if test="qo.startTime != null and qo.endTime != null">
            and a.last_time <![CDATA[ >= ]]> #{qo.startTime}
            and a.last_time <![CDATA[ <= ]]> #{qo.endTime}
        </if>
        <if test="qo.pointCodeList != null and qo.pointCodeList.size() > 0">
            and a.point_code IN
            <foreach collection="qo.pointCodeList" item="code" separator="," open="(" close=")">
                #{code}
            </foreach>
        </if>
        order BY a.last_time desc
    </select>
    <select id="listFireTrendInfo" resultType="com.redxun.fire.core.pojo.dto.FireTrendDto">
        SELECT
            count( a.id ) countNum,
            DATE_FORMAT( a.last_time, '%Y-%m-%d' ) dateTime
        FROM
            fire_info a
        WHERE
            a.type = '0'
          AND a.last_time >=  concat(#{time}, " 00:00:00")
        GROUP BY
            dateTime
        ORDER BY
            dateTime ASC
    </select>
</mapper>


