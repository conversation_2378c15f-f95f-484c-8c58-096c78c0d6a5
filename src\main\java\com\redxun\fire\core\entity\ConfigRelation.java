package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redxun.common.model.SuperEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 功能配置关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ConfigRelation extends SuperEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 配置编码
     */
    @NotBlank(message = "配置编码不能为空")
    @NotNull(message = "配置编码不能为空")
    @Size(max = 20,message = "配置编码长度最大为20")
    private String configCode;

    /**
     * 配置数据对应顺序
     */
    private Integer sort;
    /**
     * 配置数据对应的原来顺序
     */
    @TableField(exist = false)
    private Integer oldSort;

    /**
     * 配置字符串类型数据
     */
    private String configStrVal;


    /**
     * 配置int类型天数设置
     */
    private Double configIntDay;

    /**
     * 配置int类型小时设置
     */
    private Double configIntHour;

    /**
     * 配置int类型分钟设置
     */
    private Double configIntMin;

    /**
     * 配置int类型秒数设置
     */
    private Double configIntSs;

    /**
     * 配置double类型的数据设置
     */
    private Double configDoubleData;
    /**
     * 短信通知人id集
     */
    private String configTzrId;
    /**
     * 短信通知人id集
     */
    private String configTzrIdNew;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置节点时间类型开始时间设置
     */
    private String configDateStart;

    /**
     * 配置节点时间类型结束时间设置
     */
    private String configDateEnd;

    /**
     * 租用用户Id
     */
    @TableField("TENANT_ID_")
    private String tenantId;


    @Override
    public Object getPkId() {
        return null;
    }

    @Override
    public void setPkId(Object pkId) {

    }
    /**
     * 每次最小下降值（mpa/次）
     */
    private Double minDecreasePerTime;
    
    /**
     * 连续下降触发报警次数
     */
    private Integer consecutiveDecreaseAlarmCount;
    
    /**
     * 低压报警值（mpa）
     */
    private Double lowPressureAlarmValue;
}
