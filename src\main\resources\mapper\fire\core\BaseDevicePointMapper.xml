<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redxun.fire.core.mapper.BaseDevicePointMapper">

    <select id="getDevInfoByIdAndType" parameterType="String" resultType="com.redxun.fire.core.entity.BaseDevicePoint">

    </select>
    <select id="countPiontsById" resultType="java.util.Map">
        SELECT bdp.building_id,
               COUNT(bdp.building_id) AS totalPoints,
               COUNT(
                       CASE
                           WHEN bdp.dev_type_cat = '0' and bdp.dev_type_cat = '0' THEN
                               bdp.id END
                   )                  AS hostPoints

        FROM base_device_point bdp
        WHERE bdp.building_id = #{id}
    </select>

    <select id="countTypePionts" resultType="com.redxun.fire.core.pojo.dto.PointCountDto">
        select super_type as superType,count(1) as counts from base_device_point WHERE building_id = #{id} and super_type is not null GROUP BY superType;
    </select>


    <select id="selectFasName" resultType="java.lang.String">
        select t1.fas_name
        from base_device_point t1
        where t1.building_id = #{buildingId}
          and t1.fas_name is not null
        group by t1.fas_name
    </select>

    <select id="selectLoopName" resultType="map">
        select distinct concat(t1.loop_code, '-', t1.host_num) as loopCodeName
                      , t1.loop_code                           as loopCode
        from base_device_point t1
        where t1.building_id = #{param0.buildingId}
          and t1.fas_name = #{param0.fasName}
          and t1.loop_code is not null
    </select>

    <select id="selectTreePoint" resultType="map">
        select t1.point_code
             , t1.point_desc
             , t1.id as pointId
        from base_device_point t1
        where t1.building_id = #{buildingId}
          and t1.fas_name = #{fasName}
          and t1.loop_code = #{loopName}
    </select>

    <select id="selectFasNameByStep" resultType="map">
        select distinct t1.fas_name as fasName
        from base_device_point t1
        where t1.building_id = #{param.buildingId}
          and t1.fas_name is not null
    </select>

    <select id="selectLoopNameByStep" resultType="map">
        select distinct t1.fas_name                            as fasName,
                        concat(t1.host_num, '-', t1.loop_code) as loopCodeName
        from base_device_point t1
        where t1.building_id = #{param.buildingId}
          and t1.fas_name = #{param.fasName}
        order by t1.host_num, t1.loop_code
    </select>

    <select id="selectPointInfoByStep" resultType="map">
        select distinct t1.fas_name                            as fasName,
                        concat(t1.host_num, '-', t1.loop_code) as loopCodeName,
                        t1.host_num                            as hostNum,
                        t1.loop_code                           as loopCode,
                        t1.point_number                        as pointNumber,
                        t1.point_desc                          as pointDesc,
                        t1.dev_type_name                       as devTypeName,
                        t1.id                                  as pointId
        from base_device_point t1
        where t1.building_id = #{param.buildingId}
          and t1.fas_name = #{param.fasName}
          and concat(t1.host_num, '-', t1.loop_code) = #{param.loopName}
        order by t1.host_num, t1.loop_code, t1.point_number, t1.point_desc
    </select>
    <select id="selectBaseDevicePointToExport" resultType="java.util.Map">
        select
        *
        from base_device_point
        where 1=1
        <if test="param.devTypeCode != null and param.devTypeCode !=''">
            and dev_type_code  = #{param.devTypeCode}
        </if>
        <if test="param.devTypeCat !=null and param.devTypeCat !=''">
            and dev_type_cat = #{param.devTypeCat}
        </if>
        <if test="param.pointType !=null and param.pointType !=''">
            and point_type = #{param.pointType}
        </if>
        <if test="param.superType !=null and param.superType !=''">
            and super_type = #{param.superType}
        </if>
        <if test="param.buildingId !=null and param.buildingId !=''">
            and building_id = #{param.buildingId}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        order by CREATE_TIME_ desc

    </select>
    <select id="selectBaseDevicePoint2ToExport" resultType="java.util.Map">
        select * from (
        select b.building_name,p.*, '正常' fault_type_str from base_building b
        INNER JOIN base_device_point p on b.id =  p.building_id where  1=1 and  dev_type_cat = 1
        <if test="param.buildingId !=null and param.buildingId !=''">
            and p.building_id = #{param.buildingId}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and p.building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        union all
        select b.building_name , a.*,
        case
        when a.fault_status  = '0' then '单点故障'
        when a.fault_status = '2' then '设备欠压'
        when a.fault_status = '3' then '设备拆卸'
        when a.fault_status = '4' then '设备离线'
        else '正常' end as fault_type_str  from  base_device_point a
        inner join base_building b on a.building_id  = b.id and a.dev_type_code in ('WXYG','WXWG')
        <if test="param.buildingId !=null and param.buildingId !=''">
            and a.building_id = #{param.buildingId}
        </if>
        <if test="param.buildList != null and param.buildList.size > 0">
            and a.building_id in
            <foreach item="item1" index="index" collection="param.buildList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        ) a where 1=1
        <if test="param.devTypeCode != null and param.devTypeCode !=''">
            and dev_type_code  = #{param.devTypeCode}
        </if>
        <if test="param.devTypeCat !=null and param.devTypeCat !=''">
            and dev_type_cat = #{param.devTypeCat}
        </if>
        <if test="param.pointType !=null and param.pointType !=''">
            and point_type = #{param.pointType}
        </if>
        <if test="param.superType !=null and param.superType !=''">
            and super_type = #{param.superType}
        </if>

        order by a.CREATE_TIME_ desc

    </select>



    <select id="normalPointPage" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select * from base_device_point where building_id = #{param.buildingId} and dev_type_cat = 0 and point_type = 0
        and super_type = 0 and dev_type_code not in ('WXYG','WXWG')
        <if test="param.fasCode !=null and param.fasCode !=''">
            and fas_code = #{param.fasCode}
        </if>
        <if test="param.devTypeCode !=null and param.devTypeCode !=''">
            and dev_type_code = #{param.devTypeCode}
        </if>
        <if test="param.floorId !=null and param.floorId !=''">
            and floor_id = #{param.floorId}
        </if>
        <if test="param.zoneId !=null and param.zoneId !=''">
            and zone_id = #{param.zoneId}
        </if>
        <if test="param.pointNumber !=null and param.pointNumber !=''">
            and point_number like #{param.pointNumber}
        </if>
        <if test="param.pointDesc !=null and param.pointDesc !=''">
            and point_desc like #{param.pointDesc}
        </if>

    </select>


    <select id="selectNormalList" resultType="java.util.Map">
        select t1.id as id,
        t1.point_number as pointNumber,
        t1.fas_name as fasName,
        t1.point_desc as pointDesc,
        t1.zone_name as zoneName,
        t1.dev_type_name as devTypeName
        from base_device_point as t1
        where t1.building_id = #{param.id}
        and dev_type_cat = 0 and point_type = 0 and super_type = 0
        <if test="param.pointNumber !=null and param.pointNumber !='' ">
            and t1.point_number like  concat ('%',#{param.pointNumber},'%')
        </if>
        <if test="param.pointDesc !=null and param.pointDesc !='' ">
            and t1.point_desc like concat('%',#{param.pointDesc},'%')
        </if>
    </select>
    <select id="selectByParam" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select
        *
        from
        base_device_point
        where
        building_id = #{param.buildingId}
        and point_type != '1'
        and dev_type_code in
        <foreach collection="param.deviceCodes" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
    </select>

    <select id="selectIdByParam"  resultType="java.lang.String">
        select
            id
        from
        base_device_point
        where
        building_id = #{param.buildingId}
        and point_type != '1'
        and dev_type_code in
        <foreach collection="param.deviceCodes" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
    </select>

    <select id="groupByBuildingAndLoopAndDeviceType" resultType="com.redxun.fire.core.pojo.vo.LoopPointVo">
        select
        local_host_num as hostNum,
        local_loop_code as loopCode,
        dev_type_code as deviceType,
        count(*) total
        from base_device_point where building_id=#{buildId}
        AND
        point_type != '1' and
        dev_type_code
        in
        <foreach collection="deviceType" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND
        local_host_num is not NULL
        and
        local_loop_code is not null
        group by
        local_host_num,local_loop_code,dev_type_code
        order by
        local_host_num,local_loop_code

    </select>

    <select id="selectCloseStorePointList" resultType="com.redxun.fire.core.pojo.vo.CloseStorePointVO"
            parameterType="com.redxun.fire.core.pojo.qo.CloseStorePointQO">
        select a.id,
        a.building_id as buildId,
        a.host_id as hostId,
        a.did,
        a.point_type as pointType,
        a.point_type_name as pointTypeName,
        a.super_type as superType,
        a.dev_type_code as devTypeCode,
        a.dev_type_name as devTypeName,
        a.sim_num as simNum,
        a.point_status as pointStatus,
        a.position_desc as positionDesc,
        a.point_desc as pointDesc,
        a.loop_code as loopCode,
        a.floor_id as floorId,
        a.floor,
        a.floor_name as floorName,
        a.build_name as buildName,
        a.vender_code as venderCode,
        a.vender_name as venderName,
        a.join_code as joinCode,
        a.join_name as joinName,
        a.device_no as deviceNo,
        a.device_name as deviceName,
        a.local_host_num as localHostNum,
        a.local_loop_code as localLoopCode,
        a.transmission_number as transmissionNumber,
        a.point_number as pointNumber,
        a.expired_time as expiredTime,
        a.dtu_no as dtuNo,
        a.mid_merchant_id as midMerchantId,
        c.merchant_name as midMerchantName,
        c.merchant_code as midMerchantCode,
        c.type_code as midMerchantTypeCode,
        c.type_name as midMerchantTypeName,
        c.biz_type as bizType,
        a.UPDATE_TIME_ as updateTime,
        d.loop_type as loopType,
        d.close_store_remind_time as closeStoreRemindTime
        from base_device_point a
        LEFT JOIN mid_merchant c on c.id = a.mid_merchant_id
        LEFT JOIN device_monitor_info d ON a.id = d.base_point_id
        where a.building_id =#{buildId}
        AND a.super_type ='16'
        and a.point_status ='0' and a.dev_type_name = #{devTypeName}
        <if test="midMerchantTypeCode !=null and midMerchantTypeCode !='' ">
            and c.type_code =#{midMerchantTypeCode}
        </if>
        <if test="midMerchantName !=null and midMerchantName !='' ">
            and c.merchant_name =#{midMerchantName}
        </if>
        <if test="deviceNo !=null and deviceNo !='' ">
            and a.dtu_no =#{deviceNo}
        </if>
        <if test="devTypeCat !=null and devTypeCat == '0' ">
            and c.devTypeCat != '1'
        </if>
        <if test="devTypeCat !=null and devTypeCat == '1' ">
            and c.devTypeCat = '1'
        </if>
        <if test="loopType !=null and loopType !='' ">
            and d.loop_type =#{loopType}
        </if>
        order by a.UPDATE_TIME_ desc
    </select>
    <select id="selectCloseStorePointListPage" resultType="com.redxun.fire.core.pojo.vo.CloseStorePointVO"
            parameterType="com.redxun.fire.core.pojo.qo.CloseStorePointQO">
        select a.id,
        a.building_id as buildId,
        a.host_id as hostId,
        a.did,
        a.point_type as pointType,
        a.point_type_name as pointTypeName,
        a.super_type as superType,
        a.dev_type_code as devTypeCode,
        a.dev_type_name as devTypeName,
        a.sim_num as simNum,
        a.point_status as pointStatus,
        a.position_desc as positionDesc,
        a.point_desc as pointDesc,
        a.loop_code as loopCode,
        a.floor_id as floorId,
        a.floor,
        a.floor_name as floorName,
        a.build_name as buildName,
        a.vender_code as venderCode,
        a.vender_name as venderName,
        a.join_code as joinCode,
        a.join_name as joinName,
        a.device_no as deviceNo,
        a.device_name as deviceName,
        a.local_host_num as localHostNum,
        a.local_loop_code as localLoopCode,
        a.transmission_number as transmissionNumber,
        a.point_number as pointNumber,
        a.expired_time as expiredTime,
        a.dtu_no as dtuNo,
        a.mid_merchant_id as midMerchantId,
        c.merchant_name as midMerchantName,
        c.merchant_code as midMerchantCode,
        c.type_code as midMerchantTypeCode,
        c.type_name as midMerchantTypeName,
        c.biz_type as bizType,
        a.UPDATE_TIME_ as updateTime
        from base_device_point a
        LEFT JOIN mid_merchant c on c.id = a.mid_merchant_id
        where a.building_id =#{qo.buildId}
        AND a.super_type ='16'
        and a.point_status ='0'
        <if test="qo.pointDesc !=null and qo.pointDesc !='' ">
            and a.point_desc like concat('%', #{qo.pointDesc}, '%')
        </if>
        <if test="qo.midMerchantTypeCode !=null and qo.midMerchantTypeCode !='' ">
            and c.type_code =#{qo.midMerchantTypeCode}
        </if>
        <if test="qo.midMerchantName !=null and qo.midMerchantName !='' ">
            and c.merchant_name like concat('%', #{qo.midMerchantName}, '%')
        </if>
        <if test="qo.merchantIdList !=null and qo.merchantIdList.size >0 ">
            and c.id IN
            <foreach item="item2" index="index" collection="qo.merchantIdList"
                     open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>

        order by a.UPDATE_TIME_ desc
    </select>



    <select id="selectCloseStorePointListByMid" resultType="com.redxun.fire.core.pojo.vo.CloseStorePointVO"
            parameterType="com.redxun.fire.core.pojo.qo.CloseStorePointQO">
        select a.id,
        a.building_id as buildId,
        a.host_id as hostId,
        a.did,
        a.point_type as pointType,
        a.point_type_name as pointTypeName,
        a.super_type as superType,
        a.dev_type_code as devTypeCode,
        a.dev_type_name as devTypeName,
        a.sim_num as simNum,
        a.point_status as pointStatus,
        a.position_desc as positionDesc,
        a.point_desc as pointDesc,
        a.loop_code as loopCode,
        a.floor_id as floorId,
        a.floor,
        a.floor_name as floorName,
        a.build_name as buildName,
        a.vender_code as venderCode,
        a.vender_name as venderName,
        a.join_code as joinCode,
        a.join_name as joinName,
        a.device_no as deviceNo,
        a.device_name as deviceName,
        a.local_host_num as localHostNum,
        a.local_loop_code as localLoopCode,
        a.transmission_number as transmissionNumber,
        a.point_number as pointNumber,
        a.expired_time as expiredTime,
        a.dtu_no as dtuNo,
        a.mid_merchant_id as midMerchantId,
        c.merchant_name as midMerchantName,
        c.merchant_code as midMerchantCode,
        c.type_code as midMerchantTypeCode,
        c.type_name as midMerchantTypeName,
        c.biz_type as bizType,
        a.UPDATE_TIME_ as updateTime
        from base_device_point a
        LEFT JOIN mid_merchant c on c.id = a.mid_merchant_id
        where a.mid_merchant_id =#{qo.midMerchantId}
        AND a.super_type ='16'
        and a.point_status ='0'
        order by a.UPDATE_TIME_ desc
    </select>


    <select id="specialPointPage" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select *
        from base_device_point
        where
        building_id = #{param.buildingId}
        and dev_type_cat = '0'
        and point_type = '1'
        and super_type = '0'
        <if test="param.fasCode !=null and param.fasCode !=''">
            and fas_code = #{param.fasCode}
        </if>
        <if test="param.devTypeCode !=null and param.devTypeCode !=''">
            and dev_type_code = #{param.devTypeCode}
        </if>
        <if test="param.floorId !=null and param.floorId !=''">
            and floor_id = #{param.floorId}
        </if>
        <if test="param.zoneId !=null and param.zoneId !=''">
            and zone_id = #{param.zoneId}
        </if>
        <if test="param.pointNumber !=null and param.pointNumber !=''">
            and point_number like #{param.pointNumber}
        </if>
        <if test="param.pointDesc !=null and param.pointDesc !=''">
            and point_desc like #{param.pointDesc}
        </if>
        order by CREATE_TIME_ desc
    </select>

    <select id="getPointDataByBuildAndPointNumber" resultType="com.redxun.fire.core.entity.BaseDevicePoint">
        select id, dev_type_code
        from base_device_point
        where building_id = #{buildingId}
        and point_number in
        <foreach collection="pointNumberList" index="index" item="pointNumber" open="(" separator="," close=")">
            #{pointNumber}
        </foreach>
    </select>

    <select id="selectCloseStorePointVOListByMid" resultType="com.redxun.fire.core.pojo.vo.CloseStorePointVO"
            parameterType="com.redxun.fire.core.pojo.qo.CloseStorePointQO">
        select a.id,
        a.building_id as buildId,
        a.mid_merchant_id as midMerchantId,
        a.host_id as hostId,
        a.point_number as pointNumber,
        a.point_desc as pointDesc,
        a.dev_type_name as devTypeName
        from base_device_point a
        where 1=1
        <if test="qo.midMerchantIds != null and qo.midMerchantIds.size > 0">
            and a.mid_merchant_id in
            <foreach item="item1" index="index" collection="qo.midMerchantIds" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="qo.midMerchantId !=null and qo.midMerchantId !='' ">
            and a.mid_merchant_id = #{qo.midMerchantId}
        </if>
        AND a.super_type ='16'
        and a.point_status ='0'
        order by a.UPDATE_TIME_ desc
    </select>
    <select id="selectCloseStorePointPage" resultType="com.redxun.fire.core.pojo.vo.CloseStorePointVO">
        select a.id,
        a.building_id as buildId,
        a.host_id as hostId,
        a.did,
        a.point_type as pointType,
        a.point_type_name as pointTypeName,
        a.super_type as superType,
        a.dev_type_code as devTypeCode,
        a.dev_type_name as devTypeName,
        a.sim_num as simNum,
        a.point_status as pointStatus,
        a.position_desc as positionDesc,
        a.point_desc as pointDesc,
        a.loop_code as loopCode,
        a.floor_id as floorId,
        a.floor,
        a.floor_name as floorName,
        a.build_name as buildName,
        a.vender_code as venderCode,
        a.vender_name as venderName,
        a.join_code as joinCode,
        a.join_name as joinName,
        a.device_no as deviceNo,
        a.device_name as deviceName,
        a.local_host_num as localHostNum,
        a.local_loop_code as localLoopCode,
        a.transmission_number as transmissionNumber,
        a.point_number as pointNumber,
        a.expired_time as expiredTime,
        a.dtu_no as dtuNo,
        a.mid_merchant_id as midMerchantId,
        c.merchant_name as midMerchantName,
        c.merchant_code as midMerchantCode,
        c.type_code as midMerchantTypeCode,
        c.type_name as midMerchantTypeName,
        c.biz_type as bizType,
        a.UPDATE_TIME_ as updateTime,
        a.CREATE_TIME_ as createTime,
        h.iccid
        from base_device_point a
        LEFT JOIN mid_merchant c on c.id = a.mid_merchant_id
        LEFT JOIN close_store_host h on h.id = a.host_id
        where a.building_id =#{qo.buildId}
        AND a.super_type ='16'
        and a.point_status ='0'
        <if test="qo.pointDesc !=null and qo.pointDesc !='' ">
            and a.point_desc like concat('%', #{qo.pointDesc}, '%')
        </if>
        <if test="qo.floorId !=null and qo.floorId !='' ">
            and a.floor_id =#{qo.floorId}
        </if>
        <if test="qo.midMerchantTypeCode !=null and qo.midMerchantTypeCode !='' ">
            and c.type_code =#{qo.midMerchantTypeCode}
        </if>
        <if test="qo.midMerchantCode !=null and qo.midMerchantCode !='' ">
            and c.merchant_code like concat('%', #{qo.midMerchantCode}, '%')
        </if>
        <if test="qo.midMerchantName !=null and qo.midMerchantName !='' ">
            and c.merchant_name like concat('%', #{qo.midMerchantName}, '%')
        </if>
        order by a.UPDATE_TIME_ desc
    </select>


    <select id="getCameraDomain" resultType="String" parameterType="String">
        SELECT domain_address FROM camera_ip_domain WHERE ip_address = #{ipAddress}
    </select>

</mapper>
