
package com.redxun.fire.core.controller;

import com.redxun.common.annotation.ClassDefine;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.pojo.dto.BaseBuildingDto;
import com.redxun.fire.core.pojo.vo.BaseBuildInfoVo;
import com.redxun.fire.core.utils.validated.Update;
import com.redxun.fire.core.service.building.impl.BaseBuildingServiceImpl;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/base-building")
@Api(tags = "建筑物信息表")
@ClassDefine(title = "建筑物信息表", alias = "BaseBuildingController", path = "/base-building", packages = "core", packageName = "子系统名称")
public class BaseBuildingController {

    @Autowired
    BaseBuildingServiceImpl baseBuildingService;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;

    /**
     * 通过建筑物id来获取单店管理中的单店信息
     *
     * @param id 建筑物id
     * @return {@link JsonResult}
     */
    @ApiOperation(value = "通过建筑物id来获取单店管理中的单店信息")
    @GetMapping("/getBaseInfo/{id}")
    public JsonResult<BaseBuildInfoVo> getBuildingBaseInfo(HttpServletRequest request, @PathVariable("id") String id) {
        JsonResult<BaseBuildInfoVo> result = JsonResult.Success();
        BaseBuildInfoVo baseBuildInfoVo = baseBuildingService.getSquareInfoById(request, id);
        result.setData(baseBuildInfoVo);
        return result;
    }

    /**
     * 编辑时数据回显
     *
     * @param id 建筑物id
     * @return {@link JsonResult}
     */
    @GetMapping("/getBuildInfo/{id}")
    public JsonResult<BaseBuilding> getBuildingInfo(@PathVariable("id") String id) {
        JsonResult<BaseBuilding> result = JsonResult.Success();
        BaseBuilding baseBuilding = baseBuildingService.getBuildingInfoById(id);
        result.setData(baseBuilding);
        return result;
    }


    /**
     * 建筑物信息的图片编辑
     *
     * @param baseBuildingDto {@link BaseBuildingDto}
     * @return {@link JsonResult}
     */
    @PostMapping("/editBuildPicInfo")
    public JsonResult<String> transferBuildingPic(@RequestBody @Validated(Update.class) BaseBuildingDto baseBuildingDto) {
        JsonResult<String> result = JsonResult.Success();
        baseBuildingService.editBuildingPicInfo(baseBuildingDto);
        result.setData("edit building pic success");
        return result;
    }

    /**
     * 建筑物信息编辑
     *
     * @param baseBuildingDto {@link BaseBuildingDto}
     * @return {@link JsonResult}
     */
    @PostMapping("/editBuildInfo")
    public JsonResult<String> editBuildingInfo(@RequestBody @Validated BaseBuildingDto baseBuildingDto, BindingResult validResult) {
        if (validResult.hasErrors()) {
            JsonResult result = JsonResult.Fail("编辑建筑信息失败");
            List<ObjectError> allErrors = validResult.getAllErrors();
            StringBuilder sb = new StringBuilder();
            for (ObjectError error : allErrors) {
                if (error instanceof FieldError) {
                    sb.append("[" + ((FieldError) error).getField() + "]");
                }
                sb.append(error.getDefaultMessage());
                sb.append("\r\n");
            }
            result.setData(sb.toString());
            return result;
        }
        JsonResult<String> result = JsonResult.Success();
        baseBuildingService.editBuildingInfo(baseBuildingDto);
        result.setData("edit building success");
        return result;
    }

    /**
     * 获取广场列表(分页)
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/findAllPlazaPageResponse")
    @ApiOperation("获取广场列表(分页)")
    public JsonPageResult findAllPlazaPageResponse(HttpServletRequest request, @RequestBody @Validated QueryData queryData) {
        return orgMiddleService.findAllPlazaPageResponse(request, queryData);
    }

    /**
     * 判断当前广场是否为万达广场
     */
    @ResponseBody
    @GetMapping(value = "/checkWandaBuilding")
    public JsonResult checkWandaBuilding(HttpServletRequest request, String building) {
        boolean check = baseBuildingService.checkWandaBuilding(building);
        JsonResult success = JsonResult.Success();
        success.setData(check);
        return success;
    }


}

