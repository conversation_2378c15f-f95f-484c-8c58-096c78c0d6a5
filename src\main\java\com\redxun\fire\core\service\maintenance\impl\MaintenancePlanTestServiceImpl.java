package com.redxun.fire.core.service.maintenance.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.mapper.MaintenancePlanMapper;
import com.redxun.fire.core.mapper.MaintenanceScopeTestMapper;
import com.redxun.fire.core.pojo.vo.DevicePointInfoVo;
import com.redxun.fire.core.pojo.vo.LoopPointVo;
import com.redxun.fire.core.pojo.vo.ScopeTestVo;
import com.redxun.fire.core.service.device.IBaseDevicePointService;
import com.redxun.fire.core.service.device.IPointBatchService;
import com.redxun.fire.core.service.maintenance.IMaintenanceConfigService;
import com.redxun.fire.core.service.maintenance.IMaintenanceItmesRPlanService;
import com.redxun.fire.core.service.maintenance.IWbPlanFormulate;
import com.redxun.fire.core.service.maintenance.MaintenancePlanService;
import com.redxun.fire.core.service.maintenance.MaintenancePlanTestService;
import com.redxun.fire.core.service.maintenance.MaintenanceScopeTestService;
import com.redxun.fire.core.service.other.IContractConfigService;
import com.redxun.fire.core.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;


/**
 * <p>
 * 维保计划表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-07
 */
@Service
@Slf4j
public class MaintenancePlanTestServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlan> implements MaintenancePlanTestService {

    @Autowired
    MaintenancePlanService maintenancePlanService;

    @Autowired
    private IMaintenanceConfigService iMaintenanceConfigService;

    @Autowired
    private IBaseDevicePointService iBaseDevicePointService;

    @Autowired
    IMaintenanceItmesRPlanService iMaintenanceItmesRPlanService;

    @Autowired
    MaintenanceScopeTestService iMaintenanceScopeTestService;

    @Autowired
    IPointBatchService iPointBatchService;

    @Autowired
    IContractConfigService iContractConfigService;

    @Autowired
    private IWbPlanFormulate wbPlanFormulate;

    @Autowired
    private MaintenanceScopeTestMapper scopeTestMapper;

    private List<Integer> loopMonth = Arrays.asList(1, 2, 4, 5, 7, 8, 10, 11);

    /**
     * 生成测试计划（楼层 回路 防火分区）和检查计划
     *
     * @param wbContract
     * @return
     */
    @Override
    public boolean createTestPlan(HttpServletRequest request, WbContract wbContract) {
        try {
            if (wbContract != null) {
                log.info("开始生成维保计划,建筑id为{}， 合同信息为：{}", wbContract.getBuildId(), JSONObject.toJSONString(wbContract));
                Date startDate = DateUtil.parseDatetime(wbContract.getStartTime());
                Date endDate = DateUtil.parseDatetime(wbContract.getEndTime());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                int year = calendar.get(Calendar.YEAR);
                calendar.setTime(endDate);
                int year2 = calendar.get(Calendar.YEAR);
                // 回路/防火分区列表
                Map<String, List<BaseDevicePoint>> loopCodeList = new HashMap<>();
                // 每个回路/防火分区对应数量
                Map<String, Map<String, Integer>> loopCodeCount = new HashMap<>();
                Map<String, Integer> count = new HashedMap();
                // 查询回路对应设备类型
                List<MaintenanceConfig> dataByGenerationRule = iMaintenanceConfigService.getDataByGenerationRule("2", "");
                List<String> deviceType = new ArrayList<>();
                Map<String, String> deviceIdMap = new HashMap<>();
                for (MaintenanceConfig config : dataByGenerationRule) {
                    if (null != config && null != config.getDeviceCode()) {
                        deviceType.add(config.getDeviceCode());
                        deviceIdMap.put(config.getDeviceCode(), config.getId());
                    }
                }

                List<LoopPointVo> loopPointVo = iBaseDevicePointService.groupByBuildingAndLoopAndDeviceType(wbContract.getBuildId(), deviceType);

                Map<Integer, List<LoopPointVo>> loopMonthMap = new HashMap<>();

                if (!CollectionUtils.isEmpty(loopPointVo)) {

                    int totalSize = loopPointVo.size();
                    Integer loopT = 0;
                    if (totalSize > 8) {
                        loopT = totalSize / 8;
                    } else {
                        loopT = 1;
                    }

                    for (int j = 1; j <= loopMonth.size(); j++) {
                        Integer month = loopMonth.get(j - 1);
                        if (loopT > 1) {
                            if (j == loopMonth.size()) {
                                List<LoopPointVo> sub = loopPointVo.subList((j - 1) * loopT, loopPointVo.size());
                                loopMonthMap.put(month, sub);
                            } else {
                                List<LoopPointVo> sub = loopPointVo.subList((j - 1) * loopT, j * loopT);
                                loopMonthMap.put(month, sub);
                            }
                        } else {
                            if (j <= loopPointVo.size() - 1) {
                                List<LoopPointVo> sub = loopPointVo.subList((j - 1) * loopT, j * loopT);
                                loopMonthMap.put(month, sub);
                            }
                        }
                    }
                }

/*

            for (int j = 0; j < dataByGenerationRule.size(); j++) {
                // 根据设备类型查询点位
                if (dataByGenerationRule.get(j) != null && StringUtils.isNotEmpty(dataByGenerationRule.get(j).getDeviceCode())) {
                    List<BaseDevicePoint> baseDevicePoints = iBaseDevicePointService.listAllPointsByDeviceNo(wbContract.getBuildId(), dataByGenerationRule.get(j).getDeviceCode());
                    // 按照回路号进行分组
                    Map<String, List<BaseDevicePoint>> stringListMap = groupBillingDataByExcpBatchCode(baseDevicePoints);
                    Map<String, Integer> countMap = new HashMap<>();
                    List<BaseDevicePoint> codeList = new ArrayList<>();
                    for (List<BaseDevicePoint> entry : stringListMap.values()) {
                        codeList.add(entry.get(0));
                        String loopCode = entry.get(0).getLocalLoopCode();
                        String hostNum = entry.get(0).getLocalHostNum();
                        countMap.put(hostNum + "-" + loopCode, entry.size());
                    }
                    loopCodeList.put(dataByGenerationRule.get(j).getDeviceCode() + "CodeList", codeList);
                    loopCodeCount.put(dataByGenerationRule.get(j).getDeviceCode() + "CodeMap", countMap);
                    if (codeList.size() > 0) {
                        count.put(dataByGenerationRule.get(j).getDeviceCode() + "CodeStart", codeList.size() / 8);
                        // 余数
                        count.put(dataByGenerationRule.get(j).getDeviceCode() + "CodeEnd", codeList.size() % 8);
                    }
                }
            }*/
                // 查询防火分区的维保项
                List<MaintenanceConfig> zone = iMaintenanceConfigService.getDataByGenerationRule("3", "");
                for (int n = 0; n < zone.size(); n++) {
                    if (zone.get(n) != null && StringUtils.isNotEmpty(zone.get(n).getDeviceCode())) {
                        // 根据设备类型查询点位
                        List<BaseDevicePoint> baseDevicePoints2 = iBaseDevicePointService.listAllPointsByDeviceNo(wbContract.getBuildId(), zone.get(n).getDeviceCode());
                        // 按照防火分区进行分组
                        Map<String, List<BaseDevicePoint>> stringListMap2 = groupBillingDataByZone(baseDevicePoints2);
                        Map<String, Integer> countMap = new HashMap<>();
                        List<BaseDevicePoint> codeList = new ArrayList<>();
                        for (List<BaseDevicePoint> entry : stringListMap2.values()) {
                            codeList.add(entry.get(0));
                            countMap.put(entry.get(0).getZoneName(), entry.size());
                        }
                        loopCodeList.put(zone.get(n).getDeviceCode() + "ZoneList", codeList);
                        loopCodeCount.put(zone.get(n).getDeviceCode() + "ZoneMap", countMap);
                        if (codeList.size() > 0) {
                            count.put(zone.get(n).getDeviceCode() + "ZoneStart", codeList.size() / 4);
                            // 余数
                            count.put(zone.get(n).getDeviceCode() + "ZoneEnd", codeList.size() % 4);
                        }
                    }
                }
                while (year2 >= year) {
                    // 按系统-回路 1、2、4、5、7、8、10、11月份
                    int countM = 0;
                    int countY = 0;
                    for (int i = 1; i <= 12; i++) {
                        String effectiveTime = i < 10 ? year + "-0" + i : year + "-" + i;
                        // 生成维保测试计划
                        MaintenancePlan maintenancePlan = createMaintenancePlan(request, wbContract, effectiveTime, "0");
                        // 生成维保检查计划
                        MaintenancePlan maintenancePlan2 = createMaintenancePlan(request, wbContract, effectiveTime, "1");
                        log.debug(effectiveTime + "月份");
                        List<MaintenanceItmesRPlan> maintenanceItmesRPlanList = new ArrayList<>();
                        List<MaintenanceScopeTest> maintenanceScopeTestList = new ArrayList<>();
                        int counts = 0;
                        if (i == 3 || i == 6 || i == 9 || i == 12) {
                            // 查询防火分区的维保项
                            for (int n = 0; n < zone.size(); n++) {
                                MaintenanceConfig item = zone.get(n);
                                // 生成维保项和维保计划关联表
                                String fhfqid = IdGenerator.getIdStr();
                                MaintenanceItmesRPlan itmesRPlan = new MaintenanceItmesRPlan();
                                itmesRPlan.setId(fhfqid);
                                itmesRPlan.setMaintenancePlanId(maintenancePlan.getId());
                                itmesRPlan.setMaintenanceConfigId(item.getId());
                                // 3、6、9、12月分别测试25%的防火分区
                                List<BaseDevicePoint> zoneList = loopCodeList.get(zone.get(n).getDeviceCode() + "ZoneList");
                                if (zoneList != null && zoneList.size() > 0) {
                                    int endIndex = 0;
                                    Integer zoneEnd = count.get(item.getDeviceCode() + "ZoneEnd");
                                    Integer zoneStart = count.get(item.getDeviceCode() + "ZoneStart");
                                    if (zoneEnd == 0) {
                                        endIndex = zoneStart;
                                    } else {
                                        if (zoneStart > 0) {
                                            if (zoneEnd >= countY) {
                                                endIndex = zoneStart + 1;
                                            } else {
                                                endIndex = zoneStart;
                                            }
                                        } else {
                                            if (zoneEnd >= countY) {
                                                endIndex = 1;
                                            }
                                        }
                                    }
                                    if (endIndex > 0) {
                                        List<BaseDevicePoint> subList = zoneList.subList(0, checkLength(endIndex, zoneList.size()));
                                        zoneList = zoneList.subList(checkLength(endIndex, zoneList.size()), zoneList.size() - 1);
                                        loopCodeList.put(zone.get(n).getDeviceCode() + "ZoneList", zoneList);
                                        counts += subList.size();
                                        Map<String, Integer> stringIntegerMap = loopCodeCount.get(zone.get(n).getDeviceCode() + "ZoneMap");
                                        for (int k = 0; k < subList.size(); k++) {
                                            MaintenanceScopeTest test = new MaintenanceScopeTest();
                                            test.setItemsRId(fhfqid);
                                            test.setBatchId(subList.get(k).getZoneName() + "");
                                            test.setPointNum(stringIntegerMap.get(subList.get(k).getZoneName()) + "");
                                            maintenanceScopeTestList.add(test);
                                        }
                                        if (subList.size() > 0) {
                                            maintenanceItmesRPlanList.add(itmesRPlan);
                                        }
                                    }
                                }
                            }
                            countY++;
                        } else {
                            // 查询回路的维保项
//                        List<MaintenanceConfig> dataByGenerationRule = iMaintenanceConfigService.getDataByGenerationRule("2", "");
                            List<LoopPointVo> loopPointVos = loopMonthMap.get(i);
                            if (!CollectionUtils.isEmpty(loopPointVos)) {
                                for (LoopPointVo loopPoint : loopPointVos) {
                                    // 生成维保项和维保计划关联表
                                    String wbxid = IdGenerator.getIdStr();
                                    MaintenanceItmesRPlan maintenanceItmesRPlan = new MaintenanceItmesRPlan();
                                    maintenanceItmesRPlan.setId(wbxid);
                                    maintenanceItmesRPlan.setMaintenancePlanId(maintenancePlan.getId());
                                    maintenanceItmesRPlan.setMaintenanceConfigId(deviceIdMap.get(loopPoint.getDeviceType()));

                                    MaintenanceScopeTest test = new MaintenanceScopeTest();
                                    test.setItemsRId(wbxid);
                                    test.setBatchId(loopPoint.getHostNum() + "-" + loopPoint.getLoopCode());
                                    test.setPointNum(loopPoint.getTotal().toString());
                                    counts += loopPoint.getTotal();
                                    maintenanceScopeTestList.add(test);
                                    maintenanceItmesRPlanList.add(maintenanceItmesRPlan);
                                }
                            }

                   /*     for (int j = 0; j < dataByGenerationRule.size(); j++) {
                            MaintenanceConfig maintenanceConfig = dataByGenerationRule.get(j);
                            // 生成维保项和维保计划关联表
                            String wbxid = IdGenerator.getIdStr();
                            MaintenanceItmesRPlan maintenanceItmesRPlan = new MaintenanceItmesRPlan();
                            maintenanceItmesRPlan.setId(wbxid);
                            maintenanceItmesRPlan.setMaintenancePlanId(maintenancePlan.getId());
                            maintenanceItmesRPlan.setMaintenanceConfigId(maintenanceConfig.getId());
                            List<BaseDevicePoint> loopList1 = loopCodeList.get(dataByGenerationRule.get(j).getDeviceCode() + "CodeList");
                            if (loopList1 != null && loopList1.size() > 0) {
                                int endIndex = 0;
                                Integer codeEnd = count.get(maintenanceConfig.getDeviceCode() + "CodeEnd");
                                Integer codeStart = count.get(maintenanceConfig.getDeviceCode() + "CodeStart");
                                if (codeEnd == 0) {
                                    endIndex = codeStart;
                                } else {
                                    if (codeStart > 0) {
                                        if (codeEnd >= countM) {
                                            endIndex = codeStart + 1;
                                        } else {
                                            endIndex = codeStart;
                                        }
                                    } else {
                                        if (codeEnd >= countM) {
                                            endIndex = 1;
                                        }
                                    }
                                }
                                if (endIndex > 0) {
                                    List<BaseDevicePoint> subList = loopList1.subList(0, checkLength(endIndex, loopList1.size()));
                                    loopList1 = loopList1.subList(checkLength(endIndex, loopList1.size()), loopList1.size() - 1);
                                    loopCodeList.put(dataByGenerationRule.get(j).getDeviceCode() + "CodeList", loopList1);
                                    counts += subList.size();
                                    Map<String, Integer> stringIntegerMap = loopCodeCount.get(dataByGenerationRule.get(j).getDeviceCode() + "CodeMap");
                                    counts += subList.size();
                                    for (int k = 0; k < subList.size(); k++) {
                                        MaintenanceScopeTest test = new MaintenanceScopeTest();
                                        test.setItemsRId(wbxid);
                                        String loopCode = subList.get(k).getLocalLoopCode();
                                        String hostNum = subList.get(k).getLocalHostNum();
                                        test.setBatchId(hostNum + "-" + loopCode + "");
                                        test.setPointNum(stringIntegerMap.get(hostNum + "-" + loopCode) + "");
                                        maintenanceScopeTestList.add(test);
                                    }
                                    if(subList.size() > 0){
                                        maintenanceItmesRPlanList.add(maintenanceItmesRPlan);
                                    }
                                }
                            }
                        }*/
                            //countM++;
                        }
                        // 查询楼层的维保项
                        List<MaintenanceConfig> floor = iMaintenanceConfigService.getDataByGenerationRule("1", "");
                        for (int f = 0; f < floor.size(); f++) {
                            MaintenanceConfig item = floor.get(f);
                            // 生成维保项和维保计划关联表
                            String floorid = IdGenerator.getIdStr();
                            MaintenanceItmesRPlan itmesRPlan = new MaintenanceItmesRPlan();
                            itmesRPlan.setId(floorid);
                            itmesRPlan.setMaintenancePlanId(maintenancePlan.getId());
                            itmesRPlan.setMaintenanceConfigId(item.getId());
                            // 根据设备类型查询点位
                            List<BaseDevicePoint> baseDevicePoints2 = iBaseDevicePointService.listAllPointsByDeviceNo(wbContract.getBuildId(), item.getDeviceCode());
                            Map<String, List<BaseDevicePoint>> foolListMap = groupBillingDataByFool(baseDevicePoints2);
                            if (foolListMap.size() > 0) {
                                counts += baseDevicePoints2.size();
                                for (String entry : foolListMap.keySet()) {
                                    MaintenanceScopeTest test = new MaintenanceScopeTest();
                                    test.setItemsRId(floorid);
                                    test.setBatchId(entry);
                                    test.setPointNum(foolListMap.get(entry).size() + "");
                                    maintenanceScopeTestList.add(test);
                                }
                                if (foolListMap.size() > 0) {
                                    maintenanceItmesRPlanList.add(itmesRPlan);
                                }
                            }
                        }

                        Date startDateMonth = DateUtil.parseMonth(wbContract.getStartTime());
                        Date endDateMonth = DateUtil.parseMonth(wbContract.getEndTime());
                        Date planDate = DateUtil.parseMonth(effectiveTime);

                        if (((planDate.before(endDate) && planDate.after(startDate))) || startDateMonth.compareTo(planDate) == 0 || endDateMonth.compareTo(planDate) == 0) {
                            // 保存维保测试计划
                            maintenancePlan.setPointNum(counts + "");
                            try {
                                Thread.sleep(3000);
                            } catch (Exception e) {
                            }
                            boolean b1 = maintenancePlanService.save(maintenancePlan);
                            // 保存维保检查计划
                            boolean b11 = maintenancePlanService.save(maintenancePlan2);
                            // 维保项和维保计划关联表
                            if (maintenanceItmesRPlanList.size() > 0) {
                                try {
                                    Thread.sleep(3000);
                                } catch (Exception e) {
                                }
                                boolean b2 = iMaintenanceItmesRPlanService.saveBatch(maintenanceItmesRPlanList);
                            }
                            // 维保计划范围表
                            if (maintenanceScopeTestList.size() > 0) {
                                try {
                                    Thread.sleep(3000);
                                } catch (Exception e) {
                                }
                                boolean b3 = iMaintenanceScopeTestService.saveBatch(maintenanceScopeTestList);
                            }
                        }
                    }
                    year++;
                }
            } else {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("生成测试计划（楼层 回路 防火分区）和检查计划异常：", e);
        }
        return false;

    }

    /**
     * //生成维保计划
     *
     * @param wbContract
     * @throws Exception
     */
    @Override
    @Async
    public void createWbPlanAsync(HttpServletRequest request, WbContract wbContract) throws Exception {
        //生成测试计划（楼层 回路 防火分区）和检查计划
        log.info("---------[维保计划]生成测试计划（楼层 回路 防火分区）和检查计划开始----------------------");
        this.createTestPlan(request, wbContract);
        log.info("---------[维保计划]生成测试计划（楼层 回路 防火分区）和检查计划结束----------------------");
        //生成维保计划
        wbPlanFormulate.generateWbPlan(wbContract);
    }

    public int checkLength(int index, int size) {
        if (index >= size) {
            return size - 1;
        }
        return index;
    }

    public Map<String, List<BaseDevicePoint>> createMap(Map<String, List<BaseDevicePoint>> stringListMap, int start, int end) {

        Map<String, List<BaseDevicePoint>> map = new HashMap();

        int cont = 0;


        for (Map.Entry<String, List<BaseDevicePoint>> entry : stringListMap.entrySet()) {

            String mapKey = entry.getKey();

            List<BaseDevicePoint> mapValue = entry.getValue();

            if (cont > end) {

                break;

            }

            if (start > cont && cont < end) {

                map.put(mapKey, mapValue);

            }

            cont++;

        }

        return map;

    }

    public MaintenancePlan createMaintenancePlan(HttpServletRequest request, WbContract wbContract, String effectiveTime, String scheduling) {

        String id = IdGenerator.getIdStr();

        MaintenancePlan maintenancePlan = new MaintenancePlan();

        maintenancePlan.setId(id);
        //建筑id
        maintenancePlan.setBuildingId(wbContract.getBuildId());
        //建筑名称
        maintenancePlan.setBuildingName(wbContract.getBuildName());
        //维保单位id
        maintenancePlan.setMaintenanceId(wbContract.getWbTeamId());
        //维保团队名
        maintenancePlan.setMaintenanceName(wbContract.getWbTeam());
        //年月份
        maintenancePlan.setEffectiveTime(effectiveTime);
        //月度计划完成率
        maintenancePlan.setPercentageMon("0");
        //年度计划完成率
        maintenancePlan.setPercentageYear("0");

        // 测试计划系统个数默认10个
        maintenancePlan.setMaintenanceNum("10");
        // 检查计划查询配置表
        if ("1".equals(scheduling)) {
            QueryWrapper<ContractConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("contract_id", wbContract.getId());
            int count = iContractConfigService.count(queryWrapper);
            maintenancePlan.setMaintenanceNum(count + "");
        }

        maintenancePlan.setStartTime(DateUtil.parseDatetime(effectiveTime + "-01 00:00:00"));

        maintenancePlan.setEndTime(DateUtil.getMonthLastDay(effectiveTime));
        // 创建人创建时间
        String userId = request.getHeader("xfUserId");//消防userId

        maintenancePlan.setCreateBy(userId);
        maintenancePlan.setCreateTime(LocalDateTime.now());
        //维保合同id
        maintenancePlan.setMaintenanceContractId(wbContract.getId());
        //计划分类0测试计划1检查计划2联动计划3临时测试
        maintenancePlan.setScheduling(scheduling);
        //已维保点位数
        maintenancePlan.setMaintenancePoint("0");

        return maintenancePlan;

    }


    private Map<String, List<BaseDevicePoint>> groupBillingDataByExcpBatchCode(List<BaseDevicePoint> billingList) {


        Map<String, List<BaseDevicePoint>> resultMap = new HashMap<String, List<BaseDevicePoint>>();


        try {

            for (BaseDevicePoint sysUser : billingList) {

                String loopCode = sysUser.getLocalLoopCode();
                String hostNum = sysUser.getLocalHostNum();
                if (null != loopCode && hostNum != null) {
                    // 主机号 + 回路号
                    if (resultMap.containsKey(hostNum + "-" + loopCode)) {//map中某值已存在，将该数据存放到同一个key（key存放的是该值）的map中

                        resultMap.get(hostNum + "-" + loopCode).add(sysUser);

                    } else {//map中不存在，新建key，用来存放数据

                        List<BaseDevicePoint> sysUserList = new ArrayList<BaseDevicePoint>();

                        sysUserList.add(sysUser);

                        resultMap.put(hostNum + "-" + loopCode, sysUserList);

                    }

                }

            }

        } catch (Exception e) {

            log.error(e.toString());

        }

        return resultMap;

    }

    private Map<String, List<BaseDevicePoint>> groupBillingDataByFool(List<BaseDevicePoint> billingList) {


        Map<String, List<BaseDevicePoint>> resultMap = new HashMap<String, List<BaseDevicePoint>>();


        try {

            for (BaseDevicePoint sysUser : billingList) {

                if (null != sysUser.getFloorId()) {

                    if (resultMap.containsKey(sysUser.getFloorId())) {//map中某值已存在，将该数据存放到同一个key（key存放的是该值）的map中

                        resultMap.get(sysUser.getFloorId()).add(sysUser);

                    } else {//map中不存在，新建key，用来存放数据

                        List<BaseDevicePoint> sysUserList = new ArrayList<BaseDevicePoint>();

                        sysUserList.add(sysUser);

                        resultMap.put(sysUser.getFloorId() + "", sysUserList);

                    }

                }

            }

        } catch (Exception e) {

            log.error(e.toString());

        }

        return resultMap;

    }


    private Map<String, List<BaseDevicePoint>> groupBillingDataByZone(List<BaseDevicePoint> billingList) {


        Map<String, List<BaseDevicePoint>> resultMap = new HashMap<String, List<BaseDevicePoint>>();


        try {

            for (BaseDevicePoint sysUser : billingList) {

                if (null != sysUser.getZoneName()) {

                    if (resultMap.containsKey(sysUser.getZoneName())) {//map中某值已存在，将该数据存放到同一个key（key存放的是该值）的map中

                        resultMap.get(sysUser.getZoneName()).add(sysUser);

                    } else {//map中不存在，新建key，用来存放数据

                        List<BaseDevicePoint> sysUserList = new ArrayList<BaseDevicePoint>();

                        sysUserList.add(sysUser);

                        resultMap.put(sysUser.getZoneName() + "", sysUserList);

                    }

                }

            }

        } catch (Exception e) {

            log.error(e.toString());

        }

        return resultMap;

    }


    @Override
    @Async
    public void updateTestPlanRate(String buildingId, String effectiveTime) {
        log.info("延时任务同步测试计划开始,buildingId:{},effectiveTime:{}", buildingId, effectiveTime);

        //测试计划
        MaintenancePlan testPlan = maintenancePlanService.getOne(new QueryWrapper<MaintenancePlan>().lambda().eq(MaintenancePlan::getBuildingId, buildingId).eq(MaintenancePlan::getEffectiveTime, effectiveTime).eq(MaintenancePlan::getScheduling, "0"));
        //List<MaintenanceScopeTest> maintenanceScopeTests = new ArrayList<>();

        if (null != testPlan) {
            log.info("延时任务同步测试计划,buildingId:{},计划id:{}", buildingId, testPlan.getId());

            //随机3分钟，减轻批量操作压力
            int millisecond = new Random().nextInt(30000);
            try {
                Thread.sleep(millisecond);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            List<ScopeTestVo> scopeList = scopeTestMapper.getScopeListByPlanId(testPlan.getId());
            log.info("延时任务同步测试计划,buildingId:{},计划id:{},范围大小:{},延迟:{}", buildingId, testPlan.getId(), scopeList.size(), millisecond);

            Integer planFinishCt = 0;

            for (ScopeTestVo scopeTest : scopeList) {
                log.info("测试计划的维保范围id:" + scopeTest.getScopeTestId());

                //当月测试、临时、联动，测试范围
                List<DevicePointInfoVo> finishPoints = iMaintenanceScopeTestService.getWebScopeTestPointByEffTime(scopeTest.getScopeTestId(), buildingId, effectiveTime, null);
                Integer testFinishCt = 0;
                for (DevicePointInfoVo pointInfoVo : finishPoints) {
                    if (pointInfoVo.getMaintenanceStatus() == 1) {
                        testFinishCt++;
                        planFinishCt++;
                    }
                }
                MaintenanceScopeTest maintenanceScope = new MaintenanceScopeTest();
                maintenanceScope.setId(scopeTest.getScopeTestId());
                if (testFinishCt > 0) {
                    // 已维保状态
                    maintenanceScope.setMaintenanceStatus("1");
                } else {
                    // 未维保状态
                    maintenanceScope.setMaintenanceStatus("0");
                }
                maintenanceScope.setMaintenancePoint(testFinishCt.toString());
                maintenanceScope.setItemsRId(scopeTest.getItemsRId());

                //如果维保范围状态及点位没有变化，则不修改
                log.info("同步维保范围,buildId:{},scopeid:{},status:{},point:{}", buildingId, scopeTest.getScopeTestId(), scopeTest.getMaintenanceStatus() + "——" + maintenanceScope.getMaintenanceStatus(), scopeTest.getMaintenancePoint() + "——" + maintenanceScope.getMaintenancePoint());
                if (scopeTest.getMaintenanceStatus().equals(maintenanceScope.getMaintenanceStatus()) && scopeTest.getMaintenancePoint().toString().equals(maintenanceScope.getMaintenancePoint())) {
                    log.info("同步维保范围无需修改,buildId:{},scopeid:{},finish:{}", buildingId, scopeTest.getScopeTestId(), testFinishCt);
                } else {
                    int result = scopeTestMapper.updateById(maintenanceScope);
                    log.info("同步维保范围需要修改,buildId:{},scopeid:{},finish:{},result:{}", buildingId, scopeTest.getScopeTestId(), testFinishCt, result);
                }
                
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }

            }
//            Integer finishTestMaintenancePoint = scopeTestMapper.sumFinishPointByPlanId(testPlan.getId());
            testPlan.setMaintenancePoint(planFinishCt.toString());
            if (testPlan.getPointNum() != null && !"0".equals(testPlan.getPointNum())) {
                testPlan.setPercentageMon(String.format("%.3f", new BigDecimal(planFinishCt).divide(new BigDecimal(testPlan.getPointNum()), 3, BigDecimal.ROUND_HALF_UP)));
                if ("1.000".equals(testPlan.getPercentageMon())) {
                    testPlan.setState("2");
                }
            } else {
                testPlan.setPercentageMon("0");
            }
            maintenancePlanService.updateById(testPlan);

            log.info("延时任务同步测试计划结束,buildingId:{},effectiveTime:{}", buildingId, effectiveTime);
        }
    }


}
