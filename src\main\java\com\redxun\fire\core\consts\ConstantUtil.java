package com.redxun.fire.core.consts;

public class ConstantUtil {

    /**
     * 设备大类 - 水泵
     */
    public static final String DEVICE_TYPE_PUMP = "1";

    /**
     * 设备大类 - 压力表
     * 0 报警主机  1 水泵 2 压力表 3 水流表 20 电弧【20以后为第三方拓展】
     */
    public static final String DEVICE_TYPE_PRESSURE_GAGE = "2";
    /**
     * 设备大类 - 闭店监测
     */
    public static final String DEVICE_TYPE_CLOSE_STORE = "16";
    /**
     * 设备大类 - 水流表
     */
    public static final String DEVICE_TYPE_LIQUID_GAGE = "3";
    /**
     * 设备类型 - 摄像头
     */
    public static final String DEVICE_TYPE_CAMERA = "CAMERA";

    /**
     * 设备类型 - 烟温感一体摄像头
     */
    public static final String DEVICE_TYPE_SMART_CAMERA = "SMARTCAMERA";

    /**
     * 点位状态 - 正常
     * 0 - 正常 1 - 禁用  2 - 删除
     */
    public static final String POINT_STATUS_NORMAL = "0";

    /**
     * 点位状态 - 正常
     * 0 - 正常 1 - 禁用  2 - 删除
     */
    public static final String POINT_STATUS_REMOVE = "2";

    /**
     * 水压表点位状态对应redis前缀key值
     */
    public static final String REDIS_KEY_WATER_PRESSURE = "_pressure_";

    /**
     * 液位仪点位状态对应redis前缀key值
     */
    public static final String REDIS_KEY_WATER_LIQUID = "_liquid_";

    /**
     * 水泵状态对应redis前缀key值
     */
    public static final String REDIS_KEY_PUMP = "_pump_";

    /**
     * 导入模板-压力表
     */
    public static final String IMPORT_DEVICE_TYPE_PRESSURE_GAGE = "2";

    /**
     * 导入模板-液位仪
     */
    public static final String IMPORT_DEVICE_TYPE_LIQUID_GAGE = "3";

    /**
     * 水压值无变化
     */
    public static final String PRE_VALUE_UNCHANGE = "0";

    /**
     * 水压值有变化
     */
    public static final String PRE_VALUE_CHANGE = "1";
    /**
     * 远传设备点位key
     */
    public static final String FAR_EASTONE = "far_eastone:";

    /**
     * 手动状态，0-手动
     */
    public static final String PUMP_MANUAL_STATUS = "0";

    /**
     * 手动状态，1-自动
     */
    public static final String PUMP_AUTO_STATUS = "1";

    /**
     * 断电状态，0-断电
     */
    public static final String PUMP_OUTAGE_STATUS = "0";

    /**
     * 断电状态，1-已上电
     */
    public static final String PUMP_POWER_ON_STATUS = "1";

    /**
     * 水泵运行状态 0-停止
     */
    public static final String PUMP_RUN_STOP_STATUS = "0";

    /**
     * 水泵运行状态 1-运行
     */
    public static final String PUMP_RUN_ON_STATUS = "1";

    /**
     * 压力值（液位高度）正常
     */
    public static final String PRE_VALUE_NORMAL = "0";

    /**
     * 压力值（液位高度）过高
     */
    public static final String PRE_VALUE_HIGHT = "3";

    /**
     * 压力值（液位高度）过低
     */
    public static final String PRE_VALUE_LOW = "4";

    /**
     * 设备正常
     */
    public static final String PRE_DEVICE_NORNAL = "0";

    /**
     * 设备离线
     */
    public static final String PRE_DEVICE_OFF = "1";

    /**
     * 设备故障
     */
    public static final String PRE_DEVICE_FAULT = "1";

    /**
     * 分析异常
     */
    public static final String PRE_ANALYSE_ABNORMAL = "1";

    /**
     * redis设备号与建筑物对应key
     */
    public static final String EQUIPMENT_RELATION = "_equipment_";

    /**
     * 设备状态-正常
     */
    public static final String DEVICE_NORMAL = "0";

    /**
     * 设备状态-故障
     */
    public static final String DEVICE_FAULT = "2";

    /**
     * 设备状态-离线
     */
    public static final String DEVICE_OFFLINE = "1";

    /**
     * 设备类型-信号阀
     */
    public static final String DEVICE_TYPE_XHF = "XHF";
    /**
     * 设备类型-压力开关
     */
    public static final String DEVICE_TYPE_YK = "YK";

    /**
     * 消防水箱液位传感器-水箱
     */
    public static final String PRE_DEVICE_TYPE_SX = "SX";
    /**
     * 无线压力传感器喷淋系统(湿式)-湿式系统
     */
    public static final String PRE_DEVICE_TYPE_SSXT = "YLS";
    /**
     * 无线压力传感器消火栓系统-消火栓系统
     */
    public static final String PRE_DEVICE_TYPE_XHSXT = "YLX";
    /**
     * 无线压力传感器喷淋系统(预作用)-预作用系统
     */
    public static final String PRE_DEVICE_TYPE_YZYXT = "YLY";
    /**
     * 无线压力传感器喷淋系统(干式)-干式系统
     */
    public static final String PRE_DEVICE_TYPE_GSXT = "YLG";
    /**
     * 消防水池液位传感器-水池
     */
    public static final String PRE_DEVICE_TYPE_SC = "SC";
    /**
     * 消防水炮系统-水炮系统
     */
    public static final String PRE_DEVICE_TYPE_SPXT = "SPYL";
    /**
     * 喷淋报警阀前环⽹
     */
    public static final String PRE_DEVICE_TYPE_PLBJFQHW = "FQYL";

    public static final String REDIS_DEVICE_KEY_EQUIPMENT = "equipment:";

    /**
     * 点位异常状态-已处理
     */
    public static final String POINT_EXCEPTION_STATUS_DISPOSED = "1";

    /**
     * 检线相关key
     * */
    public static final String ROUTE_CHECK_POINT = "routeCheckPoint:";

    public static final String ROUTE_CHECK = "routeCheck:";

    public static final String ROUTE_CHECK_POINT_DETAILS = "routeCheckPointDetails:";
    /**
     * 远传设备(检线)点位key
     */
    public static final String FAR_EASTONE_ROUTE = "far_eastone_route:";

    public static final String CHECK_MODE = "检查模式";

    public static final String FULL_MODE = "全覆盖模式";
}
