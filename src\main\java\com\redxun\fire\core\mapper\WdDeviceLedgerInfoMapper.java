package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.WdDeviceLedgerInfo;
import com.redxun.fire.core.pojo.vo.DeviceLedgerInfoScreenVo;
import com.redxun.fire.core.pojo.vo.DeviceLedgerInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备台账信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-29
 */
public interface WdDeviceLedgerInfoMapper extends BaseMapper<WdDeviceLedgerInfo> {

    /**
     *
     * @param depIds 部门ids
     * @return
     */
    List<DeviceLedgerInfoVo> listDeviceLedgerInfos(@Param("depIds") List<String> depIds,@Param("buildId") String buildId);

    /**
     *
     * @param depIds 部门ids
     * @return
     */
    List<DeviceLedgerInfoVo> listDeviceLedgerInfoList(@Param("depIds") List<String> depIds);


    /**
     * 根据建筑ID和设备类型查询台帐信息
     *
     * @param buildId
     * @param devType
     * @return
     */
    WdDeviceLedgerInfo queryInfoByBuilIdAndDevType(@Param("buildId") String buildId, @Param("devType") String devType);

    /**
     *
     * @return
     */
    List<DeviceLedgerInfoScreenVo> listDeviceLedgerInfoListForScreen();
}