package com.redxun.fire.core.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gexin.fastjson.JSON;
import com.gexin.fastjson.JSONArray;
import com.gexin.fastjson.JSONObject;
import com.redxun.api.feign.OrgManageClient;
import com.redxun.api.model.param.OrgManageParam;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.consts.ConfigRelationConstant;
import com.redxun.fire.core.consts.TsmMessagesConstants;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.ConfigRelation;
import com.redxun.fire.core.mapper.BaseBuildingMapper;
import com.redxun.fire.core.mapper.ConfigRelationMapper;
import com.redxun.fire.core.mapper.RouteCheckRecordMapper;
import com.redxun.fire.core.pojo.bo.RouteCheckFrequencyBO;
import com.redxun.fire.core.service.alarm.IBaseEventStatisticsService;
import com.redxun.fire.core.service.common.ITsmMessagesService;
import com.redxun.fire.core.service.common.impl.DropDownServiceImpl;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.DateUtils;
import com.redxun.fire.core.utils.MessageUtil;
import com.redxun.fire.core.utils.ObjectUtil;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 訾浩
 * @since: 2024/4/25 17:04
 * @description: 每日检线记录频次统计推送定时任务
 * @cron 0 0 9 * * ?
 */
@Slf4j
@Service
public class RouteCheckRecordTimeJob extends IJobHandler {

    @Resource
    private ConfigRelationMapper configRelationMapper;

    @Resource
    private BaseBuildingMapper baseBuildingMapper;

    @Resource
    private RouteCheckRecordMapper routeCheckRecordMapper;

    @Resource
    private IBaseEventStatisticsService iBaseEventStatisticsService;

    @Resource
    private ITsmMessagesService tsmMessagesService;

    @Resource
    private OrgManageClient orgManageClient;

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    DropDownServiceImpl dropDownService;

    @Override
    @XxlJob("RouteCheckRecordTimeJob")
    public void execute() throws Exception {
        Long startTime = System.currentTimeMillis();
        Date nowDate = new Date();
        log.info("每日检线记录频次统计推送定时任务开始：" + DateUtil.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss"));

        //查询建筑表
        int i = 0;
        //获取推送开启状态
//        List<ConfigRelation> configRelations = configRelationMapper.selectList(new LambdaQueryWrapper<ConfigRelation>().eq(ConfigRelation::getConfigCode, ConfigRelationConstant.DXTZ_ROUTE_CHECK_OFFLINE_FREQUENCY));
        List<ConfigRelation> configRelations = dropDownService.queryConfigByCode(ConfigRelationConstant.DXTZ_ROUTE_CHECK_OFFLINE_FREQUENCY);

        if (configRelations.size() > 0) {
            //按排序字段进行功能划分
            Map<Integer, ConfigRelation> configMap = configRelations.stream().collect(Collectors.toMap(ConfigRelation::getSort, a -> a));
            //获取发送状态
            if (ConfigRelationConstant.SEND_MESSAGE.equals(configMap.get(0).getConfigStrVal())) {
                // 获取消息发送类型
//                List<ConfigRelation> configRelations2 = configRelationMapper.selectList(new LambdaQueryWrapper<ConfigRelation>().eq(ConfigRelation::getConfigCode, ConfigRelationConstant.DXTZ_ROUTE_CHECK_OFFLINE_UNTREATED_TYPE));
                List<ConfigRelation> configRelations2 = dropDownService.queryConfigByCode(ConfigRelationConstant.DXTZ_ROUTE_CHECK_OFFLINE_UNTREATED_TYPE);
                ConfigRelation msgConfig = configRelations2.get(0);
                String msgType = msgConfig.getConfigStrVal();

                //获取接警中心推送频次
//                List<ConfigRelation> jjzxCList = configRelationMapper.selectList(new LambdaQueryWrapper<ConfigRelation>().eq(ConfigRelation::getConfigCode, ConfigRelationConstant.JJZX_ROUTE_CHECK_OFFLINE_FREQUENCY));
                List<ConfigRelation> jjzxCList = dropDownService.queryConfigByCode(ConfigRelationConstant.JJZX_ROUTE_CHECK_OFFLINE_FREQUENCY);

                // 获取所有广场信息
                List<BaseBuilding> baseBuildings = baseBuildingMapper.selectList(null);
                // 获取所有广场7天内对应检线频次
                String time = DateUtils.getBeforeDateStr(-7);

                // 最小筛选频次
                ConfigRelation config1 = configMap.get(1);
                int minFrequency = config1.getConfigIntHour().intValue();
                List<RouteCheckFrequencyBO> bos = routeCheckRecordMapper.selectRouteCheckFrequency(time, minFrequency);
                // 数据分组
                Map<String, List<RouteCheckFrequencyBO>> map = bos.stream().collect(Collectors.groupingBy(RouteCheckFrequencyBO::getBuildingId));

                // 定义分组的范围
                ConfigRelation config2 = configMap.get(2);
                ConfigRelation config3 = configMap.get(3);
                int a = config1.getConfigIntHour().intValue();
                int b = config2.getConfigIntHour().intValue();
                int c = config3.getConfigIntHour().intValue();
                for (BaseBuilding baseBuilding : baseBuildings) {
                    String buildingId = baseBuilding.getId();
                    if (map.containsKey(buildingId)) {
                        List<RouteCheckFrequencyBO> boList = map.get(buildingId);
                        //按异常频次分组
                        Map<String, List<RouteCheckFrequencyBO>> groupedByCountRange = boList.stream()
                                .collect(Collectors.groupingBy(bo -> {
                                    int countValue = bo.getCount();
                                    if (countValue > c) {
                                        return "lv3";
                                    } else if (countValue >= b) {
                                        return "lv2";
                                    } else if (countValue >= a) {
                                        return "lv1";
                                    } else {
                                        return "Other";
                                    }
                                }));
                        //对分组进行数据进行处理发送消息
                        List<RouteCheckFrequencyBO> lv1 = groupedByCountRange.get("lv1");
                        // 频次1推送
                        msgSend(baseBuilding, groupedByCountRange.get("lv1"), config1, msgType);
                        // 频次2推送
                        msgSend(baseBuilding, groupedByCountRange.get("lv2"), config2, msgType);
                        // 频次3推送
                        msgSend(baseBuilding, groupedByCountRange.get("lv3"), config3, msgType);

                        // 接警中心推送
                        if (ObjectUtil.listNotEmptyVerify(jjzxCList)) {
                            ConfigRelation jjzxConfig = jjzxCList.get(0);
                            //接警中心频次
                            int lv = jjzxConfig.getConfigDoubleData().intValue();
                            List<RouteCheckFrequencyBO> list = boList.stream().filter(bo -> bo.getCount() > lv).collect(Collectors.toList());
                            if (ObjectUtil.listNotEmptyVerify(list)) {
                                String eventType = "7";
                                iBaseEventStatisticsService.addRouteCheckEvent(baseBuilding, list, eventType, ConfigRelationConstant.JJZX_ROUTE_CHECK_OFFLINE_FREQUENCY_NAME);
                            }
                        }
                    }
                    i++;
                }
                Long endTime = System.currentTimeMillis();
                log.info("每日检线记录频次统计推送定时任务:" + ((endTime - startTime) / 1000) + "秒,共处理门店：{}家", i);
            } else {
                log.info("每日检线记录频次统计推送定时任务：{},消息推送功能未开启", DateUtil.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        log.info("每日检线记录频次统计推送定时任务结束");
    }

    public void msgSend(BaseBuilding baseBuilding, List<RouteCheckFrequencyBO> list, ConfigRelation config, String msgType) {
        if (list == null || list.size() == 0) {
            return;
        }
        String msgTitle = "检线异常频次提醒";
        String pointNumbers = list.stream()
                .map(RouteCheckFrequencyBO::getPointNumber)
                .collect(Collectors.joining(","));
        //拼接消息体
        String msg = DateUtil.getCurrentDateStr() + "," + baseBuilding.getBuildingName()
                + pointNumbers + "远传设备对应点位一周内离线次数超过" + config.getConfigIntHour().intValue() + "次,请督办处理!";

        //是否包含短信
        boolean msg0 = msgType.contains("0");
        //是否包含万信
        boolean msg1 = msgType.contains("1");

        // 获取消息通知职务id
        String[] jobIdArray = config.getConfigTzrIdNew().split(",");
        for (String jobId : jobIdArray) {
            OrgManageParam orgManageParam = new OrgManageParam();
            //查询单店职务
            orgManageParam.setPiazzaId(baseBuilding.getPiazza());
            orgManageParam.setBusinessId("1");
            orgManageParam.setTenantId(baseBuilding.getTenantId());
            orgManageParam.setJobId(jobId);
            JsonResult jsonResult = orgManageClient.queryOrgManage(orgManageParam);
            if (!jsonResult.isSuccess()) {
                log.info("每日检线记录频次统计推送定时任务,查询推送人失败,request[{}],response[{}]", JSON.toJSONString(orgManageParam), JSON.toJSONString(jsonResult));
                continue;
            }
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(jsonResult.getData()));
            if (jsonArray.size() > 0) {
                //短信推送结果合集
                Map<String, Boolean> SmsResultMap = new HashMap();
                //消息推送结果合集
                Map<String, Boolean> NewsResultMap = new HashMap();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject user = jsonArray.getJSONObject(i);
                    List<String> userIdList = new ArrayList<>();
                    userIdList.add(user.getString("user"));
                    //0 短信方式
                    if (msg0) {
                        SmsResultMap.put(user.getString("user"), messageUtil.sendMessage("1", msgTitle, msg, userIdList).isSuccess());
                    }
                    //1 万信推送
                    if (msg1) {
                        SmsResultMap.put(user.getString("user"), messageUtil.sendMessage("2", msgTitle, msg, userIdList).isSuccess());
                    }

                }
                if (msg0) {
                    //短信通知入库
                    tsmMessagesService.addTsmMessage(jsonArray, baseBuilding, TsmMessagesConstants.TYPE_CONFIRM_FIRE_ALARM, msg, TsmMessagesConstants.REMINDERS_SMS, SmsResultMap);
                }
                if (msg1) {
                    //万信消息通知入库
                    tsmMessagesService.addTsmMessage(jsonArray, baseBuilding, TsmMessagesConstants.TYPE_CONFIRM_FIRE_ALARM, msg, TsmMessagesConstants.REMINDERS_NEWS, NewsResultMap);
                }
            }
        }
    }
}
