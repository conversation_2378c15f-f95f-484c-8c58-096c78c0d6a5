package com.redxun.fire.core.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.BaseBuilding;
import com.redxun.fire.core.entity.OsGroupList;
import com.redxun.fire.core.feign.org.OsUserClient;
import com.redxun.fire.core.utils.validated.Delete;
import com.redxun.fire.core.utils.validated.Select;
import com.redxun.fire.core.service.building.IBaseBuildingManageService;
import com.redxun.fire.core.service.common.IDropDownService;
import com.redxun.fire.core.service.alarm.WaterMonitorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.building.impl.BaseBuildingManageServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 建筑物信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-21
 */
@Slf4j
@RestController
@RequestMapping("/base-building-manage")
@SuppressWarnings("unchecked")
public class BaseBuildingManageController {

    @Autowired
    BaseBuildingManageServiceImpl baseBuildingManageService;
    @Autowired
    IBaseBuildingManageService iBaseBuildingManageService;

    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Autowired
    private IDropDownService iDropDownService;
    @Resource
    private OsUserClient osUserClient;
    @Autowired
    WaterMonitorService waterMonitorService;

    /**
     * 省市树
     *
     * @return
     */
    @GetMapping("/queryTree")
    public JsonResult queryTree() {
        log.info("获取省市树");
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseBuildingManageService.getTree());
        //log.info("jsonResult--->" + JSON.toJSONString(jsonResult));
        return jsonResult;
    }

    /**
     * 根据ID查询建筑信息
     *
     * @param baseBuilding
     * @return
     */
    @RequestMapping(value = "/getBuildingInfoById")
    public JsonResult getBuildingInfoById(@RequestBody @Validated(Select.class) BaseBuilding baseBuilding) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseBuildingManageService.getBuildingInfoById(baseBuilding.getId()));
        return jsonResult;
    }

    /**
     * 编辑建筑信息
     *
     * @param baseBuilding
     * @return
     */
//    @RequestMapping(value = "/editBuildingInfo")
    @Deprecated
    public JsonResult editBuildingInfo(HttpServletRequest request, @RequestBody @Validated(Select.class) BaseBuilding baseBuilding) {
        JsonResult jsonResult = JsonResult.Success();
        Map<String, Object> param = new HashMap<>();
        param.put("buildId", baseBuilding.getId());
        param.put("centerCode", baseBuilding.getCenter());
        param.put("centerName", baseBuilding.getOperatingCenter());
        param.put("areaCode", baseBuilding.getJurisdiction());
        param.put("areaName", baseBuilding.getJurisdictionVal());
        waterMonitorService.initStatData(param);

        jsonResult.setData(iBaseBuildingManageService.EditBuildingInfoById(request, baseBuilding));

        return jsonResult;
    }
    @RequestMapping(value = "/editBuildingInfo")
    public JsonResult editBuildingInfoNew( @RequestBody BaseBuilding baseBuilding){
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseBuildingManageService.updateById(baseBuilding));
        return jsonResult;
    }
    /**
     * 新增建筑信息
     *
     * @param baseBuildingParam
     * @return
     */
    @RequestMapping(value = "/addBuildingInfo", method = RequestMethod.POST)
    public synchronized JsonResult addBuildingInfo(HttpServletRequest request, @RequestBody @Validated JSONObject baseBuildingParam) {
        BaseBuilding baseBuilding = new BaseBuilding();
        JsonResult jsonResult = iBaseBuildingManageService.addBuilding(request, baseBuilding, baseBuildingParam);
//        baseBuilding.setCenterVal(baseBuilding.getOperatingCenter());

        return jsonResult;
    }

    /**
     * 根据ID删除建筑信息
     *
     * @param baseBuilding
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/deleteBuildingInfo")
    @ApiOperation("根据ID删除建筑信息")
    public JsonResult deleteBuildingInfo(HttpServletRequest request, @RequestBody @Validated(Delete.class) BaseBuilding baseBuilding) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseBuildingManageService.deleteBuildingById(request, baseBuilding.getId()));
        return jsonResult;
    }

    // 广场大区查询
    @GetMapping("/regionSearch")
    public JsonResult regionSearch() {
        return iBaseBuildingManageService.regionSearch();
    }

    // 广场城市公司查询
    @GetMapping("/areaSearch/{regionId}")
    public JsonResult areaSearch(@PathVariable("regionId") String regionId) {
        return iBaseBuildingManageService.areaSearch(regionId);
    }

    // 其他影院区域查询
    @GetMapping("/otherAreaSearch")
    public JsonResult otherAreaSearch() {
        return iBaseBuildingManageService.otherAreaSearch();
    }

    /**
     * 建筑管理 获取下拉框
     *
     * @param request
     * @return
     */
    @GetMapping("/getBuildingByCondition")
    public JsonResult getBuildingByCondition(HttpServletRequest request) {
//        String jsonStr = RequestUtil.getString(request,"params");
//        Map<String,Object> paramsMap=jsonToMap(jsonStr);
        String regional = request.getParameter("regional");
        String jurisdiction = request.getParameter("jurisdiction");
        String format = request.getParameter("format");
        String otherCat = request.getParameter("otherCat");
        return iBaseBuildingManageService.getBuildingSelected(format, regional, jurisdiction, otherCat);
    }

//    public static Map<String,Object> jsonToMap(String jsonStr){
//        if(StringUtils.isEmpty(jsonStr)){
//            return new HashMap<>(16);
//        }
//        try{
//            JSONObject jsonObject= JSONObject.parseObject(jsonStr);
//            Set<String> set=jsonObject.keySet();
//            Map<String,Object> params=new HashMap<>(set.size());
//            for(String key:set){
//                String val=jsonObject.getString(key);
//                if(StringUtils.isEmpty(val)){
//                    continue;
//                }
//                String str= DbUtil.encodeSql(val);
//                params.put(key,str);
//            }
//            return params;
//        }catch (Exception e){
//            return new HashMap<>(16);
//        }
//    }

    /**
     * 查询安检中心
     *
     * @return
     */
    @PostMapping("/queryCenter")
    @ResponseBody
    public Object queryCenter() {
        log.info("查询安检中心");
        return osUserClient.queryCenter();
    }

    /**
     * 查询其他
     *
     * @return
     */
    @GetMapping("/queryRest")
    public Object queryRest() {
        return osUserClient.queryRest();
    }

    /**
     * 查询全部
     *
     * @return
     */
    @PostMapping("/queryAll")
    public Object queryAll(@RequestBody @Validated OsGroupList osGroups) {
        return osUserClient.queryAll(osGroups.getOsGroups());
    }


    /**
     * 查询区域
     *
     * @return
     */
    @RequestMapping("queryArea/{id}")
    public Object queryArea(@PathVariable("id") String id) {
        return osUserClient.queryArea(id);
    }

    /**
     * 查询是否开通维保权限
     * 456364
     *
     * @return
     */
    @RequestMapping("queryMaintenance")
    public JsonResult queryMaintenance() {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iDropDownService.queryNameByTreeId("456364"));
        return jsonResult;
    }


    /**
     * 根据区域id查询
     *
     * @return
     */
    @PostMapping("/queryByArea")
    public JsonResult queryByArea(@RequestBody @Validated BaseBuilding baseBuilding) {
        JsonResult jsonResult = JsonResult.Success();
        jsonResult.setData(iBaseBuildingManageService.queryByArea(baseBuilding));
        return jsonResult;
    }

    /**
     * 广场信息 关联万信数据同步功能
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/syncBuildingForWanda")
    public synchronized JsonResult syncBuildingForWanda(@RequestBody @Validated Map<String, String> dto) {
        JsonResult jsonResult = JsonResult.Success();
        String data = iBaseBuildingManageService.syncBuildingForWanda(dto);
//        boolean data =  iBaseBuildingManageService.addBuilding(dto);
        jsonResult.setData(data);
        return jsonResult;
    }
}
