package com.redxun.fire.core.controller;

import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.service.alarm.IFireInfoService;
import com.redxun.fire.core.service.alarm.IFireRealEventService;
import com.redxun.fire.core.service.alarm.impl.FireInfoServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.support.HttpRequestHandlerServlet;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/fire/real")
public class FireRealEventController {

    @Resource
    private IFireRealEventService fireRealEventService;
    @Autowired
    private IFireInfoService fireInfoService;

    @PostMapping("/realFireAlarmPage")
    public JsonPage realFireAlarmPage(@RequestBody QueryData queryData){
        return fireRealEventService.realFireList(queryData);
    }

    @GetMapping("/realFireAlarmInfo/{id}")
    public JsonResult getRealFireInfo(@PathVariable Long id){
        return fireRealEventService.getRealFireInfo(id);
    }


    @PostMapping("/submitFeedback")
    public JsonResult submitFeedback(HttpServletRequest request, @RequestBody Map<String, String> stringStringMap){
        return fireRealEventService.commitRealEvent(request,stringStringMap);
    }

    @PostMapping("/submitComment")
    public JsonResult submitComment(HttpServletRequest request, @RequestBody Map<String, String> stringStringMap){
        return fireRealEventService.submitComment(request,stringStringMap);
    }

    @GetMapping("/recentRealFireAlarm")
    public JsonResult realFireListForThree(){
        return fireRealEventService.realFireListForThree();
    }



    //近15分钟的真实火警
    @PostMapping("/recentRealFireAlarmNearly")
    public JsonPage realFireAlarmNearly(@RequestBody QueryData queryData){
        return fireRealEventService.recentRealFireAlarmNearly(queryData);
    }

    @PostMapping("/historyRealFireAlarm")
    public JsonPage realFireHistory(@RequestBody QueryData queryData){
        return fireRealEventService.realFireHistory(queryData);
    }

    @GetMapping("/createFireEventRelation")
    public JsonResult createFireEventRelation(){
        return fireRealEventService.createFireEventRelation();
    }

    @GetMapping("/testFireRealEvent")
    public JsonResult testFireRealEvent(){
        return fireRealEventService.testFireRealEvent();
    }

    @GetMapping("/screenFireTrend")
    public JsonResult screenFireTrend(@RequestParam(required = false) String type){
        return fireInfoService.screenFireTrend(type);
    }
}
