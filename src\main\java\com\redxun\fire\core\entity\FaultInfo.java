package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 故障信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class FaultInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.UUID)
    @TableField(value = "id")
    private String id;
    /** 建筑物id */
    @TableField(value = "building_id")
    private String buildingId;
    /** 点位id */
    @TableField(value = "point_id")
    private String pointId;
    /** 硬件编号 */
    @TableField(value = "pid")
    private String pid;
    /** 传感器设备号 */
    @TableField(value = "dev_num")
    private String devNum;
    /** 点位描述 */
    @TableField(value = "point_desc")
    private String pointDesc;
    /** 消防系统类型 */
    @TableField(value = "fas_code")
    private String fasCode;
    /** 消防系统名称 */
    @TableField(value = "fas_name")
    private String fasName;
    /** 设备类型编码 */
    @TableField(value = "dev_type_code")
    private String devTypeCode;
    /** 设备类型名称 */
    @TableField(value = "dev_type_name")
    private String devTypeName;
    /** 设备状态编码 */
    @TableField(value = "dev_status_code")
    private String devStatusCode;
    /** 设备状态名称 */
    @TableField(value = "dev_status_name")
    private String devStatusName;
    /** 故障类型 */
    @TableField(value = "fault_type")
    private String faultType;
    /** 故障状态（0 未处理 1 已处理 2 处理中） */
    @TableField(value = "fault_status")
    private String faultStatus;
    /** 流水状态 */
    @TableField(value = "flow_status")
    private String flowStatus;
    /** 故障来源 */
    @TableField(value = "fault_source")
    private String faultSource;
    /** 故障描述 */
    @TableField(value = "comment")
    private String comment;
    /** 是否领取 */
    @TableField(value = "is_pick")
    private String isPick;
    /** 开始时间 */
    @TableField(value = "start_time")
    private String startTime;
    /** 结束时间 */
    @TableField(value = "end_time")
    private String endTime;
    /** 指派时间 */
    @TableField(value = "send_time")
    private String sendTime;
    /** 维修人 */
    @TableField(value = "repairman")
    private String repairman;
    /** 维修人id */
    @TableField(value = "repairman_id")
    private String repairmanId;
    /** 上报人 */
    @TableField(value = "report_user")
    private String reportUser;
    /** 上报人id */
    @TableField(value = "report_user_id")
    private String reportUserId;
    /** 上报描述 */
    @TableField(value = "report_desc")
    private String reportDesc;
    /** 处理人 */
    @TableField(value = "handle_user")
    private String handleUser;
    /** 处理人id */
    @TableField(value = "handle_user_id")
    private String handleUserId;
    /** 处理时间 */
    @TableField(value = "handle_time")
    private String handleTime;
    /** 处理情况 */
    @TableField(value = "handle_status")
    private String handleStatus;
    /** 是否应急维保 */
    @TableField(value = "emergency_status")
    private String emergencyStatus;
    /** 防火分区id */
    @TableField(value = "zone_id")
    private String zoneId;
    /** 防火分区名称 */
    @TableField(value = "zone_name")
    private String zoneName;
    /** 租户ID */
    @TableField(value = "TENANT_ID_")
    private String tenantId;
    /** 创建部门ID */
    @TableField(value = "CREATE_DEP_ID_")
    private String createDepId;
    /** 创建人ID */
    @TableField(value = "CREATE_BY_")
    private String createBy;
    /** 创建时间 */
    @TableField(value = "CREATE_TIME_")
    private java.sql.Timestamp createTime;
    /** 更新人ID */
    @TableField(value = "UPDATE_BY_")
    private String updateBy;
    /** 更新时间 */
    @TableField(value = "UPDATE_TIME_")
    private java.sql.Timestamp updateTime;
    /** 建筑名称 */
    @TableField(value = "building_name")
    private String buildingName;
    /** 建筑状态 */
    @TableField(value = "building_status")
    private String buildingStatus;
    /** 点位类型 */
    @TableField(value = "point_type")
    private String pointType;
    /** 位置描述 */
    @TableField(value = "location_describe")
    private String locationDescribe;
    /** 派发人 */
    @TableField(value = "send_user")
    private String sendUser;
    /** 上报时间 */
    @TableField(value = "reported_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date reportedTime;
    /** 点位号 */
    @TableField(value = "point_code")
    private String pointCode;
    /** 建筑状态名 */
    @TableField(value = "building_status_str")
    private String buildingStatusStr;
    /** 故障状态名 */
    @TableField(value = "fault_status_str")
    private String faultStatusStr;
    /** 设备ID */
    @TableField(value = "dev_id")
    private String devId;
    /** 开始维修时间 */
    @TableField(value = "repair_start_time")
    private String repairStartTime;
    /** 维修结束时间 */
    @TableField(value = "repair_end_time")
    private String repairEndTime;
    /** 是否忽略（0 否 1 是） */
    @TableField(value = "is_over_look")
    private String isOverLook;
    /** 最后上报时间 */
    @TableField(value = "last_time")
    private String lastTime;
    /** 是否人工上报 */
    @TableField(value = "is_manual_report")
    private String isManualReport;
    /** 所属部门的组织机构id */
    @TableField(value = "belong_dep")
    private String belongDep;
    /** 是否回路故障（0否 1是） */
    @TableField(value = "is_loop_fault")
    private String isLoopFault;
    /** 回路号 */
    @TableField(value = "loop_code")
    private String loopCode;
    /** 主机号 */
    @TableField(value = "host_id")
    private String hostId;
    /** 同回路故障id */
    @TableField(value = "loop_fault_id")
    private String loopFaultId;
    /** 是否为总部验证通过（0 否 1 是 2 审批验证） */
    @TableField(value = "repair_verify")
    private String repairVerify;
    @TableField(value = "is_maintenance_fault")
    private Integer isMaintenanceFault;

    /** 上报时间 */
    @TableField(exist = false)
    private String reportedTimeStr;

    @TableField(value = "apply_status")
    private Integer applyStatus;


}
