/*
package com.redxun.fire.core.job;


import com.redxun.fire.core.service.alarm.IAppFaultInfoService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.utils.DateUtil;
import com.redxun.fire.core.utils.HttpClientUtil;
import com.redxun.fire.core.utils.RedisUtils;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FaultProcessTask2 extends IJobHandler {
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IAppFaultInfoService appFaultInfoService;

    @Autowired
    private IBaseBuildingService baseBuildingService;

    @Value("${faultPop.url}")
    private String faultUrl;

    private String url = "180.96.11.20:28800/faultFill/sendMsg";


    @Resource
    private HttpClientUtil httpClientUtil;

*/
/*    @Scheduled(cron = "0 0/30 20-23 * * ? ")
    public void sendPositive() throws Exception {*//*

    @Override
    @XxlJob("faultProcessTask2")
    public void execute() throws Exception{
        log.info("<<====" + DateUtil.formatTime(System.currentTimeMillis()) + "推送弹窗信息" + "====>>");
        List<String> buildingIdList = appFaultInfoService.getBuildingIdList();
        if (!CollectionUtils.isEmpty(buildingIdList)) {
            //String userId = currentUser.getUserId();
            //String deptId = currentUser.getDeptId();
//            if (Objects.isNull(baseBuildingService.getBuildingInfoByDepId(deptId))) {
//                throw new NullPointerException();
//            }
//            String buildingId = baseBuildingService.getBuildingInfoByDepId(deptId).getId();

            buildingIdList.forEach(buildingId ->{
                HashMap<String, String> map = new HashMap<>();
                Map<String,Object> data = (Map) appFaultInfoService.popOrNot(buildingId).getData();
                Boolean pop = (Boolean)data.get("pop");
                Boolean timeFlag = (Boolean) data.get("timeFlag");

                if (pop) {
                    map.put("type", "fill");
                    map.put("socketType", "all");
                    map.put("timeFlag",timeFlag+"");
                    String result = httpClientUtil.sendGet(map, faultUrl);
                    if (!"success".equals(result)){
                        throw new RuntimeException("请求卡夫卡异常");
                    }
                }
            });
        }
    }

}
*/
