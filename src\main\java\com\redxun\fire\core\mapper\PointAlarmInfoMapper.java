package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redxun.fire.core.entity.pointalarmstatistic.PointAlarmInfo;
import com.redxun.fire.core.pojo.vo.FireInfoSyncVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【point_alarm_info(点位年度报警统计)】的数据库操作Mapper
* @createDate 2025-06-12 09:27:35
* @Entity generator.domain.PointAlarmInfo
*/
public interface PointAlarmInfoMapper extends BaseMapper<PointAlarmInfo> {

    List<String> getSyncTable();

    List<FireInfoSyncVo> selectSyncDataByTableAndBuildingId(@Param("tableName") String tableName, @Param("buildingId") String buildingId);

    void updateSyncTableState(@Param("tableName") String tableName);
}




