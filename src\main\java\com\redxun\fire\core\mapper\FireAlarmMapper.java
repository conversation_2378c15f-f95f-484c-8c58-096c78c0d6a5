package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FireAlarmMapper extends BaseMapper<Map> {
    List<Map<String, Object>> repairmanList(String buildingId);

    Integer backToNormal(@Param("param") Map<String, Object> param);

    List<LinkedMap<String, Object>> getUnreported(@Param("buildingId") String buildingId,  Page<?> page);

    List<LinkedMap<String, Object>> getUnhandled(@Param("buildingId") String buildingId, Page<?> page);

    List<LinkedMap<String, Object>> getFinshed(@Param("buildingId") String buildingId, @Param("types") List<String> types,  Page<?> page);

    Map<String, Object> getTestFireInfo(String id);

    Map<String,Object> getCounts(@Param("buildingId") String buildingId);

    int getCompletedCount(@Param("buildingId") String buildingId);

    List<Map<String, Object>> getFireCheckInfoList(@Param("fireId") String fireId);

    List<Map<String, Object>> statisticsDaily(@Param("param") Map param);

    List<Map<String, Object>> queryInfo(@Param("param") Map param);

    List<Map<String, Object>> selectAll(Page<?> page);

    Integer getFireCounts();

    Integer getTeamNums(String wbTeamId);

    List<LinkedMap<String, Object>> selectFeed(@Param("buildingId")String buildingId, Page<?> page);

    List<LinkedMap<String, Object>> selectUnFeed(@Param("buildingId")String buildingId, Page<?> page);

    int selectUnFeedCountAllBuild(@Param("param") Map param);

    Map<String,Object> getUnFeed(String buildingId);

    Integer getPointNum(@Param("buildingId") String buildingId, @Param("zoneName")String zoneName,@Param("check") String check);

    String getLastId(@Param("idList") List<String> idList);

    List<String> getSyncIds(@Param("pointId")String pointId,@Param("lastTime")String lastTime ,@Param("timeSection")String timeSection);
}

