package com.redxun.fire.core.consts;

/**
 * 闭店异常
 */
public interface CloseStoreExceptionConstants {
// 闭店监测异常类型
    /**
     * 异常报警
     */
    String EXPECTION_TYPE_0 = "0";
    /**
     * 电量过低
     */
    String EXPECTION_TYPE_1 = "1";
    /**
     * 设备故障
     */
    String EXPECTION_TYPE_4 = "4";
    /**
     * 设备离线
     */
    String EXPECTION_TYPE_5 = "5";
    /**
     * 其他
     */
    String EXPECTION_TYPE_6 = "6";

    /**
     * 7电流超限异常报警
     */
    String EXPECTION_TYPE_7 = "7";

    /**
     * 电流回归异常报警
     */
    String EXPECTION_TYPE_8 = "8";

    /**
     * 突变电流异常报警
     */
    String EXPECTION_TYPE_9 = "9";

    /**
     * 断电异常
     */
    String EXPECTION_TYPE_10 = "10";

}
