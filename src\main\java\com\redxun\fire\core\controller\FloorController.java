package com.redxun.fire.core.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.redxun.api.feign.ConstructClient;
import com.redxun.api.feign.OrgPosClient;
import com.redxun.api.feign.SpaceClient;
import com.redxun.api.feign.SystemClient;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.fire.core.entity.MidProjectInfo;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2024/8/20
 * @description
 */
@RestController
@RequestMapping("/floor")
public class FloorController {

    @Autowired
    SpaceClient spaceClient;

    @Resource
    ConstructClient constructClient;

    @Resource
    SystemClient systemClient;

    @Resource
    OrgPosClient orgPosClient;

    @GetMapping("getBuildingSelect")
    public JsonResult getBuildingSelect(@RequestParam String projectId) {
       if (StrUtil.isBlank(projectId)) {
           return JsonResult.getFailResult("入参不规范");
       }
        return spaceClient.getBuildingSelect(null, projectId);
    }

    @PostMapping("/getFloorPageByProjectId")
    public JsonResult getFloorPageByProjectId(@RequestBody QueryData queryData) {
        if (ObjectUtil.isEmpty(queryData)) {
            return JsonResult.getFailResult("入参不规范");
        }
        return spaceClient.getFloorPageByProjectId(queryData);
    }

    @PostMapping("delFloor")
    JsonResult<?> delFloor(@RequestBody Map<String, Object> params) {
        if (ObjectUtil.isEmpty(params) || ObjectUtil.isEmpty(params.get("id"))) {
            return JsonResult.getFailResult("入参不规范");
        }
        return constructClient.delFloor(params);
    }

    @PostMapping("saveUpdateFloor")
    JsonResult<?> saveUpdateFloor(@RequestBody Map<String, Object> params) {
        return constructClient.saveUpdateFloor(params);
    }

    @PostMapping("delBuilding")
    JsonResult<?> delBuilding(@RequestBody Map<String, Object> params) {
        if (ObjectUtil.isEmpty(params) || ObjectUtil.isEmpty(params.get("id"))) {
            return JsonResult.getFailResult("入参不规范");
        }
        return constructClient.delBuilding(params);
    }

    @PostMapping("saveUpdateBuilding")
    JsonResult<?> saveUpdateBuilding(@RequestBody Map<String, Object> params) {
        return constructClient.saveUpdateBuilding(params);
    }

    @GetMapping("getListByKey")
    JsonResult getListByKey(@RequestParam String key) {
        if (StrUtil.isBlank(key)) {
            return JsonResult.getFailResult("入参不规范");
        }
        return systemClient.getListByKey(key);
    }

    @GetMapping("getProjectByUser")
    JsonResult getProjectByUser(HttpServletRequest request) {
        String userId = request.getHeader("Wzt-Userid");
        if (StrUtil.isBlank(userId)) {
            return JsonResult.getFailResult("入参不规范");
        }
        return orgPosClient.getProjectByUser(userId);
    }

    @PostMapping("getProjectByUserOnPage")
    JsonResult getProjectByUserOnPage(@RequestBody QueryData queryData, HttpServletRequest request) {
        String userId = request.getHeader("Wzt-Userid");
        if (StrUtil.isBlank(userId)) {
            return JsonResult.getFailResult("入参不规范");
        }
        final val projectByUser = orgPosClient.getProjectByUser(userId);
        final val params = queryData.getParams();
        String relProjectsName = StrUtil.EMPTY;
        String address = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(params)) {
            relProjectsName = params.get("relProjectsName");
            address = params.get("address");
        }
        if (ObjectUtil.isNotEmpty(projectByUser) && projectByUser.getSuccess()) {
            List<MidProjectInfo> list = (List<MidProjectInfo>)projectByUser.getData();
            ObjectMapper mapper = new ObjectMapper();
            List<MidProjectInfo> listNew = mapper.convertValue(list, new TypeReference<List<MidProjectInfo>>() { });
            Map<String, Object> map = new HashMap<>();

            final val iterator = listNew.iterator();
            while (iterator.hasNext()) {
                final val next = iterator.next();
                if (StrUtil.isNotBlank(relProjectsName) && StrUtil.isNotBlank(next.getRelProjectsName())
                        && next.getRelProjectsName().indexOf(relProjectsName) == -1) {
                    iterator.remove();
                }
                if (StrUtil.isNotBlank(address) && StrUtil.isNotBlank(next.getAddress())
                        && next.getAddress().indexOf(address) == -1) {
                    iterator.remove();
                }
            }

            if (CollectionUtil.isNotEmpty(listNew)) {
                int pageNo = queryData.getPageNo() == null ? 1 : queryData.getPageNo();
                int pageSize = queryData.getPageSize() == null ? 10 : queryData.getPageSize();
                List<MidProjectInfo> pageList = page(listNew, pageNo, pageSize);
                map.put("data", pageList);
                map.put("totalCount", listNew.size());
                map.put("totalPage", (listNew.size() - 1)/pageSize + 1);
            } else {
                map.put("data", listNew);
                map.put("totalCount", listNew.size());
                map.put("totalPage", 0);
            }
            projectByUser.setData(map);
        }
        return projectByUser;
    }

    private <T> List<T> page(List<T> list, int pageNo, int pageSize) {
        int size = list.size();
        int l = (pageNo - 1) * pageSize;
        int r = pageNo * pageSize > size ? size : pageNo * pageSize;
        if (l >= size) {
            return new ArrayList<>();
        }
        return list.subList(l, r);
    }

    @GetMapping("getBuildingById")
    JsonResult getBuildingById(@RequestParam String id) {
        if (StrUtil.isBlank(id)) {
            return JsonResult.getFailResult("入参不规范");
        }
        return spaceClient.getBuildingById(id);
    }
}
