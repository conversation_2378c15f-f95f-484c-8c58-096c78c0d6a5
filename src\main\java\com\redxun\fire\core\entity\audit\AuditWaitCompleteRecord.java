package com.redxun.fire.core.entity.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redxun.fire.core.entity.BaseDevicePoint;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 待补全点位检查记录
 */
@Data
@NoArgsConstructor
public class AuditWaitCompleteRecord implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditRecordId;

    /**
     * 建筑名称
     */
    private String buildingName;

    /**
     * 点位编号
     */
    private String pointNumber;

    /**
     * 设备类型
     */
    private String devTypeName;

    /**
     * 用户传输编码
     */
    private String transmissionNumber;

    /**
     * 点位描述
     */
    private String pointDesc;

    /**
     * 防火分区
     */
    private String zoneName;

    /**
     * 楼层
     */
    private String floor;

    /**
     * 设施系统
     */
    private String fasName;

    public AuditWaitCompleteRecord(BaseDevicePoint baseDevicePoint) {
        this.pointNumber = baseDevicePoint.getPointNumber();
        this.devTypeName = baseDevicePoint.getDevTypeName();
        this.transmissionNumber = baseDevicePoint.getTransmissionNumber();
        this.floor = baseDevicePoint.getFloor()+"F";
        this.fasName = baseDevicePoint.getFasName();
        this.buildingName = baseDevicePoint.getBuildName();
        this.zoneName  = baseDevicePoint.getZoneName();
        this.pointDesc = baseDevicePoint.getPointDesc();
    }
}
