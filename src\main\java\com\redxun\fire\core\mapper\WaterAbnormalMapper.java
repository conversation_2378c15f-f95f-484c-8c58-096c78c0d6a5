package com.redxun.fire.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.redxun.fire.core.entity.WaterAbnormal;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public interface WaterAbnormalMapper extends BaseMapper<WaterAbnormal> {

    List<LinkedMap<String, Object>> waterProcessedList(Page<?> page, @Param("processStatus") String processStatus, @Param("buildingId") String buildingId, @Param("nowTime") String nowTime);

    Integer getCounts(@Param("param") Map<String, Object> param);

    Integer getCountsAllBuild(@Param("param") Map<String, Object> param);

    LinkedList<Map<String, Object>> getPointListByPage(@Param("param") HashMap<String, Object> param);

    //LinkedList<Map<String, Object>> getPumpPointListByPage(@Param("param")HashMap<String, Object> param);

   // List<LinkedMap<String,Object>> selectByIds(@Param("exceptionIds") List<String> exceptionIds,@Param("param") Map<String,Object> param);


    /**
     *  用于导出
     *
     * @param param
     * @return
     */
    List<Map> selectWaterAbnormalToExport(@Param("param") Map param);

    Double getProcessTime(@Param("param") Map<String, Object> param);

    Double getReportedDeviceProcessTime(@Param("param") Map<String, Object> param);
}
