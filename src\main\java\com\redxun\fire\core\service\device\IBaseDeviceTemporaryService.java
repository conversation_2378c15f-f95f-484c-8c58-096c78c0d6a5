package com.redxun.fire.core.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.fire.core.entity.BaseDevicePoint;
import com.redxun.fire.core.entity.BaseDeviceTemporary;
import com.redxun.fire.core.entity.BaseDeviceTemporaryList;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-24
 */
public interface IBaseDeviceTemporaryService extends IService<BaseDeviceTemporary> {
    /**
     * 添加点位信息
     *
     * @param list
     * @param percent
     * @param applyId
     * @return
     */
    Map<String, Object> addDevPoints(List<BaseDeviceTemporary> list, double percent, String applyType, String applyId);

    /**
     * 添加点位信息
     *
     * @param list
     * @return
     */
    Map<String, Object> addDevPointsT(HttpServletRequest request, BaseDeviceTemporaryList baseDeviceTemporaryList, double percent);

    /**
     * 根据审批ID获取点位信息
     *
     * @param baseDeviceTemporary
     * @return
     */
    public List<BaseDeviceTemporary> selectDevPointsByApplyId(BaseDeviceTemporary baseDeviceTemporary);

    /**
     * 新增点位
     *
     * @param deviceTemporary
     * @param approveTime
     * @return
     */
    public boolean addApplyPoint(BaseDeviceTemporary deviceTemporary, Date approveTime);

    /**
     * 编辑点位
     *
     * @param deviceTemporary
     * @return
     */
    public boolean editApplyPoint(BaseDeviceTemporary deviceTemporary);

    /**
     * 删除点位
     *
     * @param deviceTemporary
     * @return
     */
    public boolean deleteApplyPoint(BaseDeviceTemporary deviceTemporary);

    /**
     * 更新状态
     *
     * @param deviceTemporary
     * @return
     */
    public boolean updateStatusByApplyId(BaseDeviceTemporary deviceTemporary);

    /**
     * 更新点位状态
     *
     * @param
     * @return
     */
    public boolean updateDotStatus(BaseDeviceTemporary baseDeviceTemporary);

    /**
     * 根据建筑ID获取点位总数
     *
     * @param buildingId 建筑物id
     * @return
     */
    int getPointCountByBuildId(String buildingId);


    /**
     * 数字转换 did hid 等相应转换
     *
     * @param source 源字符串
     * @param length 最大长度
     * @return
     */
    String transferString(String source, Integer length);

    /**
     * 转换直接16进制
     *
     * @param localHostNum
     * @param localLoopCode
     * @param pointNumber
     * @return
     */

    String transferDidXimenzi(String localHostNum, String localLoopCode, String pointNumber);

    /**
     * 转换fuan
     *
     * @param localHostNum
     * @param localLoopCode
     * @param pointNumber
     * @return
     */
    String transferDidFuan(String localHostNum, String localLoopCode, String pointNumber);

    /**
     * 方法5
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferDidMethodFive(String localHost, String localLoop, String pointCode);

    /**
     * 方法二
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferDidMethodTwo(String localHost, String localLoop, String pointCode);

    /**
     * 方法1
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferDidMethodOne(String localHost, String localLoop, String pointCode);

    /**
     * 方法7 大华
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferDidMethodSeven(Integer localHost, Integer localLoop, Integer pointCode);

    /**
     * 方法0
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferDidMethodZero(String localHost, String localLoop, String pointCode);

    /**
     * 根据申请id查询
     *
     * @param applyId
     * @return
     */
    List<BaseDeviceTemporary> getPointByApplyId(String applyId);

    List<BaseDeviceTemporary> getPointFileByApplyId(String applyId) throws IOException;

    void setPointRedis(String id);

    void setWaterPump();

    Map<String, Object> addNewDevPoints(List<BaseDeviceTemporary> list);

    JsonResult addApplyInfo(HttpServletRequest request, BaseDeviceTemporaryList baseDeviceTemporaryList);

    void deletePointInfo(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime);

    void deleteDeviceTemporarysByTime(Date time) throws Exception;


    void editPointInfo(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime);

    /**
     * 特批点位申请处理
     *
     * @param deviceTemporaries
     * @param approveTime
     */
    void addSpecialPoint(List<BaseDeviceTemporary> deviceTemporaries, Date approveTime);

    /**
     * 调整建筑维保计划
     *
     * @param buildingId
     */
    void adjustMaintenancePlan(String buildingId, List<BaseDevicePoint> devicePoints, Boolean flag, String effectiveTime);


    /**
     * 删除点位 用于处理重复点位数据
     */
    int deletePoint(String buildingId, String flag);

    void restPlan(String buildingId);

    void restPlanByPoint(String buildingId, List<BaseDevicePoint> baseBuildingList);

    /**
     * 方法13 北大青鸟解析
     *
     * @param localHost
     * @param localLoop
     * @param pointCode
     * @return
     */
    String transferStringQingniao(String localHost, String localLoop, String pointCode);

    String transferHaiWanMethodSeven(String parseInt);
}

