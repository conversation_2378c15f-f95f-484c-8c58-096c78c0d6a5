package com.redxun.fire.core.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gexin.fastjson.JSON;
import com.google.gson.Gson;
import com.redxun.api.model.param.FireQueryBuildFloorParam;
import com.redxun.common.base.entity.JsonPage;
import com.redxun.common.base.entity.JsonPageResult;
import com.redxun.common.base.entity.JsonResult;
import com.redxun.common.base.entity.QueryData;
import com.redxun.common.tool.IdGenerator;
import com.redxun.common.tool.StringUtils;
import com.redxun.fire.core.entity.*;
import com.redxun.fire.core.entity.api.Merchant;
import com.redxun.fire.core.entity.wzt.MerchantResponse;
import com.redxun.fire.core.entity.wzt.ProjectResponse;
import com.redxun.fire.core.entity.wzt.UserOrgResponse;
import com.redxun.fire.core.entity.wzt.UsersResponse;
import com.redxun.fire.core.enums.LogTypeEnums;
import com.redxun.fire.core.service.building.IBaseBuildingFloorService;
import com.redxun.fire.core.service.building.IBaseBuildingService;
import com.redxun.fire.core.service.building.impl.MidBuildingServiceImpl;
import com.redxun.fire.core.service.other.IJournalizingService;
import com.redxun.fire.core.service.user.IOsGroupService;
import com.redxun.fire.core.service.user.IOsUserService;
import com.redxun.fire.core.service.user.IUserDataPowerService;
import com.redxun.fire.core.service.user.impl.OrgMiddleServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户数据权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Slf4j
@RestController
@RequestMapping("/user-data-power")
public class UserDataPowerController {
    @Autowired
    private IUserDataPowerService userDataPowerService;
    @Autowired
    private IJournalizingService journalizingService;
    @Autowired
    private OrgMiddleServiceImpl orgMiddleService;
    @Autowired
    private MidBuildingServiceImpl midBuildingService;
    @Autowired
    private IOsUserService osUserService;
    @Autowired
    private IBaseBuildingService baseBuildingService;
    @Autowired
    private IBaseBuildingFloorService baseBuildingFloorService;
    @Autowired
    private IOsGroupService iOsGroupService;
    Gson gson = new Gson();
    /**
     * 保存特殊授权
     *
     * @param userDataPower
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/saveUserDataPower")
    @ApiOperation("单独特殊授权保存")
    public JsonResult saveUserDataPower(HttpServletRequest request, @RequestBody @Validated UserDataPower userDataPower) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = userDataPower.getUserId();
        log.info("单独特殊授权保存-------------------------入参saveUserDataPower-userDataPower:" + JSON.toJSONString(userDataPower));
        try {
            QueryWrapper<OsUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("WZT_USER_ID_", userId);
            OsUser osUser = osUserService.getBaseMapper().selectOne(queryWrapper);
            if (osUser != null) {
                QueryWrapper<UserDataPower> queryUserDataPowerWrapper = new QueryWrapper<>();
                queryUserDataPowerWrapper.eq("USER_ID_", userId);
                String level = userDataPower.getLevelAyer();
                if ("1".equals(level)) {
                    queryUserDataPowerWrapper.eq("BUILDING_ID_", userDataPower.getBuildingId());
                } else if ("2".equals(level)) {
                    queryUserDataPowerWrapper.eq("CITY_", userDataPower.getCity());
                } else if ("3".equals(level)) {
                    queryUserDataPowerWrapper.eq("REGION_", userDataPower.getRegion());
                } else if ("4".equals(level)) {
                    queryUserDataPowerWrapper.eq("GROUP_ORG_", userDataPower.getGroupOrg());
                }
                List<UserDataPower> userDataPowerList = userDataPowerService.getBaseMapper().selectList(queryUserDataPowerWrapper);
                if (userDataPowerList != null && userDataPowerList.size() > 0) {
                    List<String> powerIdList = userDataPowerList.stream().map(e -> e.getId()).collect(Collectors.toList());
                    userDataPowerService.removeByIds(powerIdList);
                }
                userDataPower.setId(IdGenerator.getIdStr());
                userDataPower.setCreateTime(new Date());
                userDataPower.setTenantId(osUser.getTenantId());
                userDataPowerService.save(userDataPower);
                jsonResult.setData(userDataPower);
                //插入日志
                Journalizing journalizing = new Journalizing();
                journalizing.setOperationTypeCode(LogTypeEnums.RESOURCE_AUTHORIZATION.getType());
                journalizing.setOperationContent(osUser.getFullname() + "将" + userDataPower.getOrgName() + "权限授予给" + osUser.getFullname());
                journalizingService.setLogInfo(request, journalizing);
            } else {
                jsonResult.setSuccess(false);
                jsonResult.setShow(false);
                jsonResult.setMessage("该人员不在消防系统中,请先添加人员!");
            }
        } catch (Exception e) {
            log.info("单独特殊授权保存异常！" + JSON.toJSONString(userDataPower));
        }
        return jsonResult;
    }

    /**
     * 批量保存特殊授权
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/batchSaveUserDataPower")
    @ApiOperation("批量特殊授权保存")
    public JsonResult batchSaveUserDataPower(HttpServletRequest request, @RequestBody @Validated List<UserDataPower> userDataPowerList) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        String userId = request.getHeader("xfUserId");//消防userId
        log.info("批量特殊授权保存-------------------------入参batchSaveUserDataPower-userDataPowerList:" + JSON.toJSONString(userDataPowerList));
        try {
            UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
            if (userDataPowerList != null && userDataPowerList.size() > 0) {
                for (UserDataPower userDataPower : userDataPowerList) {
                    userDataPower.setId(IdGenerator.getIdStr());
                }
                userDataPowerService.saveBatch(userDataPowerList);
            }
            //插入日志
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.HOST_POINT.getType());
            journalizing.setOperationContent(dataDto.getFullName() + "特殊授权");
            journalizingService.setLogInfo(request, journalizing);
            jsonResult.setData(userDataPowerList);

        } catch (Exception e) {
            log.info("批量特殊授权保存异常！" + JSON.toJSONString(userDataPowerList));
        }
        return jsonResult;
    }

    /**
     * 批量保存特殊授权
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/deleteUserDataPower")
    @ApiOperation("删除特殊授权")
    public JsonResult deleteUserDataPower(HttpServletRequest request, @RequestBody @Validated UserDataPower userDataPower) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        log.info("删除特殊授权-------------------------入参deleteUserDataPower-userDataPowerIds:" + JSON.toJSONString(userDataPower));
        String userId = userDataPower.getUserId();
        try {
            userDataPowerService.removeById(userDataPower.getId());
            OsUser osUser = osUserService.getOne(new LambdaQueryWrapper<OsUser>().eq(OsUser::getWztUserId, userId));
            if(osUser != null){
                osUserService.removeById(osUser.getUserId());
            }
            UsersResponse.DataDTO dataDto = orgMiddleService.findUserByUserId(wztUserId);
            //插入日志
            Journalizing journalizing = new Journalizing();
            journalizing.setOperationTypeCode(LogTypeEnums.RESOURCE_AUTHORIZATION.getType());
            journalizing.setOperationContent(dataDto.getFullName() + "将" + userDataPower.getUserName() + "在" + userDataPower.getBuildingName() + "广场权限删除");
            journalizingService.setLogInfo(request, journalizing);
        } catch (Exception e) {
            log.info("删除特殊授权异常！" + JSON.toJSONString(userDataPower));
        }
        return jsonResult;
    }

    /**
     * 特殊授权人员信息列表
     *
     * @param queryData
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/userDataPowerUserList")
    @ApiOperation("特殊授权人员信息列表")
    public JsonPageResult userDataPowerUserList(@RequestBody @Validated QueryData queryData) {
        JsonPageResult jsonPageResult = JsonPageResult.getSuccess("");
        Page<UserDataPower> page = new Page<>(queryData.getPageNo(), queryData.getPageSize());
        log.info("特殊授权人员信息列表-------------------------userDataPowerUserList-queryData:" + JSON.toJSONString(queryData));
        try {
            Map<String, String> param = queryData.getParams();
            page = userDataPowerService.findUserDataPowerList(page, param);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        jsonPageResult.setResult(new JsonPage(page));
        return jsonPageResult;
    }

    /**
     * 刷新建筑信息表组织字段
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/refreshData")
    @ApiOperation("刷新建筑信息表组织字段")
    public JsonResult refreshData(HttpServletRequest request, @RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        log.info("刷新建筑信息表组织字段-------------------------入参refreshData-userDataPowerIds:" + JSON.toJSONString(map));
        String projectId = map.get("projectId");
        try {
            QueryWrapper<MidBuilding> queryMidBuildingWrapper = new QueryWrapper<>();
            queryMidBuildingWrapper.isNotNull("middle_id");
            List<MidBuilding> midBuildingList = midBuildingService.getBaseMapper().selectList(queryMidBuildingWrapper);
            if (midBuildingList != null && midBuildingList.size() > 0) {
                for (MidBuilding midBuilding : midBuildingList) {
                    BaseBuilding baseBuilding = baseBuildingService.getBaseMapper().selectById(midBuilding.getId());
                    if (baseBuilding != null) {
                        ProjectResponse projectOrgResponse = orgMiddleService.getOrgManageInfoById(midBuilding.getMiddleId());
                        if (projectOrgResponse.getSuccess() != null && projectOrgResponse.getSuccess() && projectOrgResponse.getData() != null) {
                            baseBuilding.setGroupOrg(projectOrgResponse.getData().getGroup());
                            baseBuilding.setRegion(projectOrgResponse.getData().getRegion());
                            baseBuilding.setCityCompany(projectOrgResponse.getData().getCity());
                            baseBuilding.setPiazza(projectOrgResponse.getData().getPiazza());
                            baseBuilding.setLevelAyer(projectOrgResponse.getData().getLevel());
                            baseBuilding.setMiddleId(midBuilding.getMiddleId());
                            baseBuilding.setProjectId(midBuilding.getProjectId());
                            String projectType = projectOrgResponse.getData().getBusiness();
                            baseBuilding.setProjectType(projectType);
                            /*List<String> projectIdList=new ArrayList<>();
                            projectIdList.add(midBuilding.getProjectId());
                            List<ProjectOrgResponse.DataDTO> projectOrgResponseList=orgMiddleService.queryOrgManageByProjectIdsOrOrgIds(projectIdList);
                            if(projectOrgResponseList!=null&&projectOrgResponseList.size()>0){
                                ProjectOrgResponse.DataDTO dataDto=projectOrgResponseList.get(0);
                                baseBuilding.setCenter(dataDto.getGroup());
                                baseBuilding.setCenterVal(dataDto.getGroupName());
                                baseBuilding.setJurisdiction(dataDto.getCity());
                                baseBuilding.setJurisdictionVal(dataDto.getCityName());
                                baseBuilding.setRegional(dataDto.getRegion());
                                baseBuilding.setSafetyBelt(dataDto.getRegionName());
                            }*/
                            baseBuildingService.updateById(baseBuilding);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("刷新建筑信息表组织字段异常！" + JSON.toJSONString(map));
        }
        return jsonResult;
    }

    /**
     * 刷新人员信息表组织字段
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/refreshUserData")
    @ApiOperation("刷新人员信息表组织字段")
    public JsonResult refreshUserData(HttpServletRequest request, @RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = request.getHeader("Wzt-Userid");//万中台userId
        log.info("刷新人员信息表组织字段-------------------------入参refreshUserData-userDataPowerIds:" + JSON.toJSONString(map));
        String projectId = map.get("projectId");
        try {
            QueryWrapper<OsUser> queryOsUserWrapper = new QueryWrapper<>();
            queryOsUserWrapper.isNotNull("WZT_USER_ID_");
            queryOsUserWrapper.isNull("GROUP_ORG_");
            List<OsUser> osUserList = osUserService.getBaseMapper().selectList(queryOsUserWrapper);
            if (osUserList != null && osUserList.size() > 0) {
                for (OsUser osUser : osUserList) {
                    UserOrgResponse userOrgResponse = orgMiddleService.getOrgManageByUser(osUser.getWztUserId());
                    if (userOrgResponse.getSuccess() != null && userOrgResponse.getSuccess() && userOrgResponse.getData() != null && userOrgResponse.getData().size() > 0) {
                        UserDataPower userDataPower = new UserDataPower();
                        userDataPower.setId(IdGenerator.getIdStr());
                        userDataPower.setUserId(osUser.getWztUserId());
                        userDataPower.setUserName(userOrgResponse.getData().get(0).getUserName());
                        userDataPower.setGroupOrg(userOrgResponse.getData().get(0).getGroup());
                        userDataPower.setRegion(userOrgResponse.getData().get(0).getRegion());
                        userDataPower.setCity(userOrgResponse.getData().get(0).getCity());
                        userDataPower.setBuildingId(userOrgResponse.getData().get(0).getOrg());
                        userDataPower.setLevelAyer(userOrgResponse.getData().get(0).getLevel());
                        osUser.setGroupOrg(userOrgResponse.getData().get(0).getGroup());
                        osUser.setRegion(userOrgResponse.getData().get(0).getRegion());
                        osUser.setCity(userOrgResponse.getData().get(0).getCity());
                        osUser.setPiazza(userOrgResponse.getData().get(0).getOrg());
                        osUser.setGroupOrgName(userOrgResponse.getData().get(0).getGroupName());
                        osUser.setRegionName(userOrgResponse.getData().get(0).getRegionName());
                        osUser.setCityName(userOrgResponse.getData().get(0).getCityName());
                        osUser.setPiazzaName(userOrgResponse.getData().get(0).getOrgName());
                        osUser.setLevelAyer(userOrgResponse.getData().get(0).getLevel());
                        osUser.setTenantId(userOrgResponse.getData().get(0).getTenantId());
                        osUser.setWxPosValue(userOrgResponse.getData().get(0).getPositionName());
                        osUser.setWxPosKey(userOrgResponse.getData().get(0).getPosition());
                        if ("1".equals(userOrgResponse.getData().get(0).getLevel())) {
                            osUser.setRankLevel("2");
                            String orgName = userOrgResponse.getData().get(0).getGroupName() + "-" + userOrgResponse.getData().get(0).getRegionName() + "-" + userOrgResponse.getData().get(0).getCityName() + "-" + userOrgResponse.getData().get(0).getOrgName();
                            userDataPower.setOrgName(orgName);
                        } else if ("2".equals(userOrgResponse.getData().get(0).getLevel())) {
                            osUser.setRankLevel("2");
                            String orgName = userOrgResponse.getData().get(0).getGroupName() + "-" + userOrgResponse.getData().get(0).getRegionName() + "-" + userOrgResponse.getData().get(0).getCityName();
                            userDataPower.setOrgName(orgName);
                        } else if ("3".equals(userOrgResponse.getData().get(0).getLevel())) {
                            osUser.setRankLevel("2");
                            String orgName = userOrgResponse.getData().get(0).getGroupName() + "-" + userOrgResponse.getData().get(0).getRegionName();
                            userDataPower.setOrgName(orgName);
                        } else {
                            osUser.setRankLevel("1");
                            String orgName = userOrgResponse.getData().get(0).getGroupName();
                            userDataPower.setOrgName(orgName);
                        }
                        userDataPowerService.getBaseMapper().insert(userDataPower);
                        osUser.setUserCategory("1");
                        osUser.setUserFw("1");
                        osUserService.getBaseMapper().updateById(osUser);
                    }
                }
            }
        } catch (Exception e) {
            log.info("刷新人员信息表组织字段异常！" + JSON.toJSONString(map));
        }
        return jsonResult;
    }

    /**
     * 获取人员数据权限
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getUserDataPower")
    @ApiOperation("获取人员数据权限")
    public JsonResult getUserDataPower(@RequestBody @Validated Map<String, Object> map) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = map.get("wztUserId").toString();//万中台userId
        log.info("获取人员数据权限-------------------------入参getUserDataPower:" + JSON.toJSONString(map));
        StringBuilder sql = new StringBuilder();
        try {
            QueryWrapper<UserDataPower> queryUserDataPowerWrapper = new QueryWrapper<>();
            queryUserDataPowerWrapper.eq("USER_ID_", wztUserId);
            List<UserDataPower> userDataPowerList = userDataPowerService.getBaseMapper().selectList(queryUserDataPowerWrapper);
            if (userDataPowerList != null && userDataPowerList.size() > 0) {
                sql.append("and (");
                for (int i = 0; i < userDataPowerList.size(); i++) {
                    UserDataPower userDataPower = userDataPowerList.get(i);
                    String level = userDataPower.getLevelAyer();
                    if (i == userDataPowerList.size() - 1) {
                        if ("1".equals(level)) {
                            sql.append("bab.piazza='").append(userDataPower.getBuildingId()).append("' ");
                        } else if ("2".equals(level)) {
                            sql.append("bab.city_company='").append(userDataPower.getCity()).append("' ");
                        } else if ("3".equals(level)) {
                            sql.append("bab.region='").append(userDataPower.getRegion()).append("' ");
                        } else if ("4".equals(level)) {
                            sql.append("bab.group_org='").append(userDataPower.getGroupOrg()).append("' ");
                        }
                    } else {
                        if ("1".equals(level)) {
                            sql.append("bab.piazza='").append(userDataPower.getBuildingId()).append("' or ");
                        } else if ("2".equals(level)) {
                            sql.append("bab.city_company='").append(userDataPower.getCity()).append("' or ");
                        } else if ("3".equals(level)) {
                            sql.append("bab.region='").append(userDataPower.getRegion()).append("' or ");
                        } else if ("4".equals(level)) {
                            sql.append("bab.group_org='").append(userDataPower.getGroupOrg()).append("' or ");
                        }
                    }
                }
                sql.append(") ");
                jsonResult.setData(sql.toString());
            } else {
                return jsonResult.setSuccess(false);
            }
        } catch (Exception e) {
            log.info("刷新人员信息表组织字段异常！" + JSON.toJSONString(map));
        }
        return jsonResult;
    }

    /**
     * 获取人员数据权限返回建筑信息
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getUserDataPowerForList")
    @ApiOperation("获取人员数据权限返回建筑信息")
    public JsonResult getUserDataPowerForList(@RequestBody @Validated Map<String, Object> map) {
        JsonResult jsonResult = JsonResult.Success();
        String wztUserId = map.get("wztUserId").toString();//万中台userId
        log.info("获取人员数据权限返回建筑信息-------------------------入参getUserDataPowerForList:" + JSON.toJSONString(map));
        try {
            List<BaseBuilding> buildingList = userDataPowerService.getUserDataPowerForList(wztUserId);
            jsonResult.setData(buildingList);
        } catch (Exception e) {
            log.info("获取人员数据权限返回建筑信息！" + JSON.toJSONString(map));
        }
        return jsonResult;
    }
    /**
     * 同步楼层信息
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/findFloorData")
    @ApiOperation("同步楼层信息")
    public JsonResult findFloorData(HttpServletRequest request, @RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult1 = JsonResult.Success();
        log.info("同步楼层信息任务开始");
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.isNotNull("project_id");
        List<BaseBuilding> baseBuildingList=baseBuildingService.getBaseMapper().selectList(queryWrapper);
        if(baseBuildingList!=null&&baseBuildingList.size()>0){
            log.info("建筑信息------------------------------baseBuildingList："+baseBuildingList.size());
            for(BaseBuilding baseBuilding:baseBuildingList){
                FireQueryBuildFloorParam fireQueryBuildFloorParam=new FireQueryBuildFloorParam();
                fireQueryBuildFloorParam.setProjectId(baseBuilding.getProjectId());
                JsonResult jsonResult=orgMiddleService.findFloorData(fireQueryBuildFloorParam);
                if(jsonResult.getSuccess()!=null&&jsonResult.getSuccess()){
                    JSONArray dataDTO=JSONArray.parseArray(com.alibaba.fastjson.JSON.toJSONString(jsonResult.getData()));
                    if(dataDTO!=null&&dataDTO.size()>0){
                        for(Object o:dataDTO){
                            List<BaseBuildingFloor> baseBuildingFloorList=new ArrayList<>();
                            JSONObject jsonObject=(JSONObject)o;
                            String wztConstructId=jsonObject.getString("id");//楼栋Id
                            String wztConstructName=jsonObject.getString("localName");//楼栋名称
                            JSONArray floorJson=jsonObject.getJSONArray("dwsObjectFloorList");
                            if(floorJson!=null&&floorJson.size()>0){
                                for(Object floor:floorJson){
                                    JSONObject floorJsonObject=(JSONObject)floor;
                                    String wztFloorId=floorJsonObject.getString("id");
                                    String wztFloorName=floorJsonObject.getString("localName");
                                    JSONArray xfFloorIdList=floorJsonObject.getJSONArray("fireFloorIdSet");
                                    if(xfFloorIdList!=null&&xfFloorIdList.size()>0){
                                        for(Object xfFloorId:xfFloorIdList){
                                            BaseBuildingFloor baseBuildingFloor=baseBuildingFloorService.getById(xfFloorId.toString());
                                            if(baseBuildingFloor!=null){
                                                baseBuildingFloor.setWztFloorId(wztFloorId);
                                                baseBuildingFloor.setWztFloorName(wztFloorName);
                                                baseBuildingFloor.setWztConstructId(wztConstructId);
                                                baseBuildingFloor.setWztConstructName(wztConstructName);
                                                baseBuildingFloorList.add(baseBuildingFloor);
                                            }
                                        }
                                    }
                                }
                            }
                            if(baseBuildingFloorList!=null&&baseBuildingFloorList.size()>0){
                                log.info("同步楼层信息批量更新baseBuildingFloorList:"+baseBuildingFloorList.size());
                                baseBuildingFloorService.saveOrUpdateBatch(baseBuildingFloorList);
                            }
                        }
                    }

                }else{
                    log.info("调用万中台获取楼层信息失败jsonResult:"+ JSON.toJSONString(jsonResult));
                }
            }
        }

        log.info("同步楼层信息任务结束");
        return jsonResult1;
    }
    /**
     * 同步楼层信息集合
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/findFloorDataPro")
    @ApiOperation("同步楼层信息集合")
    public JsonResult findFloorDataPro(HttpServletRequest request, @RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult1 = JsonResult.Success();
        log.info("同步楼层信息集合任务开始");
        String projectIds=map.get("projectIds");
        JSONArray projectIdArray=JSONArray.parseArray(projectIds);
        if(projectIdArray!=null&&projectIdArray.size()>0){
            log.info("建筑信息集合------------------------------baseBuildingList："+projectIdArray.size());
            for(Object baseBuilding:projectIdArray){
                String baseBuildingPg=baseBuilding.toString();
                FireQueryBuildFloorParam fireQueryBuildFloorParam=new FireQueryBuildFloorParam();
                fireQueryBuildFloorParam.setProjectId(baseBuildingPg);
                JsonResult jsonResult=orgMiddleService.findFloorData(fireQueryBuildFloorParam);
                if(jsonResult.getSuccess()!=null&&jsonResult.getSuccess()){
                    JSONArray dataDTO=JSONArray.parseArray(com.alibaba.fastjson.JSON.toJSONString(jsonResult.getData()));
                    if(dataDTO!=null&&dataDTO.size()>0){
                        for(Object o:dataDTO){
                            List<BaseBuildingFloor> baseBuildingFloorList=new ArrayList<>();
                            JSONObject jsonObject=(JSONObject)o;
                            String wztConstructId=jsonObject.getString("id");//楼栋Id
                            String wztConstructName=jsonObject.getString("localName");//楼栋名称
                            JSONArray floorJson=jsonObject.getJSONArray("dwsObjectFloorList");
                            if(floorJson!=null&&floorJson.size()>0){
                                for(Object floor:floorJson){
                                    JSONObject floorJsonObject=(JSONObject)floor;
                                    String wztFloorId=floorJsonObject.getString("id");
                                    String wztFloorName=floorJsonObject.getString("localName");
                                    JSONArray xfFloorIdList=floorJsonObject.getJSONArray("fireFloorIdSet");
                                    if(xfFloorIdList!=null&&xfFloorIdList.size()>0){
                                        for(Object xfFloorId:xfFloorIdList){
                                            BaseBuildingFloor baseBuildingFloor=baseBuildingFloorService.getById(xfFloorId.toString());
                                            if(baseBuildingFloor!=null){
                                                baseBuildingFloor.setWztFloorId(wztFloorId);
                                                baseBuildingFloor.setWztFloorName(wztFloorName);
                                                baseBuildingFloor.setWztConstructId(wztConstructId);
                                                baseBuildingFloor.setWztConstructName(wztConstructName);
                                                baseBuildingFloorList.add(baseBuildingFloor);
                                            }
                                        }
                                    }
                                }
                            }
                            if(baseBuildingFloorList!=null&&baseBuildingFloorList.size()>0){
                                log.info("同步楼层信息集合批量更新baseBuildingFloorList:"+baseBuildingFloorList.size());
                                baseBuildingFloorService.saveOrUpdateBatch(baseBuildingFloorList);
                            }
                        }
                    }

                }else{
                    log.info("调用万中台获取楼层信息集合失败jsonResult:"+ JSON.toJSONString(jsonResult));
                }
            }
        }

        log.info("同步楼层信息集合任务结束");
        return jsonResult1;
    }
    /**
     * 刷新维保单位数据添加万中台商户Id
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/refreshUserMerchantData")
    @ApiOperation("刷新维保单位数据添加万中台商户Id")
    public JsonResult refreshUserMerchantData(HttpServletRequest request, @RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult1 = JsonResult.Success();
        List<OsGroup> updateOsGroupList=new ArrayList<>();
        log.info("刷新维保单位数据添加万中台商户Id");
        QueryWrapper qw=new QueryWrapper();
        qw.eq("GROUP_TYPE_","1");
        qw.isNull("WZT_MERCHANT_ID");
        qw.isNotNull("PARENT_ID_");
        List<OsGroup> osGroupList= iOsGroupService.getBaseMapper().selectList(qw);
        if(osGroupList!=null&&osGroupList.size()>0){
            QueryWrapper buiding=new QueryWrapper();
            buiding.isNotNull("project_id");
            List<BaseBuilding> baseBuildingList=baseBuildingService.getBaseMapper().selectList(buiding);
            if(baseBuildingList==null||baseBuildingList.size()<=0){
                jsonResult1.setMessage("未查到建筑信息");
                jsonResult1.setCode(500);
                log.info("未查到建筑信息");
                return jsonResult1;
            }
            Map<String, String> groupMap = baseBuildingList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getProjectId()));
            log.info("刷新维保单位条数------------size:"+osGroupList.size());
            for(OsGroup osGroup:osGroupList){
                String projectId=groupMap.get(osGroup.getParentId());
                if(StringUtils.isEmpty(projectId)){
                    log.info("该建筑未与万中台进行关联，不存在项目Id"+JSON.toJSONString(osGroup));
                }else{
                    Merchant merchant =new Merchant();
                    merchant.setMerchantCode("1");
                    merchant.setMerchantName(osGroup.getName());
                    merchant.setMerchantType("WBGS");
                    merchant.setMerchantTypeName("维保公司");
                    merchant.setMerchantFrom("1");
                    merchant.setProjectId(projectId);
                    JsonResult jsonResultMerchant=orgMiddleService.createMerchant(merchant);
                    MerchantResponse merchantResponse = new MerchantResponse();
                    try {
                        merchantResponse = gson.fromJson(gson.toJson(jsonResultMerchant), (Type) MerchantResponse.class);
                        osGroup.setWztMerchantId(merchantResponse.getData().getId());//万中台商户Id
                        iOsGroupService.updateById(osGroup);
                    } catch (Exception e) {
                        log.info("---------------->>>>>>>>>>>>>万中台返回商户信息异常,jsonResult：{}", jsonResultMerchant);
                        //return JsonResult.Fail(500, "创建失败，请联系管理员");
                    }
                    //updateOsGroupList.add(osGroup);
                }
            }
            /*if(updateOsGroupList!=null&&updateOsGroupList.size()>0){
                iOsGroupService.updateBatchById(updateOsGroupList);
            }*/
        }else{
            jsonResult1.setMessage("未找到没有关联的维保单位");
            log.info("未找到没有关联的维保单位");
        }
        log.info("刷新维保单位数据添加万中台商户Id任务结束");
        return jsonResult1;
    }
    /**
     * 同步楼层信息
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/findFloorDataByProjectId")
    @ApiOperation("同步楼层信息")
    public JsonResult findFloorDataByProjectId(@RequestBody @Validated Map<String, String> map) {
        JsonResult jsonResult1 = JsonResult.Success();
        String projectId=map.get("projectId");
        QueryWrapper qw=new QueryWrapper();
        qw.eq("project_id",projectId);
        List<BaseBuilding> baseBuildingList=baseBuildingService.getBaseMapper().selectList(qw);
        if(baseBuildingList!=null&&baseBuildingList.size()>0){
            BaseBuilding baseBuilding=baseBuildingList.get(0);
            FireQueryBuildFloorParam fireQueryBuildFloorParam=new FireQueryBuildFloorParam();
            fireQueryBuildFloorParam.setProjectId(projectId);
            log.info("同步万中台楼层信息projectId:入参"+JSON.toJSONString(fireQueryBuildFloorParam));
            JsonResult jsonResult=orgMiddleService.findFloorData(fireQueryBuildFloorParam);
            log.info("同步万中台楼层信息projectId:出参"+JSON.toJSONString(jsonResult));
            if(jsonResult.getSuccess()!=null&&jsonResult.getSuccess()){
                JSONArray dataDTO=JSONArray.parseArray(com.alibaba.fastjson.JSON.toJSONString(jsonResult.getData()));
                if(dataDTO!=null&&dataDTO.size()>0){
                    for(Object o:dataDTO){
                        List<BaseBuildingFloor> baseBuildingFloorList=new ArrayList<>();
                        JSONObject jsonObject=(JSONObject)o;
                        String wztConstructId=jsonObject.getString("id");//楼栋Id
                        String wztConstructName=jsonObject.getString("localName");//楼栋名称
                        JSONArray floorJson=jsonObject.getJSONArray("dwsObjectFloorList");
                        if(floorJson!=null&&floorJson.size()>0){
                            log.info("解析楼栋floorJson-----------------------"+JSON.toJSONString(floorJson));
                            for(Object floor:floorJson){
                                JSONObject floorJsonObject=(JSONObject)floor;
                                String wztFloorId=floorJsonObject.getString("id");
                                String wztFloorName=floorJsonObject.getString("localName");
                                String sn=floorJsonObject.getString("sn");
                                Date createTime=floorJsonObject.getDate("createTime");
                                Date updateTime=floorJsonObject.getDate("updateTime");
                                String createBy=floorJsonObject.getString("createBy");
                                JSONArray xfFloorIdList=floorJsonObject.getJSONArray("fireFloorIdSet");
                                if(xfFloorIdList!=null&&xfFloorIdList.size()>0){
                                    log.info("解析楼层存在消防floorJson-----------------------"+JSON.toJSONString(xfFloorIdList));
                                    for(Object xfFloorId:xfFloorIdList){
                                        BaseBuildingFloor baseBuildingFloor=baseBuildingFloorService.getById(xfFloorId.toString());
                                        if(baseBuildingFloor!=null){
                                            baseBuildingFloor.setWztFloorId(wztFloorId);
                                            baseBuildingFloor.setFloorName(wztFloorName);
                                            baseBuildingFloor.setFloorNum(Integer.parseInt(sn));
                                            baseBuildingFloor.setWztFloorName(wztFloorName);
                                            baseBuildingFloor.setWztConstructId(wztConstructId);
                                            baseBuildingFloor.setWztConstructName(wztConstructName);
                                            baseBuildingFloor.setCreateTime(createTime);
                                            baseBuildingFloor.setUpdateTime(updateTime);
                                            baseBuildingFloor.setCreateBy(createBy);
                                            //baseBuildingFloorService.saveOrUpdate(baseBuildingFloor);
                                            baseBuildingFloorList.add(baseBuildingFloor);
                                        }
                                    }
                                }else{//消防不存在的楼层，新增
                                    log.info("解析楼层不存在消防floorJson-----------------------"+JSON.toJSONString(floor));
                                    QueryWrapper qq=new QueryWrapper();
                                    qq.eq("wzt_floor_id",wztFloorId);
                                    List<BaseBuildingFloor> baseBuildingFloorXF=baseBuildingFloorService.list(qq);
                                    if(baseBuildingFloorXF!=null&&baseBuildingFloorXF.size()>0){//存在就编辑
                                        for(BaseBuildingFloor bbf:baseBuildingFloorXF){
                                            bbf.setWztFloorName(wztFloorName);
                                            bbf.setFloorName(wztFloorName);
                                            bbf.setWztConstructName(wztConstructName);
                                            baseBuildingFloorList.add(bbf);
                                            //baseBuildingFloorService.getBaseMapper().updateById(bbf);
                                        }
                                    }else{
                                        BaseBuildingFloor baseBuildingFloor=new BaseBuildingFloor();
                                        baseBuildingFloor.setId(IdGenerator.getIdStr());
                                        baseBuildingFloor.setWztFloorId(wztFloorId);
                                        baseBuildingFloor.setWztFloorName(wztFloorName);
                                        baseBuildingFloor.setFloorName(wztFloorName);
                                        baseBuildingFloor.setWztConstructId(wztConstructId);
                                        baseBuildingFloor.setWztConstructName(wztConstructName);
                                        baseBuildingFloor.setCreateTime(createTime);
                                        baseBuildingFloor.setUpdateTime(updateTime);
                                        baseBuildingFloor.setCreateBy(createBy);
                                        if(StringUtils.isNotEmpty(sn)){
                                            baseBuildingFloor.setFloorNum(Integer.parseInt(sn));
                                        }
                                        baseBuildingFloor.setBuildingId(baseBuilding.getId());
                                        baseBuildingFloor.setTenantId(baseBuilding.getTenantId());
                                        log.info("插入楼层数据----------------------"+JSON.toJSONString(baseBuildingFloor));
                                        //baseBuildingFloorService.getBaseMapper().insert(baseBuildingFloor);
                                        baseBuildingFloorList.add(baseBuildingFloor);
                                    }
                                }
                            }
                        }
                        if(baseBuildingFloorList!=null&&baseBuildingFloorList.size()>0){
                            log.info("同步楼层信息批量更新baseBuildingFloorList:"+baseBuildingFloorList.size());
                            baseBuildingFloorService.saveOrUpdateBatch(baseBuildingFloorList);
                        }
                    }
                }

            }else{
                log.info("调用万中台获取楼层信息失败jsonResult:"+ JSON.toJSONString(jsonResult));
            }
        }
        log.info("同步楼层信息任务结束");
        return jsonResult1;
    }
}
