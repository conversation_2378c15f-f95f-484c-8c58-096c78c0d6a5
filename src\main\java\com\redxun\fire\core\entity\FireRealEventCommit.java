package com.redxun.fire.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.redxun.fire.config.LongToJsonSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FireRealEventCommit implements Serializable {

    @JsonSerialize(using = LongToJsonSerializer.class)
    @TableId
    private Long id;

    @JsonSerialize(using = LongToJsonSerializer.class)
    private Long fireRealId;

    private String handlerUser;

    private String handlerUserName;

    private String handlerMessage;

    private String handlerImages;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String recoverMessage;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recoverTime;

    private String recover;

    private String recoverName;

    private String handleStatus;

}
