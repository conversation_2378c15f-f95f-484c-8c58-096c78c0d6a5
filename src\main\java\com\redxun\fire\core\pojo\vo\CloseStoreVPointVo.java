package com.redxun.fire.core.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 闭店B
 */
@Data
@Accessors(chain = true)
public class CloseStoreVPointVo implements Serializable {

    //点位ID
    private String pointId;

    //设备类型
    private String devTypeName;

    //建筑ID
    private String buildId;

    //商户号
    private String merchantCode;

    //商户名称
    private String merchantName;

    //设备状态 0未初始化 1初始化中 2初始化完成待确认 3初始化完成 5设备离线 4设备故障 7电流朝鲜异常报警 8 电流回归异常报警 9 突变电流异常报警 10设备正常 11异常报警 12 电量过低
    private String deviceStatus;

    //故障状态
    private String faultStatus;

    //异常状态
    private String exceptionStatus;

    /**
     * 信号强度
     */
    private String signals;


    //模块编号
    private String deviceNo;

    private String pointNumber;

    //实时电流
    private String realTimeCurrent;

    //用电状态
    private String powerStatus;

    //闭店电流区间
    private String closeStoreCurrent;

    //配置电箱位置
    private String pointDesc;

    //营业状态
    private String businessStatus;

    //回归时长
    private String returnTime;

    //开店时间
    private String openTime;

    //闭店时间
    private String closeTime;

    //最后的心跳时间
    private String heartbeatTime;
    /**
     * 断电状态，0-断电，1-已上电
     */
    private String outageStatus;

    /**
     * 设备状态 0-正常，1-离线
     */
    private String deviceStatusFora;
    private String hostId;

    /**
     * 电池状态 0-正常，1-低电量
     */
    private String electricalStatus ;

    /**
     * 闭店设备异常状态 0正常 1闭店异常报警
     */
    private Integer alarmStatus;

    ///////////////////////////////闭店设备A2/////////////////////////////////
    //A相电压
    private String aVoltage;
    //A相电流
    private String aCurrent;
    //开关闭锁状态
    private String lockStatus;
    //开关状态
    private String switchStatus;
    //漏电保护
    private String leakageProtection;
    //A相温度
    private String aTemperature;
}
